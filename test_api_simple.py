#!/usr/bin/env python3
"""
Simple test for Multi-Agent Pipeline API without authentication
Direct test using Django's test client
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# Add the ignition-api directory to Python path
sys.path.append('/Users/<USER>/GATE/ignition/ignition-api')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'main.settings')

import django
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from assistant.views import MultiAgentPipelineView

def create_test_user():
    """Create a test user for API testing"""
    User = get_user_model()
    
    try:
        # Try to get existing test user
        user = User.objects.get(email='<EMAIL>')
        print("✅ Using existing test user")
    except User.DoesNotExist:
        # Create new test user
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword123',
            first_name='Test',
            last_name='User'
        )
        print("✅ Created new test user")
    
    return user

def test_pipeline_direct():
    """Test the pipeline API directly"""
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"🚀 Direct Multi-Agent Pipeline Test - {timestamp}")
    print("=" * 80)
    
    # Create test user
    user = create_test_user()
    
    # Test data
    test_data = {
        "user_input": "I want to learn machine learning and data science from scratch. I have basic Python knowledge but no experience with ML algorithms, statistics, or data analysis. My goal is to become proficient enough to work on real ML projects. I can dedicate 15-20 hours per week for the next 6 months.",
        "duration": "6 months",
        "language": "english"
    }
    
    print(f"📝 Test Input:")
    print(f"   User Input: {test_data['user_input'][:100]}...")
    print(f"   Duration: {test_data['duration']}")
    print(f"   Language: {test_data['language']}")
    print()
    
    try:
        # Create API view instance
        view = MultiAgentPipelineView()
        
        # Create mock request
        from django.test import RequestFactory
        from django.contrib.auth.models import AnonymousUser
        
        factory = RequestFactory()
        request = factory.post('/api/assistant/multi-agent-pipeline', 
                              data=json.dumps(test_data),
                              content_type='application/json')
        request.user = user
        request.data = test_data
        
        print("🤖 Running pipeline...")
        start_time = datetime.now()
        
        # Call the API view
        response = view.post(request)
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        print(f"⏱️ Total execution time: {execution_time:.2f}s")
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.data
            
            print(f"✅ Success: {result.get('success', False)}")
            print(f"📝 Message: {result.get('message', 'N/A')}")
            print(f"⏱️ Pipeline Time: {result.get('execution_time', 'N/A')}s")
            
            # Analyze agents status
            agents_status = result.get('agents_status', {})
            print(f"\n🤖 Agents Status:")
            
            for agent_name, status in agents_status.items():
                agent_display = agent_name.replace('_', ' ').title()
                print(f"   {agent_display}:")
                print(f"     Status: {status.get('status', 'N/A')}")
                print(f"     Execution Time: {status.get('execution_time', 'N/A')}s")
                
                if 'domain' in status:
                    print(f"     Domain: {status['domain']}")
                if 'milestones_generated' in status:
                    print(f"     Milestones Generated: {status['milestones_generated']}")
                if 'enhanced_milestones' in status:
                    print(f"     Enhanced Milestones: {status['enhanced_milestones']}")
            
            # Analyze pipeline results
            pipeline_result = result.get('pipeline_result', {})
            
            # Domain Analysis
            domain_analysis = pipeline_result.get('domain_analysis', {})
            if domain_analysis:
                print(f"\n🎯 Domain Analysis:")
                print(f"   Primary Domain: {domain_analysis.get('primary_domain', 'N/A')}")
                print(f"   Confidence: {domain_analysis.get('confidence', 'N/A')}")
                print(f"   Category: {domain_analysis.get('category', 'N/A')}")
                print(f"   Description: {domain_analysis.get('description', 'N/A')[:100]}...")
            
            # Structure Design
            structure_design = pipeline_result.get('structure_design', {})
            if structure_design:
                milestones = structure_design.get('milestones', [])
                print(f"\n🏗️ Structure Design:")
                print(f"   Total Milestones: {len(milestones)}")
                print(f"   Project Overview: {structure_design.get('project_overview', 'N/A')[:100]}...")
                
                for i, milestone in enumerate(milestones[:3], 1):  # Show first 3
                    print(f"   {i}. {milestone.get('name', 'N/A')}")
                    print(f"      Duration: {milestone.get('duration', 'N/A')}")
                    print(f"      Tasks: {len(milestone.get('tasks', []))}")
                    
                    # Show first task
                    tasks = milestone.get('tasks', [])
                    if tasks:
                        first_task = tasks[0]
                        task_name = first_task.get('name', first_task) if isinstance(first_task, dict) else str(first_task)
                        print(f"      First Task: {task_name}")
            
            # Enhanced Content
            enhanced_content = pipeline_result.get('enhanced_content', {})
            if enhanced_content:
                enhanced_milestones = enhanced_content.get('enhanced_milestones', [])
                print(f"\n📝 Enhanced Content:")
                print(f"   Enhanced Milestones: {len(enhanced_milestones)}")
                
                if enhanced_milestones:
                    first_milestone = enhanced_milestones[0]
                    subtasks = first_milestone.get('subtasks', [])
                    print(f"   First Milestone: {first_milestone.get('name', 'N/A')}")
                    print(f"   Subtasks: {len(subtasks)}")
                    
                    if subtasks:
                        print(f"   Sample Subtasks:")
                        for j, subtask in enumerate(subtasks[:3], 1):
                            subtask_title = subtask.get('title', subtask.get('name', 'N/A'))
                            print(f"     {j}. {subtask_title}")
                            
                    # Show resources if available
                    resources = first_milestone.get('resources', [])
                    if resources:
                        print(f"   Resources: {len(resources)}")
                        for j, resource in enumerate(resources[:2], 1):
                            resource_title = resource.get('title', resource) if isinstance(resource, dict) else str(resource)
                            print(f"     {j}. {resource_title}")
            
            print(f"\n🎉 Test completed successfully!")
            return True
            
        else:
            print(f"❌ API Error: {response.status_code}")
            if hasattr(response, 'data'):
                print(f"Error details: {response.data}")
            return False
            
    except Exception as e:
        import traceback
        print(f"💥 Test failed: {str(e)}")
        print(f"🔍 Full traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_pipeline_direct()
