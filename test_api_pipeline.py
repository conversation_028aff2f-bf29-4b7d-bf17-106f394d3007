#!/usr/bin/env python3
"""
Test script for Multi-Agent Pipeline API
Tests the /api/assistant/multi-agent-pipeline endpoint with different scenarios
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
PIPELINE_ENDPOINT = f"{BASE_URL}/api/assistant/multi-agent-pipeline"

# Test cases in English
TEST_CASES = [
    {
        "name": "Web Development Beginner",
        "data": {
            "user_input": "I want to learn web development from scratch. I have no knowledge of HTML, CSS, JavaScript. My goal is to create a personal website and understand how web works. I can dedicate 2-3 hours daily for the next 3 months.",
            "duration": "3 months",
            "language": "english"
        }
    },
    {
        "name": "Data Science Career Change",
        "data": {
            "user_input": "I'm a marketing professional looking to transition into data science. I have basic Excel skills but no programming experience. I want to learn Python, statistics, and machine learning to get a data analyst job. I can study 15-20 hours per week for 6 months.",
            "duration": "6 months",
            "language": "english"
        }
    },
    {
        "name": "Mobile App Development",
        "data": {
            "user_input": "I want to develop mobile apps for iOS and Android. I have some Java experience from college but haven't coded in 2 years. My goal is to build and publish a productivity app. I can work on this 10-12 hours per week for 4 months.",
            "duration": "4 months",
            "language": "english"
        }
    }
]

def get_auth_token():
    """
    Get authentication token for API testing
    You need to replace this with actual login credentials
    """
    login_url = f"{BASE_URL}/api/user/login"
    
    # Replace with actual test user credentials
    login_data = {
        "email": "<EMAIL>",
        "password": "testpassword123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            token = response.json().get('access_token')
            print(f"✅ Authentication successful")
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return None

def test_pipeline_api(auth_token, test_case):
    """
    Test the multi-agent pipeline API with a specific test case
    """
    headers = {
        'Authorization': f'Bearer {auth_token}',
        'Content-Type': 'application/json'
    }
    
    print(f"\n🧪 Testing: {test_case['name']}")
    print("=" * 60)
    print(f"📝 Input: {test_case['data']['user_input'][:80]}...")
    print(f"⏱️ Duration: {test_case['data']['duration']}")
    print(f"🌐 Language: {test_case['data']['language']}")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            PIPELINE_ENDPOINT,
            headers=headers,
            json=test_case['data'],
            timeout=120  # 2 minutes timeout
        )
        
        execution_time = time.time() - start_time
        
        print(f"\n📊 API Response:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Response Time: {execution_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ Success: {result.get('success', False)}")
            print(f"📝 Message: {result.get('message', 'N/A')}")
            print(f"⏱️ Pipeline Execution Time: {result.get('execution_time', 'N/A')}s")
            
            # Analyze agents status
            agents_status = result.get('agents_status', {})
            print(f"\n🤖 Agents Status:")
            
            for agent_name, status in agents_status.items():
                agent_display = agent_name.replace('_', ' ').title()
                print(f"   {agent_display}:")
                print(f"     Status: {status.get('status', 'N/A')}")
                print(f"     Execution Time: {status.get('execution_time', 'N/A')}s")
                
                if 'domain' in status:
                    print(f"     Domain: {status['domain']}")
                if 'milestones_generated' in status:
                    print(f"     Milestones Generated: {status['milestones_generated']}")
                if 'enhanced_milestones' in status:
                    print(f"     Enhanced Milestones: {status['enhanced_milestones']}")
            
            # Analyze pipeline results
            pipeline_result = result.get('pipeline_result', {})
            
            # Domain Analysis
            domain_analysis = pipeline_result.get('domain_analysis', {})
            if domain_analysis:
                print(f"\n🎯 Domain Analysis:")
                print(f"   Primary Domain: {domain_analysis.get('primary_domain', 'N/A')}")
                print(f"   Confidence: {domain_analysis.get('confidence', 'N/A')}")
                print(f"   Category: {domain_analysis.get('category', 'N/A')}")
            
            # Structure Design
            structure_design = pipeline_result.get('structure_design', {})
            if structure_design:
                milestones = structure_design.get('milestones', [])
                print(f"\n🏗️ Structure Design:")
                print(f"   Total Milestones: {len(milestones)}")
                
                for i, milestone in enumerate(milestones[:3], 1):  # Show first 3
                    print(f"   {i}. {milestone.get('name', 'N/A')}")
                    print(f"      Duration: {milestone.get('duration', 'N/A')}")
                    print(f"      Tasks: {len(milestone.get('tasks', []))}")
            
            # Enhanced Content
            enhanced_content = pipeline_result.get('enhanced_content', {})
            if enhanced_content:
                enhanced_milestones = enhanced_content.get('enhanced_milestones', [])
                print(f"\n📝 Enhanced Content:")
                print(f"   Enhanced Milestones: {len(enhanced_milestones)}")
                
                if enhanced_milestones:
                    first_milestone = enhanced_milestones[0]
                    subtasks = first_milestone.get('subtasks', [])
                    print(f"   First Milestone Subtasks: {len(subtasks)}")
                    
                    if subtasks:
                        print(f"   Sample Subtasks:")
                        for j, subtask in enumerate(subtasks[:2], 1):
                            subtask_title = subtask.get('title', subtask.get('name', 'N/A'))
                            print(f"     {j}. {subtask_title}")
            
            return True
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ Request timeout after {execution_time:.2f}s")
        return False
    except Exception as e:
        print(f"💥 Request failed: {str(e)}")
        return False

def main():
    """
    Main test function
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"🚀 Multi-Agent Pipeline API Test - {timestamp}")
    print("=" * 80)
    
    # Get authentication token
    print("🔐 Getting authentication token...")
    auth_token = get_auth_token()
    
    if not auth_token:
        print("❌ Cannot proceed without authentication token")
        print("\n💡 To run this test:")
        print("1. Make sure Django server is running: python manage.py runserver")
        print("2. Update login credentials in get_auth_token() function")
        print("3. Ensure you have a test user account")
        return
    
    # Test all cases
    successful_tests = 0
    total_tests = len(TEST_CASES)
    
    for test_case in TEST_CASES:
        success = test_pipeline_api(auth_token, test_case)
        if success:
            successful_tests += 1
        
        print("\n" + "-" * 80)
    
    # Summary
    print(f"\n🏁 Test Summary:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Successful: {successful_tests}")
    print(f"   Failed: {total_tests - successful_tests}")
    print(f"   Success Rate: {(successful_tests/total_tests)*100:.1f}%")

if __name__ == "__main__":
    main()
