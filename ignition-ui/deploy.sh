#!/bin/bash

# Exit when any command fails
set -e

# Thông tin server
SERVER_HOST="ec2-18-142-99-235.ap-southeast-1.compute.amazonaws.com"
SERVER_USER="ubuntu"
SSH_KEY="~/.ssh/ignition/ignition.pem"
REMOTE_DIR="/var/www/ignitionai.site"
DOMAIN="ignitionai.site"

# Sao chép file .env.production thành .env để build
echo "Setting up production environment..."
cp .env.production .env

# Build ứng dụng React
echo "Building React application..."
npm install
npm run build

# Tạo thư mục build.tar.gz
echo "Creating build archive..."
tar -czf build.tar.gz build

# Đẩy file lên server
echo "Uploading build to server..."
scp -i $SSH_KEY build.tar.gz $SERVER_USER@$SERVER_HOST:~

# Kết nối SSH và thực hiện các lệnh trên server
echo "Connecting to server and deploying..."
ssh -i $SSH_KEY $SERVER_USER@$SERVER_HOST << EOF
  # Tạo thư mục nếu chưa tồn tại
  sudo mkdir -p $REMOTE_DIR

  # Giải nén build vào thư mục
  sudo rm -rf $REMOTE_DIR/*
  sudo tar -xzf build.tar.gz -C $REMOTE_DIR --strip-components=1
  rm build.tar.gz

  # Khởi động lại Nginx để áp dụng thay đổi
  sudo systemctl reload nginx

  echo "Deployment completed successfully!"
EOF

echo "Deployment process finished!"
