<!DOCTYPE html>
<html lang="en" class="perfect-scrollbar-off nav-open">
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, shrink-to-fit=no"
    />
    <meta name="theme-color" content="#000000" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico" />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="%PUBLIC_URL%/apple-icon.png"
    />
    <link
      href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Recursive:wght@300;400;500;600;700;800;900&display=swap"
      rel="stylesheet"
    />
    <title>Ignition - AI Plan for you</title>
    <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY_HERE"></script>

    <!-- ResizeObserver Error Suppression -->
    <script>
      // Early ResizeObserver error suppression
      (function() {
        'use strict';

        // Suppress ResizeObserver errors globally
        const originalError = window.onerror;
        window.onerror = function(message, source, lineno, colno, error) {
          if (typeof message === 'string' &&
              (message.includes('ResizeObserver loop completed') ||
               message.includes('ResizeObserver loop limit exceeded'))) {
            return true; // Prevent default error handling
          }
          if (originalError) {
            return originalError.apply(this, arguments);
          }
          return false;
        };

        // Suppress unhandled promise rejections for ResizeObserver
        const originalUnhandledRejection = window.onunhandledrejection;
        window.onunhandledrejection = function(event) {
          if (event.reason && event.reason.message &&
              event.reason.message.includes('ResizeObserver')) {
            event.preventDefault();
            return;
          }
          if (originalUnhandledRejection) {
            originalUnhandledRejection.apply(this, arguments);
          }
        };

        // Override ResizeObserver to prevent loops
        if (typeof ResizeObserver !== 'undefined') {
          const OriginalResizeObserver = ResizeObserver;
          window.ResizeObserver = class extends OriginalResizeObserver {
            constructor(callback) {
              const wrappedCallback = (entries, observer) => {
                requestAnimationFrame(() => {
                  try {
                    callback(entries, observer);
                  } catch (error) {
                    if (!error.message || !error.message.includes('ResizeObserver')) {
                      console.warn('ResizeObserver error:', error);
                    }
                  }
                });
              };
              super(wrappedCallback);
            }
          };
        }
      })();
    </script>
  </head>
  <body>
    <noscript> You need to enable JavaScript to run this app. </noscript>
    <div id="root"></div>
  </body>
</html>
