# Hướng dẫn thiết lập CI/CD

Dự án này sử dụng GitHub Actions để tự động hóa quá trình kiểm tra code (CI) và triển khai (CD).

## Workflow CI/CD

Dự án sử dụng một workflow duy nhất (`.github/workflows/ci-cd.yml`) để xử lý cả CI và CD:

1. **CI (Continuous Integration)**:
   - Ch<PERSON>y khi có pull request vào nhánh master
   - Kiểm tra linting
   - Chạy tests

2. **CD (Continuous Deployment)**:
   - <PERSON><PERSON><PERSON> khi có push vào nhánh master hoặc khi được kích hoạt thủ công
   - Build ứng dụng
   - Deploy lên server

## Thiết lập Secrets

Để workflow hoạt động, bạn cần thiết lập các secrets sau trong repository GitHub:

1. Mở repository trên GitHub
2. <PERSON><PERSON><PERSON> Settings > Secrets and variables > Actions
3. Thêm các secrets sau:

### Thông tin server
- `SERVER_HOST`: Hostname của server (ví dụ: ec2-18-142-99-235.ap-southeast-1.compute.amazonaws.com)
- `SERVER_USER`: Username để SSH vào server (ví dụ: ubuntu)
- `REMOTE_DIR`: Thư mục trên server để deploy ứng dụng (ví dụ: /var/www/ignitionai.site)
- `SSH_PRIVATE_KEY`: Nội dung của private key để SSH vào server (toàn bộ nội dung của file ~/.ssh/ignition/ignition.pem)

### Biến môi trường
- `REACT_APP_API_URL`: URL của API (ví dụ: https://api.ignitionai.site)
- `REACT_APP_GOOGLE_CLIENT_ID`: Google Client ID
- `REACT_APP_GOOGLE_REDIRECT_URL_ENDPOINT`: Endpoint cho Google redirect (ví dụ: /google)

## Cách lấy SSH Private Key

1. Mở terminal
2. Chạy lệnh sau để xem nội dung của private key:
   ```
   cat ~/.ssh/ignition/ignition.pem
   ```
3. Sao chép toàn bộ nội dung (bao gồm cả dòng `-----BEGIN RSA PRIVATE KEY-----` và `-----END RSA PRIVATE KEY-----`)
4. Dán vào secret `SSH_PRIVATE_KEY` trong GitHub

## Kiểm tra workflow

Sau khi thiết lập các secrets, bạn có thể kiểm tra workflow bằng cách:

1. Tạo một commit và push lên nhánh master để kích hoạt CD
2. Tạo một pull request vào nhánh master để kích hoạt CI
3. Vào tab "Actions" trong repository GitHub để xem trạng thái của workflow

## Lưu ý

- Đảm bảo server đã được cấu hình đúng với Nginx và các thư mục cần thiết
- Đảm bảo user trên server có quyền sudo để thực hiện các lệnh trong workflow
- Workflow sẽ tự động xác định nên chạy CI hay CD dựa trên loại sự kiện (pull request hoặc push) 