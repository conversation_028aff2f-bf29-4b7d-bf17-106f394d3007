import { createSlice } from '@reduxjs/toolkit';

const userSlice = createSlice({
  name: 'user',
  initialState: {
    first_name: null,
    last_name: null,
    avatar: null,
    id: null,
  },
  reducers: {
    setUser: (state, action) => {
      state.first_name = action.payload.first_name;
      state.last_name = action.payload.last_name;
      state.avatar = action.payload.avatar;
      state.id = action.payload.id;
    },
    clearUser: (state) => {
      state.first_name = null;
      state.last_name = null;
      state.avatar = null;
      state.id = null;
    },
  },
});

export const { setUser, clearUser } = userSlice.actions;
export default userSlice.reducer;
