import React, { useEffect, useState, useCallback } from "react";
import { useLocation, Route, Routes, Navigate } from "react-router-dom";
import { Fab, Tooltip } from "@mui/material";
import MobileHeader from "components/Sidebar/MobileHeader";
import AdminFooter from "components/Footers/AdminFooter";
import SidebarComponent from "components/Sidebar/SidebarComponent";
import RightSidebar from "components/Sidebar/RightSidebar";
import Iconify from "components/Iconify/index";
import { ADMIN_PAGE_KEY, SIDEBAR_COLLAPSED_LEFT_PX, SIDEBAR_EXPANED_LEFT_PX, mainYellowColor } from "helpers/constants";
import { getRoutes } from "helpers/auth";
import routes from "routes/index";

//--------------------------------------------------------------------------------------------------

const AdminLayout = () => {
  const mainContent = React.useRef(null);
  const location = useLocation();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);
  const [isSmartphone, setIsSmartphone] = useState(window.innerWidth < 768);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(() => {
    // Get saved state from localStorage, default to true
    const saved = localStorage.getItem('rightSidebarOpen');
    return saved !== null ? JSON.parse(saved) : true;
  });

  // Kiểm tra xem có nên hiển thị right sidebar hay không dựa trên đường dẫn hiện tại
  const shouldShowRightSidebar = useCallback(() => {
    const path = location.pathname;
    console.log("Current path:", path);
    
    // Danh sách các đường dẫn cần ẩn right sidebar
    const hiddenPaths = [
      // Trang Calendar
      '/my/tasks/calendar',
      '/d/my/tasks/calendar',

      // Trang Profile
      '/profile',
      '/d/profile',

      // Trang Plan Creation
      '/plan/create',
      '/d/plan/create',

      // Trang Plan Detail
      '/plan/',
      '/d/plan/'

      // Không ẩn ở trang Home (index.js)
      // Removed todo table paths since we removed the todo list page
    ];
    
    // Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách cần ẩn không
    const shouldHide = hiddenPaths.some(hiddenPath => {
      if (hiddenPath.endsWith('/')) {
        // Đối với đường dẫn kết thúc bằng '/', kiểm tra xem path có bắt đầu bằng hiddenPath không
        return path.startsWith(hiddenPath) || path === hiddenPath.slice(0, -1);
      }
      return path === hiddenPath || path === hiddenPath + '/';
    });
    
    console.log("Should hide right sidebar:", shouldHide);
    return !shouldHide;
  }, [location.pathname]);

  useEffect(() => {
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
    mainContent.current.scrollTop = 0;
  }, [location]);

  useEffect(() => {
    const handleResize = () => {
      const newIsSmartphone = window.innerWidth < 768;
      setIsSmartphone(newIsSmartphone);
      
      if (newIsSmartphone) {
        setRightSidebarOpen(false);
      } else {
        setRightSidebarOpen(shouldShowRightSidebar());
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [location, shouldShowRightSidebar]);

  // Cập nhật trạng thái right sidebar khi đường dẫn thay đổi
  useEffect(() => {
    if (!isSmartphone) {
      const show = shouldShowRightSidebar();
      console.log("Should show right sidebar:", show);

      if (show) {
        // If we're on a page that should show the sidebar, respect saved state
        const saved = localStorage.getItem('rightSidebarOpen');
        const savedState = saved !== null ? JSON.parse(saved) : true;
        setRightSidebarOpen(savedState);
      } else {
        // If we're on a page that shouldn't show the sidebar, hide it
        setRightSidebarOpen(false);
      }
    }
  }, [location, isSmartphone, shouldShowRightSidebar]);

  const toggleRightSidebar = () => {
    const newState = !rightSidebarOpen;
    setRightSidebarOpen(newState);
    // Save state to localStorage
    localStorage.setItem('rightSidebarOpen', JSON.stringify(newState));
  };

  // Tính toán margin và width cho main content
  const leftMargin = isSmartphone ? '0' : sidebarCollapsed ? SIDEBAR_COLLAPSED_LEFT_PX : SIDEBAR_EXPANED_LEFT_PX;
  const rightMargin = isSmartphone ? '0' : (rightSidebarOpen && shouldShowRightSidebar()) ? 'min(200px, 20vw)' : '0';

  const mainContentStyle = {
    marginLeft: leftMargin,
    marginRight: rightMargin,
    transition: 'margin-left 0.3s ease, margin-right 0.3s ease',
    width: isSmartphone ? '100%' : rightMargin === '0' ? `calc(100% - ${leftMargin})` : `calc(100% - ${leftMargin} - min(200px, 20vw))`,
    maxWidth: '100%',
    overflow: 'hidden'
  };

  // Log để debug
  console.log("rightSidebarOpen:", rightSidebarOpen);
  console.log("shouldShowRightSidebar:", shouldShowRightSidebar());
  console.log("rightMargin:", rightMargin);

  return (
    <>
      {isSmartphone ? (<MobileHeader
        onCollapseChange={setSidebarCollapsed}
        logo={{
          innerLink: "/d/",
          imgSrc: require("../assets/main_logo.png"),
          imgAlt: "Logo",
        }} />
      ) : (
        <SidebarComponent onCollapseChange={setSidebarCollapsed}
          logo={{
            innerLink: "/d",
            imgSrc: require(sidebarCollapsed ? "../assets/fire_logo.png" : "../assets/main_logo.png"),
            imgAlt: "Logo",
          }} />
      )}
      <div className="main-content" ref={mainContent} style={{
        ...mainContentStyle,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
        borderRadius: '12px 0 0 0',
        backgroundColor: '#fafafa',
        minHeight: '100vh'
      }}>
        <Routes>
          {getRoutes(routes, ADMIN_PAGE_KEY)}
          <Route path="*" element={<Navigate to="/d/" replace />} />
        </Routes>
        <AdminFooter />
      </div>
      
      {/* Right Sidebar for Today's Tasks - Chỉ hiển thị ở các trang được chỉ định */}
      {shouldShowRightSidebar() && (
        <>
          <RightSidebar isOpen={rightSidebarOpen} onToggle={toggleRightSidebar} />

          {/* Floating show button when sidebar is closed */}
          {!rightSidebarOpen && !isSmartphone && (
            <Tooltip title="Show Todo List" placement="left">
              <Fab
                onClick={toggleRightSidebar}
                size="medium"
                sx={{
                  position: 'fixed',
                  top: '50%',
                  right: '16px',
                  transform: 'translateY(-50%)',
                  backgroundColor: `${mainYellowColor} !important`,
                  color: 'white !important',
                  zIndex: 1001,
                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
                  '&:hover': {
                    backgroundColor: `${mainYellowColor} !important`,
                    transform: 'translateY(-50%) scale(1.1)',
                  },
                  transition: 'transform 0.2s ease'
                }}
              >
                <Iconify icon="material-symbols:checklist" width={24} height={24} />
              </Fab>
            </Tooltip>
          )}
        </>
      )}
    </>
  );
};

export default AdminLayout;
