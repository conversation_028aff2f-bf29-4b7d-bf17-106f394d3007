import React from "react";
import { Routes } from "react-router-dom";
import { Container } from "@mui/material";
import { getRoutes } from "helpers/auth";
import { PUBLIC_PAGE_KEY } from "helpers/constants";
import routes from "routes/index";

//--------------------------------------------------------------------------------------------------

const PublicLayout = () => {
  const mainContent = React.useRef(null);

  return (
    <div className="main-content" ref={mainContent}>
      <Container fluid sx={{ minHeight: '100vh' }}>
        <Routes>
          {getRoutes(routes, PUBLIC_PAGE_KEY)}
        </Routes>
      </Container>
    </div>
  );
};

export default PublicLayout;
