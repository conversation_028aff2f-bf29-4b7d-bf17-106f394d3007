// Performance Optimizations for Better UI Responsiveness
// This file contains CSS optimizations to reduce layout thrashing and improve hover performance

// Enable hardware acceleration for common interactive elements
.MuiCard-root,
.MuiButton-root,
.MuiChip-root,
.MuiFab-root {
  will-change: transform, box-shadow;
  transform: translateZ(0); // Force hardware acceleration
}

// Optimize hover transitions to use only transform and opacity
.performance-optimized-hover {
  transition: transform 0.2s ease, opacity 0.2s ease !important;
  will-change: transform, opacity;
  
  &:hover {
    transform: translateY(-2px) translateZ(0);
  }
}

// Reduce paint complexity for plan cards
.plan-card-optimized {
  contain: layout style paint;
  will-change: transform, box-shadow;
  backface-visibility: hidden;
  perspective: 1000px;
  
  &:hover {
    transform: translateY(-3px) translateZ(0);
  }
}

// Optimize sidebar hover performance
.sidebar-optimized {
  contain: layout;
  will-change: transform;
  
  .sidebar-item {
    will-change: background-color, color;
    transition: background-color 0.15s ease, color 0.15s ease;
    
    &:hover {
      // Use only background-color changes, avoid box-shadow
      background-color: rgba(240, 165, 0, 0.1);
    }
  }
}

// Optimize plan selection cards for better hover performance
.plan-option-card {
  contain: layout style;
  will-change: transform, border-color;
  transform: translateZ(0);
  transition: transform 0.2s ease, border-color 0.2s ease;
  
  &:hover {
    transform: translateY(-2px) translateZ(0);
  }
  
  // Avoid expensive box-shadow animations
  &.selected {
    border-color: #F0A500;
  }
}

// Optimize modal animations
.modal-optimized {
  contain: layout;
  will-change: transform, opacity;
  
  .modal-content {
    contain: layout style;
    transform: translateZ(0);
  }
}

// Reduce reflow for responsive layouts
.responsive-container {
  contain: layout;
  
  .responsive-item {
    contain: layout style;
    flex: 0 1 auto;
    min-width: 0; // Prevent flex items from overflowing
  }
}

// Optimize task list performance
.task-list-optimized {
  contain: layout;
  
  .task-item {
    contain: layout style;
    will-change: transform, background-color;
    transition: transform 0.15s ease, background-color 0.15s ease;
    
    &:hover {
      transform: translateY(-1px) translateZ(0);
      background-color: rgba(240, 165, 0, 0.05);
    }
  }
}

// Optimize button hover states
.button-optimized {
  will-change: background-color, box-shadow;
  transition: background-color 0.2s ease, box-shadow 0.2s ease;
  transform: translateZ(0);
  
  &:hover {
    // Avoid transform animations on buttons for better performance
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// Optimize input field focus states
.input-optimized {
  will-change: border-color, box-shadow;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(240, 165, 0, 0.2);
  }
}

// Optimize scrollable areas
.scroll-optimized {
  contain: layout;
  overflow-anchor: none; // Prevent scroll anchoring issues
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.3);
    }
  }
}

// Optimize grid layouts
.grid-optimized {
  contain: layout;
  
  .grid-item {
    contain: layout style;
    will-change: transform;
    
    &:hover {
      transform: translateY(-2px) translateZ(0);
    }
  }
}

// Optimize typography for better rendering
.text-optimized {
  text-rendering: optimizeSpeed;
  font-smooth: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// Optimize images for better loading
.image-optimized {
  will-change: opacity;
  transition: opacity 0.3s ease;
  
  &.loading {
    opacity: 0.7;
  }
  
  &.loaded {
    opacity: 1;
  }
}

// Optimize animations to use only transform and opacity
@keyframes optimized-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

@keyframes optimized-slide-in {
  from {
    transform: translateX(-100%) translateZ(0);
  }
  to {
    transform: translateX(0) translateZ(0);
  }
}

// Apply optimizations to common Material-UI components
.MuiCard-root {
  &.performance-optimized {
    contain: layout style;
    will-change: transform, box-shadow;
    
    &:hover {
      transform: translateY(-2px) translateZ(0);
    }
  }
}

.MuiButton-root {
  &.performance-optimized {
    will-change: background-color, box-shadow;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
      // Avoid transform on buttons
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

.MuiTextField-root {
  &.performance-optimized {
    .MuiOutlinedInput-root {
      will-change: border-color, box-shadow;
      transition: border-color 0.2s ease, box-shadow 0.2s ease;
      
      &:hover {
        border-color: rgba(240, 165, 0, 0.5);
      }
      
      &.Mui-focused {
        box-shadow: 0 0 0 2px rgba(240, 165, 0, 0.2);
      }
    }
  }
}

// Optimize layout shifts
.layout-stable {
  contain: layout;
  
  * {
    box-sizing: border-box;
  }
}

// Reduce paint complexity
.paint-optimized {
  contain: paint;
  isolation: isolate;
}

// Optimize composite layers
.composite-optimized {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}
