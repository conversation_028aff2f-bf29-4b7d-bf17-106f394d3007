// Differences from the HTML to the React product

// Import unified breakpoints
@import "../shared/breakpoints";

// bootstrap
@import "~bootstrap/scss/spinners";
// react plugins
@import "plugins/plugin-react-datetime";
// core components
@import "buttons";
@import "mixins";
@import "navbar-dropdown";
@import "navbar";
@import "tables";

// Styles for main content with right sidebar
.main-content {
  transition: margin-left 0.3s ease, margin-right 0.3s ease;
  position: relative;
  min-height: 100vh;
  padding: 0 20px;
  box-sizing: border-box;

  @include desktop-only {
    padding: 0 15px;
  }

  @include tablet-only {
    padding: 0 12px;
  }

  @include mobile-only {
    padding: 0 10px;
  }
}

// Styles for content containers
.content-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

// Styles for plan detail page
.container {
  max-width: 100% !important;
  width: 100% !important;
  padding: 20px !important;
  margin: 0 !important;
}

// Add any other React specific differences here
