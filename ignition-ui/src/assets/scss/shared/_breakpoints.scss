// Unified breakpoints for consistent responsive design
// These breakpoints should be used across all components

// Breakpoint values
$breakpoint-xs: 0px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1400px;

// Media query mixins for consistent usage
@mixin mobile-only {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin mobile-up {
  @media (min-width: #{$breakpoint-sm}) {
    @content;
  }
}

@mixin tablet-only {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: #{$breakpoint-md}) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin large-desktop-only {
  @media (min-width: #{$breakpoint-lg}) and (max-width: #{$breakpoint-xl - 1px}) {
    @content;
  }
}

@mixin large-desktop-up {
  @media (min-width: #{$breakpoint-xl}) {
    @content;
  }
}

@mixin extra-large-desktop-up {
  @media (min-width: #{$breakpoint-xxl}) {
    @content;
  }
}

// Custom breakpoint mixin
@mixin custom-breakpoint($min-width: null, $max-width: null) {
  @if $min-width and $max-width {
    @media (min-width: #{$min-width}) and (max-width: #{$max-width}) {
      @content;
    }
  } @else if $min-width {
    @media (min-width: #{$min-width}) {
      @content;
    }
  } @else if $max-width {
    @media (max-width: #{$max-width}) {
      @content;
    }
  }
}

// Viewport width units for responsive components
$sidebar-width-mobile: 90vw;
$sidebar-width-tablet: 25vw;
$sidebar-width-desktop: 20vw;
$sidebar-width-large: 18vw;

// Right sidebar specific widths using vw units with max width constraint
$right-sidebar-width-mobile: min(200px, 20vw);
$right-sidebar-width-tablet: min(200px, 20vw);
$right-sidebar-width-desktop: min(200px, 20vw);
$right-sidebar-width-large: min(200px, 20vw);
$right-sidebar-width-xl: min(200px, 20vw);
