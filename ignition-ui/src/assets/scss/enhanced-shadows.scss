// Enhanced Box Shadows for Better UI Depth
// This file adds comprehensive box shadow utilities for improved visual hierarchy

// Enhanced shadow variables
$shadow-color-light: rgba(0, 0, 0, 0.08);
$shadow-color-medium: rgba(0, 0, 0, 0.12);
$shadow-color-dark: rgba(0, 0, 0, 0.16);
$shadow-color-accent: rgba(240, 165, 0, 0.15);

// Enhanced shadow utilities
.shadow-enhanced-sm {
  box-shadow: 0 2px 8px $shadow-color-light, 0 1px 4px rgba(0, 0, 0, 0.04) !important;
}

.shadow-enhanced {
  box-shadow: 0 4px 16px $shadow-color-medium, 0 2px 8px $shadow-color-light !important;
}

.shadow-enhanced-lg {
  box-shadow: 0 8px 32px $shadow-color-dark, 0 4px 16px $shadow-color-medium !important;
}

.shadow-enhanced-xl {
  box-shadow: 0 16px 48px $shadow-color-dark, 0 8px 24px $shadow-color-medium !important;
}

// Accent shadows with brand color
.shadow-accent {
  box-shadow: 0 4px 16px $shadow-color-accent, 0 2px 8px $shadow-color-light !important;
}

.shadow-accent-lg {
  box-shadow: 0 8px 32px $shadow-color-accent, 0 4px 16px $shadow-color-medium !important;
}

// Hover shadow effects
.shadow-hover-lift {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px $shadow-color-medium, 0 4px 16px $shadow-color-light !important;
  }
}

.shadow-hover-accent {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &:hover {
    box-shadow: 0 8px 32px $shadow-color-accent, 0 4px 16px $shadow-color-medium !important;
  }
}

// Apply enhanced shadows to common components
.MuiCard-root {
  box-shadow: 0 4px 16px $shadow-color-light, 0 2px 8px rgba(0, 0, 0, 0.04) !important;
  transition: all 0.3s ease !important;
  
  &:hover {
    box-shadow: 0 8px 24px $shadow-color-medium, 0 4px 12px $shadow-color-light !important;
    transform: translateY(-2px);
  }
}

.MuiPaper-root {
  &.MuiPaper-elevation1 {
    box-shadow: 0 2px 8px $shadow-color-light !important;
  }
  
  &.MuiPaper-elevation2 {
    box-shadow: 0 4px 16px $shadow-color-light !important;
  }
  
  &.MuiPaper-elevation3 {
    box-shadow: 0 6px 20px $shadow-color-medium !important;
  }
  
  &.MuiPaper-elevation4 {
    box-shadow: 0 8px 24px $shadow-color-medium !important;
  }
}

// Enhanced button shadows
.MuiButton-root {
  &.MuiButton-contained {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06) !important;
    transition: all 0.2s ease !important;
    
    &:hover {
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15), 0 3px 8px rgba(0, 0, 0, 0.1) !important;
      transform: translateY(-1px);
    }
    
    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }
  }
}

// Enhanced modal shadows
.MuiModal-root .MuiPaper-root {
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2), 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

// Enhanced dialog shadows
.MuiDialog-paper {
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2), 0 8px 24px rgba(0, 0, 0, 0.15) !important;
}

// Enhanced menu shadows
.MuiMenu-paper {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

// Enhanced tooltip shadows
.MuiTooltip-tooltip {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

// Enhanced input field shadows
.MuiTextField-root .MuiOutlinedInput-root {
  transition: all 0.2s ease !important;
  
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  }
  
  &.Mui-focused {
    box-shadow: 0 4px 12px $shadow-color-accent !important;
  }
}

// Enhanced chip shadows
.MuiChip-root {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.2s ease !important;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
    transform: translateY(-1px);
  }
}

// Enhanced floating action button shadows
.MuiFab-root {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 3px 10px rgba(0, 0, 0, 0.1) !important;
  
  &:hover {
    box-shadow: 0 8px 28px rgba(0, 0, 0, 0.2), 0 4px 14px rgba(0, 0, 0, 0.15) !important;
  }
}

// Enhanced app bar shadows
.MuiAppBar-root {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
}

// Enhanced drawer shadows
.MuiDrawer-paper {
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1) !important;
}

// Enhanced table shadows
.MuiTableContainer-root {
  box-shadow: 0 4px 16px $shadow-color-light !important;
  border-radius: 8px !important;
  overflow: hidden;
}

// Enhanced accordion shadows
.MuiAccordion-root {
  box-shadow: 0 2px 8px $shadow-color-light !important;
  
  &.Mui-expanded {
    box-shadow: 0 4px 16px $shadow-color-medium !important;
  }
}

// Enhanced stepper shadows
.MuiStepper-root {
  .MuiStep-root .MuiStepLabel-root .MuiStepIcon-root {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
  }
}

// Enhanced breadcrumb shadows
.MuiBreadcrumbs-root {
  .MuiChip-root {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08) !important;
  }
}
