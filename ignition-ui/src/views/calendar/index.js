/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
  Button,
  CircularProgress,
  Grid,
  ButtonGroup,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  Avatar,
  AvatarGroup
} from '@mui/material';
import { getHeaders } from "helpers/functions";
import { APIURL, mainYellowColor } from "helpers/constants";
import Iconify from 'components/Iconify/index';
import dayjs from 'dayjs';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';

// Task Detail Modal Component
const TaskDetailModal = ({
  open,
  onClose,
  task,
  getStatusInfo,
  calculateTaskProgress,
  getTaskDuration
}) => {
  const navigate = useNavigate();
  if (!task) return null;

  const statusInfo = getStatusInfo(task);
  const progress = calculateTaskProgress(task);
  const completedSubtasks = task.subtasks?.filter(subtask => subtask.status === 3 || subtask.progress === 100).length || 0;
  const totalSubtasks = task.subtasks?.length || 0;

  const handleViewDetails = () => {
    console.log('Calendar task "View in Plan" clicked:', task);

    // Use plan_slug if available, otherwise plan_id
    const planIdentifier = task.plan_slug || task.plan_id;

    // Set the active tab to milestones to show the task
    localStorage.setItem(`plan_${planIdentifier}_activeTab`, 'milestones');

    // Store the target task information for highlighting (same as todo list)
    const targetTaskData = {
      taskId: task.id,
      taskSlug: task.slug,
      taskName: task.name
    };
    console.log('Storing target task data from calendar:', targetTaskData);
    localStorage.setItem(`plan_${planIdentifier}_targetTask`, JSON.stringify(targetTaskData));

    // Navigate to the plan detail page
    navigate(`/d/plan/${planIdentifier}`);
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          border: '1px solid #f0f0f0'
        }
      }}
    >
      <DialogTitle sx={{ 
        pb: 1,
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'space-between'
      }}>
        <Box>
          <Typography variant="h6" sx={{ 
            fontWeight: 600,
            color: '#333',
            fontFamily: '"Recursive Variable", sans-serif',
            mb: 0.5
          }}>
            {task.name}
          </Typography>
          <Chip
            icon={<Iconify icon={statusInfo.icon} width={16} height={16} />}
            label={statusInfo.text}
            size="small"
            sx={{
              height: '24px',
              backgroundColor: `${statusInfo.color}15`,
              color: statusInfo.color,
              fontFamily: '"Recursive Variable", sans-serif',
              '& .MuiChip-label': {
                px: 1,
                fontSize: '0.75rem',
                fontWeight: 600
              }
            }}
          />
        </Box>
        <IconButton onClick={onClose} size="small">
          <Iconify icon="material-symbols:close" width={20} height={20} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {/* Timeline */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ 
            color: '#666',
            fontFamily: '"Recursive Variable", sans-serif',
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            mb: 1
          }}>
            <Iconify icon="material-symbols:schedule" width={16} height={16} />
            Timeline
          </Typography>
          <Box sx={{ 
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            backgroundColor: '#f5f7fa',
            p: 2,
            borderRadius: '8px'
          }}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="caption" sx={{ color: '#666' }}>Start Date</Typography>
              <Typography variant="body2" sx={{ fontWeight: 600, color: '#333' }}>
                {dayjs(task.start_date).format('DD MMM YYYY')}
              </Typography>
              <Typography variant="caption" sx={{ color: '#666' }}>
                {dayjs(task.start_date).format('HH:mm')}
              </Typography>
            </Box>
            <Box sx={{ flex: 1, display: 'flex', alignItems: 'center' }}>
              <Box sx={{ flex: 1, height: '2px', backgroundColor: `${statusInfo.color}30` }} />
              <Tooltip title={`${getTaskDuration(task)} days`}>
                <Box sx={{ 
                  px: 1,
                  py: 0.5,
                  backgroundColor: `${statusInfo.color}15`,
                  color: statusInfo.color,
                  borderRadius: '4px',
                  fontSize: '0.75rem',
                  fontWeight: 600
                }}>
                  {getTaskDuration(task)}d
                </Box>
              </Tooltip>
              <Box sx={{ flex: 1, height: '2px', backgroundColor: `${statusInfo.color}30` }} />
            </Box>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="caption" sx={{ color: '#666' }}>End Date</Typography>
              <Typography variant="body2" sx={{ fontWeight: 600, color: '#333' }}>
                {dayjs(task.end_date).format('DD MMM YYYY')}
              </Typography>
              <Typography variant="caption" sx={{ color: '#666' }}>
                {dayjs(task.end_date).format('HH:mm')}
              </Typography>
            </Box>
          </Box>

          {/* Deadline */}
          {task.deadline && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              backgroundColor: '#fff3cd',
              p: 2,
              borderRadius: '8px',
              border: '1px solid #ffeaa7',
              mt: 2
            }}>
              <Iconify icon="material-symbols:alarm" width={20} height={20} sx={{ color: '#f39c12' }} />
              <Box>
                <Typography variant="subtitle2" sx={{ color: '#856404', fontWeight: 600 }}>
                  Deadline: {dayjs(task.deadline).format('DD MMM YYYY')}
                </Typography>
                <Typography variant="caption" sx={{ color: '#856404' }}>
                  {dayjs(task.deadline).fromNow()}
                </Typography>
              </Box>
            </Box>
          )}
        </Box>

        {/* Description */}
        {task.description && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ 
              color: '#666',
              fontFamily: '"Recursive Variable", sans-serif',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 1
            }}>
              <Iconify icon="material-symbols:description" width={16} height={16} />
              Description
            </Typography>
            <Typography variant="body2" sx={{ 
              color: '#444',
              backgroundColor: '#f8f9fa',
              p: 2,
              borderRadius: '8px',
              whiteSpace: 'pre-wrap'
            }}>
              {task.description}
            </Typography>
          </Box>
        )}

        {/* Subtasks */}
        {task.subtasks && task.subtasks.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ 
              color: '#666',
              fontFamily: '"Recursive Variable", sans-serif',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 1
            }}>
              <Iconify icon="material-symbols:checklist" width={16} height={16} />
              Subtasks ({completedSubtasks}/{totalSubtasks})
            </Typography>
            <LinearProgress 
              variant="determinate" 
              value={progress}
              sx={{
                height: 6,
                borderRadius: 3,
                mb: 2,
                backgroundColor: `${statusInfo.color}20`,
                '& .MuiLinearProgress-bar': {
                  backgroundColor: statusInfo.color
                }
              }}
            />
            <Stack spacing={1}>
              {task.subtasks.map((subtask) => (
                <Box
                  key={subtask.id}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    p: 1,
                    borderRadius: '4px',
                    backgroundColor: (subtask.status === 3 || subtask.progress === 100) ? '#4CAF5010' : '#f8f9fa',
                    border: '1px solid',
                    borderColor: (subtask.status === 3 || subtask.progress === 100) ? '#4CAF5020' : '#f0f0f0'
                  }}
                >
                  <Iconify 
                    icon={(subtask.status === 3 || subtask.progress === 100) ? 'material-symbols:check-circle' : 'material-symbols:radio-button-unchecked'} 
                    width={16} 
                    height={16}
                    sx={{ color: (subtask.status === 3 || subtask.progress === 100) ? '#4CAF50' : '#999' }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: (subtask.status === 3 || subtask.progress === 100) ? '#4CAF50' : '#666',
                    flex: 1,
                    fontWeight: (subtask.status === 3 || subtask.progress === 100) ? 600 : 400
                  }}>
                    {subtask.name}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </Box>
        )}

        {/* Assignees */}
        {task.assignees && task.assignees.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ 
              color: '#666',
              fontFamily: '"Recursive Variable", sans-serif',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 1
            }}>
              <Iconify icon="material-symbols:group" width={16} height={16} />
              Assignees
            </Typography>
            <AvatarGroup max={5} sx={{ justifyContent: 'flex-start' }}>
              {task.assignees.map((assignee) => (
                <Tooltip key={assignee.id} title={`${assignee.first_name} ${assignee.last_name}`}>
                  <Avatar 
                    src={assignee.avatar} 
                    alt={`${assignee.first_name} ${assignee.last_name}`}
                    sx={{ width: 32, height: 32 }}
                  />
                </Tooltip>
              ))}
            </AvatarGroup>
          </Box>
        )}

        {/* Comments */}
        {task.comments && task.comments.length > 0 && (
          <Box>
            <Typography variant="subtitle2" sx={{ 
              color: '#666',
              fontFamily: '"Recursive Variable", sans-serif',
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              mb: 1
            }}>
              <Iconify icon="material-symbols:comment" width={16} height={16} />
              Comments ({task.comments.length})
            </Typography>
            <Stack spacing={2}>
              {task.comments.map((comment) => (
                <Box
                  key={comment.id}
                  sx={{
                    display: 'flex',
                    gap: 2,
                    p: 2,
                    backgroundColor: '#f8f9fa',
                    borderRadius: '8px'
                  }}
                >
                  <Avatar 
                    src={comment.user.avatar} 
                    alt={`${comment.user.first_name} ${comment.user.last_name}`}
                    sx={{ width: 32, height: 32 }}
                  />
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                      <Typography variant="subtitle2" sx={{ color: '#333' }}>
                        {comment.user.first_name} {comment.user.last_name}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#666' }}>
                        {dayjs(comment.created_at).format('HH:mm DD/MM/YYYY')}
                      </Typography>
                    </Box>
                    <Typography variant="body2" sx={{ color: '#444', whiteSpace: 'pre-wrap' }}>
                      {comment.content}
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Stack>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2.5, pt: 2, gap: 1 }}>
        <Button
          onClick={handleViewDetails}
          startIcon={<Iconify icon="material-symbols:visibility" width={20} height={20} />}
          sx={{
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            color: mainYellowColor,
            backgroundColor: 'white',
            border: `1px solid ${mainYellowColor}`,
            '&:hover': {
              backgroundColor: `${mainYellowColor}10`,
              borderColor: mainYellowColor
            }
          }}
        >
          View in Plan
        </Button>
        <Button
          onClick={onClose}
          sx={{
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            color: '#666',
            backgroundColor: 'white',
            border: '1px solid #e0e0e0',
            '&:hover': {
              backgroundColor: '#f5f5f5',
              borderColor: '#d5d5d5'
            }
          }}
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

const ScheduleView = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentDate, setCurrentDate] = useState(dayjs());
  const [viewMode, setViewMode] = useState('week'); // 'week' or 'workWeek'
  const [selectedTask, setSelectedTask] = useState(null);
  const theme = useTheme();
  useMediaQuery(theme.breakpoints.down('sm')); // Use the hook to prevent warning, even if we don't use the result

  // Fetch tasks from API
  const fetchTasks = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${APIURL}/api/retrieve/mytask`, { headers: getHeaders() });
      
      if (!response.ok) {
        throw new Error('Failed to fetch tasks');
      }
      
      const data = await response.json();
      setTasks(data);
    } catch (error) {
      console.error('Error fetching tasks:', error);
      toast.error('Failed to load tasks');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTasks();
  }, []);

  // Calculate subtask progress
  const calculateSubtaskProgress = (subtasks) => {
    if (!subtasks || subtasks.length === 0) return 0;
    const completedSubtasks = subtasks.filter(subtask => 
      subtask.status === 3 || subtask.progress === 100
    ).length;
    return (completedSubtasks / subtasks.length) * 100;
  };

  // Calculate task progress based on subtasks
  const calculateTaskProgress = (task) => {
    if (!task.subtasks || task.subtasks.length === 0) {
      return task.status === 3 ? 100 : 0;
    }
    return calculateSubtaskProgress(task.subtasks);
  };

  // Get task status based on subtasks
  const getTaskStatus = (task) => {
    // Nếu task không có subtasks, trả về status của chính task đó
    if (!task.subtasks || task.subtasks.length === 0) {
      return task.status || 1;
    }

    // Đếm số lượng subtasks đã hoàn thành (dựa vào progress hoặc status)
    const completedSubtasks = task.subtasks.filter(subtask => 
      subtask.status === 3 || subtask.progress === 100
    ).length;
    const totalSubtasks = task.subtasks.length;

    // Nếu tất cả subtasks đã hoàn thành
    if (completedSubtasks === totalSubtasks) {
      return 3; // Completed
    }
    
    // Nếu có ít nhất 1 subtask hoàn thành
    if (completedSubtasks > 0) {
      return 2; // In Progress
    }

    // Nếu chưa có subtask nào hoàn thành
    return 1; // Not Started
  };

  // Get status info with updated logic
  const getStatusInfo = (task) => {
    const status = getTaskStatus(task);
    switch (status) {
      case 3: // Completed
        return { text: 'Completed', color: '#4CAF50', icon: 'material-symbols:check-circle' };
      case 2: // In Progress
        return { text: 'In Progress', color: '#FF9800', icon: 'material-symbols:pending' };
      case 1: // Not Started
        return { text: 'Not Started', color: '#FFC107', icon: 'material-symbols:hourglass-empty' };
      default:
        return { text: 'Unknown', color: '#757575', icon: 'material-symbols:help' };
    }
  };

  // Get days for current view
  const getDays = () => {
    const days = [];
    const startOfWeek = viewMode === 'workWeek' 
      ? currentDate.startOf('week').add(1, 'day') // Monday
      : currentDate.startOf('week'); // Sunday
    const daysToShow = viewMode === 'workWeek' ? 5 : 7;

    for (let i = 0; i < daysToShow; i++) {
      days.push(startOfWeek.add(i, 'day'));
    }
    return days;
  };

  // Get tasks for a specific day
  const getTasksForDay = (date) => {
    return tasks.filter(task => {
      const taskStart = dayjs(task.start_date);
      const taskEnd = dayjs(task.end_date);
      const taskDeadline = task.deadline ? dayjs(task.deadline) : null;

      return date.isSame(taskStart, 'day') ||
             date.isSame(taskEnd, 'day') ||
             (taskDeadline && date.isSame(taskDeadline, 'day')) ||
             (date.isAfter(taskStart) && date.isBefore(taskEnd));
    });
  };

  // Check if task spans multiple days
  const isMultiDayTask = (task) => {
    const taskStart = dayjs(task.start_date);
    const taskEnd = dayjs(task.end_date);
    return !taskStart.isSame(taskEnd, 'day');
  };

  // Get task position in multi-day span
  const getTaskPosition = (task, currentDay) => {
    const taskStart = dayjs(task.start_date);
    const taskEnd = dayjs(task.end_date);
    const isStartDay = currentDay.isSame(taskStart, 'day');
    const isEndDay = currentDay.isSame(taskEnd, 'day');
    
    return { isStartDay, isEndDay };
  };

  // Get task duration in days
  const getTaskDuration = (task) => {
    const taskStart = dayjs(task.start_date);
    const taskEnd = dayjs(task.end_date);
    return taskEnd.diff(taskStart, 'day') + 1;
  };

  // Navigation functions
  const goToToday = () => setCurrentDate(dayjs());
  const goToPrevious = () => setCurrentDate(currentDate.subtract(7, 'day'));
  const goToNext = () => setCurrentDate(currentDate.add(7, 'day'));

  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: 'calc(100vh - 100px)',
        flexDirection: 'column',
        gap: 2
      }}>
        <CircularProgress size={60} sx={{ color: mainYellowColor }} />
        <Typography variant="body1" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
          Loading schedule...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      minHeight: 'calc(100vh - 65px)',
      display: 'flex',
      flexDirection: 'column',
      py: 3,
      px: { xs: 2, md: 3 }
    }}>
      <Paper 
        elevation={0} 
        sx={{ 
          p: { xs: 2, sm: 3 }, 
          borderRadius: '12px',
          border: '1px solid #f0f0f0',
          backgroundColor: '#fff',
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Header */}
        <Box sx={{ 
          mb: 3, 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center', 
          flexWrap: 'wrap', 
          gap: 2 
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography 
              variant="h5" 
              sx={{ 
                fontWeight: 600, 
                color: '#333', 
                fontFamily: '"Recursive Variable", sans-serif',
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <Iconify icon="material-symbols:calendar-view-week" width={28} height={28} color={mainYellowColor} />
              Schedule View
            </Typography>

            <ButtonGroup size="small" sx={{ ml: 2 }}>
              <Button 
                variant={viewMode === 'week' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('week')}
                sx={{
                  textTransform: 'none',
                  fontFamily: '"Recursive Variable", sans-serif',
                  backgroundColor: viewMode === 'week' ? `${mainYellowColor} !important` : 'transparent',
                  borderColor: mainYellowColor,
                  color: viewMode === 'week' ? '#fff' : mainYellowColor,
                  '&:hover': {
                    backgroundColor: viewMode === 'week' ? mainYellowColor : 'transparent',
                    borderColor: mainYellowColor
                  }
                }}
              >
                Full Week
              </Button>
              <Button 
                variant={viewMode === 'workWeek' ? 'contained' : 'outlined'}
                onClick={() => setViewMode('workWeek')}
                sx={{
                  textTransform: 'none',
                  fontFamily: '"Recursive Variable", sans-serif',
                  backgroundColor: viewMode === 'workWeek' ? `${mainYellowColor} !important` : 'transparent',
                  borderColor: mainYellowColor,
                  color: viewMode === 'workWeek' ? '#fff' : mainYellowColor,
                  '&:hover': {
                    backgroundColor: viewMode === 'workWeek' ? mainYellowColor : 'transparent',
                    borderColor: mainYellowColor
                  }
                }}
              >
                Work Week
              </Button>
            </ButtonGroup>
          </Box>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <ButtonGroup size="small">
              <Button
                onClick={goToPrevious}
                startIcon={<Iconify icon="material-symbols:chevron-left" />}
                sx={{
                  textTransform: 'none',
                  fontFamily: '"Recursive Variable", sans-serif',
                  color: '#666',
                  backgroundColor: 'white',
                  borderColor: '#e0e0e0',
                  '&:hover': {
                    backgroundColor: '#f5f5f5',
                    borderColor: '#d5d5d5'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Previous
              </Button>
              <Button
                onClick={goToToday}
                sx={{
                  textTransform: 'none',
                  fontFamily: '"Recursive Variable", sans-serif',
                  color: '#666',
                  backgroundColor: 'white',
                  borderColor: '#e0e0e0',
                  '&:hover': {
                    backgroundColor: '#f5f5f5',
                    borderColor: '#d5d5d5'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Today
              </Button>
              <Button
                onClick={goToNext}
                endIcon={<Iconify icon="material-symbols:chevron-right" />}
                sx={{
                  textTransform: 'none',
                  fontFamily: '"Recursive Variable", sans-serif',
                  color: '#666',
                  backgroundColor: 'white',
                  borderColor: '#e0e0e0',
                  '&:hover': {
                    backgroundColor: '#f5f5f5',
                    borderColor: '#d5d5d5'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Next
              </Button>
            </ButtonGroup>

            <Tooltip title="Refresh Schedule">
              <IconButton 
                onClick={fetchTasks}
                sx={{ 
                  color: '#666',
                  backgroundColor: 'white',
                  border: '1px solid #e0e0e0',
                  '&:hover': { 
                    backgroundColor: '#f5f5f5',
                    borderColor: '#d5d5d5'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                <Iconify icon="material-symbols:refresh" width={20} height={20} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>

        {/* Calendar Grid */}
        <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Days Header */}
          <Grid container spacing={1} sx={{ mb: 1, backgroundColor: '#f5f7fa', p: 1, borderRadius: '12px' }}>
            {getDays().map((day) => (
              <Grid item xs key={day.format('YYYY-MM-DD')} sx={{ width: '100%' }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 1.5,
                    textAlign: 'center',
                    backgroundColor: day.isSame(dayjs(), 'day') ? `${mainYellowColor}15` : 'white',
                    borderRadius: '8px',
                    border: '1px solid #f0f0f0',
                    boxShadow: day.isSame(dayjs(), 'day') ? `0 0 0 1px ${mainYellowColor}40` : 'none',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      backgroundColor: day.isSame(dayjs(), 'day') ? `${mainYellowColor}20` : '#fafafa'
                    }
                  }}
                >
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      display: 'block',
                      color: '#555',
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontSize: '0.75rem',
                      fontWeight: 600,
                      mb: 0.5
                    }}
                  >
                    {day.format('ddd').toUpperCase()}
                  </Typography>
                  <Typography 
                    variant="h6" 
                    sx={{ 
                      fontWeight: 700,
                      color: day.isSame(dayjs(), 'day') ? mainYellowColor : '#333',
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontSize: '1.5rem',
                      lineHeight: 1
                    }}
                  >
                    {day.format('D')}
                  </Typography>
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      display: 'block',
                      color: '#666',
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontSize: '0.75rem',
                      fontWeight: 500,
                      mt: 0.5
                    }}
                  >
                    {day.format('MMM')}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>

          {/* Tasks Grid */}
          <Grid container spacing={1} sx={{ flexGrow: 1 }}>
            {getDays().map((day) => {
              const dayTasks = getTasksForDay(day);
              return (
                <Grid item xs key={day.format('YYYY-MM-DD')} sx={{ width: '100%' }}>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 1,
                      height: '100%',
                      minHeight: '400px',
                      backgroundColor: day.isSame(dayjs(), 'day') ? '#fafafa' : '#fff',
                      borderRadius: '8px',
                      border: '1px solid #f0f0f0'
                    }}
                  >
                    {dayTasks.length === 0 ? (
                      <Box 
                        sx={{ 
                          height: '100%', 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center',
                          color: '#999',
                          fontFamily: '"Recursive Variable", sans-serif',
                          fontSize: '0.8rem'
                        }}
                      >
                        No tasks
                      </Box>
                    ) : (
                      <Stack spacing={1}>
                        {dayTasks.map((task) => {
                          const statusInfo = getStatusInfo(task);
                          const isMultiDay = isMultiDayTask(task);
                          const duration = getTaskDuration(task);
                          const { isStartDay, isEndDay } = getTaskPosition(task, day);

                          return (
                            <Paper
                              key={task.id}
                              elevation={0}
                              onClick={() => setSelectedTask(task)}
                              sx={{
                                p: 1,
                                backgroundColor: `${statusInfo.color}08`,
                                border: `1px solid ${statusInfo.color}20`,
                                borderRadius: '4px',
                                position: 'relative',
                                marginLeft: isMultiDay && !isStartDay ? '-1px' : '0',
                                marginRight: isMultiDay && !isEndDay ? '-1px' : '0',
                                borderLeft: isMultiDay ? 
                                  (isStartDay ? '2px solid ' + statusInfo.color : '1px dashed ' + statusInfo.color + '40') : 
                                  '1px solid ' + statusInfo.color + '20',
                                borderRight: isMultiDay ? 
                                  (isEndDay ? '2px solid ' + statusInfo.color : '1px dashed ' + statusInfo.color + '40') : 
                                  '1px solid ' + statusInfo.color + '20',
                                borderTop: isMultiDay && !isStartDay ? '1px dashed ' + statusInfo.color + '40' : '1px solid ' + statusInfo.color + '20',
                                borderBottom: isMultiDay && !isEndDay ? '1px dashed ' + statusInfo.color + '40' : '1px solid ' + statusInfo.color + '20',
                                cursor: 'pointer',
                                transition: 'all 0.2s ease',
                                '&:hover': {
                                  backgroundColor: `${statusInfo.color}12`,
                                  borderColor: `${statusInfo.color}50`,
                                  transform: 'translateY(-1px)',
                                  boxShadow: `0 4px 8px ${statusInfo.color}20`,
                                  zIndex: 1
                                }
                              }}
                            >
                              <Typography 
                                variant="body2" 
                                sx={{ 
                                  fontWeight: 600,
                                  color: '#333',
                                  fontFamily: '"Recursive Variable", sans-serif',
                                  fontSize: '0.8rem',
                                  mb: 0.5,
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: 'hidden'
                                }}
                              >
                                {isMultiDay && !isStartDay && !isEndDay ? (
                                  <Box sx={{ 
                                    fontSize: '0.7rem', 
                                    color: '#666',
                                    textAlign: 'center',
                                    fontStyle: 'italic'
                                  }}>
                                    ...continued
      </Box>
                                ) : task.name}
                              </Typography>
                              
                              <Box sx={{ 
                                display: 'flex', 
                                alignItems: 'center', 
                                gap: 0.5,
                                justifyContent: isMultiDay && !isStartDay && !isEndDay ? 'center' : 'flex-start'
                              }}>
                                {(isStartDay || isEndDay) && (
                                  <>
                                    <Chip
                                      icon={<Iconify icon={statusInfo.icon} width={12} height={12} />}
                                      label={isStartDay ? statusInfo.text : `Ends ${dayjs(task.end_date).format('HH:mm')}`}
                                      size="small"
                                      sx={{
                                        height: '16px',
                                        '& .MuiChip-label': {
                                          px: 1,
                                          fontSize: '0.65rem',
                                          fontWeight: 600
                                        },
                                        backgroundColor: `${statusInfo.color}15`,
                                        color: statusInfo.color,
                                        fontFamily: '"Recursive Variable", sans-serif'
                                      }}
                                    />
                                    
                                    {task.milestone_name && (
                                      <Tooltip title={task.milestone_name}>
                                        <Iconify 
                                          icon="material-symbols:flag" 
                                          width={12} 
                                          height={12} 
                                          sx={{ color: '#666' }} 
                                        />
                                      </Tooltip>
                                    )}
            </>
          )}

                                {isStartDay && (
                                  <Tooltip title={`${duration} days task (${dayjs(task.start_date).format('DD/MM HH:mm')} - ${dayjs(task.end_date).format('DD/MM HH:mm')})`}>
                                    <Box sx={{ 
                                      display: 'flex', 
                                      alignItems: 'center', 
                                      gap: 0.5,
                                      color: '#666',
                                      fontSize: '0.65rem'
                                    }}>
                                      <Iconify icon="material-symbols:schedule" width={12} height={12} />
                                      {duration}d
                                    </Box>
                                  </Tooltip>
                                )}

                                {isMultiDay && !isStartDay && !isEndDay && (
                                  <Box sx={{ 
                                    fontSize: '0.7rem', 
                                    color: '#666',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.5
                                  }}>
                                    <Iconify icon="material-symbols:arrow-right" width={12} height={12} />
                                  </Box>
                                )}
                              </Box>
                            </Paper>
                          );
                        })}
                      </Stack>
                    )}
                  </Paper>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      </Paper>

      {/* Task Detail Modal */}
      <TaskDetailModal
        open={Boolean(selectedTask)}
        onClose={() => setSelectedTask(null)}
        task={selectedTask}
        getStatusInfo={getStatusInfo}
        calculateTaskProgress={calculateTaskProgress}
        getTaskDuration={getTaskDuration}
      />
    </Box>
  );
};

export default ScheduleView;
