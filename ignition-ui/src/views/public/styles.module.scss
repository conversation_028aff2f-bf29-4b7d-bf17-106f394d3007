.mainPage {
  min-height: calc(100vh - 20px);
  font-family: 'Recursive Variable';
  max-width: 1200px;
  margin: 30px auto;
  color: #333;

  .primaryTitle {
    font-family: 'Recursive Variable';
    font-size: 1.8rem;
    font-weight: bold;
    text-align: center;
    padding-bottom: 20px;
    color: #F0A500;
    text-decoration: underline;
  }

  .shortContent {
    font-family: 'Recursive Variable';
    font-size: 1.125rem;
    line-height: 1.9rem;
    padding-bottom: 5px;
  }

  .highlight {
    font-size: 1.25rem;
    font-weight: bold;
    color: #F0A500;
  }

  .secondaryTitle {
    font-size: 1.4rem;
    font-family: 'Recursive Variable';
    color: black;
    text-decoration: underline;
    padding-bottom: 5px;
  }

  .tertiaryTitle {
    font-size: 1.25rem;
    font-family: 'Recursive Variable';
    color: black;
    padding-bottom: 5px;
    padding-left: 10px;
  }

  .tertiaryContent {
    font-size: 1.125rem;
    font-family: 'Recursive Variable';
    padding-bottom: 5px;
    padding-left: 10px;
  }

  .secondaryContent {
    font-size: 1.125rem;
    font-family: 'Recursive Variable';
    padding-bottom: 5px;
    padding-left: 10px;
  }
}