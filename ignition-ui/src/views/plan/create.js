import React, { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { Button, Container, Box, Typography, Tooltip, IconButton, Paper, Divider, CircularProgress } from '@mui/material';

import { getHeaders } from "helpers/functions";
import { APIURL } from "helpers/constants";
import { useNavigate } from 'react-router-dom';
import TextAreaBase from 'components/Input/TextAreaBase';
import Iconify from 'components/Iconify/index';
import PlanOptionsSelector from 'components/PlanOptions/PlanOptionsSelector';
import styles from './styles.module.scss';

//--------------------------------------------------------------------------------------------------

const CreatePlan = () => {
  const [promptInput, setPromptInput] = useState('');
  const [language] = useState('English'); // Default to English
  const [error, setError] = useState('');
  const [hasError, setHasError] = useState(false);
  // const [errorType, setErrorType] = useState(''); // 'network', 'server', 'validation', 'timeout', 'auth'
  // const [retryCount, setRetryCount] = useState(0);
  const navigate = useNavigate();

  // Two-step process state
  const [currentStep, setCurrentStep] = useState('input'); // 'input', 'options', 'creating'
  const [planOptions, setPlanOptions] = useState([]);
  const [selectedOption, setSelectedOption] = useState(null);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);
  const [isCreatingPlan, setIsCreatingPlan] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [planSlug, setPlanSlug] = useState('');
  const [isPlanCreated, setIsPlanCreated] = useState(false);

  const pollingIntervalRef = useRef(null);

  // Helper function to handle errors with better categorization
  const handleError = (error, context = 'general') => {
    console.error(`❌ Error in ${context}:`, error);

    let errorMessage = '';
    let errorCategory = 'general';

    if (!navigator.onLine) {
      errorMessage = 'You appear to be offline. Please check your internet connection and try again.';
      errorCategory = 'network';
    } else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
      errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      errorCategory = 'network';
    } else if (error.response?.status === 401 || error.response?.status === 403) {
      errorMessage = 'Your session has expired. Please log in again and try creating your plan.';
      errorCategory = 'auth';
    } else if (error.response?.status === 429) {
      errorMessage = 'Too many requests. Please wait a moment before trying again.';
      errorCategory = 'rate_limit';
    } else if (error.response?.status >= 500) {
      errorMessage = 'Our servers are experiencing issues. Please try again in a few moments.';
      errorCategory = 'server';
    } else if (error.response?.status === 400) {
      errorMessage = error.response?.data?.error || 'Invalid request. Please check your input and try again.';
      errorCategory = 'validation';
    } else if (error.response?.data?.error) {
      errorMessage = error.response.data.error;
      errorCategory = 'api';
    } else if (error.name === 'TimeoutError' || error.code === 'ECONNABORTED') {
      errorMessage = 'Request timed out. Please try again.';
      errorCategory = 'timeout';
    } else {
      errorMessage = `An unexpected error occurred during ${context}. Please try again.`;
      errorCategory = 'unknown';
    }

    setError(errorMessage);
    // setErrorType(errorCategory);
    setHasError(true);

    return { errorMessage, errorCategory };
  };

  // Retry function for failed operations
  // const handleRetry = async (operation, maxRetries = 2) => {
  //   if (retryCount >= maxRetries) {
  //     setError('Maximum retry attempts reached. Please try again later.');
  //     // setErrorType('max_retries');
  //     setHasError(true);
  //     return;
  //   }

  //   setRetryCount(prev => prev + 1);
  //   setError('');
  //   setHasError(false);

  //   try {
  //     await operation();
  //   } catch (error) {
  //     handleError(error, 'retry');
  //   }
  // };

  // Check for recently created plans
  const checkPlanCreationStatus = useCallback(async () => {
    console.log("🔍 Checking plan creation status...");
    try {
      const response = await axios.get(`${APIURL}/api/assistant/plan-creation-status`, {
        headers: getHeaders()
      });

      console.log("📊 Plan status response:", response.data);

      const {
        status,
        plan,
        error
      } = response.data;

      console.log(`📈 Current status: ${status}`);

      // Handle different statuses
      switch (status) {
        case 'initializing':
        case 'generating_structure':
        case 'creating_milestones':
        case 'creating_tasks':
        case 'creating_subtasks':
        case 'finalizing':
        case 'saving_plan':
          // Plan is still processing
          console.log(`⏳ Plan still processing: ${status}`);
          break;
        case 'completed':
          console.log("✅ Plan creation completed!", plan);
          setIsPlanCreated(true);
          setIsCreatingPlan(false);
          setPlanSlug(plan.slug);
          // Stop polling when plan is completed
          clearInterval(pollingIntervalRef.current);
          console.log("🛑 Stopped polling - plan completed");
          break;
        case 'failed':
          console.log("❌ Plan creation failed:", error);
          setError(error || 'Plan creation failed. Please try again.');
          setHasError(true);
          setIsCreatingPlan(false);
          setCurrentStep('options');
          clearInterval(pollingIntervalRef.current);
          break;
        case 'timeout':
          console.log("⏰ Plan creation timed out");
          setError('Plan creation timed out. Please try again.');
          setHasError(true);
          setIsCreatingPlan(false);
          setCurrentStep('options');
          clearInterval(pollingIntervalRef.current);
          break;
        case 'no_active_creation':
          console.log("🚫 No active creation found");
          // No active creation found, might be a stale request
          break;
        default:
          console.log("❓ Unknown status:", status);
          break;
      }
    } catch (error) {
      handleError(error, 'plan creation status check');
      setIsCreatingPlan(false);
      setCurrentStep('options');
      clearInterval(pollingIntervalRef.current);
    }
  }, []);

  // Stop polling when component unmounts
  useEffect(() => {
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, []);

  // Set up polling when creating plan starts
  useEffect(() => {
    if (isCreatingPlan && sessionId) {
      // Check immediately
      checkPlanCreationStatus();
    }
  }, [isCreatingPlan, sessionId, checkPlanCreationStatus]);

  // Step 1: Generate plan options
  const handleGenerateOptions = async () => {
    if (!promptInput.trim()) {
      setError('Please describe what you want from this project.');
      setHasError(true);
      return;
    }

    // Clear any previous errors
    setError('');
    setHasError(false);
    setIsLoadingOptions(true);

    try {
      const response = await axios.post(`${APIURL}/api/assistant/generate-plan-options`, {
        prompt: promptInput,
        language: language,
        duration: '3 months'
      }, { headers: getHeaders() });

      setPlanOptions(response.data.plan_options || []);
      setCurrentStep('options');

    } catch (error) {
      handleError(error, 'plan options generation');
      // On error, stay on input step and show error
      setCurrentStep('input');
    } finally {
      setIsLoadingOptions(false);
    }
  };

  // Step 2: Create plan from selected option
  const handleCreatePlanFromOption = async (optionToUse = null) => {
    console.log("🚀 Starting plan creation from selected option...");

    // Use the passed option or fall back to selectedOption state
    const planOption = optionToUse || selectedOption;

    console.log("📋 Plan option to use:", planOption);
    console.log("📋 Selected option state:", selectedOption);
    console.log("💬 Original prompt:", promptInput);

    if (!planOption) {
      console.log("❌ No option selected");
      setError('Please select a plan option first.');
      setHasError(true);
      return;
    }

    console.log("🔄 Setting up plan creation...");
    setError('');
    setHasError(false);
    setIsCreatingPlan(true);
    setCurrentStep('creating');

    try {
      console.log("📡 Sending API request to create plan...");
      const response = await axios.post(`${APIURL}/api/assistant/create-plan-from-option`, {
        selected_option: planOption,
        original_prompt: promptInput
      }, { headers: getHeaders() });

      console.log("✅ API response received:", response.data);
      console.log("🆔 Session ID:", response.data.session_id);

      setSessionId(response.data.session_id);

      // Start polling for plan creation status - faster polling for better UX
      console.log("⏰ Starting polling every 2 seconds...");
      pollingIntervalRef.current = setInterval(checkPlanCreationStatus, 2000);

    } catch (error) {
      handleError(error, 'plan creation from option');
      setIsCreatingPlan(false);
      setCurrentStep('options');
    }
  };

  const handleNavigateToPlan = () => {
    navigate("/d/plan/" + planSlug, { replace: true });
  };

  const handleResetForm = () => {
    setCurrentStep('input');
    setPlanOptions([]);
    setSelectedOption(null);
    setIsLoadingOptions(false);
    setIsCreatingPlan(false);
    setIsPlanCreated(false);
    setSessionId(null);
    setError('');
    setHasError(false);
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }
  };

  const handleSelectOption = (option) => {
    setSelectedOption(option);
  };

  const handleBackToInput = () => {
    setCurrentStep('input');
    setPlanOptions([]);
    setSelectedOption(null);
    setError('');
    setHasError(false);
  };

  return (
    <Container maxWidth="xl" className={styles.mainCreateContainer}>
      <Box className={styles.boxWrapper}>
        {/* Step 1: Input Form */}
        {currentStep === 'input' && !isLoadingOptions && (
          <Paper elevation={3} className={styles.paperContent}>
            {/* Header Section with Rocket Icon */}
            <Box sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
              mb: 5,
              pt: 2
            }}>
              <Box sx={{
                backgroundColor: 'rgba(240, 165, 0, 0.1)',
                borderRadius: '50%',
                p: 2,
                mb: 3,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: 100,
                height: 100,
                boxShadow: '0 4px 20px rgba(240, 165, 0, 0.2)'
              }}>
                <Iconify icon="mdi:rocket-launch" width={60} height={60} color="#F0A500" />
              </Box>
              <Typography variant="h4" className={styles.paperTitle} sx={{ mb: 2 }}>
                Welcome to the Ignition
              </Typography>
              <Typography variant="body1" className={styles.paperBodyContent} sx={{ maxWidth: '700px' }}>
                Describe your project and we'll generate 3 different plan approaches for you to choose from.
              </Typography>
            </Box>

            <Divider sx={{ mb: 5, borderColor: 'rgba(0,0,0,0.1)' }} />

            {/* Form Section */}
            <Box className={styles.formSection}>
              <Box className={styles.boxInputContent}>
                <Box display="flex" alignItems="center" justifyContent='space-between'>
                  <Typography variant="h6" className={styles.titleInput} sx={{ display: 'flex', alignItems: 'center' }}>
                    <Iconify icon="mdi:target" width={24} height={24} color="#F0A500" style={{ marginRight: '12px' }} />
                    What do you want out from this project?
                  </Typography>
                  <Tooltip title={<>
                    <strong>What is this field?</strong><br />
                    This field is for describing your project. Please provide as much detail as possible to help the AI understand your requirements.<br />
                    <strong>What should you include?</strong><br />
                    - Brief description of the project.<br />
                    - Key objectives and goals.<br />
                    - Any specific tasks or milestones.<br />
                    The more detailed you are, the better the generated plan will be.
                  </>}>
                    <IconButton size="small">
                      <Iconify icon="octicon:info-16" width={18} height={18} color="#F0A500" />
                    </IconButton>
                  </Tooltip>
                </Box>
                <TextAreaBase
                  id="prompt"
                  value={promptInput}
                  handleChange={setPromptInput}
                  minRows={5}
                  multiline
                  placeholder="E.g., I need a comprehensive testing plan for a new mobile app. The app has user authentication, profile management, content browsing, and payment features. I want to ensure all features work correctly across different devices and platforms..."
                  errorText={error && !promptInput ? error : ''}
                  required
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: '12px',
                      '&.Mui-focused fieldset': {
                        borderColor: '#F0A500',
                        borderWidth: '2px'
                      }
                    }
                  }}
                />
                <Typography variant="caption" sx={{ display: 'block', mt: 1, color: '#666', fontStyle: 'italic' }}>
                  Suggestion: Be specific about your project scope, timeline, and expected outcomes. Include any technical requirements or constraints.
                </Typography>
              </Box>

              <Button
                variant="contained"
                onClick={handleGenerateOptions}
                fullWidth
                className={styles.genPlanBtn}
                startIcon={<Iconify icon="mingcute:ai-line" width={24} height={24} />}
                sx={{
                  mt: 5,
                  height: '60px',
                  borderRadius: '16px',
                  boxShadow: '0 6px 16px rgba(240, 165, 0, 0.3)',
                  fontSize: '1.2rem',
                  fontWeight: 700
                }}
              >
                GENERATE PLAN OPTIONS
              </Button>
            </Box>
          </Paper>
        )}

        {/* Loading Screen for Options Generation */}
        {isLoadingOptions && (
          <Box className={styles.loadingBox}>
            <Box className={styles.loadingContainer}>
              <Paper elevation={4} className={styles.loadingCard}>
                <Box sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  bgcolor: 'rgba(240, 165, 0, 0.2)',
                  overflow: 'hidden',
                  borderRadius: '20px 20px 0 0'
                }}>
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    height: '100%',
                    width: '30%',
                    bgcolor: '#F0A500',
                    animation: 'loadingProgress 2s infinite ease-in-out',
                    borderRadius: '0 4px 4px 0'
                  }} />
                </Box>

                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                  <CircularProgress
                    size={60}
                    thickness={4}
                    sx={{
                      color: '#F0A500',
                      mb: 3
                    }}
                  />
                  <Typography variant="h5" align="center" className={styles.titleGenerating}>
                    Generating plan options...
                  </Typography>

                  <Typography variant="body2" sx={{
                    mt: 2,
                    color: '#666',
                    textAlign: 'center',
                    px: 2
                  }}>
                    Creating 3 different plan approaches for you to choose from
                  </Typography>
                </Box>
              </Paper>
            </Box>
          </Box>
        )}

        {/* Step 2: Plan Options Selection */}
        {currentStep === 'options' && !isLoadingOptions && (
          <Box sx={{
            width: '100vw',
            maxWidth: 'none',
            mx: 0,
            px: 2,
            position: 'relative',
            left: '50%',
            right: '50%',
            marginLeft: '-50vw',
            marginRight: '-50vw',
            fontFamily: '"Recursive Variable", sans-serif'
          }}>
            {/* Back Button */}
            <Box sx={{
              width: '100%',
              mb: 4,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              pt: 2
            }}>
              <Box sx={{
                width: '100%',
                maxWidth: '1200px',
                px: 3,
                display: 'flex',
                justifyContent: 'flex-start'
              }}>
                <Button
                  variant="text"
                  onClick={handleBackToInput}
                  startIcon={<Iconify icon="material-symbols:arrow-back" width={18} height={18} />}
                  sx={{
                    color: '#F0A500',
                    fontFamily: '"Recursive Variable", sans-serif',
                    fontWeight: 500,
                    fontSize: '0.95rem',
                    textTransform: 'uppercase',
                    letterSpacing: '0.5px',
                    padding: '8px 16px',
                    borderRadius: '6px',
                    '&:hover': {
                      backgroundColor: 'rgba(240, 165, 0, 0.08)',
                      color: '#d89400'
                    },
                    transition: 'all 0.2s ease'
                  }}
                >
                  BACK TO EDIT
                </Button>
              </Box>
            </Box>

            <PlanOptionsSelector
              planOptions={planOptions}
              selectedOption={selectedOption}
              onSelectOption={handleSelectOption}
              onConfirmSelection={handleCreatePlanFromOption}
              loading={isCreatingPlan}
            />
          </Box>
        )}

        {/* Step 3: Creating Plan */}
        {currentStep === 'creating' && (
          <Box className={styles.loadingBox}>
            <Box className={styles.loadingContainer}>
              <Paper elevation={4} className={styles.loadingCard}>
                <Box sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  bgcolor: 'rgba(240, 165, 0, 0.2)',
                  overflow: 'hidden',
                  borderRadius: '20px 20px 0 0'
                }}>
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    height: '100%',
                    width: '60%',
                    bgcolor: '#F0A500',
                    animation: 'loadingProgress 2s infinite ease-in-out',
                    borderRadius: '0 4px 4px 0'
                  }} />
                </Box>

                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                  <CircularProgress
                    size={60}
                    thickness={4}
                    sx={{
                      color: '#F0A500',
                      mb: 3
                    }}
                  />
                  <Typography variant="h5" align="center" className={styles.titleGenerating}>
                    Creating your detailed plan...
                  </Typography>

                  <Typography variant="body2" sx={{
                    mt: 2,
                    color: '#666',
                    textAlign: 'center',
                    px: 2
                  }}>
                    Generating detailed subtasks for each task in your selected plan
                  </Typography>
                </Box>
              </Paper>
            </Box>
          </Box>
        )}

        {/* Success State */}
        {isPlanCreated && (
          <Box className={styles.loadingBox}>
            <Box sx={{ textAlign: 'center', maxWidth: '500px', p: 5, bgcolor: 'white', borderRadius: '16px', boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)' }}>
              <Box sx={{
                backgroundColor: 'rgba(76, 175, 80, 0.1)',
                borderRadius: '50%',
                p: 2,
                mb: 3,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                width: 100,
                height: 100,
                margin: '0 auto',
                boxShadow: '0 4px 20px rgba(76, 175, 80, 0.2)'
              }}>
                <Iconify icon="mdi:check-circle" width={64} height={64} color="#4CAF50" />
              </Box>
              <Typography variant="h5" align="center" className={styles.titleGenerating} sx={{ color: '#333', animation: 'none' }}>
                Plan has been created successfully!
              </Typography>
              <Typography variant="body1" sx={{ mt: 3, mb: 5, color: '#555' }}>
                Your detailed plan with subtasks is ready. Would you like to view it or create another one?
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 3, flexWrap: 'wrap' }}>
                <Button
                  variant="contained"
                  className={styles.gotoDetailPageBtn}
                  onClick={handleNavigateToPlan}
                  startIcon={<Iconify icon="material-symbols:visibility-outline" width={22} height={22} />}
                  sx={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(240, 165, 0, 0.3)',
                    padding: '12px 28px',
                    fontSize: '1.1rem',
                    fontWeight: 600
                  }}
                >
                  View Plan
                </Button>
                <Button
                  variant="contained"
                  className={styles.reRunGenBtn}
                  onClick={handleResetForm}
                  startIcon={<Iconify icon="material-symbols:refresh" width={22} height={22} />}
                  sx={{
                    borderRadius: '12px',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                    padding: '12px 28px',
                    fontSize: '1.1rem',
                    fontWeight: 600
                  }}
                >
                  Create Another
                </Button>
              </Box>
            </Box>
          </Box>
        )}

        {/* Error Display */}
        {hasError && (
          <Box className={styles.loadingBox}>
            <Box className={styles.loadingContainer}>
              <Paper elevation={4} className={styles.loadingCard}>
                <Box sx={{
                  mt: 4,
                  p: 3,
                  bgcolor: 'rgba(244, 67, 54, 0.1)',
                  borderRadius: '12px',
                  border: '1px solid rgba(244, 67, 54, 0.2)'
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                    <Iconify icon="material-symbols:error-outline" width={24} height={24} color="#f44336" />
                    <Box>
                      <Typography variant="h6" sx={{ color: '#f44336', fontWeight: 600, mb: 1 }}>
                        Plan Creation Failed
                      </Typography>
                      <Typography variant="body2" sx={{ color: '#666', mb: 3 }}>
                        {error}
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                        <Button
                          variant="contained"
                          onClick={handleResetForm}
                          startIcon={<Iconify icon="material-symbols:refresh" width={20} height={20} />}
                          sx={{
                            bgcolor: '#f44336',
                            '&:hover': { bgcolor: '#d32f2f' },
                            borderRadius: '8px',
                            textTransform: 'none',
                            fontWeight: 600
                          }}
                        >
                          Try Again
                        </Button>
                        <Button
                          variant="outlined"
                          onClick={() => navigate('/d/')}
                          sx={{
                            borderColor: '#f44336',
                            color: '#f44336',
                            '&:hover': { borderColor: '#d32f2f', bgcolor: 'rgba(244, 67, 54, 0.04)' },
                            borderRadius: '8px',
                            textTransform: 'none',
                            fontWeight: 600
                          }}
                        >
                          Go to Dashboard
                        </Button>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </Paper>
            </Box>
          </Box>
        )}
      </Box>


    </Container>
  );
};

export default CreatePlan;
