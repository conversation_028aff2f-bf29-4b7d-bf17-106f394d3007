import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  fetchPlanInfo,
  getInvitedUsers,
  sendInvitation,
  checkUserExistence,
  deletePlan,
  optOutPlan,
  updatePlanName
} from '../../services';
import { successSnackbar, errorSnackbar } from 'components/Snackbar';
import eventEmitter, { EVENTS } from 'utils/eventEmitter';

// Status constants
export const STATUS = {
  NOT_STARTED: 1,
  IN_PROGRESS: 2,
  COMPLETED: 3
};

// Status configuration constants
export const STATUS_CONFIG = {
  [STATUS.NOT_STARTED]: { 
    color: '#FFC107', 
    label: 'Not Started', 
    icon: 'material-symbols:hourglass-empty' 
  },
  [STATUS.IN_PROGRESS]: { 
    color: '#FF9800', 
    label: 'In Progress', 
    icon: 'material-symbols:pending' 
  },
  [STATUS.COMPLETED]: { 
    color: '#4CAF50', 
    label: 'Completed', 
    icon: 'material-symbols:check-circle' 
  }
};

const usePlanData = (planSlug) => {
  const [planInfo, setPlanInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [invitedUsers, setInvitedUsers] = useState([]);
  const [email, setEmail] = useState('');
  const [isSending, setIsSending] = useState(false);
  const navigate = useNavigate();

  // Fetch plan info
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetchPlanInfo(planSlug);
        setPlanInfo(response.data);
        setError(false);
      } catch (err) {
        console.error('Error fetching plan info:', err);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    if (planSlug) {
      fetchData();
    }
  }, [planSlug]);

  // Fetch invited users - using useCallback to avoid creating a new function on each render
  const fetchInvitedUsers = useCallback(async () => {
    try {
      const response = await getInvitedUsers(planSlug);
      console.log('API Response - Invited Users:', response);
      
      // Process data from API
      let usersData = [];
      if (response && response.data) {
        usersData = response.data;
      } else if (Array.isArray(response)) {
        usersData = response;
      }
      
      console.log('Processed users data:', usersData);
      setInvitedUsers(usersData);
    } catch (error) {
      console.error('Error fetching invited users:', error);
      setInvitedUsers([]);
    }
  }, [planSlug]);

  // Only call fetchInvitedUsers once when planInfo has been loaded
  useEffect(() => {
    if (planSlug && !loading) {
      fetchInvitedUsers();
    }
  }, [planSlug, loading, fetchInvitedUsers]);

  // Log invitedUsers whenever it changes
  useEffect(() => {
    console.log('Current invitedUsers state:', invitedUsers);
  }, [invitedUsers]);

  // Handle email change
  const handleEmailChange = (value) => {
    setEmail(value);
  };

  // Handle invite user
  const handleInviteUser = async (emailToInvite, accessLevel = 'viewer') => {
    if (!emailToInvite) return;

    setIsSending(true);
    try {
      // Check if user exists
      const result = await checkUserExistence(emailToInvite);

      // Send invitation with access level
      const invitationResponse = await sendInvitation(planInfo, emailToInvite, accessLevel);

      // Refresh both plan info and invited users
      const response = await fetchPlanInfo(planSlug);
      setPlanInfo(response.data);
      await fetchInvitedUsers();

      // Show enhanced success message
      let successMessage;
      if (invitationResponse?.success_message) {
        successMessage = invitationResponse.success_message;
      } else if (invitationResponse?.is_reinvitation) {
        successMessage = `🔄 Re-invitation sent to ${emailToInvite} with ${accessLevel} access! They'll receive a new email.`;
      } else if (result.exists) {
        successMessage = `🎉 ${emailToInvite} has been added to the project with ${accessLevel} access! They can access it immediately.`;
      } else {
        successMessage = `📧 Invitation sent to ${emailToInvite} with ${accessLevel} access! They'll receive an email with instructions to join.`;
      }

      // Show success notification
      successSnackbar(successMessage);

      // Also log to console for debugging
      console.log('✅ Invitation Success:', successMessage);

      return true;
    } catch (err) {
      console.error('Error inviting user:', err);

      // Handle specific error cases
      let errorMessage = `Could not send invitation to ${emailToInvite}`;

      if (err.response?.data?.error === 'rate_limit') {
        // Handle rate limiting specifically
        const remainingSeconds = err.response.data.remaining_seconds || 60;
        errorMessage = `⏰ Please wait ${remainingSeconds} seconds before sending another invitation to this email`;
      } else if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.response?.status === 404) {
        errorMessage = `User with email ${emailToInvite} was not found. Please check the email address and try again.`;
      } else if (err.response?.status === 400) {
        errorMessage = err.response?.data?.message || 'Invalid request. Please check the email address.';
      } else if (err.response?.status === 429) {
        errorMessage = 'Too many invitation requests. Please wait before trying again.';
      } else if (err.response?.status >= 500) {
        errorMessage = 'Server error occurred. Please try again later.';
      }

      errorSnackbar(errorMessage);
      return false;
    } finally {
      setIsSending(false);
    }
  };

  // Handle delete plan
  const handleDeletePlan = async () => {
    try {
      await deletePlan(planInfo.slug);

      // Emit event to notify other components (like todo list) that plan was deleted
      eventEmitter.emit(EVENTS.PLAN_DELETED, { planSlug: planInfo.slug });

      successSnackbar('Plan has been deleted successfully.');
      navigate('/d/plan');
      return true;
    } catch (err) {
      console.error('Error deleting plan:', err);
      errorSnackbar('Could not delete the plan.');
      return false;
    }
  };

  // Handle opt out of plan
  const handleOptOutPlan = async () => {
    try {
      await optOutPlan(planInfo.slug);

      // Emit event to notify other components that user opted out of plan
      eventEmitter.emit(EVENTS.PLAN_OPTED_OUT, { planSlug: planInfo.slug });

      successSnackbar('You have successfully opted out of the plan.');
      navigate('/d/');
      return true;
    } catch (err) {
      console.error('Error opting out of plan:', err);
      errorSnackbar('Could not opt out of the plan.');
      return false;
    }
  };

  // Handle update plan name
  const handleUpdatePlanName = async (newName) => {
    try {
      await updatePlanName(planInfo.slug, newName);

      // Update local state
      setPlanInfo(prev => ({
        ...prev,
        name: newName
      }));

      successSnackbar('Plan name updated successfully.');
      return true;
    } catch (err) {
      console.error('Error updating plan name:', err);

      // Handle specific error cases
      let errorMessage = 'Could not update the plan name.';

      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.status === 404) {
        errorMessage = 'Plan was not found. It may have been deleted.';
      } else if (err.response?.status === 400) {
        errorMessage = err.response?.data?.error || 'Invalid plan name. Please try again.';
      } else if (err.response?.status >= 500) {
        errorMessage = 'Server error occurred. Please try again later.';
      }

      errorSnackbar(errorMessage);
      return false;
    }
  };

  // Refresh plan data
  const refreshPlanData = async () => {
    try {
      setLoading(true);
      const response = await fetchPlanInfo(planSlug);
      setPlanInfo(response.data);
      await fetchInvitedUsers();
    } catch (error) {
      console.error('Error refreshing plan data:', error);
      setError('Failed to refresh plan data');
    } finally {
      setLoading(false);
    }
  };

  // Calculate subtask progress
  const calculateSubtaskProgress = (subtask) => {
    // Kiểm tra subtask tồn tại
    if (!subtask) {
      return 0;
    }
    
    // If progress is explicitly set, use it
    if (subtask.progress !== undefined && subtask.progress !== null) {
      return subtask.progress;
    }
    
    // Otherwise, determine based on status
    switch (subtask.status) {
      case STATUS.COMPLETED:
        return 100;
      case STATUS.IN_PROGRESS:
        return 50;
      default:
        return 0;
    }
  };

  // Calculate subtask status
  const getSubtaskStatus = (subtask) => {
    const progress = calculateSubtaskProgress(subtask);
    
    if (progress === 100 || subtask.status === STATUS.COMPLETED) {
      return STATUS.COMPLETED;
    } else if (progress > 0 || subtask.status === STATUS.IN_PROGRESS) {
      return STATUS.IN_PROGRESS;
    }
    
    return STATUS.NOT_STARTED;
  };

  // Calculate task progress based on subtasks
  const calculateTaskProgress = (task) => {
    // Kiểm tra task tồn tại
    if (!task) {
      return 0;
    }
    
    // Đảm bảo subtasks là một mảng
    const subtasks = task.subtasks || [];
    
    // If no subtasks, base on task status
    if (subtasks.length === 0) {
      switch (task.status) {
        case STATUS.COMPLETED:
          return 100;
        case STATUS.IN_PROGRESS:
          return 50;
        default:
          return 0;
      }
    }
    
    // Calculate progress based on subtasks
    let totalProgress = 0;
    subtasks.forEach(subtask => {
      totalProgress += calculateSubtaskProgress(subtask);
    });
    
    return Math.floor(totalProgress / subtasks.length);
  };

  // Determine task status
  const getTaskStatus = (task) => {
    const subtasks = task.subtasks || [];
    
    // If no subtasks, base on task status
    if (subtasks.length === 0) {
      return task.status || STATUS.NOT_STARTED;
    }
    
    // Check status of subtasks
    let completedSubtasks = 0;
    let inProgressSubtasks = 0;
    
    subtasks.forEach(subtask => {
      const status = getSubtaskStatus(subtask);
      if (status === STATUS.COMPLETED) {
        completedSubtasks++;
      } else if (status === STATUS.IN_PROGRESS) {
        inProgressSubtasks++;
      }
    });
    
    if (completedSubtasks === subtasks.length) {
      return STATUS.COMPLETED;
    } else if (completedSubtasks > 0 || inProgressSubtasks > 0) {
      return STATUS.IN_PROGRESS;
    }
    
    return STATUS.NOT_STARTED;
  };

  // Calculate milestone progress based on tasks
  const calculateMilestoneProgress = (milestone) => {
    // Kiểm tra milestone tồn tại
    if (!milestone) {
      return 0;
    }
    
    // Đảm bảo tasks là một mảng
    const tasks = milestone.tasks || [];
    
    // If no tasks, return 0
    if (tasks.length === 0) {
      return 0;
    }
    
    // Calculate progress based on tasks
    let totalProgress = 0;
    tasks.forEach(task => {
      // Đảm bảo task tồn tại và có thuộc tính cần thiết
      if (task) {
        totalProgress += calculateTaskProgress(task);
      }
    });
    
    return Math.floor(totalProgress / tasks.length);
  };

  // Determine milestone status
  const getMilestoneStatus = (milestone) => {
    const tasks = milestone.tasks || [];
    if (tasks.length === 0) return STATUS.NOT_STARTED;
    
    let completedTasks = 0;
    let inProgressTasks = 0;
    
    tasks.forEach(task => {
      const status = getTaskStatus(task);
      if (status === STATUS.COMPLETED) {
        completedTasks++;
      } else if (status === STATUS.IN_PROGRESS) {
        inProgressTasks++;
      }
    });
    
    if (completedTasks === tasks.length) {
      return STATUS.COMPLETED;
    } else if (completedTasks > 0 || inProgressTasks > 0) {
      return STATUS.IN_PROGRESS;
    }
    
    return STATUS.NOT_STARTED;
  };

  // Calculate overall plan progress
  const calculatePlanProgress = () => {
    if (!planInfo || !planInfo.milestones || planInfo.milestones.length === 0) {
      return 0;
    }
    
    let totalProgress = 0;
    planInfo.milestones.forEach(milestone => {
      totalProgress += calculateMilestoneProgress(milestone);
    });
    
    return Math.floor(totalProgress / planInfo.milestones.length);
  };

  // Determine overall plan status
  const getPlanStatus = () => {
    if (!planInfo || !planInfo.milestones || planInfo.milestones.length === 0) {
      return STATUS.NOT_STARTED;
    }
    
    let completedMilestones = 0;
    let inProgressMilestones = 0;
    
    planInfo.milestones.forEach(milestone => {
      const status = getMilestoneStatus(milestone);
      if (status === STATUS.COMPLETED) {
        completedMilestones++;
      } else if (status === STATUS.IN_PROGRESS) {
        inProgressMilestones++;
      }
    });
    
    if (completedMilestones === planInfo.milestones.length) {
      return STATUS.COMPLETED;
    } else if (completedMilestones > 0 || inProgressMilestones > 0) {
      return STATUS.IN_PROGRESS;
    }
    
    return STATUS.NOT_STARTED;
  };

  // Calculate plan statistics
  const calculatePlanStats = () => {
    if (!planInfo) return { 
      milestones: 0, 
      tasks: 0, 
      subtasks: 0, 
      progress: 0,
      completedTasks: 0,
      inProgressTasks: 0,
      notStartedTasks: 0,
      comments: 0,
      status: STATUS.NOT_STARTED
    };
    
    const milestones = planInfo.milestones?.length || 0;
    
    let tasks = 0;
    let subtasks = 0;
    let completedTasks = 0;
    let inProgressTasks = 0;
    let notStartedTasks = 0;
    let comments = 0;
    
    planInfo.milestones?.forEach(milestone => {
      const milestoneTasks = milestone.tasks?.length || 0;
      tasks += milestoneTasks;
      
      milestone.tasks?.forEach(task => {
        subtasks += task.subtasks?.length || 0;
        comments += task.comments?.length || 0;
        
        // Use getTaskStatus to determine accurate status
        const taskStatus = getTaskStatus(task);
        if (taskStatus === STATUS.COMPLETED) {
          completedTasks++;
        } else if (taskStatus === STATUS.IN_PROGRESS) {
          inProgressTasks++;
        } else {
          notStartedTasks++;
        }
      });
    });
    
    // Use calculatePlanProgress to calculate accurate progress
    const progress = calculatePlanProgress();
    const status = getPlanStatus();
    
    return {
      milestones,
      tasks,
      subtasks,
      progress,
      completedTasks,
      inProgressTasks,
      notStartedTasks,
      comments,
      status
    };
  };

  return {
    planInfo,
    setPlanInfo, // Export setPlanInfo for optimistic updates
    loading,
    error,
    invitedUsers,
    email,
    isSending,
    fetchInvitedUsers,
    refreshPlanData,
    handleEmailChange,
    handleInviteUser,
    handleDeletePlan,
    handleOptOutPlan,
    handleUpdatePlanName,
    calculatePlanStats,
    // Export calculation functions for use in other components
    calculateSubtaskProgress,
    getSubtaskStatus,
    calculateTaskProgress,
    getTaskStatus,
    calculateMilestoneProgress,
    getMilestoneStatus,
    calculatePlanProgress,
    getPlanStatus,
    STATUS,
    STATUS_CONFIG
  };
};

export default usePlanData; 