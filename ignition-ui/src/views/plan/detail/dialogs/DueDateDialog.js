import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  TextField,
  Paper,
  Grid
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from 'helpers/constants';

const DueDateDialog = ({
  open,
  onClose,
  task,
  onUpdateDueDate,
  loading = false
}) => {
  // Format date as YYYY-MM-DD for input - wrapped in useCallback
  const formatDateForInput = useCallback((date) => {
    const d = new Date(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
  }, []);

  // Parse dates or set defaults - wrapped in useCallback
  const getInitialStartDate = useCallback(() => {
    if (task?.start_date) {
      return task.start_date;
    }
    return formatDateForInput(new Date()); // Today
  }, [task, formatDateForInput]);

  const getInitialEndDate = useCallback(() => {
    if (task?.end_date) {
      return task.end_date;
    }

    // Default: 2 days from today
    const date = new Date();
    date.setDate(date.getDate() + 2);
    return formatDateForInput(date);
  }, [task, formatDateForInput]);

  const [startDate, setStartDate] = useState(getInitialStartDate());
  const [endDate, setEndDate] = useState(getInitialEndDate());
  const [startDateError, setStartDateError] = useState('');
  const [endDateError, setEndDateError] = useState('');

  // Reset dates when task changes
  useEffect(() => {
    if (open) {
      setStartDate(getInitialStartDate());
      setEndDate(getInitialEndDate());
      setStartDateError('');
      setEndDateError('');
    }
  }, [open, task, getInitialStartDate, getInitialEndDate]);

  // Validate dates
  const validateDates = useCallback(() => {
    let isValid = true;

    if (!startDate) {
      setStartDateError('Start date is required');
      isValid = false;
    } else {
      setStartDateError('');
    }

    if (!endDate) {
      setEndDateError('End date is required');
      isValid = false;
    } else if (startDate && endDate && new Date(startDate) > new Date(endDate)) {
      setEndDateError('End date must be after start date');
      isValid = false;
    } else {
      setEndDateError('');
    }

    return isValid;
  }, [startDate, endDate, setStartDateError, setEndDateError]);

  const handleStartDateChange = useCallback((e) => {
    const newDate = e.target.value;
    setStartDate(newDate);

    // If end date is before new start date, update end date
    if (endDate && new Date(endDate) < new Date(newDate)) {
      const date = new Date(newDate);
      date.setDate(date.getDate() + 1);
      setEndDate(formatDateForInput(date));
    }

    setStartDateError('');
  }, [endDate, setStartDate, setEndDate, formatDateForInput, setStartDateError]);

  const handleEndDateChange = useCallback((e) => {
    setEndDate(e.target.value);
    setEndDateError('');
  }, [setEndDate, setEndDateError]);

  const handleSave = useCallback(() => {
    if (validateDates()) {
      onUpdateDueDate({
        ...task,
        start_date: startDate,
        end_date: endDate
      });

      onClose();
    }
  }, [validateDates, onUpdateDueDate, task, startDate, endDate, onClose]);

  // Quick date presets - memoized to prevent recreation on each render
  const presets = useMemo(() => [
    {
      label: 'Today',
      apply: () => {
        const today = formatDateForInput(new Date());
        setStartDate(today);
        setEndDate(today);
      }
    },
    {
      label: 'Tomorrow',
      apply: () => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const formattedTomorrow = formatDateForInput(tomorrow);
        setStartDate(formattedTomorrow);
        setEndDate(formattedTomorrow);
      }
    },
    {
      label: 'This week',
      apply: () => {
        const today = new Date();
        const endOfWeek = new Date();
        endOfWeek.setDate(today.getDate() + (6 - today.getDay()));

        setStartDate(formatDateForInput(today));
        setEndDate(formatDateForInput(endOfWeek));
      }
    },
    {
      label: 'Next week',
      apply: () => {
        const today = new Date();
        const startOfNextWeek = new Date();
        startOfNextWeek.setDate(today.getDate() + (7 - today.getDay() + 1));

        const endOfNextWeek = new Date(startOfNextWeek);
        endOfNextWeek.setDate(startOfNextWeek.getDate() + 6);

        setStartDate(formatDateForInput(startOfNextWeek));
        setEndDate(formatDateForInput(endOfNextWeek));
      }
    },
  ], [formatDateForInput, setStartDate, setEndDate]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottom: '1px solid #f0f0f0',
        py: 1.5,
        px: 2,
        bgcolor: '#FAFAFA'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Iconify icon="material-symbols:calendar-month" width={20} height={20} color={mainYellowColor} sx={{ mr: 2 }} />
          <Typography variant="subtitle1" sx={{ fontFamily: '"Recursive Variable", sans-serif', fontWeight: 600 }}>
            Set Due Date for "{task?.name}"
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small" sx={{ p: 0.5 }}>
          <Iconify icon="material-symbols:close" width={18} height={18} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 2, pb: 1.5, px: 2, bgcolor: '#FAFAFA' }}>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1, fontWeight: 500, color: '#555' }}>
            Quick presets:
          </Typography>
          <Grid container spacing={1}>
            {presets.map((preset, index) => (
              <Grid item xs={6} sm={3} key={index}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 1,
                    textAlign: 'center',
                    cursor: 'pointer',
                    border: '1px solid #e0e0e0',
                    borderRadius: '6px',
                    '&:hover': {
                      bgcolor: `${mainYellowColor}20`,
                      borderColor: mainYellowColor
                    }
                  }}
                  onClick={preset.apply}
                >
                  <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                    {preset.label}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" sx={{ mb: 1, fontWeight: 500, color: '#555' }}>
            Custom dates:
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="Start Date"
                type="date"
                value={startDate}
                onChange={handleStartDateChange}
                fullWidth
                size="small"
                error={!!startDateError}
                helperText={startDateError}
                InputLabelProps={{
                  shrink: true,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '6px',
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                label="End Date"
                type="date"
                value={endDate}
                onChange={handleEndDateChange}
                fullWidth
                size="small"
                error={!!endDateError}
                helperText={endDateError}
                InputLabelProps={{
                  shrink: true,
                }}
                inputProps={{
                  min: startDate,
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '6px',
                  }
                }}
              />
            </Grid>
          </Grid>
        </Box>

        <Box sx={{ mt: 2, p: 2, bgcolor: '#f5f5f5', borderRadius: '6px', border: '1px dashed #ccc' }}>
          <Typography variant="body2" sx={{ color: '#666', fontSize: '0.85rem' }}>
            <strong>Tip:</strong> Setting realistic due dates helps your team plan better and improves project tracking. Consider task dependencies when setting dates.
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 2, py: 1.5, borderTop: '1px solid #f0f0f0', bgcolor: '#FAFAFA' }}>
        <Button
          onClick={onClose}
          variant="outlined"
          size="small"
          sx={{
            borderRadius: '6px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            mr: 1,
            py: 0.5,
            fontSize: '0.8rem'
          }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          variant="contained"
          size="small"
          disabled={loading}
          sx={{
            bgcolor: mainYellowColor,
            '&:hover': {
              bgcolor: '#e0a800',
            },
            borderRadius: '6px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            py: 0.5,
            fontSize: '0.8rem'
          }}
          startIcon={<Iconify icon="material-symbols:save" width={14} height={14} />}
        >
          Save Due Date
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DueDateDialog;
