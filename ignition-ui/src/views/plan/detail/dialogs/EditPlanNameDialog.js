import React, { useState, useEffect } from 'react';
import { 
  <PERSON>alog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  TextField,
  Typography, 
  CircularProgress
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import styles from '../styles.module.scss';

const EditPlanNameDialog = ({ open, onClose, onSave, currentName }) => {
  const [loading, setLoading] = useState(false);
  const [planName, setPlanName] = useState('');
  const [error, setError] = useState('');

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (open) {
      setPlanName(currentName || '');
      setError('');
    }
  }, [open, currentName]);

  const handleSave = async () => {
    const trimmedName = planName.trim();
    
    if (!trimmedName) {
      setError('Plan name cannot be empty');
      return;
    }

    if (trimmedName === currentName) {
      onClose();
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const success = await onSave(trimmedName);
      if (success) {
        onClose();
      }
    } catch (err) {
      console.error('Error updating plan name:', err);
      setError('Failed to update plan name. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !loading) {
      handleSave();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
      className={styles.editDialog}
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle 
        className={styles.dialogTitle}
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          fontFamily: '"Recursive Variable", sans-serif',
          fontSize: '1.3rem',
          fontWeight: 600,
          color: '#333',
          pb: 1
        }}
      >
        <Iconify icon="material-symbols:edit" width={24} height={24} color="#F0A500" />
        Edit Plan Name
      </DialogTitle>
      
      <DialogContent className={styles.dialogContent}>
        <Typography 
          variant="body2" 
          sx={{ 
            mb: 2, 
            color: '#666',
            fontFamily: '"Recursive Variable", sans-serif'
          }}
        >
          Enter a new name for your plan:
        </Typography>
        
        <TextField
          autoFocus
          fullWidth
          value={planName}
          onChange={(e) => setPlanName(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Enter plan name"
          error={!!error}
          helperText={error}
          disabled={loading}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              fontFamily: '"Recursive Variable", sans-serif',
              '&.Mui-focused fieldset': {
                borderColor: '#F0A500',
                borderWidth: '2px'
              }
            },
            '& .MuiInputBase-input': {
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 500
            }
          }}
        />
      </DialogContent>
      
      <DialogActions className={styles.dialogActions}>
        <Button 
          onClick={onClose}
          disabled={loading}
          sx={{ 
            color: '#666',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSave}
          disabled={loading || !planName.trim()}
          variant="contained"
          startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <Iconify icon="material-symbols:save" width={16} height={16} />}
          sx={{ 
            backgroundColor: '#F0A500',
            '&:hover': { backgroundColor: '#d89400' },
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
        >
          {loading ? 'Saving...' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditPlanNameDialog;
