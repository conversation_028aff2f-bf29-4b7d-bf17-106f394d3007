import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Alert,
  Snackbar
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import { showToast } from 'components/NotificationToast';
import styles from '../styles.module.scss';

const InviteDialog = ({ open, onClose, onInvite, planInfo }) => {
  const [email, setEmail] = useState('');
  const [accessLevel, setAccessLevel] = useState('viewer');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [showNotification, setShowNotification] = useState(false);

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
    setError('');
    setSuccess(false);
  };

  const validateEmail = (email) => {
    const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
  };

  const handleInvite = async () => {
    if (!email) {
      setError('Please enter an email address');
      return;
    }

    if (!validateEmail(email)) {
      setError('Please enter a valid email address');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const result = await onInvite(email, accessLevel);
      if (result) {
        // Show success message for 3 seconds before closing
        setSuccess(true);
        setSuccessMessage(`🎉 Invitation sent to ${email}! They'll receive an email with instructions to join the project.`);
        setShowNotification(true);

        // Show prominent toast notification
        showToast(
          `🎉 Invitation sent successfully to ${email}! They'll receive an email with instructions to join the project.`,
          'success',
          6000
        );

        setTimeout(() => {
          setEmail('');
          setAccessLevel('viewer');
          setSuccess(false);
          setSuccessMessage('');
          setShowNotification(false);
          onClose();
        }, 3000);
      }
    } catch (err) {
      console.error('Error inviting user:', err);

      // Handle specific error cases
      let errorMessage = 'Failed to send invitation. Please try again.';

      if (err.response?.data?.error) {
        errorMessage = err.response.data.error;
      } else if (err.response?.status === 404) {
        errorMessage = `User with email ${email} was not found. Please check the email address and try again.`;
      } else if (err.response?.status === 400) {
        errorMessage = err.response?.data?.message || 'Invalid email address or user already has access.';
      } else if (err.response?.status >= 500) {
        errorMessage = 'Server error occurred. Please try again later.';
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !loading) {
      handleInvite();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={loading ? null : onClose}
      maxWidth="sm"
      fullWidth
      className={styles.inviteDialog}
      PaperProps={{
        sx: {
          borderRadius: '12px',
          padding: '8px'
        }
      }}
    >
      <DialogTitle 
        className={styles.dialogTitle}
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          fontFamily: '"Recursive Variable", sans-serif',
          fontSize: '1.3rem',
          fontWeight: 600,
          color: '#333',
          pb: 1
        }}
      >
        <Iconify icon="material-symbols:person-add" width={24} height={24} color={mainYellowColor} />
        Invite to Plan
      </DialogTitle>
      
      <DialogContent className={styles.dialogContent}>
        {success ? (
          <Box sx={{ textAlign: 'center', py: 2 }}>
            <Iconify
              icon="material-symbols:check-circle"
              width={48}
              height={48}
              color={mainYellowColor}
              sx={{ mb: 2 }}
            />
            <Typography
              variant="h6"
              sx={{
                mb: 1,
                color: '#333',
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600
              }}
            >
              Invitation Sent Successfully!
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#555',
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              {successMessage}
            </Typography>
          </Box>
        ) : (
          <>
            <Typography
              variant="body2"
              sx={{
                mb: 2,
                color: '#555',
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              Invite someone to collaborate on "{planInfo?.name}". They will receive an email invitation.
            </Typography>
        
        <TextField
          fullWidth
          label="Email Address"
          variant="outlined"
          value={email}
          onChange={handleEmailChange}
          onKeyDown={handleKeyDown}
          error={!!error}
          helperText={error}
          disabled={loading}
          placeholder="<EMAIL>"
          sx={{
            mb: 2,
            '& .MuiOutlinedInput-root': {
              borderRadius: '8px',
              fontFamily: '"Recursive Variable", sans-serif'
            },
            '& .MuiInputLabel-root': {
              fontFamily: '"Recursive Variable", sans-serif'
            }
          }}
        />

            <FormControl fullWidth sx={{ mb: 1 }}>
              <InputLabel
                sx={{ fontFamily: '"Recursive Variable", sans-serif' }}
              >
                Access Level
              </InputLabel>
              <Select
                value={accessLevel}
                onChange={(e) => setAccessLevel(e.target.value)}
                label="Access Level"
                disabled={loading}
                sx={{
                  borderRadius: '8px',
                  fontFamily: '"Recursive Variable", sans-serif'
                }}
              >
                <MenuItem value="viewer">Viewer - Can view plan and tasks</MenuItem>
                <MenuItem value="editor">Editor - Can edit tasks and content</MenuItem>
                <MenuItem value="owner">Owner - Full access including member management</MenuItem>
              </Select>
            </FormControl>
          </>
        )}
      </DialogContent>
      
      {!success && (
        <DialogActions className={styles.dialogActions}>
          <Button
            onClick={onClose}
            disabled={loading}
            sx={{
              color: '#666',
              textTransform: 'none',
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleInvite}
            disabled={loading || !email}
            variant="contained"
            startIcon={loading ? <CircularProgress size={16} color="inherit" /> : <Iconify icon="material-symbols:send" width={16} height={16} />}
            sx={{
              backgroundColor: mainYellowColor,
              color: '#333',
              textTransform: 'none',
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600,
              '&:hover': {
                backgroundColor: '#e0a800'
              }
            }}
          >
            {loading ? 'Sending...' : 'Send Invitation'}
          </Button>
        </DialogActions>
      )}

      {/* Global notification snackbar */}
      <Snackbar
        open={showNotification}
        autoHideDuration={6000}
        onClose={() => setShowNotification(false)}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        sx={{ zIndex: 9999 }}
      >
        <Alert
          onClose={() => setShowNotification(false)}
          severity="success"
          sx={{
            width: '100%',
            backgroundColor: '#4caf50',
            color: 'white',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            '& .MuiAlert-icon': {
              color: 'white'
            }
          }}
        >
          📧 Invitation sent successfully! The recipient will receive an email with instructions to join.
        </Alert>
      </Snackbar>
    </Dialog>
  );
};

export default InviteDialog; 