import React, { useState } from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography, 
  Box,
  IconButton,
  TextField,
  Avatar,
  Divider,
  CircularProgress,
  Paper
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from 'helpers/constants';
import { useSelector } from 'react-redux';

const CommentDialog = ({
  open,
  onClose,
  task,
  comments = [],
  onAddComment,
  onUpdateComment,
  onDeleteComment,
  loading = false
}) => {
  const [newComment, setNewComment] = useState('');
  const [editingComment, setEditingComment] = useState(null);
  const [editedContent, setEditedContent] = useState('');
  const currentUser = useSelector((state) => state.user);

  const handleAddComment = () => {
    if (newComment.trim()) {
      onAddComment(newComment.trim());
      setNewComment('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleAddComment();
    }
  };

  const startEditComment = (comment) => {
    setEditingComment(comment);
    setEditedContent(comment.content);
  };

  const cancelEditComment = () => {
    setEditingComment(null);
    setEditedContent('');
  };

  const saveEditedComment = () => {
    if (editedContent.trim() && editingComment) {
      onUpdateComment(editingComment.id, editedContent.trim());
      setEditingComment(null);
      setEditedContent('');
    }
  };

  const handleDeleteComment = (commentId) => {
    if (window.confirm('Are you sure you want to delete this comment?')) {
      onDeleteComment(commentId);
    }
  };

  const getUserName = (comment) => {
    if (comment.user) {
      if (comment.user.first_name && comment.user.last_name) {
        return `${comment.user.first_name} ${comment.user.last_name}`;
      }
      return comment.user.name || comment.user.username || comment.user.email;
    }
    return 'Unknown User';
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
          maxHeight: '70vh',
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderBottom: '1px solid #f0f0f0',
        py: 1.5,
        px: 2,
        bgcolor: '#FAFAFA'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Iconify icon="material-symbols:comment" width={20} height={20} color={mainYellowColor} sx={{ mr: 2 }} />
          <Typography variant="subtitle1" sx={{ fontFamily: '"Recursive Variable", sans-serif', fontWeight: 600 }}>
            Comments for "{task?.name}"
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small" sx={{ p: 0.5 }}>
          <Iconify icon="material-symbols:close" width={18} height={18} />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 2, pb: 1.5, px: 2, bgcolor: '#FAFAFA' }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
            <CircularProgress size={32} sx={{ color: mainYellowColor }} />
          </Box>
        ) : comments.length === 0 ? (
          <Box sx={{ py: 3, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif' }}>
              No comments yet. Be the first to comment!
            </Typography>
          </Box>
        ) : (
          <Box sx={{ mb: 1.5, maxHeight: '40vh', overflow: 'auto', pr: 1 }}>
            {comments.map((comment, index) => (
              <Paper
                key={comment.id || index}
                elevation={0}
                sx={{
                  mb: 1.5,
                  p: 1.5,
                  borderRadius: '8px',
                  bgcolor: '#FFF',
                  border: '1px solid #f0f0f0'
                }}
              >
                <Box sx={{ display: 'flex' }}>
                  <Avatar
                    src={comment.user?.avatar || comment.avatar}
                    alt={getUserName(comment) || 'User'}
                    sx={{ width: 32, height: 32, mr: 2 }}
                  >
                    {(getUserName(comment) || 'U')[0]}
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <Box>
                        <Typography variant="subtitle2" sx={{ fontFamily: '"Recursive Variable", sans-serif', fontWeight: 600, color: '#333', fontSize: '0.85rem' }}>
                          {getUserName(comment) || 'Unknown User'}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.75rem' }}>
                          {comment.created_at || 'Just now'}
                        </Typography>
                      </Box>

                      {(comment.user?.id === currentUser?.id || comment.user_id === currentUser?.id) && (
                        <Box>
                          <IconButton
                            size="small"
                            onClick={() => startEditComment(comment)}
                            sx={{ p: 0.3 }}
                          >
                            <Iconify icon="material-symbols:edit-outline" width={14} height={14} color="#666" />
                          </IconButton>
                          <IconButton
                            size="small"
                            onClick={() => handleDeleteComment(comment.id)}
                            sx={{ p: 0.3 }}
                          >
                            <Iconify icon="material-symbols:delete-outline" width={14} height={14} color="#F44336" />
                          </IconButton>
                        </Box>
                      )}
                    </Box>

                    {editingComment && editingComment.id === comment.id ? (
                      <Box sx={{ mt: 1 }}>
                        <TextField
                          fullWidth
                          multiline
                          rows={2}
                          value={editedContent}
                          onChange={(e) => setEditedContent(e.target.value)}
                          variant="outlined"
                          size="small"
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              borderRadius: '6px',
                              fontSize: '0.85rem'
                            }
                          }}
                        />
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
                          <Button
                            onClick={cancelEditComment}
                            variant="outlined"
                            size="small"
                            sx={{
                              mr: 1,
                              borderRadius: '4px',
                              textTransform: 'none',
                              fontFamily: '"Recursive Variable", sans-serif',
                              py: 0.5,
                              fontSize: '0.75rem'
                            }}
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={saveEditedComment}
                            variant="contained"
                            size="small"
                            sx={{
                              bgcolor: mainYellowColor,
                              '&:hover': {
                                bgcolor: '#e0a800',
                              },
                              borderRadius: '4px',
                              textTransform: 'none',
                              fontFamily: '"Recursive Variable", sans-serif',
                              py: 0.5,
                              fontSize: '0.75rem'
                            }}
                          >
                            Save
                          </Button>
                        </Box>
                      </Box>
                    ) : (
                      <Typography
                        variant="body2"
                        sx={{
                          mt: 0.5,
                          whiteSpace: 'pre-wrap',
                          fontFamily: '"Recursive Variable", sans-serif',
                          lineHeight: 1.4,
                          color: '#333',
                          fontSize: '0.85rem'
                        }}
                      >
                        {comment.content}
                      </Typography>
                    )}
                  </Box>
                </Box>
              </Paper>
            ))}
          </Box>
        )}

        <Divider sx={{ my: 1.5 }} />

        <Paper
          elevation={0}
          sx={{
            p: 1.5,
            borderRadius: '8px',
            bgcolor: '#FFF',
            border: '1px solid #f0f0f0'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
            <Avatar
              src={currentUser?.avatar}
              alt={currentUser?.name || 'You'}
              sx={{ width: 32, height: 32, mr: 2, mt: 0.5 }}
            >
              {(currentUser?.name || 'Y')[0]}
            </Avatar>
            <Box sx={{ flexGrow: 1 }}>
              <TextField
                fullWidth
                multiline
                rows={1}
                placeholder="Add a comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                onKeyDown={handleKeyPress}
                variant="outlined"
                size="small"
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '6px',
                    fontSize: '0.85rem'
                  }
                }}
              />
            </Box>
          </Box>
        </Paper>
      </DialogContent>

      <DialogActions sx={{ px: 2, py: 1.5, borderTop: '1px solid #f0f0f0', bgcolor: '#FAFAFA' }}>
        <Button
          onClick={onClose}
          variant="outlined"
          size="small"
          sx={{
            borderRadius: '6px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            mr: 1,
            py: 0.5,
            fontSize: '0.8rem'
          }}
        >
          Close
        </Button>
        <Button
          onClick={handleAddComment}
          variant="contained"
          size="small"
          disabled={!newComment.trim()}
          sx={{
            bgcolor: mainYellowColor,
            '&:hover': {
              bgcolor: '#e0a800',
            },
            borderRadius: '6px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            py: 0.5,
            fontSize: '0.8rem'
          }}
          startIcon={<Iconify icon="material-symbols:send" width={14} height={14} />}
        >
          Comment
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CommentDialog;
