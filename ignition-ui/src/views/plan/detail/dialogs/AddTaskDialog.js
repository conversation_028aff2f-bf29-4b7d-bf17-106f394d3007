import React, { useState } from 'react';
import { 
  <PERSON>alog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  TextField, 
  Button, 
  Box, 
  Typography,
  IconButton
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from 'helpers/constants';

const AddTaskDialog = ({ open, onClose, milestone, onAddTask }) => {
  const [taskName, setTaskName] = useState('');
  const [description, setDescription] = useState('');
  const [startDate, setStartDate] = useState(dayjs());
  const [endDate, setEndDate] = useState(dayjs().add(1, 'day'));
  const [deadline, setDeadline] = useState(dayjs().add(3, 'day'));
  const [error, setError] = useState('');

  const handleSubmit = () => {
    if (!taskName.trim()) {
      setError('Please enter task name');
      return;
    }

    if (endDate.isBefore(startDate)) {
      setError('End date must be after start date');
      return;
    }

    const newTask = {
      name: taskName.trim(),
      description: description.trim(),
      start_date: startDate.format('YYYY-MM-DD'),
      end_date: endDate.format('YYYY-MM-DD'),
      deadline: deadline.format('YYYY-MM-DD'),
      milestone: milestone.id
    };

    onAddTask(newTask);
    handleClose();
  };

  const handleClose = () => {
    setTaskName('');
    setDescription('');
    setStartDate(dayjs());
    setEndDate(dayjs().add(1, 'day'));
    setDeadline(dayjs().add(3, 'day'));
    setError('');
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose} 
      maxWidth="sm" 
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          boxShadow: '0 8px 24px rgba(0,0,0,0.1)'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        borderBottom: '1px solid #f0f0f0',
        pb: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Iconify icon="material-symbols:add-task" width={24} height={24} color={mainYellowColor} sx={{ mr: 1 }} />
          <Typography variant="h6" sx={{ fontFamily: '"Recursive Variable", sans-serif', fontWeight: 600 }}>
            Add New Task
          </Typography>
        </Box>
        <IconButton onClick={handleClose} size="small">
          <Iconify icon="material-symbols:close" width={20} height={20} />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ mb: 3 }}>
          <Typography 
            variant="subtitle2" 
            sx={{ 
              mb: 1, 
              fontFamily: '"Recursive Variable", sans-serif',
              color: '#333',
              fontWeight: 600
            }}
          >
            Task Name <span style={{ color: 'red' }}>*</span>
          </Typography>
          <TextField
            fullWidth
            value={taskName}
            onChange={(e) => setTaskName(e.target.value)}
            placeholder="Enter task name..."
            variant="outlined"
            size="small"
            error={!!error && !taskName.trim()}
            helperText={!taskName.trim() && error ? error : ''}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
              }
            }}
          />
        </Box>
        
        <Box sx={{ mb: 3 }}>
          <Typography 
            variant="subtitle2" 
            sx={{ 
              mb: 1, 
              fontFamily: '"Recursive Variable", sans-serif',
              color: '#333',
              fontWeight: 600
            }}
          >
            Description
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Enter task description (optional)..."
            variant="outlined"
            size="small"
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
              }
            }}
          />
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            <Typography 
              variant="subtitle2" 
              sx={{ 
                mb: 1, 
                fontFamily: '"Recursive Variable", sans-serif',
                color: '#333',
                fontWeight: 600
              }}
            >
              Start date
            </Typography>
            <TextField
              fullWidth
              type="date"
              value={startDate.format('YYYY-MM-DD')}
              onChange={(e) => setStartDate(dayjs(e.target.value))}
              placeholder="Start date"
              InputLabelProps={{
                shrink: true,
              }}
              sx={{ mb: 2 }}
            />
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography 
              variant="subtitle2" 
              sx={{ 
                mb: 1, 
                fontFamily: '"Recursive Variable", sans-serif',
                color: '#333',
                fontWeight: 600
              }}
            >
              End date
            </Typography>
            <TextField
              fullWidth
              type="date"
              value={endDate.format('YYYY-MM-DD')}
              onChange={(e) => setEndDate(dayjs(e.target.value))}
              placeholder="End date"
              InputLabelProps={{
                shrink: true,
              }}
              error={endDate.isBefore(startDate)}
              helperText={endDate.isBefore(startDate) ? 'End date must be after start date' : ''}
              sx={{ mb: 2 }}
            />
          </Box>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography
            variant="subtitle2"
            sx={{
              mb: 1,
              fontFamily: '"Recursive Variable", sans-serif',
              color: '#333',
              fontWeight: 600
            }}
          >
            Deadline
          </Typography>
          <TextField
            fullWidth
            type="date"
            value={deadline.format('YYYY-MM-DD')}
            onChange={(e) => setDeadline(dayjs(e.target.value))}
            placeholder="Deadline"
            InputLabelProps={{
              shrink: true,
            }}
            sx={{ mb: 2 }}
          />
        </Box>

        {error && !error.includes('task name') && !error.includes('End date') && (
          <Typography color="error" variant="body2" sx={{ mt: 1 }}>
            {error}
          </Typography>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #f0f0f0' }}>
        <Button 
          onClick={handleClose}
          variant="outlined"
          sx={{ 
            borderRadius: '8px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            mr: 1
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSubmit}
          variant="contained"
          sx={{ 
            bgcolor: mainYellowColor,
            '&:hover': {
              bgcolor: '#e6a800',
            },
            borderRadius: '8px',
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600
          }}
          startIcon={<Iconify icon="material-symbols:add" width={20} height={20} />}
        >
          Add Task
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddTaskDialog; 