import React, { useEffect, useMemo, useState } from 'react';
import { Box, Typography, Paper, Avatar, Chip, Button, Grid } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from '../styles.module.scss';

const Members = ({ invitedUsers, planOwner, onInvite, expanded = false, planInfo }) => {
  const [showAllMembers, setShowAllMembers] = useState(expanded);

  // Check if current user can invite (only owners can invite)
  const canInvite = planInfo?.user_access_level?.access_level === 'owner';

  // Use unified members from planInfo if available, fallback to invitedUsers
  const members = useMemo(() => {
    if (planInfo?.members && Array.isArray(planInfo.members)) {
      return planInfo.members;
    }
    // Fallback to old invitedUsers format for backward compatibility
    return Array.isArray(invitedUsers) ? invitedUsers : [];
  }, [planInfo?.members, invitedUsers]);

  // Filter out the plan owner from members list (they're shown separately)
  // Also filter out pending users - Members view shows only joined users
  const nonOwnerMembers = useMemo(() => {
    return members.filter(member =>
      !member.is_head_owner &&
      member.email !== planOwner?.email &&
      member.status === 'joined'  // Only show joined users in Members view
    );
  }, [members, planOwner?.email]);

  // Calculate total members (including owner if exists)
  const hasOwner = !!(planOwner || planInfo?.user);
  const totalMembers = nonOwnerMembers.length + (hasOwner ? 1 : 0);

  // Log to check data
  useEffect(() => {
    console.log('🔍 Members component debug:');
    console.log('  - planInfo.members:', planInfo?.members);
    console.log('  - nonOwnerMembers:', nonOwnerMembers);
    console.log('  - planOwner:', planOwner);
    console.log('  - totalMembers:', totalMembers);
    console.log('  - planOwner exists?', !!planOwner);
    console.log('  - planOwner email:', planOwner?.email);
    console.log('  - planOwner name:', planOwner?.first_name, planOwner?.last_name);
  }, [planInfo?.members, nonOwnerMembers, planOwner, totalMembers]);

  const handleViewAllMembers = () => {
    setShowAllMembers(true);
  };

  return (
    <Paper
      elevation={0}
      className={styles.membersCard}
      sx={{
        borderRadius: '12px',
        mb: 0.5,
        py: 1,
        px: 1.5
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Iconify icon="material-symbols:people" width={20} height={20} color={mainYellowColor} />
          <Typography
            variant="h6"
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600,
              fontSize: '1.2rem',
              color: '#555',
              margin: 0
            }}
          >
            Members ({totalMembers})
          </Typography>
        </Box>

        {canInvite && (
          <Button
            onClick={onInvite}
            variant="contained"
            startIcon={<Iconify icon="material-symbols:person-add" width={14} height={14} />}
            size="small"
            sx={{
              backgroundColor: mainYellowColor,
              color: '#333',
              fontWeight: 600,
              textTransform: 'none',
              borderRadius: '6px',
              fontFamily: '"Recursive Variable", sans-serif',
              padding: '2px 8px',
              fontSize: '0.9rem',
              minWidth: '60px',
              height: '24px',
              '&:hover': {
                backgroundColor: '#e0a800'
              }
            }}
          >
            Invite
          </Button>
        )}
      </Box>

      <Grid container spacing={1}>
        {/* Owner - Always show if planOwner exists or fallback to planInfo.user */}
        {(planOwner || planInfo?.user) && (
          <Grid item xs={12} sm={6}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                p: 0.75,
                borderRadius: '6px',
                backgroundColor: '#f9f9f9',
                height: '100%',
                border: `1px solid ${mainYellowColor}40`
              }}
            >
              <Avatar
                src={(planOwner || planInfo?.user)?.avatar}
                alt={(planOwner || planInfo?.user)?.first_name}
                sx={{
                  width: 32,
                  height: 32,
                  border: `2px solid ${mainYellowColor}`
                }}
              >
                {!(planOwner || planInfo?.user)?.avatar && (
                  <Iconify icon="material-symbols:crown" width={16} height={16} color={mainYellowColor} />
                )}
              </Avatar>
              <Box sx={{ flexGrow: 1, minWidth: 0, overflow: 'hidden' }}>
                <Typography variant="body1" sx={{ fontWeight: 600, fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', lineHeight: 1.2, color: '#333', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {(planOwner || planInfo?.user)?.first_name} {(planOwner || planInfo?.user)?.last_name || ''}
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.8rem', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {(planOwner || planInfo?.user)?.email} • owner
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Iconify icon="material-symbols:crown" width={16} height={16} color={mainYellowColor} />
                <Chip
                  label="Owner"
                  size="small"
                  sx={{
                    backgroundColor: `${mainYellowColor}20`,
                    color: '#333',
                    fontWeight: 600,
                    fontFamily: '"Recursive Variable", sans-serif',
                    height: '20px',
                    '& .MuiChip-label': {
                      padding: '0 6px',
                      fontSize: '0.75rem'
                    }
                  }}
                />
              </Box>
            </Box>
          </Grid>
        )}

        {/* Members */}
        {nonOwnerMembers.length > 0 ? (
          nonOwnerMembers.slice(0, showAllMembers ? nonOwnerMembers.length : (nonOwnerMembers.length > 3 ? 3 : nonOwnerMembers.length)).map((member, index) => {
            // Unified member data structure
            const displayName = member.first_name
              ? `${member.first_name} ${member.last_name || ''}`.trim()
              : member.email;

            const getStatusInfo = (status) => {
              switch (status) {
                case 'joined':
                  return { label: 'Joined', color: '#4CAF50', bgColor: '#4CAF5020' };
                case 'pending':
                  return { label: 'Pending', color: '#FFC107', bgColor: '#FFC10720' };
                case 'rejected':
                  return { label: 'Rejected', color: '#f44336', bgColor: '#f4433620' };
                default:
                  return { label: 'Unknown', color: '#666', bgColor: '#66666620' };
              }
            };

            const statusInfo = getStatusInfo(member.status);

            return (
              <Grid item xs={12} sm={6} key={`member-${member.id || index}-${member.email}`}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                    p: 0.75,
                    borderRadius: '6px',
                    backgroundColor: '#f9f9f9',
                    opacity: member.status === 'joined' ? 1 : 0.7,
                    height: '100%'
                  }}
                >
                  <Avatar
                    src={member.avatar}
                    alt={displayName}
                    sx={{ width: 32, height: 32 }}
                  />
                  <Box sx={{ flexGrow: 1, minWidth: 0, overflow: 'hidden' }}>
                    <Typography variant="body1" sx={{ fontWeight: 600, fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', lineHeight: 1.2, color: '#333', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {displayName}
                    </Typography>
                    <Typography variant="body2" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.8rem', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                      {member.email} • {member.access_level}
                    </Typography>
                  </Box>
                  <Chip
                    label={statusInfo.label}
                    size="small"
                    sx={{
                      backgroundColor: statusInfo.bgColor,
                      color: statusInfo.color,
                      fontWeight: 600,
                      fontFamily: '"Recursive Variable", sans-serif',
                      height: '20px',
                      '& .MuiChip-label': {
                        padding: '0 6px',
                        fontSize: '0.75rem'
                      }
                    }}
                  />
                </Box>
              </Grid>
            );
          })
        ) : (
          <Grid item xs={12}>
            <Typography variant="body2" sx={{ color: '#666', fontStyle: 'italic', p: 1, textAlign: 'center' }}>
              No members invited yet
            </Typography>
          </Grid>
        )}
      </Grid>

      {!showAllMembers && nonOwnerMembers.length > 3 && (
        <Button
          onClick={handleViewAllMembers}
          variant="text"
          sx={{
            color: mainYellowColor,
            fontWeight: 600,
            textTransform: 'none',
            fontFamily: '"Recursive Variable", sans-serif',
            fontSize: '0.9rem',
            padding: '4px 0',
            minHeight: '24px',
            mt: 1,
            display: 'block',
            ml: 'auto'
          }}
        >
          View all {nonOwnerMembers.length} members
        </Button>
      )}
    </Paper>
  );
};

export default Members;
