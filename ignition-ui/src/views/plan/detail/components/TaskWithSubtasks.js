import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Chip,
  LinearProgress,
  Tooltip,
  Card,
  CardContent,
  Divider,
  Button,
  Avatar,
  AvatarGroup,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';
import { mainYellowColor } from 'helpers/constants';
import CommentDialog from '../dialogs/CommentDialog';
import AssignMemberDialog from '../dialogs/AssignMemberDialog';
import { getComments, addComment, updateComment, deleteComment, assignMembersToTask } from '../../services';
import { toast } from 'react-toastify';
import dayjs from 'dayjs';

const TaskWithSubtasks = ({ task, onUpdateTask, onUpdateSubtask, onAddSubtask, onDeleteTask, onDeleteSubtask }) => {
  const [commentDialogOpen, setCommentDialogOpen] = useState(false);
  const [comments, setComments] = useState([]);
  const [loadingComments, ] = useState(false);
  const [, setCommentCount] = useState(0);
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const menuOpen = Boolean(menuAnchorEl);
  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);

  // Load comment count when component mounts
  useEffect(() => {
    const fetchCommentCount = async () => {
      try {
        const response = await getComments(task.id);
        setCommentCount(response.data?.length || 0);
      } catch (error) {
        console.error('Error fetching comment count:', error);
      }
    };
    fetchCommentCount();
  }, [task.id]);

  const handleAddComment = async (content) => {
    try {
      const response = await addComment(task.id, content);
      const newComment = response.data;
      setComments([...comments, newComment]);
      setCommentCount(prev => prev + 1);
      toast.success('Comment added successfully');
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    }
  };

  const handleUpdateComment = async (commentId, content) => {
    try {
      const response = await updateComment(commentId, content);
      setComments(comments.map(c => c.id === commentId ? response.data : c));
      toast.success('Comment updated successfully');
    } catch (error) {
      console.error('Error updating comment:', error);
      toast.error('Failed to update comment');
    }
  };

  const handleDeleteComment = async (commentId) => {
    try {
      await deleteComment(commentId);
      setComments(comments.filter(c => c.id !== commentId));
      setCommentCount(prev => prev - 1);
      toast.success('Comment deleted successfully');
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Failed to delete comment');
    }
  };

  // Menu handlers
  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  const handleEditTask = () => {
    if (onUpdateTask) {
      onUpdateTask(task);
    }
    handleMenuClose();
  };

  const handleDeleteTask = () => {
    if (onDeleteTask) {
      onDeleteTask(task);
    }
    handleMenuClose();
  };

  const handleAddNewSubtask = () => {
    if (onAddSubtask) {
      onAddSubtask(task);
    }
    handleMenuClose();
  };

  // Handle assigning members to a task
  const handleAssignMembers = async (selectedUserIds) => {
    try {
      // Convert IDs to numbers
      const numericIds = selectedUserIds.map(id => Number(id));

      const response = await assignMembersToTask(task.slug, numericIds);

      // Update local task state with new assignees from API response
      const updatedTask = {
        ...task,
        assignees: response.assignees || numericIds.map(id => ({
          id: id,
          first_name: '',
          last_name: '',
          email: ''
        }))
      };
      onUpdateTask(updatedTask);

      setAssignMemberDialogOpen(false);
    } catch (error) {
      console.error('Error assigning members:', error);
      toast.error('Failed to assign members');
    }
  };

  // Open the assign members dialog
  const handleOpenAssignMembersDialog = () => {
    setAssignMemberDialogOpen(true);
    handleMenuClose();
  };

  // Calculate progress
  const calculateProgress = () => {
    const subtasks = task.subtasks || [];
    if (subtasks.length === 0) {
      return task.status === STATUS.COMPLETED ? 100 :
        task.status === STATUS.IN_PROGRESS ? 50 : 0;
    }

    let totalProgress = 0;
    subtasks.forEach(subtask => {
      if (subtask.status === STATUS.COMPLETED || subtask.progress === 100) {
        totalProgress += 100;
      } else if (subtask.status === STATUS.IN_PROGRESS || subtask.progress > 0) {
        totalProgress += subtask.progress || 50;
      }
    });

    return Math.floor(totalProgress / subtasks.length);
  };

  const progress = calculateProgress();

  // Determine status
  const getStatus = () => {
    const subtasks = task.subtasks || [];
    if (subtasks.length === 0) {
      return STATUS_CONFIG[task.status || STATUS.NOT_STARTED];
    }

    if (progress === 100) {
      return STATUS_CONFIG[STATUS.COMPLETED];
    } else if (progress > 0) {
      return STATUS_CONFIG[STATUS.IN_PROGRESS];
    }
    return STATUS_CONFIG[STATUS.NOT_STARTED];
  };

  const status = getStatus();

  return (
    <Card
      elevation={0}
      sx={{
        borderRadius: '12px',
        border: '1px solid #f0f0f0',
        transition: 'all 0.2s ease',
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        '&:hover': {
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
          borderColor: '#e0e0e0'
        }
      }}
    >
      <CardContent sx={{ p: 0, flexGrow: 1 }}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, width: '100%' }}>
          {/* Left section - Task details */}
          <Box sx={{
            p: 3,
            flexGrow: 1,
            borderRight: { xs: 'none', md: '1px solid #f0f0f0' },
            borderBottom: { xs: '1px solid #f0f0f0', md: 'none' },
            width: { xs: '100%', md: '60%' }
          }}>
            {/* Task Header */}
            <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1.5, flexGrow: 1 }}>
                <Box
                  sx={{
                    width: 16,
                    height: 16,
                    borderRadius: '50%',
                    backgroundColor: status.color,
                    mt: 0.5,
                    flexShrink: 0
                  }}
                />
                <Box sx={{ flexGrow: 1 }}>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 600,
                      color: '#333',
                      mb: 1,
                      fontFamily: '"Recursive Variable", sans-serif',
                      lineHeight: 1.3,
                      wordWrap: 'break-word',
                      overflowWrap: 'break-word',
                      hyphens: 'auto',
                      display: '-webkit-box',
                      WebkitLineClamp: 3,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      maxHeight: '4.5em' // Approximately 3 lines
                    }}
                  >
                    {task.name}
                  </Typography>

                  {task.description && (
                    <Typography
                      variant="body2"
                      sx={{
                        color: '#555',
                        fontSize: '0.875rem',
                        mb: 2,
                        fontFamily: '"Recursive Variable", sans-serif',
                        lineHeight: 1.5
                      }}
                    >
                      {task.description}
                    </Typography>
                  )}
                </Box>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', ml: 1 }}>
                {/* Assignee Avatars Section */}
                {task.assignees && task.assignees.length > 0 && (
                  <AvatarGroup
                    max={3}
                    sx={{
                      mr: 1.5,
                      '& .MuiAvatar-root': {
                        width: 28,
                        height: 28,
                        fontSize: '0.75rem',
                        border: '2px solid #fff'
                      }
                    }}
                  >
                    {task.assignees.map((assignee, index) => (
                      <Tooltip
                        key={assignee.id || index}
                        title={assignee.first_name && assignee.last_name
                          ? `${assignee.first_name} ${assignee.last_name}`
                          : assignee.email || `Member ${index + 1}`}
                        arrow
                      >
                        <Avatar
                          src={assignee.avatar}
                          alt={assignee.first_name || `Member ${index + 1}`}
                          sx={{
                            bgcolor: mainYellowColor,
                          }}
                        >
                          {assignee.first_name
                            ? assignee.first_name.charAt(0).toUpperCase()
                            : assignee.email
                              ? assignee.email.charAt(0).toUpperCase()
                              : `M${index + 1}`}
                        </Avatar>
                      </Tooltip>
                    ))}
                  </AvatarGroup>
                )}

                <Tooltip title="Task Actions">
                  <IconButton
                    size="small"
                    onClick={handleMenuOpen}
                    sx={{
                      color: '#666',
                      padding: '8px',
                      borderRadius: '8px',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        color: '#333',
                        backgroundColor: 'rgba(240, 165, 0, 0.1)',
                        transform: 'scale(1.2)',
                        padding: '12px',
                        boxShadow: '0 4px 12px rgba(240, 165, 0, 0.2)'
                      }
                    }}
                  >
                    <Iconify icon="mdi:dots-vertical" width={20} height={20} />
                  </IconButton>
                </Tooltip>

                <Menu
                  anchorEl={menuAnchorEl}
                  open={menuOpen}
                  onClose={handleMenuClose}
                  transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                  anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                  PaperProps={{
                    sx: {
                      boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
                      borderRadius: '12px',
                      width: 220,
                      border: '1px solid #f0f0f0',
                      mt: 1
                    }
                  }}
                >
                  <MenuItem
                    onClick={handleEditTask}
                    sx={{
                      fontFamily: '"Recursive Variable", sans-serif',
                      py: 1.5,
                      '&:hover': {
                        backgroundColor: 'rgba(240, 165, 0, 0.08)',
                        '& .MuiListItemIcon-root': {
                          color: '#F0A500'
                        }
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Iconify icon="mdi:pencil" width={18} height={18} />
                    </ListItemIcon>
                    <ListItemText>Edit</ListItemText>
                  </MenuItem>

                  <MenuItem
                    onClick={handleAddNewSubtask}
                    sx={{
                      fontFamily: '"Recursive Variable", sans-serif',
                      py: 1.5,
                      '&:hover': {
                        backgroundColor: 'rgba(240, 165, 0, 0.08)',
                        '& .MuiListItemIcon-root': {
                          color: '#F0A500'
                        }
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Iconify icon="mdi:plus-circle" width={18} height={18} />
                    </ListItemIcon>
                    <ListItemText>Add Subtask</ListItemText>
                  </MenuItem>

                  <MenuItem
                    onClick={handleOpenAssignMembersDialog}
                    sx={{
                      fontFamily: '"Recursive Variable", sans-serif',
                      py: 1.5,
                      '&:hover': {
                        backgroundColor: 'rgba(240, 165, 0, 0.08)',
                        '& .MuiListItemIcon-root': {
                          color: '#F0A500'
                        }
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Iconify icon="mdi:account-multiple-plus" width={18} height={18} />
                    </ListItemIcon>
                    <ListItemText>Assign Members</ListItemText>
                  </MenuItem>

                  <Divider />

                  <MenuItem
                    onClick={handleDeleteTask}
                    sx={{
                      color: 'error.main',
                      fontFamily: '"Recursive Variable", sans-serif',
                      py: 1.5,
                      '&:hover': {
                        backgroundColor: 'rgba(244, 67, 54, 0.08)',
                        '& .MuiListItemIcon-root': {
                          color: '#f44336'
                        }
                      }
                    }}
                  >
                    <ListItemIcon sx={{ color: 'error.main' }}>
                      <Iconify icon="mdi:delete" width={18} height={18} />
                    </ListItemIcon>
                    <ListItemText>Delete Task</ListItemText>
                  </MenuItem>
                </Menu>
              </Box>
            </Box>

            {/* Task stats - Milestone, Date, Progress */}
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2 }}>
              {task.milestoneName && (
                <Chip
                  icon={<Iconify icon="mdi:flag" width={16} height={16} />}
                  label={task.milestoneName}
                  size="small"
                  sx={{
                    backgroundColor: '#f0f0f0',
                    '& .MuiChip-label': {
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontSize: '0.75rem'
                    }
                  }}
                />
              )}

              {task.due_date && (
                <Chip
                  icon={<Iconify icon="mdi:calendar" width={16} height={16} />}
                  label={dayjs(task.due_date).format('DD/MM/YYYY')}
                  size="small"
                  sx={{
                    backgroundColor: dayjs(task.due_date).isBefore(dayjs()) && task.status !== STATUS.COMPLETED
                      ? '#ffefef'
                      : '#f0f0f0',
                    color: dayjs(task.due_date).isBefore(dayjs()) && task.status !== STATUS.NOT_STARTED
                      ? '#d32f2f'
                      : 'inherit',
                    '& .MuiChip-label': {
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontSize: '0.75rem'
                    }
                  }}
                />
              )}

              <Chip
                icon={<Iconify icon={status.icon} width={16} height={16} />}
                label={status.label}
                size="small"
                sx={{
                  backgroundColor: `${status.color}20`,
                  color: status.color,
                  '& .MuiChip-label': {
                    fontFamily: '"Recursive Variable", sans-serif',
                    fontSize: '0.75rem'
                  }
                }}
              />
            </Box>

            {/* Display Assignees Detail Section */}
            {task.assignees && task.assignees.length > 0 && (
              <Box sx={{
                mb: 2,
                p: 1.5,
                backgroundColor: '#f9f9f9',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                flexWrap: 'wrap'
              }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: '#555',
                    fontFamily: '"Recursive Variable", sans-serif',
                    mr: 1.5,
                    fontWeight: 500
                  }}
                >
                  Assigned to:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {task.assignees.map((assignee, index) => (
                    <Chip
                      key={assignee.id || index}
                      avatar={<Avatar src={assignee.avatar} sx={{ bgcolor: mainYellowColor }}>
                        {assignee.first_name
                          ? assignee.first_name.charAt(0).toUpperCase()
                          : assignee.email
                            ? assignee.email.charAt(0).toUpperCase()
                            : `M${index + 1}`}
                      </Avatar>}
                      label={assignee.first_name && assignee.last_name
                        ? `${assignee.first_name} ${assignee.last_name}`
                        : assignee.email || `Member ${index + 1}`}
                      sx={{
                        bgcolor: 'white',
                        border: '1px solid #eeeeee',
                        '& .MuiChip-label': {
                          fontFamily: '"Recursive Variable", sans-serif',
                          fontSize: '0.75rem'
                        }
                      }}
                    />
                  ))}
                </Box>
                <Tooltip title="Assign Members">
                  <IconButton
                    size="small"
                    onClick={handleOpenAssignMembersDialog}
                    sx={{
                      ml: 'auto',
                      color: '#666',
                      '&:hover': {
                        color: mainYellowColor,
                        bgcolor: '#f5f5f5'
                      }
                    }}
                  >
                    <Iconify icon="mdi:account-edit" width={18} height={18} />
                  </IconButton>
                </Tooltip>
              </Box>
            )}

            {/* "Assign Members" button if no members are assigned */}
            {(!task.assignees || task.assignees.length === 0) && (
              <Button
                variant="outlined"
                startIcon={<Iconify icon="mdi:account-plus" width={18} height={18} />}
                onClick={handleOpenAssignMembersDialog}
                size="small"
                sx={{
                  mb: 2,
                  borderColor: '#e0e0e0',
                  color: '#666',
                  textTransform: 'none',
                  fontFamily: '"Recursive Variable", sans-serif',
                  fontSize: '0.8rem',
                  '&:hover': {
                    borderColor: mainYellowColor,
                    backgroundColor: '#fff9e6'
                  }
                }}
              >
                Assign Members
              </Button>
            )}

            {/* Progress bar */}
            <Box sx={{ mb: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                <Typography
                  variant="caption"
                  sx={{
                    color: '#666',
                    fontFamily: '"Recursive Variable", sans-serif'
                  }}
                >
                  Progress
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    fontWeight: 500,
                    color: '#333',
                    fontFamily: '"Recursive Variable", sans-serif'
                  }}
                >
                  {progress}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={progress}
                sx={{
                  height: 6,
                  borderRadius: 1,
                  backgroundColor: '#f0f0f0',
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: progress === 100 ? '#4caf50' : mainYellowColor
                  }
                }}
              />
            </Box>
          </Box>

          {/* Right section - Subtasks */}
          {task.subtasks && task.subtasks.length > 0 && (
            <Box sx={{
              p: 3,
              bgcolor: '#fafafa',
              width: { xs: '100%', md: '40%' },
              display: 'flex',
              flexDirection: 'column'
            }}>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 2, fontSize: '0.9rem', fontFamily: '"Recursive Variable", sans-serif', color: '#444' }}>
                Subtasks ({task.subtasks.length})
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5, flexGrow: 1, overflowY: 'auto' }}>
                {task.subtasks.map((subtask, index) => {
                  const subtaskStatus = STATUS_CONFIG[subtask.status || STATUS.NOT_STARTED];
                  return (
                    <Box
                      key={subtask.id || index}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        p: 1.5,
                        borderRadius: '8px',
                        backgroundColor: '#fff',
                        border: '1px solid #f0f0f0',
                        '&:hover': {
                          backgroundColor: '#f9f9f9'
                        }
                      }}
                    >
                      <Box
                        sx={{
                          width: 10,
                          height: 10,
                          borderRadius: '50%',
                          backgroundColor: subtaskStatus.color,
                          mr: 1.5
                        }}
                      />
                      <Typography variant="body2" sx={{
                        flexGrow: 1,
                        fontSize: '0.85rem',
                        fontFamily: '"Recursive Variable", sans-serif',
                        color: subtask.status === STATUS.COMPLETED ? '#4CAF50' : '#333',
                        fontWeight: subtask.status === STATUS.COMPLETED ? 500 : 400
                      }}>
                        {subtask.name}
                      </Typography>

                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 1 }}>
                        {subtask.progress !== null && subtask.progress !== undefined && (
                          <Typography variant="body2" sx={{ fontSize: '0.75rem', color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontWeight: 500 }}>
                            {subtask.progress}%
                          </Typography>
                        )}

                        <Chip
                          label={subtaskStatus.label}
                          size="small"
                          sx={{
                            height: '20px',
                            fontSize: '0.7rem',
                            backgroundColor: `${subtaskStatus.color}20`,
                            color: subtaskStatus.color,
                            fontWeight: 600,
                            fontFamily: '"Recursive Variable", sans-serif'
                          }}
                        />

                        {onUpdateSubtask && (
                          <Tooltip title="Edit Subtask">
                            <IconButton
                              size="small"
                              onClick={() => onUpdateSubtask(subtask)}
                              sx={{ p: 0.5 }}
                            >
                              <Iconify icon="eva:edit-fill" width={14} height={14} color="#999" />
                            </IconButton>
                          </Tooltip>
                        )}

                        {onDeleteSubtask && (
                          <Tooltip title="Delete Subtask">
                            <IconButton
                              size="small"
                              onClick={() => onDeleteSubtask(subtask)}
                              sx={{ p: 0.5 }}
                            >
                              <Iconify icon="eva:trash-2-outline" width={14} height={14} color="#f44336" />
                            </IconButton>
                          </Tooltip>
                        )}
                      </Box>
                    </Box>
                  );
                })}
              </Box>
            </Box>
          )}
        </Box>
      </CardContent>

      {/* Comment Dialog */}
      <CommentDialog
        open={commentDialogOpen}
        onClose={() => setCommentDialogOpen(false)}
        task={task}
        comments={comments}
        onAddComment={handleAddComment}
        onUpdateComment={handleUpdateComment}
        onDeleteComment={handleDeleteComment}
        loading={loadingComments}
      />

      {/* Assign Member Dialog */}
      <AssignMemberDialog
        open={assignMemberDialogOpen}
        onClose={() => setAssignMemberDialogOpen(false)}
        task={task}
        onAssignMembers={handleAssignMembers}
      />
    </Card>
  );
};

export default TaskWithSubtasks;
