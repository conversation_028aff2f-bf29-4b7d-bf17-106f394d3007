import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Avatar,
  Tooltip
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";

const AccessManagement = ({ 
  planInfo, 
  userAccessLevel,
  onAddAccess,
  onUpdateAccess,
  onRemoveAccess 
}) => {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newAccessLevel, setNewAccessLevel] = useState('viewer');
  const [loading, setLoading] = useState(false);

  // Use unified members data that includes both joined users and pending invites
  const allMembers = planInfo?.members || [];
  const isOwner = userAccessLevel?.access_level === 'owner';
  const isHeadOwner = userAccessLevel?.is_head_owner;

  const getAccessLevelColor = (level) => {
    switch (level) {
      case 'owner': return '#ef4444';
      case 'editor': return '#f97316';
      case 'viewer': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getAccessLevelIcon = (level) => {
    switch (level) {
      case 'owner': return 'material-symbols:admin-panel-settings';
      case 'editor': return 'material-symbols:edit';
      case 'viewer': return 'material-symbols:visibility';
      default: return 'material-symbols:person';
    }
  };

  const handleAddAccess = async () => {
    if (!newUserEmail.trim()) return;
    
    setLoading(true);
    try {
      await onAddAccess(newUserEmail, newAccessLevel);
      setNewUserEmail('');
      setNewAccessLevel('viewer');
      setAddDialogOpen(false);
    } catch (error) {
      console.error('Error adding access:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateAccess = async (accessId, newLevel, makeHeadOwner = false) => {
    try {
      await onUpdateAccess(accessId, newLevel, makeHeadOwner);
    } catch (error) {
      console.error('Error updating access:', error);
    }
  };

  const handleRemoveAccess = async (member) => {
    try {
      // Pass member data to parent for proper handling of both access removal and invite cancellation
      await onRemoveAccess(member);
    } catch (error) {
      console.error('Error removing access:', error);
    }
  };

  const canManageUser = (member) => {
    if (member.is_head_owner) return false; // Cannot manage head owner
    if (member.access_level === 'owner' && !isHeadOwner) return false; // Only head owner can manage owners
    return isOwner; // Owners can manage editors, viewers, and pending invites
  };

  return (
    <Paper 
      elevation={0} 
      sx={{ 
        p: 3, 
        borderRadius: '12px',
        border: '1px solid #f0f0f0',
        backgroundColor: '#fff'
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography 
          variant="h6" 
          sx={{ 
            fontWeight: 600, 
            color: '#333',
            fontFamily: '"Recursive Variable", sans-serif',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <Iconify icon="material-symbols:security" width={24} height={24} color={mainYellowColor} />
          Access Management
        </Typography>
        
        {isOwner && (
          <Button
            variant="contained"
            startIcon={<Iconify icon="material-symbols:person-add" />}
            onClick={() => setAddDialogOpen(true)}
            sx={{
              backgroundColor: mainYellowColor,
              color: '#fff',
              textTransform: 'none',
              fontFamily: '"Recursive Variable", sans-serif',
              '&:hover': {
                backgroundColor: '#e6940a'
              }
            }}
          >
            Add Member
          </Button>
        )}
      </Box>

      <List>
        {allMembers.map((member, index) => {
          const displayName = member.first_name && member.last_name
            ? `${member.first_name} ${member.last_name}`
            : member.email;

          const getStatusInfo = (status) => {
            switch (status) {
              case 'joined':
                return { label: 'Active', color: '#4CAF50', bgColor: '#4CAF5020' };
              case 'pending':
                return { label: 'Pending', color: '#FFC107', bgColor: '#FFC10720' };
              case 'rejected':
                return { label: 'Rejected', color: '#f44336', bgColor: '#f4433620' };
              default:
                return { label: 'Unknown', color: '#666', bgColor: '#66666620' };
            }
          };

          const statusInfo = getStatusInfo(member.status);

          return (
            <ListItem
              key={`member-${member.id || index}-${member.email}`}
              sx={{
                border: '1px solid #f0f0f0',
                borderRadius: '8px',
                mb: 1,
                backgroundColor: member.status === 'joined' ? '#fafafa' : '#f9f9f9',
                opacity: member.status === 'joined' ? 1 : 0.8
              }}
            >
              <Avatar
                src={member.avatar}
                alt={displayName}
                sx={{ mr: 2, bgcolor: mainYellowColor }}
              >
                {member.first_name
                  ? member.first_name.charAt(0).toUpperCase()
                  : member.email.charAt(0).toUpperCase()
                }
              </Avatar>
            
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography
                      variant="subtitle1"
                      sx={{
                        fontWeight: 600,
                        fontFamily: '"Recursive Variable", sans-serif'
                      }}
                    >
                      {displayName}
                    </Typography>
                    {member.is_head_owner && (
                      <Chip
                        label="Head Owner"
                        size="small"
                        sx={{
                          backgroundColor: '#fef3c7',
                          color: '#92400e',
                          fontWeight: 600,
                          fontSize: '0.7rem'
                        }}
                      />
                    )}
                  </Box>
                }
                secondary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                    <Chip
                      icon={<Iconify icon={getAccessLevelIcon(member.access_level)} width={16} height={16} />}
                      label={member.access_level.charAt(0).toUpperCase() + member.access_level.slice(1)}
                      size="small"
                      sx={{
                        backgroundColor: `${getAccessLevelColor(member.access_level)}20`,
                        color: getAccessLevelColor(member.access_level),
                        fontWeight: 600,
                        fontFamily: '"Recursive Variable", sans-serif'
                      }}
                    />
                    <Chip
                      label={statusInfo.label}
                      size="small"
                      sx={{
                        backgroundColor: statusInfo.bgColor,
                        color: statusInfo.color,
                        fontWeight: 600,
                        fontFamily: '"Recursive Variable", sans-serif'
                      }}
                    />
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#666',
                        fontFamily: '"Recursive Variable", sans-serif'
                      }}
                    >
                      {member.email}
                    </Typography>
                  </Box>
                }
              />
            
              <ListItemSecondaryAction>
                {canManageUser(member) && (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {member.access_level === 'owner' && isHeadOwner && member.status === 'joined' && (
                      <Tooltip title="Transfer Head Owner">
                        <IconButton
                          size="small"
                          onClick={() => handleUpdateAccess(member.id, 'owner', true)}
                          sx={{ color: '#f59e0b' }}
                        >
                          <Iconify icon="material-symbols:crown" width={20} height={20} />
                        </IconButton>
                      </Tooltip>
                    )}

                    <Tooltip title={member.status === 'pending' ? 'Cancel Invitation' : 'Remove Access'}>
                      <IconButton
                        size="small"
                        onClick={() => handleRemoveAccess(member)}
                        sx={{ color: '#ef4444' }}
                      >
                        <Iconify
                          icon={member.status === 'pending' ? 'material-symbols:cancel' : 'material-symbols:person-remove'}
                          width={20}
                          height={20}
                        />
                      </IconButton>
                    </Tooltip>
                  </Box>
                )}
              </ListItemSecondaryAction>
            </ListItem>
          );
        })}
      </List>

      {/* Add Access Dialog */}
      <Dialog 
        open={addDialogOpen} 
        onClose={() => setAddDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
          Add Team Member
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={newUserEmail}
            onChange={(e) => setNewUserEmail(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <FormControl fullWidth>
            <InputLabel>Access Level</InputLabel>
            <Select
              value={newAccessLevel}
              label="Access Level"
              onChange={(e) => setNewAccessLevel(e.target.value)}
            >
              <MenuItem value="viewer">Viewer - Can view plan content</MenuItem>
              <MenuItem value="editor">Editor - Can edit plan content</MenuItem>
              {isHeadOwner && (
                <MenuItem value="owner">Owner - Can manage team members</MenuItem>
              )}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setAddDialogOpen(false)}
            sx={{ fontFamily: '"Recursive Variable", sans-serif' }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleAddAccess}
            variant="contained"
            disabled={loading || !newUserEmail.trim()}
            sx={{
              backgroundColor: mainYellowColor,
              fontFamily: '"Recursive Variable", sans-serif',
              '&:hover': { backgroundColor: '#e6940a' }
            }}
          >
            {loading ? 'Adding...' : 'Add Member'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default AccessManagement;
