import CommentDialog from '../dialogs/CommentDialog';
import { getComments, addComment, updateComment, deleteComment } from '../../services';
import { Badge, Box, IconButton, Tooltip, CircularProgress, Typography } from '@mui/material';
import { toast } from 'react-toastify';
import { mainYellowColor } from 'helpers/constants';
import Iconify from 'components/Iconify';

const TaskItem = ({ task, onUpdateTask, onDeleteTask, ...props }) => {
  const [commentDialogOpen, setCommentDialogOpen] = useState(false);
  const [comments, setComments] = useState([]);
  const [loadingComments, setLoadingComments] = useState(false);
  const [commentCount, setCommentCount] = useState(0);

  // Load comment count when component mounts
  useEffect(() => {
    const fetchCommentCount = async () => {
      try {
        const response = await getComments(task.id);
        setCommentCount(response.data?.length || 0);
      } catch (error) {
        console.error('Error fetching comment count:', error);
      }
    };
    fetchCommentCount();
  }, [task.id]);

  const handleOpenComments = async () => {
    setCommentDialogOpen(true);
    setLoadingComments(true);
    try {
      const response = await getComments(task.id);
      setComments(response.data || []);
      setCommentCount(response.data?.length || 0);
    } catch (error) {
      console.error('Error fetching comments:', error);
      toast.error('Failed to load comments');
    } finally {
      setLoadingComments(false);
    }
  };

  const handleAddComment = async (content) => {
    try {
      const response = await addComment(task.id, content);
      const newComment = response.data;
      setComments([...comments, newComment]);
      setCommentCount(prev => prev + 1);
      toast.success('Comment added successfully');
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    }
  };

  const handleUpdateComment = async (commentId, content) => {
    try {
      const response = await updateComment(commentId, content);
      setComments(comments.map(c => c.id === commentId ? response.data : c));
      toast.success('Comment updated successfully');
    } catch (error) {
      console.error('Error updating comment:', error);
      toast.error('Failed to update comment');
    }
  };

  const handleDeleteComment = async (commentId) => {
    try {
      await deleteComment(commentId);
      setComments(comments.filter(c => c.id !== commentId));
      setCommentCount(prev => prev - 1);
      toast.success('Comment deleted successfully');
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Failed to delete comment');
    }
  };

  return (
    <Box sx={{ mb: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
          {task.name}
        </Typography>
        
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {/* Comment Button */}
          <Tooltip title={`${commentCount} Comments`}>
            <Badge 
              badgeContent={commentCount} 
              color="warning"
              sx={{
                '& .MuiBadge-badge': {
                  bgcolor: mainYellowColor,
                  color: 'white',
                }
              }}
            >
              <IconButton
                size="small"
                onClick={handleOpenComments}
                disabled={loadingComments}
                sx={{ 
                  color: mainYellowColor,
                  '&:hover': {
                    bgcolor: 'rgba(255, 193, 7, 0.08)'
                  }
                }}
              >
                {loadingComments ? (
                  <CircularProgress size={20} sx={{ color: mainYellowColor }} />
                ) : (
                  <Iconify icon="material-symbols:comment-outline" width={20} height={20} />
                )}
              </IconButton>
            </Badge>
          </Tooltip>

          {/* Edit Button */}
          <Tooltip title="Edit Task">
            <IconButton
              size="small"
              onClick={() => onUpdateTask(task)}
              sx={{ color: 'text.secondary' }}
            >
              <Iconify icon="eva:edit-fill" width={20} height={20} />
            </IconButton>
          </Tooltip>

          {/* Delete Button */}
          <Tooltip title="Delete Task">
            <IconButton
              size="small"
              onClick={() => onDeleteTask(task)}
              sx={{ color: 'error.main' }}
            >
              <Iconify icon="eva:trash-2-outline" width={20} height={20} />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Subtasks section */}
      {props.children}

      {/* Comment Dialog */}
      <CommentDialog
        open={commentDialogOpen}
        onClose={() => setCommentDialogOpen(false)}
        task={task}
        comments={comments}
        onAddComment={handleAddComment}
        onUpdateComment={handleUpdateComment}
        onDeleteComment={handleDeleteComment}
        loading={loadingComments}
      />
    </Box>
  );
}; 