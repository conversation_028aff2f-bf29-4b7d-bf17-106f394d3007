import React from 'react';
import { Box, Typography, Chip, Paper } from '@mui/material';
import { mainYellowColor } from 'helpers/constants';

const AgentExamples = ({ onExampleClick }) => {
  const examples = [
    {
      category: "📊 Project Analysis",
      items: [
        "Analyze my project progress",
        "What's the current status of my project?",
        "Show me project statistics",
        "Which milestones are behind schedule?",
        "What tasks need attention?"
      ]
    },
    {
      category: "🎯 Content Management", 
      items: [
        "Add a milestone for testing phase",
        "Create tasks for the setup milestone",
        "Add subtasks to break down the development task",
        "Update the milestone description",
        "Change task priority to high"
      ]
    },
    {
      category: "✅ Status Updates",
      items: [
        "Mark all setup tasks as completed",
        "Update task status to in progress",
        "Complete the first milestone",
        "Set task priority to high",
        "Mark subtask as done"
      ]
    },
    {
      category: "🔄 Improvements",
      items: [
        "Suggest improvements for my project",
        "What milestones need more tasks?",
        "Identify gaps in my project structure",
        "Recommend next steps",
        "Optimize my project timeline"
      ]
    },
    {
      category: "🗑️ Cleanup",
      items: [
        "Delete empty milestones",
        "Remove duplicate tasks",
        "Clean up incomplete subtasks",
        "Delete the testing milestone",
        "Remove outdated tasks"
      ]
    }
  ];

  return (
    <Box sx={{ p: 2 }}>
      <Typography
        variant="h6"
        sx={{
          fontFamily: '"Recursive Variable", sans-serif',
          fontWeight: 600,
          color: '#333',
          mb: 2
        }}
      >
        💡 What can I help you with?
      </Typography>
      
      {examples.map((category, categoryIndex) => (
        <Paper
          key={categoryIndex}
          elevation={1}
          sx={{
            p: 2,
            mb: 2,
            backgroundColor: '#fafafa',
            border: `1px solid ${mainYellowColor}20`
          }}
        >
          <Typography
            variant="subtitle2"
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600,
              color: '#333',
              mb: 1.5
            }}
          >
            {category.category}
          </Typography>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {category.items.map((example, index) => (
              <Chip
                key={index}
                label={example}
                variant="outlined"
                size="small"
                onClick={() => onExampleClick(example)}
                sx={{
                  backgroundColor: '#fff',
                  border: `1px solid ${mainYellowColor}40`,
                  color: '#666',
                  fontSize: '0.75rem',
                  height: '28px',
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: `${mainYellowColor}10`,
                    borderColor: mainYellowColor,
                    color: '#333'
                  }
                }}
              />
            ))}
          </Box>
        </Paper>
      ))}
      
      <Box
        sx={{
          mt: 3,
          p: 2,
          backgroundColor: `${mainYellowColor}10`,
          borderRadius: '8px',
          border: `1px solid ${mainYellowColor}30`
        }}
      >
        <Typography
          variant="body2"
          sx={{
            fontFamily: '"Recursive Variable", sans-serif',
            color: '#666',
            fontStyle: 'italic'
          }}
        >
          💬 <strong>Pro tip:</strong> You can ask me anything in natural language! 
          I understand context and can make direct changes to your project structure.
        </Typography>
      </Box>
    </Box>
  );
};

export default AgentExamples;
