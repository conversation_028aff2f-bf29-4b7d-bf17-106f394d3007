import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  TextField, 
  InputAdornment, 
  MenuItem, 
  Select, 
  FormControl, 
  InputLabel,
  Grid
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import TaskWithSubtasks from './TaskWithSubtasks';

const TaskList = ({ 
  milestones, 
  onUpdateTask, 
  onUpdateSubtask, 
  onAddSubtask, 
  onDeleteTask, 
  onDeleteSubtask 
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterMilestone, setFilterMilestone] = useState('all');

  // Lấy tất cả các task từ tất cả các milestone
  const getAllTasks = () => {
    let allTasks = [];
    milestones?.forEach(milestone => {
      if (milestone.tasks && milestone.tasks.length > 0) {
        // Thêm thông tin milestone vào mỗi task
        const tasksWithMilestone = milestone.tasks.map(task => ({
          ...task,
          milestoneName: milestone.name,
          milestoneId: milestone.id
        }));
        allTasks = [...allTasks, ...tasksWithMilestone];
      }
    });
    return allTasks;
  };

  const allTasks = getAllTasks();

  // Lọc task theo tìm kiếm và bộ lọc
  const filteredTasks = allTasks.filter(task => {
    // Lọc theo tìm kiếm
    const matchesSearch = searchTerm === '' || 
      task.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (task.description && task.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    // Lọc theo trạng thái
    const matchesStatus = filterStatus === 'all' || 
      (filterStatus === 'completed' && task.status === 3) ||
      (filterStatus === 'in-progress' && task.status === 2) ||
      (filterStatus === 'not-started' && task.status === 1);
    
    // Lọc theo milestone
    const matchesMilestone = filterMilestone === 'all' || 
      task.milestoneId.toString() === filterMilestone;
    
    return matchesSearch && matchesStatus && matchesMilestone;
  });

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography 
          variant="h6" 
          sx={{ 
            fontWeight: 600, 
            color: '#333', 
            fontFamily: '"Recursive Variable", sans-serif',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <Iconify icon="material-symbols:task" width={24} height={24} color={mainYellowColor} />
          All Tasks ({filteredTasks.length})
        </Typography>
      </Box>

      <Paper 
        elevation={0}
        sx={{ 
          p: 2, 
          mb: 3, 
          borderRadius: '12px',
          border: '1px solid #f0f0f0'
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search tasks..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="material-symbols:search" width={20} height={20} />
                  </InputAdornment>
                ),
                style: { fontFamily: '"Recursive Variable", sans-serif' }
              }}
              size="small"
              sx={{ 
                backgroundColor: '#f9f9f9', 
                borderRadius: '8px',
                '& .MuiInputBase-root': {
                  fontFamily: '"Recursive Variable", sans-serif'
                }
              }}
            />
          </Grid>
          <Grid item xs={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>Status</InputLabel>
              <Select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                label="Status"
                sx={{ fontFamily: '"Recursive Variable", sans-serif' }}
              >
                <MenuItem value="all" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>All statuses</MenuItem>
                <MenuItem value="completed" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>Completed</MenuItem>
                <MenuItem value="in-progress" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>In Progress</MenuItem>
                <MenuItem value="not-started" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>Not Started</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6} md={3}>
            <FormControl fullWidth size="small">
              <InputLabel sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>Milestone</InputLabel>
              <Select
                value={filterMilestone}
                onChange={(e) => setFilterMilestone(e.target.value)}
                label="Milestone"
                sx={{ fontFamily: '"Recursive Variable", sans-serif' }}
              >
                <MenuItem value="all" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>All milestones</MenuItem>
                {milestones?.map(milestone => (
                  <MenuItem key={milestone.id} value={milestone.id.toString()} sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
                    {milestone.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {filteredTasks.length === 0 ? (
        <Box 
          sx={{ 
            textAlign: 'center', 
            py: 4, 
            backgroundColor: '#f9f9f9',
            borderRadius: '12px',
            border: '1px dashed #ddd'
          }}
        >
          <Iconify 
            icon="material-symbols:search-off" 
            width={48} 
            height={48} 
            sx={{ color: '#999', mb: 2 }} 
          />
          <Typography variant="body1" sx={{ color: '#666', fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem' }}>
            No tasks match your search criteria
          </Typography>
        </Box>
      ) : (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {filteredTasks.map((task, index) => (
            <TaskWithSubtasks 
              key={index}
              task={task} 
              onUpdateTask={onUpdateTask}
              onUpdateSubtask={onUpdateSubtask}
              onAddSubtask={onAddSubtask}
              onDeleteTask={onDeleteTask}
              onDeleteSubtask={onDeleteSubtask}
            />
          ))}
        </Box>
      )}
    </Box>
  );
};

export default TaskList; 