import React from 'react';
import { Typography, Paper, Box } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from '../styles.module.scss';

const Description = ({ planInfo }) => {
  return (
    <Paper 
      elevation={0}
      className={styles.descriptionCard}
      sx={{ mb: 0.5, py: 1, px: 1.5 }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', mb: 1 }}>
        <Iconify icon="fluent:text-description-32-filled" width={20} height={20} color={mainYellowColor} />
        <Typography 
          variant="h6" 
          sx={{ 
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            fontSize: '1.2rem',
            color: '#555',
            margin: 0
          }}
        >
          Description
        </Typography>
      </Box>
      
      <Typography 
        variant="body1" 
        className={styles.descriptionText}
        sx={{ fontFamily: '"Recursive Variable", sans-serif', fontSize: '0.9rem', lineHeight: 1.4, color: '#666' }}
      >
        {planInfo?.description || 'No description available for this plan.'}
      </Typography>
    </Paper>
  );
};

export default Description; 