import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  Card,
  CardContent,
  Alert,
  Switch,
  FormControlLabel
} from '@mui/material';
// import { useSelector } from 'react-redux';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from 'helpers/constants';
import { APIURL, getHeaders } from 'helpers/utils';
import googleCalendarService from 'services/GoogleCalendarService';

dayjs.extend(isBetween);

const EnhancedTimeline = ({ planInfo, onTaskClick }) => {
  const [currentDate, setCurrentDate] = useState(dayjs());
  const [tasks, setTasks] = useState([]);
  // const [loading, setLoading] = useState(false);
  // const [selectedTask, setSelectedTask] = useState(null);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [showTimeSlotDialog, setShowTimeSlotDialog] = useState(false);
  // const [googleCalendarEvents, setGoogleCalendarEvents] = useState([]);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [timeSlots, setTimeSlots] = useState([]);
  const [calendarIntegration, setCalendarIntegration] = useState(false);
  // const [calendarStatus, setCalendarStatus] = useState({ initialized: false, signedIn: false });

  // const currentUser = useSelector((state) => state.user);

  // Get 7 days starting from current week
  const getDays = () => {
    const startOfWeek = currentDate.startOf('week');
    return Array.from({ length: 7 }, (_, i) => startOfWeek.add(i, 'day'));
  };

  // Initialize Google Calendar integration
  useEffect(() => {
    const initializeCalendar = async () => {
      await googleCalendarService.initialize();
      const status = googleCalendarService.getIntegrationStatus();
      // setCalendarStatus(status);
      setCalendarIntegration(status.signedIn);
    };

    initializeCalendar();
  }, []);

  // Fetch tasks from plan
  useEffect(() => {
    if (planInfo?.milestones) {
      const allTasks = [];
      planInfo.milestones.forEach(milestone => {
        if (milestone.tasks) {
          milestone.tasks.forEach(task => {
            allTasks.push({
              ...task,
              milestone_name: milestone.name,
              milestone_id: milestone.id
            });
          });
        }
      });
      setTasks(allTasks);
    }
  }, [planInfo]);

  // Get tasks for a specific day
  const getTasksForDay = (date) => {
    return tasks.filter(task => {
      const taskStart = task.start_date ? dayjs(task.start_date) : null;
      const taskEnd = task.end_date ? dayjs(task.end_date) : null;
      const taskDeadline = task.deadline ? dayjs(task.deadline) : null;

      return (taskStart && date.isSame(taskStart, 'day')) ||
             (taskEnd && date.isSame(taskEnd, 'day')) ||
             (taskDeadline && date.isSame(taskDeadline, 'day')) ||
             (taskStart && taskEnd && date.isBetween(taskStart, taskEnd, 'day', '[]'));
    });
  };

  // Get status info for task
  const getStatusInfo = (task) => {
    const statusMap = {
      1: { label: 'Pending', color: '#FFA726' },
      2: { label: 'In Progress', color: '#42A5F5' },
      3: { label: 'Completed', color: '#66BB6A' },
      4: { label: 'On Hold', color: '#EF5350' }
    };
    return statusMap[task.status] || statusMap[1];
  };

  // Check if task spans multiple days
  // const isMultiDayTask = (task) => {
  //   const taskStart = task.start_date ? dayjs(task.start_date) : null;
  //   const taskEnd = task.end_date ? dayjs(task.end_date) : null;
  //   return taskStart && taskEnd && !taskStart.isSame(taskEnd, 'day');
  // };

  // Get task position for multi-day tasks
  // const getTaskPosition = (task, currentDay) => {
  //   const taskStart = task.start_date ? dayjs(task.start_date) : null;
  //   const taskEnd = task.end_date ? dayjs(task.end_date) : null;
  //
  //   if (!taskStart || !taskEnd) return { isStartDay: true, isEndDay: true };
  //
  //   return {
  //     isStartDay: currentDay.isSame(taskStart, 'day'),
  //     isEndDay: currentDay.isSame(taskEnd, 'day')
  //   };
  // };

  // Navigation functions
  const goToPrevious = () => setCurrentDate(currentDate.subtract(7, 'day'));
  const goToNext = () => setCurrentDate(currentDate.add(7, 'day'));
  const goToToday = () => setCurrentDate(dayjs());

  // Handle task click
  const handleTaskClick = (task) => {
    // setSelectedTask(task);
    if (onTaskClick) {
      onTaskClick(task);
    }
  };

  // Handle empty time slot click for scheduling
  const handleTimeSlotClick = async (date) => {
    setSelectedDate(date);

    if (calendarIntegration) {
      // Get time slots from Google Calendar
      const slots = await googleCalendarService.getFreeTimeSlots(date);
      setTimeSlots(slots);
      setShowTimeSlotDialog(true);
    } else {
      // Show simple task scheduling dialog
      setShowScheduleDialog(true);
    }
  };

  // Handle Google Calendar integration toggle
  const handleCalendarToggle = async (enabled) => {
    if (enabled) {
      const success = await googleCalendarService.signIn();
      if (success) {
        setCalendarIntegration(true);
        // const status = googleCalendarService.getIntegrationStatus();
        // setCalendarStatus(status);
      }
    } else {
      await googleCalendarService.signOut();
      setCalendarIntegration(false);
      // const status = googleCalendarService.getIntegrationStatus();
      // setCalendarStatus(status);
    }
  };

  // Get available tasks for scheduling (tasks without dates)
  const getUnscheduledTasks = () => {
    return tasks.filter(task => !task.start_date && !task.end_date);
  };

  // Schedule task to selected time slot
  const scheduleTask = async (task, date, timeSlot = null) => {
    try {
      if (timeSlot && calendarIntegration) {
        // Schedule with specific time slot using Google Calendar integration
        await googleCalendarService.scheduleTaskToTimeSlot(task, date, timeSlot);
      } else {
        // Simple date scheduling
        const response = await fetch(`${APIURL}/api/tasks/${task.slug}/update`, {
          method: 'PUT',
          headers: {
            ...getHeaders(),
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            start_date: date.format('YYYY-MM-DD'),
            end_date: date.format('YYYY-MM-DD')
          })
        });

        if (!response.ok) {
          throw new Error('Failed to update task');
        }
      }

      // Update local state
      setTasks(prevTasks =>
        prevTasks.map(t =>
          t.id === task.id
            ? {
                ...t,
                start_date: date.format('YYYY-MM-DD'),
                end_date: date.format('YYYY-MM-DD'),
                start_time: timeSlot ? timeSlot.start : null,
                end_time: timeSlot ? timeSlot.end : null
              }
            : t
        )
      );

      setShowScheduleDialog(false);
      setShowTimeSlotDialog(false);
      setSelectedTimeSlot(null);
      setSelectedDate(null);
    } catch (error) {
      console.error('Error scheduling task:', error);
    }
  };

  return (
    <Box sx={{ width: '100%', height: '100%' }}>
      {/* Header */}
      <Box sx={{ 
        mb: 3, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center', 
        flexWrap: 'wrap', 
        gap: 2 
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography 
            variant="h6" 
            sx={{ 
              fontWeight: 600, 
              color: '#333', 
              fontFamily: '"Recursive Variable", sans-serif',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <Iconify icon="material-symbols:calendar-view-week" width={24} height={24} color={mainYellowColor} />
            Project Timeline
          </Typography>
          
          <Chip
            label={`${tasks.length} tasks`}
            size="small"
            sx={{
              backgroundColor: `${mainYellowColor}20`,
              color: mainYellowColor,
              fontWeight: 600
            }}
          />

          {/* Google Calendar Integration Toggle */}
          <FormControlLabel
            control={
              <Switch
                checked={calendarIntegration}
                onChange={(e) => handleCalendarToggle(e.target.checked)}
                size="small"
                sx={{
                  '& .MuiSwitch-switchBase.Mui-checked': {
                    color: mainYellowColor,
                  },
                  '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                    backgroundColor: mainYellowColor,
                  },
                }}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Iconify icon="logos:google-calendar" width={16} height={16} />
                <Typography variant="caption" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
                  Google Calendar
                </Typography>
              </Box>
            }
            sx={{ ml: 1 }}
          />
        </Box>

        {/* Navigation Controls */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="Previous week">
            <IconButton onClick={goToPrevious} size="small">
              <Iconify icon="material-symbols:chevron-left" width={20} height={20} />
            </IconButton>
          </Tooltip>
          
          <Button
            onClick={goToToday}
            variant="outlined"
            size="small"
            sx={{
              borderColor: mainYellowColor,
              color: mainYellowColor,
              '&:hover': {
                backgroundColor: `${mainYellowColor}10`,
                borderColor: mainYellowColor
              }
            }}
          >
            Today
          </Button>
          
          <Tooltip title="Next week">
            <IconButton onClick={goToNext} size="small">
              <Iconify icon="material-symbols:chevron-right" width={20} height={20} />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      {/* Week Display */}
      <Typography 
        variant="h6" 
        sx={{ 
          textAlign: 'center', 
          mb: 2, 
          fontFamily: '"Recursive Variable", sans-serif',
          color: '#666'
        }}
      >
        {getDays()[0].format('MMMM YYYY')} • Week of {getDays()[0].format('MMM D')}
      </Typography>

      {/* Calendar Grid */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Days Header */}
        <Grid container spacing={1} sx={{ mb: 1, backgroundColor: '#f5f7fa', p: 1, borderRadius: '12px' }}>
          {getDays().map((day) => (
            <Grid item xs key={day.format('YYYY-MM-DD')} sx={{ width: '100%' }}>
              <Paper
                elevation={0}
                sx={{
                  p: 1.5,
                  textAlign: 'center',
                  backgroundColor: day.isSame(dayjs(), 'day') ? `${mainYellowColor}15` : 'white',
                  borderRadius: '8px',
                  border: '1px solid #f0f0f0',
                  boxShadow: day.isSame(dayjs(), 'day') ? `0 0 0 1px ${mainYellowColor}40` : 'none'
                }}
              >
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: '#666',
                    fontFamily: '"Recursive Variable", sans-serif',
                    fontWeight: 600,
                    display: 'block'
                  }}
                >
                  {day.format('ddd')}
                </Typography>
                <Typography 
                  variant="h6" 
                  sx={{ 
                    fontWeight: day.isSame(dayjs(), 'day') ? 700 : 600,
                    color: day.isSame(dayjs(), 'day') ? mainYellowColor : '#333',
                    fontFamily: '"Recursive Variable", sans-serif'
                  }}
                >
                  {day.format('D')}
                </Typography>
              </Paper>
            </Grid>
          ))}
        </Grid>

        {/* Tasks Grid */}
        <Grid container spacing={1} sx={{ flexGrow: 1 }}>
          {getDays().map((day) => {
            const dayTasks = getTasksForDay(day);
            return (
              <Grid item xs key={day.format('YYYY-MM-DD')} sx={{ width: '100%' }}>
                <Paper
                  elevation={0}
                  onClick={() => handleTimeSlotClick(day)}
                  sx={{
                    p: 1,
                    height: '100%',
                    minHeight: '400px',
                    backgroundColor: day.isSame(dayjs(), 'day') ? '#fafafa' : '#fff',
                    borderRadius: '8px',
                    border: '1px solid #f0f0f0',
                    cursor: dayTasks.length === 0 ? 'pointer' : 'default',
                    '&:hover': {
                      backgroundColor: dayTasks.length === 0 ? '#f9f9f9' : undefined
                    }
                  }}
                >
                  {dayTasks.length === 0 ? (
                    <Box 
                      sx={{ 
                        height: '100%', 
                        display: 'flex', 
                        flexDirection: 'column',
                        alignItems: 'center', 
                        justifyContent: 'center',
                        color: '#999',
                        fontFamily: '"Recursive Variable", sans-serif',
                        fontSize: '0.8rem',
                        gap: 1
                      }}
                    >
                      <Iconify icon="material-symbols:add-circle-outline" width={24} height={24} />
                      <Typography variant="caption" sx={{ textAlign: 'center' }}>
                        Click to schedule tasks
                      </Typography>
                    </Box>
                  ) : (
                    <Stack spacing={1}>
                      {dayTasks.map((task) => {
                        const statusInfo = getStatusInfo(task);
                        // const isMultiDay = isMultiDayTask(task);
                        // const { isStartDay, isEndDay } = getTaskPosition(task, day);

                        return (
                          <Paper
                            key={task.id}
                            elevation={0}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleTaskClick(task);
                            }}
                            sx={{
                              p: 1,
                              backgroundColor: `${statusInfo.color}08`,
                              border: `1px solid ${statusInfo.color}20`,
                              borderRadius: '4px',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                backgroundColor: `${statusInfo.color}15`,
                                transform: 'translateY(-1px)',
                                boxShadow: `0 2px 8px ${statusInfo.color}20`
                              }
                            }}
                          >
                            <Typography 
                              variant="body2" 
                              sx={{ 
                                fontWeight: 600,
                                color: '#333',
                                fontFamily: '"Recursive Variable", sans-serif',
                                fontSize: '0.8rem',
                                lineHeight: 1.2,
                                mb: 0.5
                              }}
                            >
                              {task.name}
                            </Typography>
                            
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                              <Chip
                                label={statusInfo.label}
                                size="small"
                                sx={{
                                  backgroundColor: statusInfo.color,
                                  color: 'white',
                                  fontSize: '0.65rem',
                                  height: '18px',
                                  fontWeight: 600
                                }}
                              />
                              
                              <Typography 
                                variant="caption" 
                                sx={{ 
                                  color: '#666',
                                  fontSize: '0.7rem',
                                  fontFamily: '"Recursive Variable", sans-serif'
                                }}
                              >
                                {task.milestone_name}
                              </Typography>
                            </Box>
                          </Paper>
                        );
                      })}
                    </Stack>
                  )}
                </Paper>
              </Grid>
            );
          })}
        </Grid>
      </Box>

      {/* Simple Task Scheduling Dialog */}
      <Dialog
        open={showScheduleDialog}
        onClose={() => setShowScheduleDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Schedule Task for {selectedDate?.format('MMMM D, YYYY')}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
            Select a task to schedule for this date:
          </Typography>

          <Stack spacing={1}>
            {getUnscheduledTasks().map((task) => (
              <Card
                key={task.id}
                elevation={0}
                sx={{
                  border: '1px solid #f0f0f0',
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: '#f9f9f9'
                  }
                }}
                onClick={() => scheduleTask(task, selectedDate)}
              >
                <CardContent sx={{ p: 2 }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                    {task.name}
                  </Typography>
                  <Typography variant="caption" sx={{ color: '#666' }}>
                    {task.milestone_name}
                  </Typography>
                </CardContent>
              </Card>
            ))}

            {getUnscheduledTasks().length === 0 && (
              <Typography variant="body2" sx={{ color: '#999', textAlign: 'center', py: 2 }}>
                No unscheduled tasks available
              </Typography>
            )}
          </Stack>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowScheduleDialog(false)}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>

      {/* Google Calendar Time Slot Dialog */}
      <Dialog
        open={showTimeSlotDialog}
        onClose={() => setShowTimeSlotDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="logos:google-calendar" width={24} height={24} />
            Schedule Task for {selectedDate?.format('MMMM D, YYYY')}
          </Box>
        </DialogTitle>
        <DialogContent>
          {calendarIntegration && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Time slots are synchronized with your Google Calendar. Green slots are available, red slots have conflicts.
              </Typography>
            </Alert>
          )}

          <Grid container spacing={2}>
            {/* Available Time Slots */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                Available Time Slots
              </Typography>

              <Stack spacing={1} sx={{ maxHeight: '400px', overflow: 'auto' }}>
                {timeSlots.filter(slot => slot.available).map((slot, index) => (
                  <Card
                    key={index}
                    elevation={0}
                    sx={{
                      border: '1px solid #4caf50',
                      backgroundColor: '#f1f8e9',
                      cursor: 'pointer',
                      '&:hover': {
                        backgroundColor: '#e8f5e8'
                      }
                    }}
                    onClick={() => setSelectedTimeSlot(slot)}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Iconify icon="material-symbols:check-circle" width={20} height={20} color="#4caf50" />
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          {slot.start} - {slot.end}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                ))}

                {timeSlots.filter(slot => slot.available).length === 0 && (
                  <Typography variant="body2" sx={{ color: '#999', textAlign: 'center', py: 2 }}>
                    No available time slots
                  </Typography>
                )}
              </Stack>
            </Grid>

            {/* Unscheduled Tasks */}
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
                Tasks to Schedule
              </Typography>

              <Stack spacing={1} sx={{ maxHeight: '400px', overflow: 'auto' }}>
                {getUnscheduledTasks().map((task) => (
                  <Card
                    key={task.id}
                    elevation={0}
                    sx={{
                      border: selectedTimeSlot ? '1px solid #f0f0f0' : '1px solid #e0e0e0',
                      cursor: selectedTimeSlot ? 'pointer' : 'default',
                      opacity: selectedTimeSlot ? 1 : 0.7,
                      '&:hover': {
                        backgroundColor: selectedTimeSlot ? '#f9f9f9' : undefined
                      }
                    }}
                    onClick={() => selectedTimeSlot && scheduleTask(task, selectedDate, selectedTimeSlot)}
                  >
                    <CardContent sx={{ p: 2 }}>
                      <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                        {task.name}
                      </Typography>
                      <Typography variant="caption" sx={{ color: '#666' }}>
                        {task.milestone_name}
                      </Typography>
                    </CardContent>
                  </Card>
                ))}

                {getUnscheduledTasks().length === 0 && (
                  <Typography variant="body2" sx={{ color: '#999', textAlign: 'center', py: 2 }}>
                    No unscheduled tasks available
                  </Typography>
                )}
              </Stack>
            </Grid>
          </Grid>

          {selectedTimeSlot && (
            <Alert severity="success" sx={{ mt: 2 }}>
              <Typography variant="body2">
                Selected time slot: <strong>{selectedTimeSlot.start} - {selectedTimeSlot.end}</strong>
                <br />
                Click on a task above to schedule it for this time.
              </Typography>
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => {
            setShowTimeSlotDialog(false);
            setSelectedTimeSlot(null);
          }}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnhancedTimeline;
