/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Avatar,
  CircularProgress,
  Chip,
  Tooltip
} from '@mui/material';
import { useLocation } from 'react-router-dom';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import agentService from 'services/AgentService';
import { MarkdownText } from 'utils/markdownParser';

const AgentTab = ({ planInfo, onPlanUpdate }) => {
  const [conversations, setConversations] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const location = useLocation();

  // Load conversations and subscribe to updates
  useEffect(() => {
    if (!planInfo?.id) return;

    // Load initial conversations
    const initialConversations = agentService.getConversations(planInfo.id);
    setConversations(initialConversations);

    // Subscribe to conversation updates
    const unsubscribe = agentService.subscribe(planInfo.id, (updatedConversations) => {
      setConversations(updatedConversations);
    });

    // Check for pending message from ChatbotBar
    const pendingMessage = localStorage.getItem('pending_agent_message');
    if (pendingMessage) {
      try {
        const messageData = JSON.parse(pendingMessage);
        if (messageData.planInfo?.id === planInfo?.id) {
          handleSendMessage(messageData.message);
        }
        // Clear the pending message
        localStorage.removeItem('pending_agent_message');
      } catch (error) {
        console.error('Error processing pending message:', error);
        localStorage.removeItem('pending_agent_message');
      }
    }

    // If coming from chatbot bar via navigation state, add the initial message
    if (location.state?.message) {
      handleSendMessage(location.state.message);
    }

    // Cleanup subscription on unmount
    return unsubscribe;
  }, [planInfo?.id, location.state]);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversations]);

  // Get loading state from service
  const isLoading = agentService.getLoadingState(planInfo?.id);

  const handleSendMessage = async (messageText = currentMessage) => {
    if (!messageText.trim() || !planInfo?.id) return;

    // Check if request is already in progress
    if (agentService.isRequestInProgress(planInfo.id)) {
      console.log('Request already in progress');
      return;
    }

    setCurrentMessage('');

    // Use agent service for background processing
    const success = await agentService.sendMessage(
      planInfo.id,
      planInfo.slug,
      messageText.trim(),
      onPlanUpdate
    );

    if (!success) {
      console.error('Failed to send message');
    }
  };





  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '12px 12px 0 0',
          border: '1px solid #f0f0f0',
          borderBottom: 'none',
          backgroundColor: '#fafafa'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              backgroundColor: mainYellowColor,
              width: 40,
              height: 40
            }}
          >
            <Iconify icon="mdi:robot" width={24} height={24} color="#fff" />
          </Avatar>
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333'
              }}
            >
              AI Project Agent
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              Managing: {planInfo?.name}
            </Typography>
          </Box>
          <Chip
            label="Beta"
            size="small"
            sx={{
              backgroundColor: `${mainYellowColor}20`,
              color: mainYellowColor,
              fontWeight: 600,
              ml: 'auto'
            }}
          />
        </Box>
      </Paper>

      {/* Messages Area */}
      <Paper
        elevation={0}
        sx={{
          flex: 1,
          border: '1px solid #f0f0f0',
          borderTop: 'none',
          borderBottom: 'none',
          overflow: 'auto',
          p: 2,
          backgroundColor: '#fff',
          // Hide any potential dots or indicators
          '& *::after, & *::before': {
            display: 'none !important'
          },
          '& .MuiPagination-root, & .MuiPaginationItem-root': {
            display: 'none !important'
          },
          // Hide any dots that might be added by libraries
          '& [class*="dot"], & [class*="dots"], & [id*="dot"], & [id*="dots"]': {
            display: 'none !important'
          }
        }}
      >
        {conversations.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              textAlign: 'center'
            }}
          >
            <Avatar
              sx={{
                backgroundColor: `${mainYellowColor}20`,
                width: 60,
                height: 60,
                mb: 2
              }}
            >
              <Iconify icon="mdi:robot" width={32} height={32} color={mainYellowColor} />
            </Avatar>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333',
                mb: 1
              }}
            >
              🤖 AI Project Assistant
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif',
                maxWidth: 500,
                mb: 3
              }}
            >
              I'm your intelligent project management assistant! I can analyze your project, answer questions, and make direct changes to milestones, tasks, and subtasks. Just tell me what you need!
            </Typography>

            {/* Quick Action Suggestions */}
            <Box sx={{ mb: 3 }}>
              <Typography
                variant="subtitle2"
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  fontWeight: 600,
                  color: '#333',
                  mb: 1.5
                }}
              >
                💡 Try asking me:
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                {[
                  "📊 Analyze my project progress",
                  "🎯 What milestones need more tasks?",
                  "✅ Mark all setup tasks as completed",
                  "📝 Add a milestone for testing phase",
                  "🔄 Suggest improvements for my project",
                  "📈 Show me project statistics"
                ].map((suggestion, index) => (
                  <Chip
                    key={index}
                    label={suggestion}
                    variant="outlined"
                    size="small"
                    onClick={() => {
                      const cleanSuggestion = (suggestion || '').replace(/^[📊🎯✅📝🔄📈]\s/, '');
                      setCurrentMessage(cleanSuggestion);
                      if (inputRef.current) {
                        inputRef.current.focus();
                      }
                    }}
                    sx={{
                      justifyContent: 'flex-start',
                      backgroundColor: '#fff',
                      border: `1px solid ${mainYellowColor}40`,
                      color: '#666',
                      fontSize: '0.8rem',
                      height: '32px',
                      cursor: 'pointer',
                      '&:hover': {
                        backgroundColor: `${mainYellowColor}10`,
                        borderColor: mainYellowColor
                      }
                    }}
                  />
                ))}
              </Box>
            </Box>
          </Box>
        ) : (
          <Box>
            {conversations.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                  mb: 2
                }}
              >
                <Box
                  sx={{
                    maxWidth: '70%',
                    display: 'flex',
                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: 1
                  }}
                >
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor
                    }}
                  >
                    <Iconify
                      icon={message.type === 'user' ? "material-symbols:person" : "mdi:robot"}
                      width={18}
                      height={18}
                      color={message.type === 'user' ? '#666' : '#fff'}
                    />
                  </Avatar>
                  <Box>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        borderRadius: '12px',
                        backgroundColor: message.type === 'user' ? mainYellowColor :
                                       message.isError ? '#ffebee' :
                                       message.isSuccess ? '#e8f5e8' :
                                       message.isLoading ? '#f0f8ff' : '#f5f5f5',
                        color: message.type === 'user' ? '#fff' : '#333',
                        border: message.isError ? '1px solid #f44336' :
                               message.isSuccess ? '1px solid #4caf50' : 'none'
                      }}
                    >
                      {message.isLoading ? (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <CircularProgress size={16} className="visible-progress" sx={{ color: mainYellowColor }} />
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: '"Recursive Variable", sans-serif',
                              color: '#666'
                            }}
                          >
                            Thinking...
                          </Typography>
                        </Box>
                      ) : (
                        message.type === 'assistant' ? (
                          message.content && message.content.trim() ? (
                            <MarkdownText
                              sx={{
                                fontFamily: '"Recursive Variable", sans-serif',
                                lineHeight: 1.5,
                                fontSize: '0.875rem',
                                '& strong': {
                                  fontWeight: 600
                                },
                                '& em': {
                                  fontStyle: 'italic'
                                },
                                '& h1, & h2, & h3': {
                                  fontFamily: '"Recursive Variable", sans-serif',
                                  color: '#333'
                                },
                                '& ul': {
                                  margin: '8px 0',
                                  paddingLeft: '20px'
                                },
                                '& li': {
                                  margin: '4px 0'
                                },
                                '& code': {
                                  backgroundColor: '#f5f5f5',
                                  padding: '2px 4px',
                                  borderRadius: '3px',
                                  fontFamily: 'monospace',
                                  fontSize: '0.8rem'
                                },
                                // Hide any unwanted circular elements or buttons
                                '& button:empty, & div:empty, & span:empty': {
                                  display: 'none !important'
                                },
                                '& .MuiCircularProgress-root, & .MuiButton-root:empty, & .MuiChip-root:empty': {
                                  display: 'none !important'
                                },
                                // Hide any elements with no meaningful content
                                '& *:not(input):not(textarea):not(br):not(hr):not(img):not(svg):empty': {
                                  display: 'none !important'
                                }
                              }}
                            >
                              {message.content}
                            </MarkdownText>
                          ) : (
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: '"Recursive Variable", sans-serif',
                                lineHeight: 1.5,
                                fontStyle: 'italic',
                                color: '#999'
                              }}
                            >
                              No content to display
                            </Typography>
                          )
                        ) : (
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: '"Recursive Variable", sans-serif',
                              lineHeight: 1.5,
                              whiteSpace: 'pre-line'
                            }}
                          >
                            {message.content}
                          </Typography>
                        )
                      )}

                      {/* Quick Action Buttons for AI responses */}
                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (
                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {message.actions.slice(0, 3)
                            .filter(action => action && action.description && action.description.trim())
                            .map((action, actionIndex) => (
                            <Chip
                              key={actionIndex}
                              label={action.description}
                              size="small"
                              onClick={() => {
                                // Handle quick action
                                const actionDescription = action.description || 'perform action';
                                setCurrentMessage(action.originalMessage || `Please ${actionDescription.toLowerCase()}`);
                                if (inputRef.current) {
                                  inputRef.current.focus();
                                }
                              }}
                              sx={{
                                backgroundColor: '#fff',
                                border: `1px solid ${mainYellowColor}`,
                                color: mainYellowColor,
                                fontSize: '0.7rem',
                                height: '24px',
                                cursor: 'pointer',
                                '&:hover': {
                                  backgroundColor: `${mainYellowColor}10`
                                }
                              }}
                            />
                          ))}
                        </Box>
                      )}
                    </Paper>
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#999',
                        fontFamily: '"Recursive Variable", sans-serif',
                        fontSize: '0.7rem',
                        mt: 0.5,
                        display: 'block',
                        textAlign: message.type === 'user' ? 'right' : 'left'
                      }}
                    >
                      {formatTimestamp(message.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))}
            {/* Only show global loading if no individual messages are loading to prevent duplicate "Thinking..." */}
            {isLoading && !conversations.some(conv => conv.isLoading) && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: mainYellowColor
                    }}
                  >
                    <Iconify icon="mdi:robot" width={18} height={18} color="#fff" />
                  </Avatar>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 1.5,
                      borderRadius: '12px',
                      backgroundColor: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <CircularProgress size={16} className="visible-progress" sx={{ color: mainYellowColor }} />
                    <Typography
                      variant="body2"
                      sx={{
                        fontFamily: '"Recursive Variable", sans-serif',
                        color: '#666'
                      }}
                    >
                      Thinking...
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            )}
            <div ref={messagesEndRef} />
          </Box>
        )}
      </Paper>

      {/* Input Area */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '0 0 12px 12px',
          border: '1px solid #f0f0f0',
          borderTop: 'none',
          // Hide any potential dots, indicators, or circular elements
          '& *::after, & *::before': {
            display: 'none !important'
          },
          '& .MuiPagination-root, & .MuiPaginationItem-root': {
            display: 'none !important'
          },
          '& .MuiCircularProgress-root:not(.visible-progress)': {
            display: 'none !important'
          },
          '& button:empty, & div:empty:not(.spacer), & span:empty': {
            display: 'none !important'
          },
          '& .MuiChip-root:empty, & .MuiButton-root:empty': {
            display: 'none !important'
          },
          // Hide any elements with no text content
          '& *:not(input):not(textarea):not(br):not(hr):not(img):not(svg):not(.visible-progress):empty': {
            display: 'none !important'
          }
        }}
      >
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            inputRef={inputRef}
            value={currentMessage}
            onChange={(e) => setCurrentMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything! E.g., 'Add a milestone for testing' or 'What's my project progress?'"
            multiline
            maxRows={3}
            fullWidth
            variant="outlined"
            disabled={isLoading}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                fontFamily: '"Recursive Variable", sans-serif'
              }
            }}
          />
          <Tooltip title="Send message">
            <IconButton
              onClick={() => handleSendMessage()}
              disabled={!currentMessage.trim() || isLoading}
              sx={{
                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',
                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',
                '&:hover': {
                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'
                }
              }}
            >
              <Iconify icon="material-symbols:send" width={20} height={20} />
            </IconButton>
          </Tooltip>
        </Box>
      </Paper>
    </Box>
  );
};

export default AgentTab;
