import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Container, Box, Tabs, Tab, CircularProgress, Alert, Collapse } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor, APIURL } from "helpers/constants";
import { successSnackbar, errorSnackbar } from 'components/Snackbar/index';
import { toast } from 'react-toastify';
import { getHeaders } from "helpers/functions";

// Components
import Header from './components/Header';
import Description from './components/Description';
import Statistics from './components/Statistics';
import Progress from './components/Progress';
import Members from './components/Members';
import MilestoneList from './components/MilestoneList';

import AccessManagement from './components/AccessManagement';
import ChatbotBar from './components/ChatbotBar';
import AgentTab from './components/AgentTab';
import GanttChart from 'components/GanttChart/GanttChart';
import EnhancedTimeline from './components/EnhancedTimeline';

// Hooks
import usePlanData from './hooks/usePlanData';
import useViewMode from './hooks/useViewMode';

// Dialogs
import InviteDialog from './dialogs/InviteDialog';
import DeleteDialog from './dialogs/DeleteDialog';
import OptOutDialog from './dialogs/OptOutDialog';
import ConfirmDialog from './dialogs/ConfirmDialog';
import EditPlanNameDialog from './dialogs/EditPlanNameDialog';

// Services
import {
  updateMilestone,
  deleteMilestone,
  updateTask,
  updateSubtask,
  addTask,
  addSubtask,
  deleteTask,
  deleteSubtask,
  assignMembersToTask
} from '../services';

// Styles
import styles from './styles.module.scss';

const PlanDetail = () => {
  const { param } = useParams();
  const [activeTab, setActiveTab] = useState(() => {
    // Get active tab value from localStorage, default to 'overview' if not found
    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';
  });
  const [plan, setPlan] = useState(null);
  const [dialogState, setDialogState] = useState({
    invite: false,
    delete: false,
    optOut: false,
    deleteTask: false,
    deleteSubtask: false,
    editPlanName: false
  });
  const [selectedTaskToDelete, ] = useState(null);
  const [selectedSubtaskToDelete, ] = useState(null);
  const [invitationSuccess, setInvitationSuccess] = useState(null);
  const [refreshKey, setRefreshKey] = useState(0); // For forcing re-renders

  // Custom hooks
  const {
    planInfo,
    setPlanInfo,
    loading,
    error,
    invitedUsers,
    refreshPlanData,
    handleDeletePlan,
    handleOptOutPlan,
    handleUpdatePlanName,
    handleInviteUser,
    calculatePlanStats,
    calculateSubtaskProgress,
    getSubtaskStatus,
    calculateTaskProgress,
    getTaskStatus,
    calculateMilestoneProgress,
    getMilestoneStatus
  } = usePlanData(param);

  const {
    viewMode,
    handleViewModeChange
  } = useViewMode();

  // Update plan state when planInfo changes
  useEffect(() => {
    if (planInfo) {
      setPlan(planInfo);
    }
  }, [planInfo]);

  // Handle task highlighting from todo list navigation
  useEffect(() => {
    if (planInfo && param) {
      const targetTaskData = localStorage.getItem(`plan_${param}_targetTask`);
      console.log('Target task data from localStorage:', targetTaskData);
      if (targetTaskData) {
        try {
          const targetTask = JSON.parse(targetTaskData);
          console.log('Parsed target task:', targetTask);

          // Find the milestone containing the target task
          let targetMilestoneId = null;
          planInfo.milestones?.forEach(milestone => {
            if (milestone.tasks?.some(task => task.id === targetTask.taskId || task.slug === targetTask.taskSlug)) {
              targetMilestoneId = milestone.id || milestone.name;
            }
          });

          if (targetMilestoneId) {
            // Collapse all milestones except the target one
            planInfo.milestones?.forEach(milestone => {
              const milestoneKey = milestone.id || milestone.name;
              const shouldExpand = milestoneKey === targetMilestoneId;
              localStorage.setItem(`milestone_${milestoneKey}_expanded`, JSON.stringify(shouldExpand));
            });

            // Force a re-render by updating a state variable
            setRefreshKey(prev => prev + 1);

            // Use a more robust approach with MutationObserver to wait for the task element
            const findAndHighlightTask = () => {
              // Make sure we're on the milestones tab
              if (activeTab !== 'milestones') {
                console.log('Not on milestones tab yet, current tab:', activeTab);
                return false;
              }
              console.log('Looking for task with ID:', targetTask.taskId, 'or slug:', targetTask.taskSlug, 'name:', targetTask.taskName);

              // Try multiple selectors to find the task element
              let taskElement = document.querySelector(`[data-task-id="${targetTask.taskId}"]`);
              if (!taskElement) {
                taskElement = document.querySelector(`[data-task-slug="${targetTask.taskSlug}"]`);
              }

              // Fallback: try to find by task name text content
              if (!taskElement && targetTask.taskName) {
                const allTaskNameElements = document.querySelectorAll('[class*="taskName"]');
                for (const el of allTaskNameElements) {
                  if (el.textContent && el.textContent.trim() === targetTask.taskName.trim()) {
                    // Find the closest task container
                    taskElement = el.closest('[class*="taskItem"]') || el.closest('[data-task-id]') || el.closest('[data-task-slug]');
                    console.log('Found task by name:', taskElement);
                    break;
                  }
                }
              }

              console.log('Found task element:', taskElement);

              if (taskElement) {
                // Scroll to the task
                taskElement.scrollIntoView({ behavior: 'smooth', block: 'center' });

                console.log('Applying flash animation to task element');

                // Add 500ms delay before starting the flash animation
                setTimeout(() => {
                  console.log('Starting flash animation after delay');

                  // Create a faster, lighter flash animation using JavaScript
                  let opacity = 0.2;
                  let decreasing = false;

                  const flashInterval = setInterval(() => {
                    if (!decreasing) {
                      opacity += 0.15;
                      if (opacity >= 0.4) {
                        decreasing = true;
                      }
                    } else {
                      opacity -= 0.1;
                      if (opacity <= 0) {
                        clearInterval(flashInterval);
                        taskElement.style.removeProperty('background-color');
                        taskElement.style.removeProperty('box-shadow');
                        console.log('Flash animation completed');
                        return;
                      }
                    }

                    // Use a lighter yellow color (255, 235, 59 = light yellow)
                    taskElement.style.setProperty('background-color', `rgba(255, 235, 59, ${opacity})`, 'important');
                    taskElement.style.setProperty('box-shadow', `0 0 ${opacity * 15}px rgba(255, 235, 59, ${opacity * 0.8})`, 'important');
                  }, 50); // Faster interval: 50ms instead of 100ms

                }, 500); // 500ms delay before starting flash animation

                return true; // Found and highlighted
              }
              return false; // Not found
            };

            // Try to find the task immediately
            setTimeout(() => {
              if (!findAndHighlightTask()) {
                // If not found, set up a MutationObserver to watch for DOM changes
                console.log('Task not found immediately, setting up observer...');

                const observer = new MutationObserver((mutations) => {
                  if (findAndHighlightTask()) {
                    observer.disconnect(); // Stop observing once found
                  }
                });

                // Start observing
                observer.observe(document.body, {
                  childList: true,
                  subtree: true
                });

                // Stop observing after 5 seconds to prevent memory leaks
                setTimeout(() => {
                  observer.disconnect();
                  console.log('Task highlighting observer stopped after timeout');
                }, 5000);
              }
            }, 1000);

            // Clean up the localStorage after use
            localStorage.removeItem(`plan_${param}_targetTask`);
          }
        } catch (error) {
          console.error('Error parsing target task data:', error);
          localStorage.removeItem(`plan_${param}_targetTask`);
        }
      }
    }
  }, [planInfo, param, activeTab]);

  // Remove active tab from localStorage when component unmounts
  useEffect(() => {
    return () => {
      localStorage.removeItem(`plan_${param}_activeTab`);
    };
  }, [param]);

  // Calculate plan statistics
  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;

  // Debug plan owner data
  useEffect(() => {
    if (planInfo) {
      console.log('🔍 Plan Detail Debug:');
      console.log('  - planInfo.owner:', planInfo.owner);
      console.log('  - planInfo.user:', planInfo.user);
      console.log('  - planInfo.members:', planInfo.members);
      console.log('  - invitedUsers:', invitedUsers);
    }
  }, [planInfo, invitedUsers]);

  // Dialog handlers
  const openDialog = (dialogName) => {
    setDialogState(prev => ({ ...prev, [dialogName]: true }));
  };

  const closeDialog = (dialogName) => {
    setDialogState(prev => ({ ...prev, [dialogName]: false }));
  };

  // Wrapper for invite user with success message
  const handleInviteUserWithNotification = async (email, accessLevel) => {
    const result = await handleInviteUser(email, accessLevel);
    if (result) {
      // Set success message for display
      setInvitationSuccess(`🎉 Invitation sent successfully to ${email}! They'll receive an email with instructions to join the project.`);

      // Clear success message after 6 seconds
      setTimeout(() => {
        setInvitationSuccess(null);
      }, 6000);

      closeDialog('invite');
    }
    return result;
  };

  // Tab change handler
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
    // Save active tab to localStorage
    localStorage.setItem(`plan_${param}_activeTab`, newValue);
  };

  // Handle switching to Agent tab from ChatbotBar
  const handleSwitchToAgent = (conversationData) => {
    console.log('PlanDetail - Switching to agent tab with data:', conversationData); // Debug log
    setActiveTab('agent');
    localStorage.setItem(`plan_${param}_activeTab`, 'agent');
    // Store the conversation data for the Agent tab to pick up
    localStorage.setItem('pending_agent_message', JSON.stringify(conversationData));
  };

  // Access management handlers
  const handleAddAccess = async (email, accessLevel) => {
    try {
      const response = await fetch(`${APIURL}/api/plans/${param}/access`, {
        method: 'POST',
        headers: {
          ...getHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, access_level: accessLevel })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to add access');
      }

      // Refresh plan data
      await refreshPlanData();
      toast.success('Access granted successfully');
    } catch (error) {
      console.error('Error adding access:', error);
      toast.error(error.message || 'Failed to add access');
      throw error;
    }
  };

  const handleUpdateAccess = async (accessId, accessLevel, isHeadOwner = false) => {
    try {
      const response = await fetch(`${APIURL}/api/plans/${param}/access/${accessId}`, {
        method: 'PUT',
        headers: {
          ...getHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          access_level: accessLevel,
          is_head_owner: isHeadOwner
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update access');
      }

      // Refresh plan data
      await refreshPlanData();
      toast.success('Access updated successfully');
    } catch (error) {
      console.error('Error updating access:', error);
      toast.error(error.message || 'Failed to update access');
      throw error;
    }
  };

  const handleRemoveAccess = async (member) => {
    try {
      let response;
      let successMessage;

      if (member.status === 'pending') {
        // Cancel invitation for pending users
        console.log('Canceling invitation for:', member.email);

        // Find the invitation to cancel
        // We'll need to call a cancel invitation endpoint
        response = await fetch(`${APIURL}/api/plans/${param}/invitations/cancel`, {
          method: 'POST',
          headers: {
            ...getHeaders(),
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email: member.email })
        });

        successMessage = 'Invitation canceled successfully';
      } else {
        // Remove access for joined users
        console.log('Removing access for member ID:', member.id);

        response = await fetch(`${APIURL}/api/plans/${param}/access/${member.id}`, {
          method: 'DELETE',
          headers: getHeaders()
        });

        successMessage = 'Access removed successfully';
      }

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || error.message || 'Failed to remove access');
      }

      // Refresh plan data
      await refreshPlanData();
      toast.success(successMessage);
    } catch (error) {
      console.error('Error removing access:', error);
      toast.error(error.message || 'Failed to remove access');
      throw error;
    }
  };

  // Handle milestone update
  const handleUpdateMilestone = async (updatedMilestone) => {
    try {
      console.log('Updating milestone:', updatedMilestone);
      await updateMilestone(updatedMilestone);

      // Update local state
      const updatedPlan = { ...plan };
      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);

      if (milestoneIndex !== -1) {
        updatedPlan.milestones[milestoneIndex] = {
          ...updatedPlan.milestones[milestoneIndex],
          ...updatedMilestone
        };
        setPlan(updatedPlan);
      }

      successSnackbar('Milestone updated successfully');
    } catch (error) {
      console.error('Error updating milestone:', error);
      errorSnackbar('Failed to update milestone');
    }
  };

  // Handle delete milestone
  const handleDeleteMilestone = async (milestoneToDelete) => {
    try {
      console.log('Deleting milestone:', milestoneToDelete);
      await deleteMilestone(milestoneToDelete);

      // Update local state by removing the milestone
      const updatedPlan = { ...plan };
      updatedPlan.milestones = updatedPlan.milestones.filter(m => m.id !== milestoneToDelete.id);
      setPlan(updatedPlan);

      // Also update planInfo if it exists
      if (planInfo) {
        const updatedPlanInfo = { ...planInfo };
        updatedPlanInfo.milestones = updatedPlanInfo.milestones.filter(m => m.id !== milestoneToDelete.id);
        setPlanInfo(updatedPlanInfo);
      }

      successSnackbar(`Milestone "${milestoneToDelete.name}" deleted successfully`);
    } catch (error) {
      console.error('Error deleting milestone:', error);
      errorSnackbar('Failed to delete milestone');
    }
  };

  // Handle task update
  const handleUpdateTask = async (updatedTask) => {
    try {
      console.log('Updating task:', updatedTask);
      await updateTask(updatedTask);

      // Update local state
      const updatedPlan = { ...plan };
      const milestoneIndex = updatedPlan.milestones.findIndex(m =>
        m.tasks && m.tasks.some(t => t.id === updatedTask.id)
      );

      if (milestoneIndex !== -1) {
        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);

        if (taskIndex !== -1) {
          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {
            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],
            ...updatedTask
          };
          setPlan(updatedPlan);
        }
      }

      successSnackbar('Task updated successfully');
    } catch (error) {
      console.error('Error updating task:', error);

      // Check if it's a sequential completion error
      if (error.response?.data?.message?.includes('Sequential task completion')) {
        errorSnackbar(error.response.data.message);
      } else if (error.message?.includes('Sequential task completion')) {
        errorSnackbar(error.message);
      } else {
        errorSnackbar('Failed to update task');
      }
    }
  };

  // Handle subtask update
  const handleUpdateSubtask = async (updatedSubtask) => {
    try {
      console.log('Updating subtask:', updatedSubtask);

      // Optimistic update: Update local state immediately for better UX
      const updatedPlan = { ...planInfo };
      let taskToUpdate = null;

      // Find and update subtask in local state
      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];
          if (!task.subtasks) continue;

          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);
          if (subtaskIndex !== -1) {
            // Update subtask in local state
            task.subtasks[subtaskIndex] = {
              ...task.subtasks[subtaskIndex],
              ...updatedSubtask
            };
            taskToUpdate = task;
            break;
          }
        }
        if (taskToUpdate) break;
      }

      // Update local state immediately (optimistic update)
      if (taskToUpdate) {
        // Calculate new task status and progress locally
        const subtasks = taskToUpdate.subtasks || [];
        const completedSubtasks = subtasks.filter(s => s.status === 3).length; // status=3 is DONE
        const totalSubtasks = subtasks.length;

        if (totalSubtasks > 0) {
          const progress = Math.floor((completedSubtasks / totalSubtasks) * 100);
          let newStatus = 1; // TODO

          if (completedSubtasks === 0) {
            newStatus = 1; // TODO
          } else if (completedSubtasks === totalSubtasks) {
            newStatus = 3; // DONE
          } else {
            newStatus = 2; // IN_PROGRESS
          }

          // Update task in local state
          taskToUpdate.status = newStatus;
          taskToUpdate.progress = progress;
        }

        // Update local state without full refresh
        setPlanInfo(updatedPlan);
      }

      // Call API to update subtask (backend will also update task automatically)
      await updateSubtask(updatedSubtask);

      successSnackbar('Subtask updated successfully');
    } catch (error) {
      console.error('Error updating subtask:', error);

      // Check if it's a sequential completion error
      if (error.response?.data?.message?.includes('Sequential task completion')) {
        errorSnackbar(error.response.data.message);
      } else if (error.message?.includes('Sequential task completion')) {
        errorSnackbar(error.message);
      } else {
        errorSnackbar('Failed to update subtask');
      }

      // On error, refresh data to get correct state
      await refreshPlanData();
    }
  };

  // Handle add task
  const handleAddTask = async (newTask) => {
    try {
      console.log('Adding new task:', newTask);
      const response = await addTask(newTask);
      console.log('Add task response:', response);

      // Update local state
      const updatedPlan = { ...plan };

      // Find milestone to add new task
      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);

      if (milestoneIndex !== -1) {
        // Add new task to milestone
        if (!updatedPlan.milestones[milestoneIndex].tasks) {
          updatedPlan.milestones[milestoneIndex].tasks = [];
        }

        // Add the new task with data from response
        const taskToAdd = response.data || {
          ...newTask,
          id: Date.now(), // Temporary ID if response doesn't provide one
          subtasks: []
        };

        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);
        setPlan(updatedPlan);
      }

      successSnackbar('Task added successfully');
    } catch (error) {
      console.error('Error adding task:', error);
      errorSnackbar('Failed to add task');
    }
  };

  // Handle add subtask
  const handleAddSubtask = async (newSubtask) => {
    try {
      console.log('Adding new subtask:', newSubtask);
      const response = await addSubtask(newSubtask);
      console.log('Add subtask response:', response);

      // Update local state
      const updatedPlan = { ...plan };

      // Find task to add new subtask
      let taskFound = false;

      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];

          if (task.slug === newSubtask.task) {
            // Add new subtask to task
            if (!task.subtasks) {
              task.subtasks = [];
            }

            // Add the new subtask with data from response
            const subtaskToAdd = response.data || {
              ...newSubtask,
              id: Date.now() // Temporary ID if response doesn't provide one
            };

            task.subtasks.push(subtaskToAdd);
            taskFound = true;
            break;
          }
        }

        if (taskFound) break;
      }

      setPlan(updatedPlan);
      successSnackbar('Subtask added successfully');
    } catch (error) {
      console.error('Error adding subtask:', error);
      errorSnackbar('Failed to add subtask');
    }
  };

  // Handle delete task
  const handleDeleteTask = async (taskToDelete) => {
    try {
      console.log('Deleting task:', taskToDelete);
      await deleteTask(taskToDelete.slug);

      // Update local state
      const updatedPlan = { ...plan };

      // Find milestone containing the task to delete
      const milestoneIndex = updatedPlan.milestones.findIndex(m =>
        m.tasks && m.tasks.some(t => t.id === taskToDelete.id)
      );

      if (milestoneIndex !== -1) {
        // Filter out the task to delete
        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(
          t => t.id !== taskToDelete.id
        );

        setPlan(updatedPlan);
      }

      successSnackbar('Task deleted successfully');
    } catch (error) {
      console.error('Error deleting task:', error);
      errorSnackbar('Failed to delete task');
    }
  };

  // Handle delete subtask
  const handleDeleteSubtask = async (subtaskToDelete) => {
    try {
      console.log('Deleting subtask:', subtaskToDelete);

      // Optimistic update: Remove subtask from local state immediately
      const updatedPlan = { ...planInfo };
      let taskToUpdate = null;

      // Find and remove subtask from local state
      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];
          if (!task.subtasks) continue;

          const originalLength = task.subtasks.length;
          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);

          if (task.subtasks.length < originalLength) {
            taskToUpdate = task;
            break;
          }
        }
        if (taskToUpdate) break;
      }

      // Update task status and progress after subtask removal
      if (taskToUpdate) {
        const subtasks = taskToUpdate.subtasks || [];
        const completedSubtasks = subtasks.filter(s => s.status === 3).length;
        const totalSubtasks = subtasks.length;

        if (totalSubtasks === 0) {
          // No subtasks left, keep current task status
          taskToUpdate.progress = taskToUpdate.status === 3 ? 100 : 0;
        } else {
          const progress = Math.floor((completedSubtasks / totalSubtasks) * 100);
          let newStatus = 1; // TODO

          if (completedSubtasks === 0) {
            newStatus = 1; // TODO
          } else if (completedSubtasks === totalSubtasks) {
            newStatus = 3; // DONE
          } else {
            newStatus = 2; // IN_PROGRESS
          }

          taskToUpdate.status = newStatus;
          taskToUpdate.progress = progress;
        }

        // Update local state without full refresh
        setPlanInfo(updatedPlan);
      }

      // Call API to delete subtask
      await deleteSubtask(subtaskToDelete.slug);

      successSnackbar('Subtask deleted successfully');
    } catch (error) {
      console.error('Error deleting subtask:', error);
      errorSnackbar('Failed to delete subtask');

      // On error, refresh data to get correct state
      await refreshPlanData();
    }
  };

  // Handle assign members to task
  const handleAssignMembers = async (taskToAssign, memberIds) => {
    try {
      console.log('Assigning members to task:', taskToAssign, memberIds);
      await assignMembersToTask(taskToAssign.slug, memberIds);

      // Update local state
      const updatedPlan = { ...plan };

      // Find the task to update
      let taskFound = false;

      for (let i = 0; i < updatedPlan.milestones.length; i++) {
        const milestone = updatedPlan.milestones[i];
        if (!milestone.tasks) continue;

        for (let j = 0; j < milestone.tasks.length; j++) {
          const task = milestone.tasks[j];
          if (task.id === taskToAssign.id) {
            // Update assignees
            // Find user objects for the selected member IDs
            const assignedMembers = memberIds.map(memberId => {
              // Check if it's the plan owner
              if (planInfo.owner && planInfo.owner.id === memberId) {
                return {
                  id: planInfo.owner.id,
                  first_name: planInfo.owner.first_name,
                  last_name: planInfo.owner.last_name,
                  email: planInfo.owner.email,
                  avatar: planInfo.owner.avatar
                };
              }

              // Check in invited users
              const invitedUser = planInfo.invited_users?.find(
                user => user.invited_user_info && user.invited_user_info.id === memberId
              );

              if (invitedUser) {
                return {
                  id: invitedUser.invited_user_info.id,
                  first_name: invitedUser.invited_user_info.first_name,
                  last_name: invitedUser.invited_user_info.last_name,
                  email: invitedUser.email,
                  avatar: invitedUser.invited_user_info.avatar
                };
              }

              return null;
            }).filter(member => member !== null);

            task.assignees = assignedMembers;
            taskFound = true;
            break;
          }
        }

        if (taskFound) break;
      }

      setPlan(updatedPlan);
      toast.success('Members assigned successfully');
    } catch (error) {
      console.error('Error assigning members to task:', error);
      toast.error('Failed to assign members to task');
    }
  };

  if (error) {
    return (
      <Container className={styles.container}>
        <Alert severity="error" sx={{ mt: 4 }}>
          An error occurred while loading plan information. Please try again later.
        </Alert>
      </Container>
    );
  }

  return (
    <Container
      maxWidth={false}
      className={styles.container}
      sx={{
        padding: '20px',
        minHeight: 'calc(100vh - 65px)',
        fontFamily: '"Recursive Variable", sans-serif',
        width: '100%',
        maxWidth: '100% !important'
      }}
    >
      {/* Success notification for invitations */}
      <Collapse in={!!invitationSuccess}>
        <Alert
          severity="success"
          onClose={() => setInvitationSuccess(null)}
          sx={{
            mb: 2,
            backgroundColor: '#4caf50',
            color: 'white',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 600,
            '& .MuiAlert-icon': {
              color: 'white'
            },
            '& .MuiAlert-action': {
              color: 'white'
            }
          }}
        >
          {invitationSuccess}
        </Alert>
      </Collapse>

      {loading ? (
        <Box className={styles.loadingContainer}>
          <CircularProgress size={60} sx={{ color: mainYellowColor }} />
        </Box>
      ) : (
        <>
          {/* Header Section */}
          <Header
            planInfo={planInfo}
            viewMode={viewMode}
            onViewModeChange={handleViewModeChange}
            onOpenInviteDialog={() => openDialog('invite')}
            onOpenDeleteDialog={() => openDialog('delete')}
            onOpenOptOutDialog={() => openDialog('optOut')}
            onOpenEditPlanNameDialog={() => openDialog('editPlanName')}
          />

          {/* Tabs Navigation */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 0.5, mt: -1 }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                minHeight: '36px',
                '& .MuiTab-root': {
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.9rem',
                  minWidth: 'auto',
                  minHeight: '36px',
                  px: 2,
                  py: 0.5,
                  fontFamily: '"Recursive Variable", sans-serif'
                },
                '& .Mui-selected': {
                  color: `${mainYellowColor} !important`,
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: mainYellowColor,
                  height: '2px'
                }
              }}
            >
              <Tab
                icon={<Iconify icon="material-symbols:dashboard" width={16} height={16} />}
                iconPosition="start"
                label="Overview"
                value="overview"
                sx={{ gap: '4px' }}
              />
              <Tab
                icon={<Iconify icon="material-symbols:view-list" width={16} height={16} />}
                iconPosition="start"
                label="Project Details"
                value="milestones"
                sx={{ gap: '4px' }}
              />



              <Tab
                icon={<Iconify icon="material-symbols:calendar-month" width={16} height={16} />}
                iconPosition="start"
                label="Calendar"
                value="calendar"
                sx={{ gap: '4px' }}
              />
              <Tab
                icon={<Iconify icon="mdi:robot" width={16} height={16} />}
                iconPosition="start"
                label="Agent"
                value="agent"
                sx={{ gap: '4px' }}
              />
              {planInfo?.user_access_level?.access_level === 'owner' && (
                <Tab
                  icon={<Iconify icon="material-symbols:security" width={16} height={16} />}
                  iconPosition="start"
                  label="Access"
                  value="access"
                  sx={{ gap: '4px' }}
                />
              )}
            </Tabs>
          </Box>

          {/* Tab Content */}
          <Box className={styles.tabContent}>
            {activeTab === 'overview' && (
              <Box className={styles.overviewTab} sx={{ gap: 0.5 }}>
                <Description planInfo={planInfo} />
                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 1, mb: 0.5 }}>
                  <Statistics stats={stats} />
                  <Progress stats={stats} />
                </Box>

                {planInfo?.user_access_level?.access_level === 'owner' ? (
                  <AccessManagement
                    planInfo={planInfo}
                    userAccessLevel={planInfo?.user_access_level}
                    onAddAccess={handleAddAccess}
                    onUpdateAccess={handleUpdateAccess}
                    onRemoveAccess={handleRemoveAccess}
                  />
                ) : (
                  <Members
                    invitedUsers={invitedUsers}
                    planOwner={planInfo?.owner || planInfo?.user}
                    onInvite={() => openDialog('invite')}
                    planInfo={planInfo}
                  />
                )}

                {/* Gantt Chart Section */}
                <GanttChart
                  projectData={planInfo}
                  onTaskClick={(task) => {
                    console.log('Task clicked:', task);
                    // You can add task detail modal or navigation here
                  }}
                  calculateMilestoneProgress={calculateMilestoneProgress}
                  calculateTaskProgress={calculateTaskProgress}
                />
              </Box>
            )}



            {activeTab === 'milestones' && (
              <>
                <ChatbotBar
                  planInfo={planInfo}
                  onPlanUpdate={(updatedPlan) => {
                    // Handle plan updates from AI agent
                    console.log('Plan updated by AI:', updatedPlan);
                  }}
                  onSwitchToAgent={handleSwitchToAgent}
                />
                <MilestoneList
                  key={refreshKey} // Force re-render when refreshKey changes
                  milestones={planInfo?.milestones}
                  viewMode={viewMode}
                  compact={false}
                  showSubtasks={true}
                  calculateMilestoneProgress={calculateMilestoneProgress}
                  getMilestoneStatus={getMilestoneStatus}
                  calculateTaskProgress={calculateTaskProgress}
                  getTaskStatus={getTaskStatus}
                  calculateSubtaskProgress={calculateSubtaskProgress}
                  getSubtaskStatus={getSubtaskStatus}
                  onUpdateMilestone={handleUpdateMilestone}
                  onDeleteMilestone={handleDeleteMilestone}
                  onUpdateTask={handleUpdateTask}
                  onUpdateSubtask={handleUpdateSubtask}
                  onAddTask={handleAddTask}
                  onAddSubtask={handleAddSubtask}
                  onDeleteTask={handleDeleteTask}
                  onDeleteSubtask={handleDeleteSubtask}
                  onAssignMembers={handleAssignMembers}
                  invitedUsers={planInfo?.invited_users || []}
                  planOwner={planInfo?.owner || planInfo?.user}
                  userAccessLevel={planInfo?.user_access_level}
                />
              </>
            )}



            {activeTab === 'calendar' && (
              <Box sx={{ mt: 2 }}>
                <EnhancedTimeline
                  planInfo={planInfo}
                  onTaskClick={(task) => {
                    console.log('Calendar task clicked:', task);
                    // Navigate to task or show task details
                    const targetTaskData = {
                      taskId: task.id,
                      taskSlug: task.slug,
                      taskName: task.name
                    };
                    localStorage.setItem(`plan_${planInfo.slug}_targetTask`, JSON.stringify(targetTaskData));
                    setActiveTab('milestones');
                  }}
                />
              </Box>
            )}

            {activeTab === 'agent' && (
              <AgentTab
                planInfo={planInfo}
                onPlanUpdate={() => {
                  // Refresh plan data when AI agent makes changes
                  console.log('Plan updated by AI agent, refreshing data...');
                  refreshPlanData();
                }}
              />
            )}

            {activeTab === 'access' && planInfo?.user_access_level?.access_level === 'owner' && (
              <AccessManagement
                planInfo={planInfo}
                userAccessLevel={planInfo?.user_access_level}
                onAddAccess={handleAddAccess}
                onUpdateAccess={handleUpdateAccess}
                onRemoveAccess={handleRemoveAccess}
              />
            )}
          </Box>
        </>
      )}

      {/* Dialogs */}
      <InviteDialog
        open={dialogState.invite}
        onClose={() => closeDialog('invite')}
        onInvite={handleInviteUserWithNotification}
        planInfo={planInfo}
      />

      <DeleteDialog
        open={dialogState.delete}
        onClose={() => closeDialog('delete')}
        onDelete={handleDeletePlan}
      />

      <OptOutDialog
        open={dialogState.optOut}
        onClose={() => closeDialog('optOut')}
        onOptOut={handleOptOutPlan}
      />

      <ConfirmDialog
        open={dialogState.deleteTask}
        onClose={() => setDialogState(prev => ({ ...prev, deleteTask: false }))}
        onConfirm={() => handleDeleteTask(selectedTaskToDelete)}
        title="Delete Task"
        description={`Are you sure you want to delete the task "${selectedTaskToDelete?.name}"? This action cannot be undone.`}
      />

      <ConfirmDialog
        open={dialogState.deleteSubtask}
        onClose={() => setDialogState(prev => ({ ...prev, deleteSubtask: false }))}
        onConfirm={() => handleDeleteSubtask(selectedSubtaskToDelete)}
        title="Delete Subtask"
        description={`Are you sure you want to delete the subtask "${selectedSubtaskToDelete?.name}"? This action cannot be undone.`}
      />

      <EditPlanNameDialog
        open={dialogState.editPlanName}
        onClose={() => closeDialog('editPlanName')}
        onSave={handleUpdatePlanName}
        currentName={planInfo?.name}
      />
    </Container>
  );
};

export default PlanDetail;
