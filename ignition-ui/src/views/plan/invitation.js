/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { 
  Typography, 
  Box, 
  Container, 
  Card, 
  CardContent,
  Button,
  CircularProgress
} from "@mui/material";
import { APIURL, ACCEPT_STATUS, DECLINE_STATUS } from "helpers/constants";
import { getHeaders, removeCookies } from "helpers/functions";
import { clearUser } from '../../redux/userSlice';
import Iconify from 'components/Iconify/index';
import styles from '../auth/styles.module.scss';

//-------------------------------------------------------------------------------------------------

const AcceptInvitation = () => {
  const { param } = useParams();
  const [error, setError] = useState();
  const [loading, setLoading] = useState(true);
  const [invitation, setInvitation] = useState();
  const [isNotRegistered, setIsNotRegistered] = useState();
  const [invitationEmail, setinvitationEmail] = useState();
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleCheckInvitation = async () => {
    if (!param) {
      setError('The parameter for this invitation is missing');
      setLoading(false);
      return;
    }

    try {
      const formData = new FormData();
      formData.append("signed_id", param);

      const customHeader = getHeaders() ? { headers: getHeaders() } : {};
      const response = await axios.post(`${APIURL}/api/plan/check-invitation`, formData, customHeader);
      setInvitation(response.data.invitation);
    } catch (error) {
      if (error.response.data.is_not_registered) {
        setIsNotRegistered(true);
        setinvitationEmail(error.response.data.invitation_email);
      }
      setError(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateInvitationStatus = async (status) => {
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append("accepted", status);
      await axios.post(`${APIURL}/api/plan/update-invitation-status/${invitation.id}`,
        formData,
        { headers: getHeaders() }
      );
      navigate(`/d/plan/${invitation.plan_slug}`, { replace: true });
    } catch (error) {
      setError(error.response.data.message);
      setLoading(false);
    }
  };

  useEffect(() => {
    handleCheckInvitation();
  }, []);

  const handleLogout = () => {
    removeCookies('accessToken');
    removeCookies('refreshToken');
    dispatch(clearUser());
    navigate(`/register-by-invitation/${param}/${invitationEmail}`, { replace: true });
  };

  const handleDecline = () => {
    navigate(`/d/`, { replace: true });
  };

  return (
    <Container 
      maxWidth="md" 
      className={styles.loginContainer}
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh'
      }}
    >
      <Box sx={{ width: '100%', maxWidth: { md: '75%' }, mx: 'auto' }}>
        <Card className={styles.loginCard}>
          <Box className={styles.cardHeader}>
            <Box className={styles.headerContent}>
              <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
              <Box className={styles.headerTextContainer}>
                <Typography variant="h4" className={styles.headerTitle}>
                  Ignition
                </Typography>
                <Typography variant="body1" className={styles.headerSubtitle}>
                  {loading ? "Checking Invitation" : 
                   error ? "Invitation Error" : 
                   isNotRegistered ? "Registration Required" : "Plan Invitation"}
                </Typography>
              </Box>
            </Box>
          </Box>

          <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 }, textAlign: 'center' }}>
            <Box 
              sx={{ 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center', 
                justifyContent: 'center',
                py: 4
              }}
            >
              {loading ? (
                <>
                  <Box sx={{ mb: 3 }}>
                    <CircularProgress size={80} sx={{ color: '#F0A500' }} />
                  </Box>
                  <Typography 
                    variant="h5" 
                    className={`${styles.fontRecursive} ${styles.successTitle}`}
                    sx={{ mb: 2, fontWeight: 600 }}
                  >
                    Checking Invitation
                  </Typography>
                  <Typography 
                    variant="body1" 
                    className={`${styles.fontRecursive} ${styles.successMessage}`}
                    sx={{ fontSize: '1.1rem' }}
                  >
                    Please wait while we verify your invitation...
                  </Typography>
                </>
              ) : isNotRegistered ? (
                <>
                  <Box 
                    className={styles.successIconContainer}
                    sx={{ 
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      backgroundColor: '#ff9800',
                      border: '2px solid #ff9800',
                      boxShadow: '0 0 15px rgba(255, 152, 0, 0.5)',
                      margin: '0 auto'
                    }}
                  >
                    <Iconify 
                      icon="mdi:account-alert" 
                      width={60} 
                      height={60}
                      className={styles.successIcon}
                      sx={{ 
                        color: '#ffffff',
                      }} 
                    />
                  </Box>

                  <Typography 
                    variant="h5" 
                    className={`${styles.fontRecursive} ${styles.successTitle}`}
                    sx={{ mb: 2, fontWeight: 600, mt: 4 }}
                  >
                    Registration Required
                  </Typography>

                  <Typography 
                    variant="body1" 
                    className={`${styles.fontRecursive} ${styles.successMessage}`}
                    sx={{ fontSize: '1.1rem', mb: 4 }}
                  >
                    The email invited to this plan is not registered in Ignition.
                    <br />Do you want to create an account with this email?
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                    <Button 
                      variant="contained" 
                      className={styles.submitBtn}
                      onClick={handleLogout}
                    >
                      Create Account
                    </Button>
                    <Button 
                      variant="outlined" 
                      sx={{ 
                        backgroundColor: '#f5f5f5',
                        borderColor: '#e0e0e0', 
                        color: '#757575',
                        fontWeight: 500,
                        textTransform: 'none',
                        fontSize: '1rem',
                        padding: '6px 16px',
                        minWidth: '120px',
                        '&:hover': {
                          backgroundColor: '#eeeeee',
                          borderColor: '#bdbdbd'
                        }
                      }}
                      onClick={handleDecline}
                    >
                      Decline
                    </Button>
                  </Box>
                </>
              ) : error ? (
                <>
                  <Box 
                    className={styles.successIconContainer}
                    sx={{ 
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      backgroundColor: '#d32f2f',
                      border: '2px solid #d32f2f',
                      boxShadow: '0 0 15px rgba(211, 47, 47, 0.5)',
                      margin: '0 auto'
                    }}
                  >
                    <Iconify 
                      icon="mdi:alert" 
                      width={60} 
                      height={60}
                      className={styles.successIcon}
                      sx={{ 
                        color: '#ffffff',
                      }} 
                    />
                  </Box>
                  
                  <Typography 
                    variant="h5" 
                    className={`${styles.fontRecursive} ${styles.successTitle}`}
                    sx={{ mb: 2, fontWeight: 600 }}
                  >
                    Invitation Error
                  </Typography>
                  
                  <Typography 
                    variant="body1" 
                    className={`${styles.fontRecursive} ${styles.successMessage}`}
                    sx={{ fontSize: '1.1rem', mb: 4 }}
                  >
                    {error}
                  </Typography>
                  
                  <Button 
                    variant="contained" 
                    className={styles.submitBtn}
                    onClick={() => navigate('/d/')}
                  >
                    Go to Dashboard
                  </Button>
                </>
              ) : (
                <>
                  <Box 
                    className={styles.successIconContainer}
                    sx={{ 
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      backgroundColor: '#4caf50',
                      border: '2px solid #4caf50',
                      boxShadow: '0 0 15px rgba(76, 175, 80, 0.5)',
                      margin: '0 auto'
                    }}
                  >
                    <Iconify 
                      icon="mdi:email-check" 
                      width={60} 
                      height={60}
                      className={styles.successIcon}
                      sx={{ 
                        color: '#ffffff',
                      }} 
                    />
                  </Box>
                  
                  <Typography 
                    variant="h5" 
                    className={`${styles.fontRecursive} ${styles.successTitle}`}
                    sx={{ mb: 2, fontWeight: 600, mt: 4 }}
                  >
                    Plan Invitation
                  </Typography>
                  
                  <Typography 
                    variant="body1" 
                    className={`${styles.fontRecursive} ${styles.successMessage}`}
                    sx={{ fontSize: '1.1rem', mb: 1 }}
                  >
                    You have been invited to join the plan
                  </Typography>
                  
                  <Typography 
                    variant="h6" 
                    className={styles.fontRecursive}
                    sx={{ 
                      fontWeight: 700, 
                      color: '#F0A500', 
                      fontSize: '1.5rem',
                      mb: 1
                    }}
                  >
                    {invitation?.plan_name}
                  </Typography>
                  
                  <Typography 
                    variant="body1" 
                    className={`${styles.fontRecursive} ${styles.successMessage}`}
                    sx={{ fontSize: '1.1rem', mb: 1 }}
                  >
                    by
                  </Typography>
                  
                  <Typography 
                    variant="h6" 
                    className={styles.fontRecursive}
                    sx={{ 
                      fontWeight: 700, 
                      color: '#F0A500', 
                      fontSize: '1.5rem',
                      mb: 4
                    }}
                  >
                    {invitation?.invited_by}
                  </Typography>
                  
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                    <Button 
                      variant="contained" 
                      className={styles.submitBtn}
                      onClick={() => handleUpdateInvitationStatus(ACCEPT_STATUS)}
                    >
                      Accept
                    </Button>
                    <Button 
                      variant="outlined" 
                      sx={{ 
                        backgroundColor: '#f5f5f5',
                        borderColor: '#e0e0e0', 
                        color: '#757575',
                        fontWeight: 500,
                        textTransform: 'none',
                        fontSize: '1rem',
                        padding: '6px 16px',
                        minWidth: '120px',
                        '&:hover': {
                          backgroundColor: '#eeeeee',
                          borderColor: '#bdbdbd'
                        }
                      }}
                      onClick={() => handleUpdateInvitationStatus(DECLINE_STATUS)}
                    >
                      Decline
                    </Button>
                  </Box>
                </>
              )}
            </Box>
          </CardContent>
        </Card>
        
        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/d/" className={styles.customeALink}>
            <Iconify icon="mdi:view-dashboard" width={20} height={20} className={styles.linkIcon} />
            Back to Dashboard
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default AcceptInvitation;
