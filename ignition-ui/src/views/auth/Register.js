import React, { useState, useEffect } from "react";
import {
  InputAdornment,
  IconButton,
  Button,
  Typography,
  Box,
  Container,
  Card,
  CardContent,
  FormControl,
  CircularProgress
} from "@mui/material";
import { iconPrimaryColor, APIURL, mainYellowColor } from "helpers/constants";
import { errorSnackbar, successSnackbar } from 'components/Snackbar/index';
import Iconify from 'components/Iconify/index';
import InputBase from 'components/Input/InputBase';
import InputPasswordBase from 'components/Input/InputPasswordBase';
import PhoneNumberInput from 'components/Input/PhoneNumberInput';
import GoogleSignInButton from 'components/Button/GoogleSignInButton';
import axios from 'axios';
import { parsePhoneNumber } from 'libphonenumber-js';
import styles from './styles.module.scss';


const Register = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    phoneNumber: '',
  });
  const [isRegistered, setIsRegistered] = useState(false);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [showPass, setShowPass] = useState(false);
  const [showResendButton, setShowResendButton] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [registeredEmail, setRegisteredEmail] = useState('');

  // Show resend button after 1 minute and handle cooldown
  useEffect(() => {
    if (isRegistered) {
      // Show resend button after 1 minute (60 seconds)
      const timer = setTimeout(() => {
        setShowResendButton(true);
      }, 60000);

      return () => clearTimeout(timer);
    }
  }, [isRegistered]);

  // Handle resend cooldown countdown
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleChange = (field, value) => {
    setFormData((prevState) => ({
      ...prevState,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[field] || (field === 'phoneNumber' && errors.phone_number)) {
      const newErrors = { ...errors };
      delete newErrors[field];
      if (field === 'phoneNumber') {
        delete newErrors.phone_number;
      }
      setErrors(newErrors);
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = ['First name is required'];
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = ['Last name is required'];
    }

    if (!formData.email.trim()) {
      newErrors.email = ['Email is required'];
    }

    if (!formData.password.trim()) {
      newErrors.password = ['Password is required'];
    }

    if (!formData.phoneNumber.trim()) {
      newErrors.phone_number = ['Phone number is required'];
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    const { email, password, firstName, lastName, phoneNumber } = formData;

    // Validate required fields
    if (!firstName || !lastName || !email || !password) {
      errorSnackbar('Please fill in all required fields (marked with *)');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      errorSnackbar('Please enter a valid email address');
      return;
    }

    const registrationData = new FormData();
    registrationData.append('email', email);
    registrationData.append('password', password);
    registrationData.append('first_name', firstName);
    registrationData.append('last_name', lastName);

    // Handle phone number parsing (optional field)
    if (phoneNumber && phoneNumber.trim() && phoneNumber.length > 3) {
      try {
        const parsedPhone = parsePhoneNumber(phoneNumber);
        if (parsedPhone) {
          registrationData.append('phone_number', parsedPhone.nationalNumber);
          registrationData.append('country_code', `+${parsedPhone.countryCallingCode}`);
        }
      } catch (error) {
        console.warn('Phone number parsing error:', error);
        // If parsing fails, still send the raw phone number if it looks valid
        if (phoneNumber.replace(/\D/g, '').length >= 7) {
          registrationData.append('phone_number', phoneNumber);
        }
      }
    }

    setLoading(true);
    try {
      const response = await axios.post(`${APIURL}/api/user/create`, registrationData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      if (response.data.status === 201) {
        setRegisteredEmail(email); // Store the email for resend functionality
        setIsRegistered(true);
      }
    } catch (error) {
      console.error("error:: ", error);
      errorSnackbar(error?.response?.data?.message);
      setErrors(error.response.data || {});
    } finally {
      setLoading(false);
    }
  };

  const handleResendActivation = async () => {
    if (!registeredEmail) {
      errorSnackbar('Email address not found. Please try registering again.');
      return;
    }

    setResendLoading(true);
    try {
      const response = await axios.post(`${APIURL}/api/user/resend-activation`, {
        email: registeredEmail
      }, {
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.data.status === 200) {
        successSnackbar('Activation email has been resent! Please check your inbox.');
        setResendCooldown(60); // 60 second cooldown before next resend
      }
    } catch (error) {
      console.error("Resend error:", error);
      const errorMessage = error?.response?.data?.message || 'Failed to resend activation email. Please try again.';
      errorSnackbar(errorMessage);
    } finally {
      setResendLoading(false);
    }
  };

  const handleShowPass = () => {
    setShowPass(!showPass);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleRegister();
    }
  };

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        {!isRegistered ? (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box
                className={styles.headerContent}
                onClick={() => window.location.href = '/'}
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    opacity: 0.8,
                    transform: 'scale(1.02)',
                    transition: 'all 0.2s ease'
                  }
                }}
              >
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Create a new account
                  </Typography>
                </Box>
              </Box>
            </Box>

            <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 } }}>
              <Box className={styles.googleButton}>
                <GoogleSignInButton action="Sign up" />
              </Box>

              <div className={styles.divider}>
                <span>Or sign up with credentials</span>
              </div>

              <Box component="form" onKeyPress={handleKeyPress} noValidate>
                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="text"
                      handleChange={handleChange}
                      errorText={errors.firstName ? errors.firstName[0] : null}
                      placeholder="First Name *"
                      value={formData.firstName}
                      keyword="firstName"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:person" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="text"
                      handleChange={handleChange}
                      errorText={errors.lastName ? errors.lastName[0] : null}
                      placeholder="Last Name *"
                      value={formData.lastName}
                      keyword="lastName"
                      required
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:person" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputBase
                      type="email"
                      handleChange={handleChange}
                      errorText={errors.email ? errors.email[0] : null}
                      placeholder="Email"
                      value={formData.email}
                      keyword="email"
                      required={true}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="ic:baseline-email" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <PhoneNumberInput
                    value={formData.phoneNumber}
                    onChange={(value) => handleChange('phoneNumber', value)}
                    error={errors.phone_number ? errors.phone_number[0] : null}
                    placeholder="Phone Number *"
                    required
                  />
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth>
                  <div className={styles.inputField}>
                    <InputPasswordBase
                      showPass={showPass}
                      handleChange={handleChange}
                      errorText={errors.password ? errors.password[0] : null}
                      placeholder="Password"
                      value={formData.password}
                      name="password"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:lock" color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton onClick={handleShowPass} edge="end">
                              <Iconify icon={showPass ? 'mdi:eye-off' : 'mdi:eye'} color={iconPrimaryColor} />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                </FormControl>

                <Box display="flex" justifyContent="center" mt={4}>
                  <Button
                    onClick={handleRegister}
                    variant="contained"
                    className={styles.submitBtn}
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign up'}
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box
                className={styles.headerContent}
                onClick={() => window.location.href = '/'}
                sx={{
                  cursor: 'pointer',
                  '&:hover': {
                    opacity: 0.8,
                    transform: 'scale(1.02)',
                    transition: 'all 0.2s ease'
                  }
                }}
              >
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Registration Successful
                  </Typography>
                </Box>
              </Box>
            </Box>
            <CardContent sx={{
              pb: { xs: 3, lg: 5 },
              px: { xs: 5, lg: 7 },
              textAlign: 'center'
            }}>
              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ mb: 5, mt: 2 }}>
                  <Box
                    className={styles.successIconContainer}
                    sx={{
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      backgroundColor: '#4caf50',
                      border: '2px solid #4caf50',
                      boxShadow: '0 0 15px rgba(76, 175, 80, 0.5)',
                      margin: '0 auto'
                    }}
                  >
                    <Iconify
                      icon="mdi:check"
                      width={60}
                      height={60}
                      className={styles.successIcon}
                      sx={{
                        color: '#ffffff',
                      }}
                    />
                  </Box>
                </Box>
                <Typography
                  variant="h5"
                  className={styles.successTitle}
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: mainYellowColor,
                    fontFamily: "'Recursive Variable', sans-serif"
                  }}
                >
                  Registration Successful!
                </Typography>
                <Typography
                  variant="body1"
                  className={styles.successMessage}
                  sx={{
                    fontSize: '1.25rem',
                    color: mainYellowColor,
                    maxWidth: '100%',
                    mx: 'auto',
                    lineHeight: 1.6,
                    fontFamily: "'Recursive Variable', sans-serif",
                    mb: 3
                  }}
                >
                  We've sent you an activation email. Please check your inbox and click the activation link to complete your registration.
                </Typography>

                {/* Resend button appears after 1 minute */}
                {showResendButton && (
                  <Box sx={{ mt: 3, textAlign: 'center' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        mb: 2,
                        color: '#666',
                        fontFamily: "'Recursive Variable', sans-serif"
                      }}
                    >
                      Didn't receive the email? Check your spam folder or resend it.
                    </Typography>

                    <Button
                      onClick={handleResendActivation}
                      disabled={resendLoading || resendCooldown > 0}
                      variant="outlined"
                      sx={{
                        borderColor: mainYellowColor,
                        color: mainYellowColor,
                        fontFamily: "'Recursive Variable', sans-serif",
                        fontWeight: 'bold',
                        px: 4,
                        py: 1.5,
                        '&:hover': {
                          borderColor: mainYellowColor,
                          backgroundColor: `${mainYellowColor}15`,
                        },
                        '&:disabled': {
                          borderColor: '#ccc',
                          color: '#999',
                        }
                      }}
                    >
                      {resendLoading ? (
                        <>
                          <CircularProgress size={20} sx={{ mr: 1, color: mainYellowColor }} />
                          Sending...
                        </>
                      ) : resendCooldown > 0 ? (
                        `Resend in ${resendCooldown}s`
                      ) : (
                        'Resend Activation Email'
                      )}
                    </Button>
                  </Box>
                )}

                {!showResendButton && (
                  <Typography
                    variant="body2"
                    sx={{
                      mt: 3,
                      color: '#888',
                      fontFamily: "'Recursive Variable', sans-serif",
                      fontSize: '0.9rem'
                    }}
                  >
                    Resend option will be available in a moment...
                  </Typography>
                )}
              </Box>
            </CardContent>
          </Card>
        )}

        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/login" className={styles.customeALink}>
            <Iconify icon="mdi:login" width={20} height={20} className={styles.linkIcon} />
            Already have an account? Sign in
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default Register;
