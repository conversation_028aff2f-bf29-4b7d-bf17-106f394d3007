import React, { useState } from "react";
import {
  InputAdornment,
  IconButton,
  Button,
  Typography,
  Box,
  Container,
  Card,
  CardContent,
  FormControl,
  CircularProgress
} from "@mui/material";
import { useParams, useNavigate } from "react-router-dom";
import { mainYellowColor, APIURL, iconPrimaryColor } from "helpers/constants";
import axios from 'axios';
import styles from './styles.module.scss';
import InputPasswordBase from 'components/Input/InputPasswordBase';
import Iconify from 'components/Iconify/index';

const Reset = () => {
  const { uid, token } = useParams();
  const [password, setPassword] = useState('');
  const [passwordConfirm, setPasswordConfirm] = useState('');
  const [errors, setErrors] = useState({});
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPass, setShowPass] = useState(false);
  const [showPassConfirm, setShowPassConfirm] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleShowPass = () => {
    setShowPass(!showPass);
  };

  const handleShowPassConfirm = () => {
    setShowPassConfirm(!showPassConfirm);
  };

  const handleChange = (field, value) => {
    if (field === 'password') {
      setPassword(value);
    } else if (field === 'passwordConfirm') {
      setPasswordConfirm(value);
    }
  };

  const handleResetPassword = async () => {
    setLoading(true);
    try {
      await axios.post(`${APIURL}/api/user/password-reset/${uid}/${token}/`,
        { password, password_confirm: passwordConfirm },
        { headers: { 'Content-Type': 'application/json' } }
      );
      setIsSuccess(true);
      setTimeout(() => { navigate('/login', { replace: true }) }, 3000);
    } catch (error) {
      if (typeof (error?.response?.data?.message) === 'string') {
        setErrors({ password_confirm: [error.response.data.message] });
      } else {
        setErrors(error.response?.data.message || {});
      }
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleResetPassword();
    }
  };

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        {!isSuccess ? (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Reset Password
                  </Typography>
                </Box>
              </Box>
            </Box>

            <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 } }}>
              <div className={styles.divider}>
                <span>Enter your new password to reset</span>
              </div>

              <Box component="form" onKeyPress={handleKeyPress} noValidate>
                <FormControl className={styles.formGroup} fullWidth sx={{ mb: 3 }}>
                  <div className={styles.inputField}>
                    <InputPasswordBase
                      showPass={showPass}
                      handleChange={(field, value) => handleChange('password', value)}
                      errorText={errors.password}
                      placeholder="New Password"
                      value={password}
                      name="password"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:lock" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton onClick={handleShowPass} edge="end">
                              <Iconify icon={showPass ? 'mdi:eye-off' : 'mdi:eye'} color={iconPrimaryColor} />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.password && <div className={styles.errorMessage}>{errors.password[0]}</div>}
                </FormControl>

                <FormControl className={styles.formGroup} fullWidth>
                  <div className={styles.inputField}>
                    <InputPasswordBase
                      showPass={showPassConfirm}
                      handleChange={(field, value) => handleChange('passwordConfirm', value)}
                      errorText={errors.password_confirm}
                      placeholder="Confirm New Password"
                      value={passwordConfirm}
                      name="passwordConfirm"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="material-symbols:lock" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton onClick={handleShowPassConfirm} edge="end">
                              <Iconify icon={showPassConfirm ? 'mdi:eye-off' : 'mdi:eye'} color={iconPrimaryColor} />
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.password_confirm && <div className={styles.errorMessage}>{errors.password_confirm[0]}</div>}
                </FormControl>

                <Box display="flex" justifyContent="center" mt={4}>
                  <Button
                    onClick={handleResetPassword}
                    variant="contained"
                    className={styles.submitBtn}
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Reset Password'}
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Password Reset Complete
                  </Typography>
                </Box>
              </Box>
            </Box>
            <CardContent sx={{
              pb: { xs: 3, lg: 5 },
              px: { xs: 5, lg: 7 },
              textAlign: 'center'
            }}>
              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ mb: 5, mt: 2 }}>
                  <Box
                    className={styles.successIconContainer}
                    sx={{
                      width: 100,
                      height: 100,
                      borderRadius: '50%',
                      backgroundColor: '#2e7d32',
                      border: `3px solid #4caf50`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto',
                      position: 'relative',
                      boxShadow: `0 4px 20px rgba(46, 125, 50, 0.4)`,
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: -2,
                        left: -2,
                        right: -2,
                        bottom: -2,
                        borderRadius: '50%',
                        background: `linear-gradient(45deg, #4caf50, transparent, #4caf50, transparent)`,
                        opacity: 0.3,
                        zIndex: -1
                      }
                    }}
                  >
                    <Box
                      sx={{
                        width: 70,
                        height: 70,
                        borderRadius: '50%',
                        backgroundColor: 'rgba(255, 255, 255, 0.2)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                      }}
                    >
                      <Iconify
                        icon="mdi:lock-check"
                        width={50}
                        height={50}
                        className={styles.successIcon}
                        sx={{
                          color: '#ffffff',
                          filter: 'drop-shadow(0 0 5px rgba(255, 255, 255, 0.5))'
                        }}
                      />
                    </Box>
                  </Box>
                </Box>
                <Typography
                  variant="h5"
                  className={styles.successTitle}
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: mainYellowColor,
                    fontFamily: "'Recursive Variable', sans-serif"
                  }}
                >
                  Success message
                </Typography>
                <Typography
                  variant="body1"
                  className={styles.successMessage}
                  sx={{
                    fontSize: '1.25rem',
                    color: mainYellowColor,
                    maxWidth: '100%',
                    mx: 'auto',
                    lineHeight: 1.6,
                    fontFamily: "'Recursive Variable', sans-serif"
                  }}
                >
                  Your password has been reset successfully.
                  <br />
                  <span style={{ fontSize: '0.9rem', opacity: 0.8 }}>Redirecting to login page...</span>
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/login" className={styles.customeALink}>
            <Iconify icon="mdi:login" width={20} height={20} className={styles.linkIcon} />
            Back to login
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default Reset;
