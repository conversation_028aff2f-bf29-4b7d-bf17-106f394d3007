/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useState } from "react";
import axios from 'axios';
import {
  Typography,
  Box,
  Container,
  Card,
  CardContent
} from "@mui/material";
import { useParams, useNavigate } from 'react-router-dom';
import { APIURL } from "helpers/constants";
import Iconify from 'components/Iconify/index';
import styles from './styles.module.scss';

//--------------------------------------------------------------------------------------------------

const Activate = () => {
  const { param1, param2 } = useParams();
  const [respMessage, setRespMessage] = useState();
  const [isSuccess, setIsSuccess] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    handleActivate(param1, param2);
  }, [param1, param2]);

  const handleActivate = async (param1, param2) => {
    const formData = new FormData();
    formData.append('uid', param1);
    formData.append('token', param2)
    try {
      const response = await axios.post(`${APIURL}/api/user/activate`,
        formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
      if (response.data.status === 201) {
        setRespMessage(response.data.message);
        setIsSuccess(true);
        setTimeout(() => { navigate('/login', { replace: true }); }, 3000);
      }
    } catch (error) {
      setRespMessage(error.response.data);
      setIsSuccess(false);
    }
  };

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        <Card className={styles.loginCard}>
          <Box className={styles.cardHeader}>
            <Box
              className={styles.headerContent}
              onClick={() => window.location.href = '/'}
              sx={{
                cursor: 'pointer',
                '&:hover': {
                  opacity: 0.8,
                  transform: 'scale(1.02)',
                  transition: 'all 0.2s ease'
                }
              }}
            >
              <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
              <Box className={styles.headerTextContainer}>
                <Typography variant="h4" className={styles.headerTitle}>
                  Ignition
                </Typography>
                <Typography variant="body1" className={styles.headerSubtitle}>
                  Account Activation
                </Typography>
              </Box>
            </Box>
          </Box>

          <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 }, textAlign: 'center' }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                py: 4
              }}
            >
              <Box
                className={styles.successIconContainer}
                sx={{
                  mb: 3,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '100px',
                  height: '100px',
                  borderRadius: '50%',
                  backgroundColor: '#4caf50',
                  border: '2px solid #4caf50',
                  boxShadow: '0 0 15px rgba(76, 175, 80, 0.5)'
                }}
              >
                <Iconify
                  icon={isSuccess ? "mdi:check" : "mdi:alert-circle"}
                  width={60}
                  height={60}
                  className={styles.successIcon}
                  sx={{ 
                    color: isSuccess ? '#ffffff' : '#d32f2f',
                  }}
                />
              </Box>

              <Typography
                variant="h5"
                className={`${styles.fontRecursive} ${styles.successTitle}`}
                sx={{ mb: 2, fontWeight: 600 }}
              >
                {isSuccess ? 'Account Activated' : 'Activation Failed'}
              </Typography>

              <Typography
                variant="body1"
                className={`${styles.fontRecursive} ${styles.successMessage}`}
                sx={{ fontSize: '1.1rem' }}
              >
                {respMessage}
              </Typography>

              {isSuccess && (
                <Typography
                  variant="body2"
                  className={styles.fontRecursive}
                  sx={{ mt: 3, opacity: 0.7 }}
                >
                  Redirecting to login page in a few seconds...
                </Typography>
              )}
            </Box>
          </CardContent>
        </Card>

        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/login" className={styles.customeALink}>
            <Iconify icon="mdi:login" width={20} height={20} className={styles.linkIcon} />
            Back to login
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default Activate;
