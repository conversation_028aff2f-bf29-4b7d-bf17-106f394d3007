/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState } from "react";
import { CircularProgress, Box, Typography, Card, CardContent, Container } from '@mui/material';
import { useLocation, useNavigate } from "react-router-dom";
// import { successSnackbar, errorSnackbar } from 'components/Snackbar/index';
import { APIURL } from "helpers/constants";
import Iconify from 'components/Iconify/index';
import queryString from "query-string";
import axios from "axios";
import Cookies from 'js-cookie';
import styles from './styles.module.scss';


const GoogleAuthHandle = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [message, setMessage] = useState("Processing your login, please wait...");

  useEffect(() => {
    const values = queryString.parse(location.search);
    const code = values.code ? values.code : null;
    if (code) {
      onGooglelogin();
    }
  }, []);

  const onGooglelogin = async () => {
    const status = await googleLoginHandler(location.search);
    setIsProcessing(false);

    if (status === 200) {
      setIsSuccess(true);
      setMessage("Login successful! You will be redirected in a few seconds.");
      // successSnackbar("Login Success, you will be redirected in 3 seconds");
      setTimeout(() => { navigate('/d/') }, 3000);
    } else {
      setIsSuccess(false);
      setMessage("Login failed. Something went wrong!");
      // errorSnackbar("Login Error. Something went wrong!", true);
      setTimeout(() => { navigate('/login') }, 3000);
    }
    return;
  }

  const googleLoginHandler = (code) => {
    return axios.get(`${APIURL}/api/auth/google${code}`).then((res) => {
      const { user, access_token, refresh_token } = res.data;
      Cookies.set('accessToken', access_token, { expires: 7 });
      Cookies.set('refreshToken', refresh_token, { expires: 7 });
      localStorage.setItem("email", user.email);
      return res.status;
    }).catch((err) => {
      return err;
    });
  };

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        <Card className={styles.loginCard}>
          <Box className={styles.cardHeader}>
            <Box className={styles.headerContent}>
              <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
              <Box className={styles.headerTextContainer}>
                <Typography variant="h4" className={styles.headerTitle}>
                  Ignition
                </Typography>
                <Typography variant="body1" className={styles.headerSubtitle}>
                  Google Authentication
                </Typography>
              </Box>
            </Box>
          </Box>

          <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 }, textAlign: 'center' }}>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                py: 4
              }}>
              {isProcessing ? (
                <Box sx={{ mb: 3 }}>
                  <CircularProgress size={80} sx={{ color: '#F0A500' }} />
                </Box>
              ) : (
                <Box
                  className={styles.successIconContainer}
                  sx={{
                    mb: 3,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '100px',
                    height: '100px',
                    borderRadius: '50%',
                    backgroundColor: isSuccess ? '#4caf50' : '#d32f2f',
                    border: `2px solid ${isSuccess ? '#4caf50' : '#d32f2f'}`,
                    boxShadow: `0 0 15px ${isSuccess ? 'rgba(76, 175, 80, 0.5)' : 'rgba(211, 47, 47, 0.5)'}`,
                    margin: '0 auto'
                  }}>
                  <Iconify
                    icon={isSuccess ? "mdi:check" : "mdi:close"}
                    width={60}
                    height={60}
                    className={styles.successIcon}
                    sx={{ color: '#ffffff' }}
                  />
                </Box>
              )}

              <Typography
                variant="h5"
                className={`${styles.fontRecursive} ${styles.successTitle}`}
                sx={{ mb: 2, fontWeight: 600, mt: 4 }}>
                {isProcessing ? "Authentication in Progress" : (isSuccess ? "Authentication Successful" : "Authentication Failed")}
              </Typography>

              <Typography
                variant="body1"
                className={`${styles.fontRecursive} ${styles.successMessage}`}
                sx={{ fontSize: '1.1rem' }}
              >
                {message}
              </Typography>

              {!isProcessing && (
                <Typography
                  variant="body2"
                  className={styles.fontRecursive}
                  sx={{ mt: 3, opacity: 0.7 }}
                >
                  {isSuccess ? "Redirecting to dashboard in a few seconds..." : "Redirecting to login page in a few seconds..."}
                </Typography>
              )}
            </Box>
          </CardContent>
        </Card>

        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/login" className={styles.customeALink}>
            <Iconify icon="mdi:login" width={20} height={20} className={styles.linkIcon} />
            Back to login
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default GoogleAuthHandle;
