import React from 'react';
import { Box, Card, Skeleton } from '@mui/material';
import styles from "../styles.module.scss";

const SkeletonCard = ({ index }) => (
  <Card className={styles.planCard} key={index}>
    <Box className={styles.cardHeader}>
      <Skeleton variant="circular" width={24} height={24} />
      <Skeleton variant="text" width="70%" height={32} sx={{ ml: 1 }} />
    </Box>

    {/* Description skeleton */}
    <Skeleton variant="rectangular" width="100%" height={60} sx={{ borderRadius: '4px', mb: 2 }} />

    <Box className={styles.cardMeta}>
      <Skeleton variant="text" width={100} height={24} />
      <Skeleton variant="text" width={100} height={24} />
    </Box>

    {/* Structure skeleton */}
    <Box className={styles.planStructure}>
      {[1, 2, 3].map((_, i) => (
        <Box key={i} className={styles.structureItem}>
          <Skeleton variant="rectangular" width={32} height={32} sx={{ borderRadius: '8px' }} />
          <Box className={styles.structureInfo}>
            <Skeleton variant="text" width={60} height={16} />
            <Skeleton variant="text" width={30} height={24} />
          </Box>
        </Box>
      ))}
    </Box>

    {/* Progress skeleton */}
    <Box className={styles.progressSection}>
      <Box className={styles.progressHeader}>
        <Skeleton variant="text" width={60} height={16} />
        <Skeleton variant="text" width={30} height={16} />
      </Box>
      <Skeleton variant="rectangular" width="100%" height={6} sx={{ borderRadius: '3px', mb: 1 }} />
      <Skeleton variant="rectangular" width="100%" height={8} sx={{ borderRadius: '4px', mb: 1 }} />
      <Box className={styles.taskStatusLegend}>
        {[1, 2, 3].map((_, i) => (
          <Box key={i} className={styles.legendItem}>
            <Skeleton variant="rectangular" width={12} height={12} sx={{ borderRadius: '2px' }} />
            <Skeleton variant="text" width={60} height={16} />
          </Box>
        ))}
      </Box>
    </Box>

    {/* Milestone chart skeleton */}
    <Box className={styles.milestoneChart}>
      <Skeleton variant="text" width={120} height={16} sx={{ mb: 1 }} />
      <Box className={styles.milestoneChartContainer}>
        {[1, 2, 3, 4, 5].map((_, i) => (
          <Box key={i} className={styles.milestoneChartItem}>
            <Skeleton variant="rectangular" width="100%" height={40} sx={{ borderRadius: '4px' }} />
            <Skeleton variant="text" width={10} height={16} sx={{ mt: 0.5 }} />
          </Box>
        ))}
      </Box>
    </Box>

    {/* Creator skeleton */}
    <Box className={styles.planCreator}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Skeleton variant="circular" width={24} height={24} />
        <Box>
          <Skeleton variant="text" width={60} height={12} />
          <Skeleton variant="text" width={80} height={16} />
        </Box>
      </Box>
      <Skeleton variant="text" width={100} height={16} />
    </Box>
  </Card>
);

export default SkeletonCard; 