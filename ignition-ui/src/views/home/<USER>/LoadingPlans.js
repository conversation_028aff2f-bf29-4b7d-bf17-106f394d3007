import React from 'react';
import { Grid } from '@mui/material';
import styles from "../styles.module.scss";
import SkeletonCard from './SkeletonCard';

const LoadingPlans = ({ viewMode }) => {
  return (
    <Grid container spacing={viewMode === 'grid' ? 2 : 1} className={styles.planGrid}>
      {Array.from(new Array(viewMode === 'grid' ? 6 : 4)).map((_, index) => (
        <Grid item xs={12} sm={viewMode === 'grid' ? 6 : 12} md={viewMode === 'grid' ? 4 : 12} key={index}>
          <SkeletonCard index={index} />
        </Grid>
      ))}
    </Grid>
  );
};

export default LoadingPlans;
