import { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { getHeaders } from "helpers/functions";
import { APIURL } from "helpers/constants";

const usePlanFetching = (filters) => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const observerRef = useRef(null);
  const observer = useRef(null);

  const fetchPlans = useCallback(async (append = false, pageToFetch = 1) => {
    if (append) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      let queryParams = '';
      if (filters.text) queryParams += `&name=${filters.text}`;
      if (filters.inviteUser) queryParams += `&invite_user=${filters.inviteUser}`;
      if (filters.createdDate) queryParams += `&created_date=${filters.createdDate}`;

      let url = `${APIURL}/api/retrieve/myplan?${queryParams}&page=${pageToFetch}`;
      const response = await axios.get(url, { headers: getHeaders() });

      if (append) {
        setPlans((prevPlans) => [...prevPlans, ...response.data.results]);
      } else {
        setPlans(response.data.results);
      }

      setHasMore(response.data.next !== null);
    } catch (error) {
      console.log('Error loading: ', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [filters]);

  // Initial load when filters change
  useEffect(() => {
    setPage(1);
    setPlans([]);
    setHasMore(true);
    fetchPlans(false, 1);
  }, [filters, fetchPlans]);

  // Intersection Observer for lazy loading
  const handleObserver = useCallback((entries) => {
    const target = entries[0];
    if (target.isIntersecting && hasMore && !loadingMore && !loading) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchPlans(true, nextPage);
    }
  }, [hasMore, loadingMore, loading, page, fetchPlans]);

  useEffect(() => {
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver(handleObserver, {
      rootMargin: '200px', // Start loading when 200px away from the trigger
    });

    if (observerRef.current) {
      observer.current.observe(observerRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [handleObserver]);

  return {
    plans,
    loading,
    loadingMore,
    hasMore,
    observerRef
  };
};

export default usePlanFetching;
