import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { Link } from "react-router-dom";
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from "../styles.module.scss";

const EmptyState = () => {
  return (
    <Box className={styles.emptyState}>
      <Iconify icon="mdi:folder-open" width={80} height={80} color={mainYellowColor} />
      <Typography 
        variant="h6" 
        className={styles.emptyStateTitle}
        sx={{
          fontFamily: '"Recursive Variable", sans-serif',
          fontWeight: 700,
          color: '#333',
          marginTop: '24px',
          fontSize: '1.5rem'
        }}
      >
        No Plans Found
      </Typography>
      <Typography 
        variant="body2" 
        className={styles.emptyStateSubtitle}
        sx={{
          fontFamily: '"Recursive Variable", sans-serif',
          color: '#666',
          marginTop: '8px',
          maxWidth: '400px'
        }}
      >
        You don't have any plans yet. Create a new plan to get started!
      </Typography>
      <Button
        component={Link}
        to="/d/plan/create"
        variant="contained"
        className={styles.createEmptyButton}
        startIcon={<Iconify icon="icons8:idea" width={20} height={20} />}
        sx={{
          marginTop: '24px',
          backgroundColor: '#F0A500',
          color: 'white',
          fontFamily: '"Recursive Variable", sans-serif',
          fontWeight: 600,
          borderRadius: '8px',
          padding: '8px 24px',
          textTransform: 'none',
          '&:hover': {
            backgroundColor: '#d89400'
          }
        }}
      >
        CREATE NEW PLAN
      </Button>
    </Box>
  );
};

export default EmptyState;
