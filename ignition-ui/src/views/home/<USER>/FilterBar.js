import React from 'react';
import { Box, IconButton, Chip, Tooltip } from '@mui/material';
import dayjs from 'dayjs';
import Iconify from 'components/Iconify/index';
import styles from "../styles.module.scss";

const FilterBar = ({
  filters,
  open,
  id,
  filterAnchor,
  onFilterClick,
  onRemoveFilter,
  onClearAllFilters
}) => {
  return (
    <Box className={styles.filterSection}>
      <IconButton
        onClick={onFilterClick}
        className={`${styles.actionButton} ${open ? styles.activeFilter : ''}`}
        aria-describedby={id}
        sx={{
          backgroundColor: open ? '#F0A500' : '#f5f5f5',
          borderRadius: '8px',
          padding: '6px',
          color: open ? 'white' : 'inherit',
          '&:hover': {
            backgroundColor: open ? '#F0A500' : '#e0e0e0'
          }
        }}
      >
        <Iconify icon="mage:filter" width={20} height={20} />
      </IconButton>

      <Box className={styles.filterChips}>
        {filters.text && (
          <Chip
            icon={<Iconify icon="codicon:project" width={16} height={16} />}
            label={filters.text}
            onDelete={() => onRemoveFilter('text')}
            className={styles.filterChip}
            size="small"
          />
        )}
        {filters.inviteUser && (
          <Chip
            icon={<Iconify icon="ph:share" width={16} height={16} />}
            label={filters.inviteUser}
            onDelete={() => onRemoveFilter('inviteUser')}
            className={styles.filterChip}
            size="small"
          />
        )}
        {filters.createdDate && (
          <Chip
            icon={<Iconify icon="fluent:document-text-clock-24-regular" width={16} height={16} />}
            label={dayjs(filters.createdDate).format('MMM DD')}
            onDelete={() => onRemoveFilter('createdDate')}
            className={styles.filterChip}
            size="small"
          />
        )}
        {(filters.text || filters.inviteUser || filters.createdDate) && (
          <Tooltip title="Clear all filters" arrow>
            <IconButton onClick={onClearAllFilters} className={styles.clearButton} size="small">
              <Iconify icon="fluent-mdl2:clear-filter" width={16} height={16} />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    </Box>
  );
};

export default FilterBar;
