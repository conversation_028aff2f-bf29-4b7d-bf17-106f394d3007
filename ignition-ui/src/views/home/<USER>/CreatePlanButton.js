import React from 'react';
import { Button, Tooltip } from '@mui/material';
import { Link } from "react-router-dom";
import Iconify from 'components/Iconify/index';
import styles from "../styles.module.scss";

const CreatePlanButton = () => {
  return (
    <Tooltip title="Create new plan" arrow>
      <Button
        component={Link}
        to="/d/plan/create"
        variant="contained"
        className={styles.createButton}
        startIcon={<Iconify icon="icons8:idea" width={20} height={20} />}
        sx={{
          backgroundColor: '#F0A500',
          color: 'white',
          fontFamily: '"Recursive Variable", sans-serif',
          fontWeight: 600,
          borderRadius: '8px',
          padding: '6px 16px',
          textTransform: 'none',
          '&:hover': {
            backgroundColor: '#d89400'
          }
        }}
      >
        NEW PLAN
      </Button>
    </Tooltip>
  );
};

export default CreatePlanButton;
