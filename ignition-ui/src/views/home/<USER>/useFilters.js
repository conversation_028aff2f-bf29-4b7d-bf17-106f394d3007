import { useState } from 'react';

const useFilters = (initialFilters, onFilterChange) => {
  const [filterAnchor, setFilterAnchor] = useState(null);
  const [filters, setFilters] = useState(initialFilters);
  const [tempFilters, setTempFilters] = useState(initialFilters);

  const handleFilterClick = (event) => {
    setFilterAnchor(filterAnchor ? null : event.currentTarget);
  };

  const handleTempFilterChange = (e) => {
    setTempFilters({ ...tempFilters, [e.target.name]: e.target.value });
  };

  const handleApplyFilters = () => {
    setFilters(tempFilters);
    setFilterAnchor(null);
    if (onFilterChange) {
      onFilterChange(tempFilters);
    }
  };

  const handleClosePopper = () => {
    setFilterAnchor(null);
  };

  const handleRemoveFilter = (filterName) => {
    const updatedFilters = { ...filters, [filterName]: '' };
    setFilters(updatedFilters);
    setTempFilters(updatedFilters);
    if (onFilterChange) {
      onFilterChange(updatedFilters);
    }
  };

  const handleClearAllFilters = () => {
    setFilters(initialFilters);
    setTempFilters(initialFilters);
    if (onFilterChange) {
      onFilterChange(initialFilters);
    }
  };

  const open = Boolean(filterAnchor);
  const id = open ? 'filter-popper' : undefined;

  return {
    filterAnchor,
    filters,
    tempFilters,
    open,
    id,
    handleFilterClick,
    handleTempFilterChange,
    handleApplyFilters,
    handleClosePopper,
    handleRemoveFilter,
    handleClearAllFilters,
    setTempFilters
  };
};

export default useFilters;
