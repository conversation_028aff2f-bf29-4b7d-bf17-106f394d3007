import React, { useState, useEffect } from "react";
import {
  Container,
  Grid,
  Box,
  Typography,
  Avatar,
  Button,
  Divider,
  Chip,
  Tab,
  Tabs,
  CircularProgress,
  Paper,
  InputAdornment,
  Switch,
  FormControlLabel,
  Card,
  CardContent

} from "@mui/material";
import { styled } from '@mui/material/styles';
import { FileUploader } from "react-drag-drop-files";
import { successSnackbar, errorSnackbar } from 'components/Snackbar/index';
import { addUserSkill, deleteUserSkill, updateProfileService, fetchAccountInfoService } from '../services';
import { mainYellowColor, APIURL } from "helpers/constants";
import { getHeaders } from "helpers/functions";
import { setUser } from '../../../redux/userSlice';
import { useDispatch } from 'react-redux';
import Iconify from 'components/Iconify/index';
import useProfile from 'hooks/useProfile';
import dayjs from 'dayjs';
import InputBase from 'components/Input/InputBase';
import PhoneNumberInput from 'components/Input/PhoneNumberInput';
import { parsePhoneNumber } from 'libphonenumber-js';



// Styled components
const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 150,
  height: 150,
  border: `4px solid ${mainYellowColor}`,
  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
  cursor: 'pointer',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontFamily: 'Recursive Variable',
  fontSize: '1.1rem',
  fontWeight: 'bold',
  position: 'relative',
  transition: 'all 0.3s ease',
  '&.Mui-selected': {
    color: mainYellowColor,
  },
  '&:not(.Mui-selected)': {
    color: '#555',
    '&:hover': {
      color: '#333',
      backgroundColor: 'rgba(240, 165, 0, 0.05)',
    },
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: '1px solid #eaeaea',
  '& .MuiTabs-indicator': {
    backgroundColor: mainYellowColor,
    height: 3,
    transition: 'all 0.3s ease',
  },
}));


const SectionTitle = styled(Typography)(({ theme }) => ({
  fontFamily: 'Recursive Variable',
  fontSize: '1.5rem',
  fontWeight: 'bold',
  color: '#0F52BA',
  marginBottom: 8,
}));

const SaveButton = styled(Button)(({ theme }) => ({
  backgroundColor: mainYellowColor,
  color: '#333',
  fontFamily: 'Recursive Variable',
  fontSize: '1.125rem',
  fontWeight: 'bold',
  padding: '8px 24px',
  '&:hover': {
    backgroundColor: '#d89400',
  },
}));

const SkillChip = styled(Chip)(({ theme, bgcolor }) => ({
  margin: '0 8px 8px 0',
  fontFamily: 'Recursive Variable',
  fontSize: '1rem',
  fontWeight: 'bold',
  backgroundColor: bgcolor || mainYellowColor,
  color: 'white',
  '&:hover': {
    opacity: 0.9,
  },
}));

// Bright colors for skills
const brightColors = [
  '#FF5733', '#FF8D1A', '#FFC300', '#28B463', '#1ABC9C',
  '#3498DB', '#9B59B6', '#E74C3C', '#FF69B4', '#FF4500',
  '#FF6347', '#8A2BE2', '#FF1493', '#00BFFF', '#FFB6C1', '#CD5C5C',
];

// Add function to generate random number in min-max range
const getRandomNumber = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;


// Add function to get or create skill color from localStorage
const getOrCreateSkillColor = (skillId) => {
  // Get colors object from localStorage
  const storedColors = localStorage.getItem('skillColors');
  let skillColors = storedColors ? JSON.parse(storedColors) : {};

  // If skill already has a color, return it
  if (skillColors[skillId]) {
    return skillColors[skillId];
  }

  // If not, create a new color
  const newColor = brightColors[Math.floor(Math.random() * brightColors.length)];

  // Save new color to object and update localStorage
  skillColors[skillId] = newColor;
  localStorage.setItem('skillColors', JSON.stringify(skillColors));

  return newColor;
};

// Extended list of suggested skills
const suggestedSkills = [
  'JavaScript', 'React', 'Node.js', 'TypeScript', 'HTML', 'CSS', 'Python', 'Java',
  'SQL', 'Git', 'Docker', 'AWS', 'Redux', 'Vue.js', 'Angular', 'Express',
  'MongoDB', 'PostgreSQL', 'GraphQL', 'REST API', 'Next.js', 'Gatsby',
  'Webpack', 'Babel', 'SASS/SCSS', 'TailwindCSS', 'Material UI', 'Bootstrap',
  'Jest', 'Cypress', 'Testing Library', 'CI/CD', 'Agile', 'Scrum', 'Kanban',
  'Firebase', 'Serverless', 'Microservices', 'DevOps', 'Linux', 'Kubernetes'
];

// Create an InputWithLabel component to display label outside input
const InputWithLabel = ({ label, ...props }) => {
  return (
    <Box sx={{ mb: 1.5 }}>
      <Typography
        sx={{
          fontFamily: 'Recursive Variable',
          fontSize: '1rem',
          fontWeight: 'bold',
          mb: 0.5,
          color: '#333'
        }}>
        {label}
      </Typography>
      <InputBase {...props} />
    </Box>
  );
};

// Main component
const Profile = () => {
  const dispatch = useDispatch();
  const { profile, skills, loading, skillsLoading, setProfile, setSkills, setLoading } = useProfile();
  const [newSkill, setNewSkill] = useState("");
  const [newSkillError, setNewSkillError] = useState("");
  const [formErrors, setFormErrors] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [autoSaveStatus, setAutoSaveStatus] = useState(''); // 'saving', 'saved', 'error'
  const [autoSaveTimeouts, setAutoSaveTimeouts] = useState({});
  const maxChars = 200;

  // Autosave functionality
  const autoSaveProfile = async (fieldName, value) => {
    try {
      setAutoSaveStatus('saving');

      const headers = getHeaders();
      if (!headers) {
        throw new Error('Authentication required');
      }

      // Create FormData with updated field
      const formData = new FormData();
      const updatedProfile = { ...profile, [fieldName]: value };

      formData.append('first_name', updatedProfile.firstName || '');
      formData.append('last_name', updatedProfile.lastName || '');
      formData.append('description', updatedProfile.description || '');
      formData.append('address', updatedProfile.address || '');
      formData.append('occupation', updatedProfile.occupation || '');
      formData.append('phone_number', updatedProfile.phoneNumber || '');
      formData.append('country_code', updatedProfile.countryCode || '');
      formData.append('website', updatedProfile.website || '');
      formData.append('email_notifications_enabled', updatedProfile.emailNotificationsEnabled ?? true);
      formData.append('sms_notifications_enabled', updatedProfile.smsNotificationsEnabled ?? false);

      const response = await fetch(`${APIURL}/api/user/update-profile`, {
        method: 'PUT',
        headers: { ...headers },
        body: formData
      });

      if (response.ok) {
        setAutoSaveStatus('saved');
        // Clear saved status after 2 seconds
        setTimeout(() => setAutoSaveStatus(''), 2000);
      } else {
        throw new Error('Failed to save');
      }
    } catch (error) {
      console.error('Autosave error:', error);
      setAutoSaveStatus('error');
      setTimeout(() => setAutoSaveStatus(''), 3000);
    }
  };

  // Debounced autosave function
  const debouncedAutoSave = (fieldName, value) => {
    // Clear existing timeout for this field
    if (autoSaveTimeouts[fieldName]) {
      clearTimeout(autoSaveTimeouts[fieldName]);
    }

    // Set new timeout
    const timeoutId = setTimeout(() => {
      autoSaveProfile(fieldName, value);
    }, 1500); // 1.5 second delay

    setAutoSaveTimeouts(prev => ({
      ...prev,
      [fieldName]: timeoutId
    }));
  };

  // Initialize charCount with actual length of profile.desc
  const [charCount, setCharCount] = useState(0);

  // Random stats each time component renders
  const [projectCount] = useState(() => getRandomNumber(5, 20));
  const [connectionCount] = useState(() => getRandomNumber(3, 15));
  const [taskCount] = useState(() => getRandomNumber(10, 50));

  useEffect(() => {
    if (profile.selectedImage) {
      setSelectedImage(profile.selectedImage);
    }

    // Update charCount when profile.desc changes
    if (profile.desc) {
      setCharCount(profile.desc.length);
    }
  }, [profile.selectedImage, profile.desc]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleChange = (e) => {
    const { name, value } = e.target || { name: e.name, value: e.value };
    setProfile((prevState) => ({
      ...prevState,
      [name]: value,
    }));

    // Update charCount when desc changes
    if (name === 'desc') {
      setCharCount(value.length);
    }

    // Trigger autosave for profile fields (exclude email as it's disabled)
    if (name !== 'mail') {
      debouncedAutoSave(name, value);
    }
  };

  const handleImageUpload = async (file) => {
    setSelectedImage(file);
    const formData = new FormData();
    formData.append('first_name', profile.firstName);
    formData.append('last_name', profile.lastName);
    formData.append('description', profile.desc);
    formData.append('address', profile.address);
    formData.append('occupation', profile.occupation);
    formData.append('phone_number', profile.phoneNumber);
    formData.append('country_code', profile.countryCode || '');
    formData.append('email_notifications_enabled', profile.emailNotificationsEnabled);
    formData.append('sms_notifications_enabled', profile.smsNotificationsEnabled);

    if (file instanceof File) {
      formData.append('avatar', file);
    }

    try {
      const response = await updateProfileService(formData);
      if (response.status === 201) {
        successSnackbar("Avatar updated successfully");
        const res = await fetchAccountInfoService();
        dispatch(setUser({
          first_name: res.data.first_name,
          last_name: res.data.last_name,
          avatar: res.data.avatar,
          id: res.data.id,
        }));
      }
    } catch (error) {
      if (error.response?.data?.message) {
        setFormErrors(error.response.data.message);
      } else {
        errorSnackbar("Failed to update avatar");
      }
    }
  };

  const handleAddSkill = async () => {
    if (!newSkill) {
      setNewSkillError("Skill cannot be empty");
      return;
    }
    try {
      const res = await addUserSkill({ name: newSkill });
      if (res.status === 201) {
        setSkills(prevSkills => [...prevSkills, { id: res.data.id, name: res.data.name }]);
        setNewSkill("");
        setNewSkillError("");
      }
    } catch (error) {
      console.error(error);
      errorSnackbar(error?.response?.data?.message || "Failed to add skill");
    }
  };

  const handleDeleteSkill = async (skill) => {
    try {
      const res = await deleteUserSkill(skill.id);
      if (res.status === 204) {
        setSkills(prevSkills => prevSkills.filter(s => s.id !== skill.id));
      }
    } catch (error) {
      console.error(error);
      errorSnackbar(error?.response?.data?.message || "Failed to delete skill");
    }
  };

  const handleSaveProfile = async () => {
    setLoading(true);
    setFormErrors({});

    const formData = new FormData();
    formData.append('first_name', profile?.firstName || '');
    formData.append('last_name', profile?.lastName || '');
    formData.append('description', profile?.desc || '');
    formData.append('address', profile?.address || '');
    formData.append('occupation', profile?.occupation || '');
    formData.append('phone_number', profile?.phoneNumber || '');
    formData.append('country_code', profile?.countryCode || '');

    // Add new fields
    formData.append('company', profile?.company || '');
    formData.append('position', profile?.position || '');
    formData.append('full_address', profile?.fullAddress || '');
    formData.append('linkedin', profile?.linkedin || '');
    formData.append('github', profile?.github || '');
    formData.append('twitter', profile?.twitter || '');
    formData.append('website', profile?.website || '');
    formData.append('email_notifications_enabled', profile?.emailNotificationsEnabled ?? true);
    formData.append('sms_notifications_enabled', profile?.smsNotificationsEnabled ?? false);

    try {
      const response = await updateProfileService(formData);
      if (response.status === 201) {
        successSnackbar("Profile updated successfully");
        const res = await fetchAccountInfoService();
        dispatch(setUser({
          first_name: res.data.first_name,
          last_name: res.data.last_name,
          avatar: res.data.avatar,
          id: res.data.id,
        }));
      }
    } catch (error) {
      if (error.response?.data?.message) {
        setFormErrors(error.response.data.message);
      } else {
        errorSnackbar("Failed to update profile");
      }
    } finally {
      setLoading(false);
    }
  };

  const handlePreferenceChange = async (preferenceName, value) => {
    try {
      setAutoSaveStatus('saving');

      // Check if user is authenticated
      const headers = getHeaders();
      if (!headers) {
        errorSnackbar("Authentication required. Please log in again.");
        setAutoSaveStatus('error');
        setTimeout(() => setAutoSaveStatus(''), 3000);
        return;
      }

      // Update local state immediately for better UX
      setProfile(prevProfile => ({
        ...prevProfile,
        [preferenceName]: value
      }));

      // Make API call to update preference
      const requestBody = {};
      if (preferenceName === 'sequentialTaskCompletion') {
        requestBody.sequential_task_completion = value;
      } else if (preferenceName === 'discoverableOnPlatform') {
        requestBody.discoverable_on_platform = value;
      }

      const response = await fetch(`${APIURL}/api/user/preferences`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          ...headers
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        setAutoSaveStatus('saved');
        setTimeout(() => setAutoSaveStatus(''), 2000);
      } else {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('Permission denied.');
        } else {
          throw new Error(errorData.detail || errorData.message || 'Failed to update preference');
        }
      }
    } catch (error) {
      console.error('Preference update error:', error);
      // Revert local state on error
      setProfile(prevProfile => ({
        ...prevProfile,
        [preferenceName]: !value
      }));
      setAutoSaveStatus('error');
      setTimeout(() => setAutoSaveStatus(''), 3000);
      errorSnackbar(error.message || "Failed to update preference");
    }
  };

  const handleNotificationPreferenceChange = async (preferenceName, value) => {
    try {
      setAutoSaveStatus('saving');

      // Check if user is authenticated
      const headers = getHeaders();
      if (!headers) {
        errorSnackbar("Authentication required. Please log in again.");
        setAutoSaveStatus('error');
        setTimeout(() => setAutoSaveStatus(''), 3000);
        return;
      }

      // Update local state immediately for better UX
      setProfile(prevProfile => ({
        ...prevProfile,
        [preferenceName]: value
      }));

      // Create FormData for notification preferences (same as notifications page)
      const formData = new FormData();
      formData.append('first_name', profile?.firstName || '');
      formData.append('last_name', profile?.lastName || '');
      formData.append('description', profile?.description || '');
      formData.append('address', profile?.address || '');
      formData.append('occupation', profile?.occupation || '');
      formData.append('phone_number', profile?.phoneNumber || '');
      formData.append('country_code', profile?.countryCode || '');
      formData.append('website', profile?.website || '');

      // Set notification preferences
      if (preferenceName === 'emailNotificationsEnabled') {
        formData.append('email_notifications_enabled', value);
        formData.append('sms_notifications_enabled', profile?.smsNotificationsEnabled ?? false);
      } else if (preferenceName === 'smsNotificationsEnabled') {
        formData.append('email_notifications_enabled', profile?.emailNotificationsEnabled ?? true);
        formData.append('sms_notifications_enabled', value);
      }

      const response = await fetch(`${APIURL}/api/user/update-profile`, {
        method: 'PUT',
        headers: {
          ...headers
        },
        body: formData
      });

      if (response.ok) {
        setAutoSaveStatus('saved');
        setTimeout(() => setAutoSaveStatus(''), 2000);
      } else {
        const errorData = await response.json().catch(() => ({}));
        if (response.status === 401) {
          throw new Error('Authentication failed. Please log in again.');
        } else if (response.status === 403) {
          throw new Error('Permission denied.');
        } else {
          throw new Error(errorData.message || 'Failed to update notification preference');
        }
      }
    } catch (error) {
      console.error('Notification preference update error:', error);
      // Revert local state on error
      setProfile(prevProfile => ({
        ...prevProfile,
        [preferenceName]: !value
      }));
      setAutoSaveStatus('error');
      setTimeout(() => setAutoSaveStatus(''), 3000);
      errorSnackbar(error.message || "Failed to update notification preference");
    }
  };

  // Profile Information Tab
  const renderProfileTab = () => (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SectionTitle>Basic Information</SectionTitle>
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="First Name"
            id="firstName"
            value={profile?.firstName || ''}
            handleChange={(value) => handleChange({ target: { name: 'firstName', value } })}
            errorText={formErrors.first_name ? formErrors.first_name[0] : ""}
            placeholder="First Name"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:account-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Last Name"
            id="lastName"
            value={profile?.lastName || ''}
            handleChange={(value) => handleChange({ target: { name: 'lastName', value } })}
            errorText={formErrors.last_name ? formErrors.last_name[0] : ""}
            placeholder="Last Name"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:account-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>Professional Information</SectionTitle>
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Occupation"
            id="occupation"
            value={profile?.occupation || ''}
            handleChange={(value) => handleChange({ target: { name: 'occupation', value } })}
            errorText={formErrors.occupation ? formErrors.occupation[0] : ""}
            placeholder="Occupation"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:briefcase-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Company/Organization"
            id="company"
            value={profile?.company || ''}
            handleChange={(value) => handleChange({ target: { name: 'company', value } })}
            placeholder="Company or Organization"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:domain" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Address"
            id="address"
            value={profile?.address || ''}
            handleChange={(value) => handleChange({ target: { name: 'address', value } })}
            errorText={formErrors.address ? formErrors.address[0] : ""}
            placeholder="Address"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:map-marker-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Position/Title"
            id="position"
            value={profile?.position || ''}
            handleChange={(value) => handleChange({ target: { name: 'position', value } })}
            placeholder="Position or Title"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:badge-account-horizontal-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>About Me</SectionTitle>
        </Grid>

        <Grid item xs={12}>
          <InputWithLabel
            label="About Me"
            id="desc"
            value={profile?.desc || ''}
            handleChange={(value) => {
              handleChange({ target: { name: 'desc', value } });
            }}
            multiline={true}
            minRows={4}
            errorText={formErrors.desc ? formErrors.desc[0] : ""}
            helperText={`${charCount}/${maxChars} characters`}
            placeholder="Tell us about yourself"
          />
        </Grid>
      </Grid>
    </Box>
  );

  // Contact Information Tab
  const renderContactTab = () => (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SectionTitle>Primary Contact Information</SectionTitle>
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Email Address"
            id="mail"
            value={profile?.mail || ''}
            handleChange={(value) => handleChange({ target: { name: 'mail', value } })}
            disabled={true}
            placeholder="Email Address"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:email-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <Box sx={{ mb: 1.5 }}>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1rem',
                fontWeight: 'bold',
                mb: 0.5,
                color: '#333'
              }}>
              Phone Number
            </Typography>
            <PhoneNumberInput
              value={profile?.phoneNumber && profile?.countryCode ?
                `${profile.countryCode}${profile.phoneNumber}` :
                profile?.phoneNumber || ''}
              onChange={(value) => {
                // Parse the phone number to separate country code and national number
                if (value) {
                  try {
                    const parsed = parsePhoneNumber(value);
                    if (parsed) {
                      handleChange({ target: { name: 'phoneNumber', value: parsed.nationalNumber } });
                      handleChange({ target: { name: 'countryCode', value: `+${parsed.countryCallingCode}` } });
                    } else {
                      handleChange({ target: { name: 'phoneNumber', value } });
                    }
                  } catch (error) {
                    handleChange({ target: { name: 'phoneNumber', value } });
                  }
                } else {
                  handleChange({ target: { name: 'phoneNumber', value: '' } });
                  handleChange({ target: { name: 'countryCode', value: '' } });
                }
              }}
              error={formErrors.phone_number ? formErrors.phone_number[0] : ""}
              placeholder="Phone Number"
            />
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>Contact Address</SectionTitle>
        </Grid>

        <Grid item xs={12} md={12}>
          <InputWithLabel
            label="Full Address"
            id="fullAddress"
            value={profile?.fullAddress || ''}
            handleChange={(value) => handleChange({ target: { name: 'fullAddress', value } })}
            multiline={true}
            minRows={2}
            placeholder="Full Address"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:map-marker-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>Social Media</SectionTitle>
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="LinkedIn"
            id="linkedin"
            value={profile?.linkedin || ''}
            handleChange={(value) => handleChange({ target: { name: 'linkedin', value } })}
            placeholder="https://linkedin.com/in/username"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:linkedin" width={24} height={24} color="#0077B5" />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="GitHub"
            id="github"
            value={profile?.github || ''}
            handleChange={(value) => handleChange({ target: { name: 'github', value } })}
            placeholder="https://github.com/username"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:github" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Twitter/X"
            id="twitter"
            value={profile?.twitter || ''}
            handleChange={(value) => handleChange({ target: { name: 'twitter', value } })}
            placeholder="https://twitter.com/username"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:twitter" width={24} height={24} color="#1DA1F2" />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Personal Website"
            id="website"
            value={profile?.website || ''}
            handleChange={(value) => handleChange({ target: { name: 'website', value } })}
            placeholder="https://yourwebsite.com"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:web" width={24} height={24} color="#4CAF50" />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
            <Iconify
              icon="mdi:calendar-check"
              width={24}
              height={24}
              sx={{ color: mainYellowColor, mr: 2 }}
            />
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1rem',
                fontWeight: 'bold',
                mr: 2
              }}
            >
              Join Date:
            </Typography>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1.125rem',
                color: mainYellowColor,
                textDecoration: 'underline'
              }}
            >
              {dayjs(profile?.dateJoined).format('MMMM DD, YYYY')}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  // Skills Tab
  const renderSkillsTab = () => (
    <Box>
      <Grid container spacing={1}>
        <Grid item xs={12}>
          <SectionTitle sx={{ color: '#0F52BA', mb: 1 }}>Add New Skill</SectionTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box sx={{ flexGrow: 1, maxWidth: 400, mr: 2 }}>
              <InputWithLabel
                label="New Skill"
                id="newSkill"
                value={newSkill}
                handleChange={setNewSkill}
                errorText={newSkillError}
                placeholder="Enter a new skill"
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify icon="mdi:lightning-bolt" width={24} height={24} color={mainYellowColor} />
                    </InputAdornment>
                  ),
                }}
              />
            </Box>
            <Button
              variant="contained"
              onClick={handleAddSkill}
              sx={{
                backgroundColor: mainYellowColor,
                color: '#333',
                fontFamily: 'Recursive Variable',
                fontWeight: 'bold',
                height: 40,
                fontSize: '0.9rem',
                px: 2,
                mt: 2.5
              }}
              startIcon={<Iconify icon="mdi:plus" width={16} height={16} />}
            >
              Add Skill
            </Button>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle sx={{ color: '#0F52BA', mb: 1 }}>My Skills</SectionTitle>
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ minHeight: 100 }}>
            {skillsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
                <CircularProgress sx={{ color: mainYellowColor }} />
              </Box>
            ) : skills.length > 0 ? (
              <Box>
                <Typography
                  sx={{
                    fontFamily: 'Recursive Variable',
                    fontSize: '1rem',
                    fontWeight: 'bold',
                    mb: 1,
                    color: '#555'
                  }}
                >
                  Click on a skill to delete it
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {skills.map((skill) => (
                    <SkillChip
                      key={skill.id}
                      label={skill.name}
                      onDelete={() => handleDeleteSkill(skill)}
                      bgcolor={getOrCreateSkillColor(skill.id)}
                      sx={{ mb: 1, px: 1 }}
                    />
                  ))}
                </Box>
              </Box>
            ) : (
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  textAlign: 'center',
                  backgroundColor: '#f5f5f5',
                  borderRadius: 2
                }}
              >
                <Iconify
                  icon="mdi:information-outline"
                  width={32} height={32}
                  sx={{ color: '#757575', mb: 1 }}
                />
                <Typography
                  sx={{
                    fontFamily: 'Recursive Variable',
                    fontSize: '1.125rem',
                    fontWeight: 'bold',
                    color: '#333',
                    mb: 0.5
                  }}
                >
                  No skills available
                </Typography>
                <Typography
                  sx={{
                    fontFamily: 'Recursive Variable',
                    fontSize: '0.9rem',
                    color: '#666'
                  }}
                >
                  Please add a skill to display here.
                </Typography>
              </Paper>
            )}
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle sx={{ color: '#0F52BA', mb: 1 }}>Suggested Skills</SectionTitle>
        </Grid>

        <Grid item xs={12} sx={{ mb: 2 }}>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            {/* Random 15 skills from suggestion list */}
            {[...suggestedSkills]
              .sort(() => 0.5 - Math.random())
              .slice(0, 15)
              .map((skill) => (
                <Chip
                  key={skill}
                  label={skill}
                  onClick={() => {
                    setNewSkill(skill);
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                  }}
                  sx={{
                    backgroundColor: '#f0f0f0',
                    color: '#333',
                    fontFamily: 'Recursive Variable',
                    fontSize: '0.9rem',
                    fontWeight: 'bold',
                    m: 0.5,
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: '#e0e0e0',
                    },
                  }}
                />
              ))}
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
  // Preferences Tab
  const renderPreferencesTab = () => (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SectionTitle>Task Management Preferences</SectionTitle>
        </Grid>

        <Grid item xs={12}>
          <Card sx={{ boxShadow: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      color: '#333',
                      mb: 0.5
                    }}
                  >
                    Sequential Task Completion
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '0.95rem',
                      color: '#666',
                      mb: 1
                    }}
                  >
                    When enabled, you can only complete tasks in order within each milestone.
                    Previous tasks must be completed before you can mark later tasks as done.
                  </Typography>
                </Box>

                <FormControlLabel
                  control={
                    <Switch
                      checked={profile?.sequentialTaskCompletion ?? false}
                      onChange={(event) => handlePreferenceChange('sequentialTaskCompletion', event.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: mainYellowColor,
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: mainYellowColor,
                        },
                      }}
                    />
                  }
                  label={
                    <Typography
                      sx={{
                        fontFamily: 'Recursive Variable',
                        fontSize: '1rem',
                        fontWeight: 'bold',
                        color: profile?.sequentialTaskCompletion ? '#333' : '#999'
                      }}
                    >
                      {profile?.sequentialTaskCompletion ? 'Enabled' : 'Disabled'}
                    </Typography>
                  }
                  sx={{ mt: 1 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Notification Preferences Section */}
        <Grid item xs={12}>
          <SectionTitle>Notification Preferences</SectionTitle>
        </Grid>

        {/* Email Notifications */}
        <Grid item xs={12}>
          <Card sx={{ boxShadow: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      color: '#333',
                      mb: 0.5
                    }}
                  >
                    Email Notifications
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '0.95rem',
                      color: '#666',
                      mb: 1
                    }}
                  >
                    Receive email notifications about project updates, task assignments, deadlines, and collaboration activities.
                  </Typography>
                </Box>

                <FormControlLabel
                  control={
                    <Switch
                      checked={profile?.emailNotificationsEnabled ?? true}
                      onChange={(event) => handleNotificationPreferenceChange('emailNotificationsEnabled', event.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: mainYellowColor,
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: mainYellowColor,
                        },
                      }}
                    />
                  }
                  label={
                    <Typography
                      sx={{
                        fontFamily: 'Recursive Variable',
                        fontSize: '1rem',
                        fontWeight: 'bold',
                        color: profile?.emailNotificationsEnabled ? '#333' : '#999'
                      }}
                    >
                      {profile?.emailNotificationsEnabled ? 'Enabled' : 'Disabled'}
                    </Typography>
                  }
                  sx={{ mt: 1 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* SMS Notifications */}
        <Grid item xs={12}>
          <Card sx={{ boxShadow: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      color: '#333',
                      mb: 0.5
                    }}
                  >
                    SMS Notifications
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '0.95rem',
                      color: '#666',
                      mb: 1
                    }}
                  >
                    Receive SMS text messages for urgent notifications, deadline reminders, and critical project updates.
                  </Typography>
                </Box>

                <FormControlLabel
                  control={
                    <Switch
                      checked={profile?.smsNotificationsEnabled ?? false}
                      onChange={(event) => handleNotificationPreferenceChange('smsNotificationsEnabled', event.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: mainYellowColor,
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: mainYellowColor,
                        },
                      }}
                    />
                  }
                  label={
                    <Typography
                      sx={{
                        fontFamily: 'Recursive Variable',
                        fontSize: '1rem',
                        fontWeight: 'bold',
                        color: profile?.smsNotificationsEnabled ? '#333' : '#999'
                      }}
                    >
                      {profile?.smsNotificationsEnabled ? 'Enabled' : 'Disabled'}
                    </Typography>
                  }
                  sx={{ mt: 1 }}
                />
              </Box>

              {profile?.smsNotificationsEnabled && !profile?.phoneNumber && (
                <Box
                  sx={{
                    mt: 2,
                    p: 2,
                    backgroundColor: '#fff3cd',
                    border: '1px solid #ffeaa7',
                    borderRadius: 1
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '0.9rem',
                      color: '#856404',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <Iconify icon="mdi:alert" width={16} height={16} sx={{ mr: 1 }} />
                    Please add your phone number in the Contact Information tab to receive SMS notifications.
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Privacy Preferences Section */}
        <Grid item xs={12}>
          <SectionTitle>Privacy Preferences</SectionTitle>
        </Grid>

        <Grid item xs={12}>
          <Card sx={{ boxShadow: 2 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      color: '#333',
                      mb: 0.5
                    }}
                  >
                    Platform Discovery
                  </Typography>
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '0.95rem',
                      color: '#666',
                      mb: 1
                    }}
                  >
                    When enabled, other users can find and connect with you through the Collaboration page.
                    When disabled, you won't appear in search results or user discovery.
                  </Typography>
                </Box>

                <FormControlLabel
                  control={
                    <Switch
                      checked={profile?.discoverableOnPlatform ?? true}
                      onChange={(event) => handlePreferenceChange('discoverableOnPlatform', event.target.checked)}
                      sx={{
                        '& .MuiSwitch-switchBase.Mui-checked': {
                          color: mainYellowColor,
                        },
                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                          backgroundColor: mainYellowColor,
                        },
                      }}
                    />
                  }
                  label={
                    <Typography
                      sx={{
                        fontFamily: 'Recursive Variable',
                        fontSize: '1rem',
                        fontWeight: 'bold',
                        color: profile?.discoverableOnPlatform ? '#333' : '#999'
                      }}
                    >
                      {profile?.discoverableOnPlatform ? 'Discoverable' : 'Hidden'}
                    </Typography>
                  }
                  sx={{ mt: 1 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );





  return (
    <Container maxWidth="lg" sx={{ py: 4, minHeight: 'calc(100vh - 100px)' }}>
      {/* Autosave Status Indicator */}
      {autoSaveStatus && (
        <Box
          sx={{
            position: 'fixed',
            top: 20,
            right: 20,
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            px: 2,
            py: 1,
            borderRadius: 2,
            backgroundColor:
              autoSaveStatus === 'saving' ? '#fff3cd' :
              autoSaveStatus === 'saved' ? '#d4edda' :
              autoSaveStatus === 'error' ? '#f8d7da' : 'transparent',
            border:
              autoSaveStatus === 'saving' ? '1px solid #ffeaa7' :
              autoSaveStatus === 'saved' ? '1px solid #c3e6cb' :
              autoSaveStatus === 'error' ? '1px solid #f5c6cb' : 'none',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}
        >
          {autoSaveStatus === 'saving' && (
            <>
              <CircularProgress size={16} sx={{ color: '#856404' }} />
              <Typography sx={{ color: '#856404', fontSize: '0.9rem', fontWeight: 'bold' }}>
                Saving...
              </Typography>
            </>
          )}
          {autoSaveStatus === 'saved' && (
            <>
              <Iconify icon="mdi:check-circle" width={16} height={16} sx={{ color: '#155724' }} />
              <Typography sx={{ color: '#155724', fontSize: '0.9rem', fontWeight: 'bold' }}>
                Saved
              </Typography>
            </>
          )}
          {autoSaveStatus === 'error' && (
            <>
              <Iconify icon="mdi:alert-circle" width={16} height={16} sx={{ color: '#721c24' }} />
              <Typography sx={{ color: '#721c24', fontSize: '0.9rem', fontWeight: 'bold' }}>
                Save Failed
              </Typography>
            </>
          )}
        </Box>
      )}

      <Paper
        elevation={3}
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          mb: { xs: 3, md: 4 }
        }}
      >
        {/* Header Section with Avatar */}
        <Box
          sx={{
            p: 4,
            background: 'linear-gradient(135deg, #1e1e2d 0%, #2a2a3c 100%)',
            position: 'relative',
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'center', md: 'flex-end' },
            gap: 3
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <FileUploader
              handleChange={handleImageUpload}
              name="file"
              types={["JPG", "PNG", "GIF", "JPEG"]}
            >
              {selectedImage ? (
                <ProfileAvatar
                  src={selectedImage instanceof File ? URL.createObjectURL(selectedImage) : selectedImage}
                  alt={`${profile?.firstName} ${profile?.lastName}`}
                />
              ) : (
                <ProfileAvatar>
                  {loading ? (
                    <CircularProgress size={60} sx={{ color: mainYellowColor }} />
                  ) : (
                    <Typography
                      sx={{
                        fontSize: '3rem',
                        fontWeight: 'bold',
                        color: mainYellowColor
                      }}
                    >
                      {profile?.firstName?.[0]}{profile?.lastName?.[0]}
                    </Typography>
                  )}
                </ProfileAvatar>
              )}
            </FileUploader>
          </Box>

          <Box sx={{ color: 'white', flex: 1 }}>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: { xs: '1.8rem', md: '2.2rem' },
                fontWeight: 'bold',
                color: mainYellowColor
              }}
            >
              {`${profile?.firstName || ''} ${profile?.lastName || ''}`}
            </Typography>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1.2rem',
                color: '#fff',
                mb: 1
              }}
            >
              {profile?.occupation || 'No occupation set'} {profile?.address ? `| ${profile?.address}` : ''}
            </Typography>

            {/* Stats in small format under occupation */}
            <Box sx={{
              display: 'flex',
              gap: 5,
              mt: 2,
              color: 'white'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify icon="codicon:project" width={20} height={20} sx={{ color: mainYellowColor }} />
                <Typography sx={{ fontWeight: 'bold', fontSize: '1.2rem', ml: 1 }}>{projectCount}</Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify icon="fluent-mdl2:add-friend" width={20} height={20} sx={{ color: mainYellowColor }} />
                <Typography sx={{ fontWeight: 'bold', fontSize: '1.2rem', ml: 1 }}>{connectionCount}</Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify icon="material-symbols:task-alt" width={20} height={20} sx={{ color: mainYellowColor }} />
                <Typography sx={{ fontWeight: 'bold', fontSize: '1.2rem', ml: 1 }}>{taskCount}</Typography>
              </Box>
            </Box>
          </Box>

          <SaveButton
            variant="contained"
            onClick={handleSaveProfile}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {loading ? "Saving..." : "Save"}
          </SaveButton>
        </Box>

        {/* Tabs Section */}
        <Box sx={{
          px: { xs: 1, sm: 2 },
          backgroundColor: '#f9f9f9'
        }}>
          <StyledTabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ minHeight: 50 }}
          >
            <StyledTab label="Profile Information" />
            <StyledTab label="Contact Information" />
            <StyledTab label="Skills" />
            <StyledTab label="Preferences" />

          </StyledTabs>
        </Box>

        {/* Tab Content */}
        <Box sx={{ p: { xs: 2, sm: 3, md: 4 }, minHeight: '400px' }}>
          {activeTab === 0 && renderProfileTab()}
          {activeTab === 1 && renderContactTab()}
          {activeTab === 2 && renderSkillsTab()}
          {activeTab === 3 && renderPreferencesTab()}

        </Box>
      </Paper>
    </Container>
  );
};

export default Profile;
