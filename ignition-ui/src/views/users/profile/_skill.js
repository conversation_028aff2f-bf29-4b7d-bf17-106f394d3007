import React, { useState, useEffect } from 'react';
import { <PERSON>kel<PERSON>, <PERSON>, Card, CardContent, Button, Typography, Box, Autocomplete, TextField } from '@mui/material';
import InputBase from 'components/Input/InputBase';
import InfoIcon from '@mui/icons-material/Info';
import { getCommonSkills } from '../services';
import styles from '../styles.module.scss';

//--------------------------------------------------------------------------------------------------

// Skill Input Component with Autocomplete
const SkillAutocomplete = ({ newSkill, setNewSkill, handleAddSkill, newSkillError }) => {
  const [commonSkills, setCommonSkills] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchCommonSkills();
  }, []);

  const fetchCommonSkills = async (searchQuery = '') => {
    try {
      setLoading(true);
      const response = await getCommonSkills(searchQuery);
      setCommonSkills(response.data || []);
    } catch (error) {
      console.error('Error fetching common skills:', error);
      setCommonSkills([]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (event, value, reason) => {
    if (reason === 'input') {
      setNewSkill(value);
      // Debounce search
      const timeoutId = setTimeout(() => {
        if (value.length > 0) {
          fetchCommonSkills(value);
        } else {
          fetchCommonSkills();
        }
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleAddSkill();
    }
  };

  return (
    <Box className={styles.boxInputAddSkills}>
      <Box className='w-100'>
        <Autocomplete
          freeSolo
          options={commonSkills.map(skill => skill.name)}
          value={newSkill}
          onInputChange={handleInputChange}
          renderInput={(params) => (
            <TextField
              {...params}
              placeholder="Search or add new skill"
              variant="outlined"
              size="small"
              error={!!newSkillError}
              helperText={newSkillError}
              onKeyPress={handleKeyPress}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                }
              }}
            />
          )}
          loading={loading}
          loadingText="Searching skills..."
          noOptionsText="No matching skills found"
          sx={{ width: '100%' }}
        />
      </Box>
      <Button className={styles.addOrSaveBtn} onClick={handleAddSkill}>Add</Button>
    </Box>
  );
};

const brightColors = [
  '#FF5733', // Bright Red-Orange
  '#FF8D1A', // Orange
  '#FFC300', // Yellow
  '#28B463', // Green
  '#1ABC9C', // Teal
  '#3498DB', // Light Blue
  '#9B59B6', // Purple
  '#E74C3C', // Red
  '#FF69B4', // Hot Pink
  '#FF4500', // OrangeRed
  '#FF6347', // Tomato
  '#8A2BE2', // BlueViolet
  '#FF1493', // DeepPink
  '#00BFFF', // DeepSkyBlue
  '#FFB6C1', // LightPink
  '#CD5C5C', // IndianRed
];
const getRandomColor = () => brightColors[Math.floor(Math.random() * brightColors.length)];

export const ProfileSkills = ({
  skills,
  skillsLoading,
  handleDeleteSkill,
  newSkill,
  setNewSkill,
  handleAddSkill,
  newSkillError,
}) => (
  <Card sx={{ boxShadow: 3, mt: 0 }}>
    <Box className={styles.cardHeader}>
      <Typography className={styles.cardHeaderTitle}>My Skills</Typography>
    </Box>
    <CardContent>
      <SkillAutocomplete
        newSkill={newSkill}
        setNewSkill={setNewSkill}
        handleAddSkill={handleAddSkill}
        newSkillError={newSkillError}
      />

      <Box sx={{ height: 150 }}>
        {skillsLoading ? (<>
          <Skeleton variant="rectangular" width="100%" height={40} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={40} sx={{ mb: 2 }} />
          <Skeleton variant="rectangular" width="100%" height={40} sx={{ mb: 2 }} />
        </>) : (
          skills.length > 0 ? (
            skills.map((skill) => (
              <Chip
                key={skill.id}
                label={skill.name}
                onDelete={() => handleDeleteSkill(skill)}
                className={styles.skillChip}
                sx={{
                  bgcolor: getRandomColor(),
                  color: 'white',
                }}
              />
            ))
          ) : (
            <Box className={styles.noSkillBox}>
              <InfoIcon className={styles.iconInfo} />
              <Typography className={styles.noSkillTitle}>
                No skills available
              </Typography>
              <Typography className={styles.noSkillSubTitle}>
                Please add a skill to display here.
              </Typography>
            </Box>
          )
        )}
      </Box>
    </CardContent>
  </Card>
);
