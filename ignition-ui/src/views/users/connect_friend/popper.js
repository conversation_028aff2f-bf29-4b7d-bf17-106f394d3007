import React from 'react';
import { <PERSON>, <PERSON>per, Button, ClickAwayListener } from '@mui/material';
import Iconify from 'components/Iconify/index';
import InputBase from 'components/Input/InputBase';
import { mainYellowColor } from "helpers/constants";
import styles from "../styles.module.scss";

//--------------------------------------------------------------------------------------------------

const FilterPopper = ({ id, open, anchorEl, filters, onFilterChange, onApplyFilters, onClose }) => {
  return (
    <Popper id={id} open={open} anchorEl={anchorEl} placement="bottom-start" sx={{ zIndex: 1 }}>
      <ClickAwayListener onClickAway={onClose}>
        <Box className={styles.popperFilterSection} sx={{ boxShadow: 3 }}>
          <Box className={styles.inputGroup}>
            <Box sx={{ mr: '8px' }}><Iconify icon="eva:search-outline" width={30} height={30} color={mainYellowColor} /></Box>
            <InputBase
              id="keyword-input"
              value={filters.keyword}
              placeholder="Search by keyword..."
              handleChange={(e) => onFilterChange({ target: { name: 'keyword', value: e } })}
              errorText=""
              helperText=""
              required
            />
          </Box>
          <Box className={styles.inputGroup}>
            <Box sx={{ mr: '8px' }}><Iconify icon="mdi:account" width={30} height={30} color={mainYellowColor} /></Box>
            <InputBase
              id="skills-input"
              value={filters.skills}
              placeholder="Search by skills..."
              handleChange={(e) => onFilterChange({ target: { name: 'skills', value: e } })}
              errorText=""
              helperText=""
              required
            />
          </Box>
          <Button variant="contained" onClick={onApplyFilters} className={styles.applyBtn}>
            Apply
          </Button>
        </Box>
      </ClickAwayListener>
    </Popper>
  );
};

export default FilterPopper;
