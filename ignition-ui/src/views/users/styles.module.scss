.profileLabel {
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 10px;
  background-color: transparent;
  color: #333;
  font-family: 'Recursive Variable';
  font-size: 1.125rem;
}

/* Main Connect Friend  */
.mainContactContainer {
  min-height: calc(100vh - 65px);
  display: flex;
  flex-grow: 1;
  padding-top: 20px;

  .searchBox {
    display: flex;
    justify-content: center;
    margin-bottom: 4rem;
  }

  .avatar {
    width: 120px;
    height: 120px;
    margin-right: 16px;
  }

  .rightWrapperContent {
    width: calc(100% - 140px);
  }

  .contactCard {
    padding: 24px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: left;
    margin-bottom: 24px;
    font-family: 'Recursive Variable';
    display: flex;
  }

  .userInfo {
    display: flex;
    align-items: center;
  }

  .userName {
    font-size: 1.4rem;
    font-family: 'Recursive Variable';
    font-weight: bold;
    color: #F0A500;
  }

  .subInfomation {
    color: #333;
    font-size: 1.125rem;
    font-family: 'Recursive Variable';
    margin-top: 5px;

    >span {
      margin-right: 10px;
    }
  }

  .viewProfileButton {
    font-family: 'Recursive Variable';
    font-weight: bold;
    background-image: linear-gradient(#333, #575757);
    transition: all 0.15s ease-in-out;
    color: white;
    display: flex;
    align-items: center;
    margin-top: 10px;
    width: 300px;

    &:hover {
      background-image: linear-gradient(#ca8a00, #F0A500);
      color: white;
      font-weight: bolder;
    }
  }

  .homePageCommonIconButn {
    background-color: #333;
    padding: 8px 18px;
    border-radius: 8px;

    &:hover {
      background-color: #333;
    }
  }

  .filterBoxParams {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .filterBoxParamUnit {
    display: flex;
    align-items: center;
    margin-left: 20px;

    .chipParamVal {
      background-color: #F0A500;
      color: white;
      font-family: 'Recursive Variable';
      font-weight: bold;
      margin-left: 5px;

      >svg {
        color: white;
      }
    }
  }

  .loadMoreContract {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 0;
  }
}

.noDataPlanMain {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;

  .title {
    font-family: 'Recursive Variable';
    margin-top: 20px;
    color: #333;
    font-size: 1.4rem;
  }

  .subTitle {
    font-family: 'Recursive Variable';
    margin-top: 10px;
    color: #333;
    font-size: 1.125rem;
  }
}

/* Popper Filter Section  */
.popperFilterSection {
  padding: 16px;
  background-color: white;
  border-radius: 8px;
  min-width: 350px;

  .applyBtn {
    font-family: 'Recursive Variable';
    font-weight: bold;
    background-color: #333;
    color: white;
    border-radius: 5px;
    margin-top: 10px;

    &:hover {
      background-color: #333;
    }
  }
}

.inputGroup {
  margin-top: 10px;
  display: flex;
  align-items: center;
}

/* Keyframes Section  */
@keyframes switch-bg {
  0% {
    background-image: linear-gradient(rgb(227, 227, 227), whitesmoke);
  }

  100% {
    background-image: linear-gradient(#ca8a00, #F0A500);
  }
}