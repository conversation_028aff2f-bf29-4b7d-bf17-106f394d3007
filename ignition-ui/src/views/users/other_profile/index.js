import React, { useState, useEffect } from "react";
import {
  Contain<PERSON>,
  <PERSON>,
  Typography,
  Avatar,
  Button,
  Tab,
  Tabs,
  Paper,
  Skeleton,
  GlobalStyles,
  styled as muiStyled
} from "@mui/material";
import { styled } from '@mui/material/styles';
import { fetchOtherAccountInfo } from '../services';
import { ProfileComponent } from './_profile';
import { ContactComponent } from './_contact';
import { ProfileSkills } from './_skill';
import { mainYellowColor } from "helpers/constants";
import Iconify from 'components/Iconify/index';
import { useParams } from 'react-router-dom';
import InputBase from 'components/Input/InputBase';

// Thêm style trực tiếp vào tài liệu HTML
const inlineStyle = document.createElement('style');
inlineStyle.innerHTML = `
  .darkDisabledText .MuiInputBase-input {
    color: #000 !important;
    -webkit-text-fill-color: #000 !important;
    opacity: 1 !important;
    font-weight: 700 !important;
  }
  .darkDisabledText.Mui-disabled {
    background: rgba(0, 0, 0, 0.02) !important;
  }
  .darkDisabledText.Mui-disabled .MuiInputBase-input {
    color: #000 !important;
    -webkit-text-fill-color: #000 !important;
    opacity: 1 !important;
    font-weight: 700 !important;
  }
  .darkDisabledText.Mui-disabled .MuiInputAdornment-root {
    color: #555 !important;
  }
`;

document.head.appendChild(inlineStyle);

// Global styles để override các style của MUI
const globalStyles = (
  <GlobalStyles
    styles={{
      '.MuiInputBase-root.Mui-disabled': {
        background: 'rgba(0, 0, 0, 0.02) !important',
      },
      '.MuiInputBase-root.Mui-disabled .MuiInputBase-input': {
        color: '#000 !important',
        WebkitTextFillColor: '#000 !important',
        opacity: '1 !important',
        fontWeight: '600 !important',
      },
      '.MuiInputBase-root.Mui-disabled .MuiInputAdornment-root': {
        color: '#555 !important',
      },
      '.MuiInputBase-root.Mui-disabled::before, .MuiInputBase-root.Mui-disabled::after': {
        borderBottomColor: 'rgba(0, 0, 0, 0.42) !important',
      }
    }}
  />
);

// Bright colors for skills - tái sử dụng ở nhiều nơi
export const brightColors = [
  '#FF5733', // Bright Red-Orange
  '#FF8D1A', // Orange
  '#FFC300', // Yellow
  '#28B463', // Green
  '#1ABC9C', // Teal
  '#3498DB', // Light Blue
  '#9B59B6', // Purple
  '#E74C3C', // Red
  '#FF69B4', // Hot Pink
  '#FF4500', // OrangeRed
  '#FF6347', // Tomato
  '#8A2BE2', // BlueViolet
  '#FF1493', // DeepPink
  '#00BFFF', // DeepSkyBlue
  '#FFB6C1', // LightPink
  '#CD5C5C', // IndianRed
];

// Add function to get or create skill color from localStorage
export const getOrCreateSkillColor = (skillId) => {
  // Get colors object from localStorage
  const storedColors = localStorage.getItem('skillColors');
  let skillColors = storedColors ? JSON.parse(storedColors) : {};

  // If skill already has a color, return it
  if (skillColors[skillId]) {
    return skillColors[skillId];
  }

  // If not, create a new color
  const newColor = brightColors[Math.floor(Math.random() * brightColors.length)];

  // Save new color to object and update localStorage
  skillColors[skillId] = newColor;
  localStorage.setItem('skillColors', JSON.stringify(skillColors));

  return newColor;
};

// Styled components
const ProfileAvatar = styled(Avatar)(({ theme }) => ({
  width: 150,
  height: 150,
  border: `4px solid ${mainYellowColor}`,
  boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.05)',
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontFamily: 'Recursive Variable',
  fontSize: '1.1rem',
  fontWeight: 'bold',
  position: 'relative',
  transition: 'all 0.3s ease',
  '&.Mui-selected': {
    color: mainYellowColor,
  },
  '&:not(.Mui-selected)': {
    color: '#555',
    '&:hover': {
      color: '#333',
      backgroundColor: 'rgba(240, 165, 0, 0.05)',
    },
  },
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  borderBottom: '1px solid #eaeaea',
  '& .MuiTabs-indicator': {
    backgroundColor: mainYellowColor,
    height: 3,
    transition: 'all 0.3s ease',
  },
}));

// Custom InputBase component với màu chữ đen đậm hơn khi disabled
const StyledInputBase = muiStyled(InputBase)({
  '&.Mui-disabled': {
    backgroundColor: 'rgba(0, 0, 0, 0.02) !important',
    '& .MuiInputBase-input': {
      color: '#000 !important', 
      WebkitTextFillColor: '#000 !important',
      opacity: '1 !important',
      fontWeight: '700 !important',
      cursor: 'default !important',
    },
    '& .MuiInputAdornment-root': {
      color: '#555 !important',
    },
    '&::before, &::after': {
      borderBottomColor: 'rgba(0, 0, 0, 0.42) !important',
    }
  }
});

const SaveButton = styled(Button)(({ theme }) => ({
  backgroundColor: mainYellowColor,
  color: '#333',
  fontFamily: 'Recursive Variable',
  fontSize: '1.125rem',
  fontWeight: 'bold',
  padding: '8px 24px',
  '&:hover': {
    backgroundColor: '#d89400',
  },
}));

const Profile = () => {
  const { param } = useParams();
  const [profile, setProfile] = useState({
    firstName: "",
    lastName: "",
    desc: "",
    occupation: "",
    address: "",
    phoneNumber: "",
    selectedImage: null,
  });
  const [skills, setSkills] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  
  // Random stats
  const projectCount = 9;
  const connectionCount = 6;
  const taskCount = 21;

  useEffect(() => {
    const loadAccountInfo = async () => {
      try {
        const data = await fetchOtherAccountInfo(param);
        setProfile({
          firstName: data?.first_name,
          lastName: data?.last_name,
          desc: data?.description,
          email: data?.email,
          occupation: data?.occupation,
          address: data?.address,
          phoneNumber: data?.phone_number,
          selectedImage: data?.avatar,
          dateJoined: data?.date_joined
        });
        setSkills(data?.skills || []);
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    };

    loadAccountInfo();
  }, [param]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4, minHeight: 'calc(100vh - 100px)' }}>
      {globalStyles}
      <Paper
        elevation={3}
        sx={{
          borderRadius: 3,
          overflow: 'hidden',
          mb: { xs: 3, md: 4 }
        }}
      >
        {/* Header Section with Avatar */}
        <Box
          sx={{
            p: 4,
            background: 'linear-gradient(135deg, #1e1e2d 0%, #2a2a3c 100%)',
            position: 'relative',
            display: 'flex',
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: { xs: 'center', md: 'flex-end' },
            gap: 3
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            {loading ? (
              <Skeleton variant="circular" width={150} height={150} />
            ) : (
              profile.selectedImage ? (
                <ProfileAvatar
                  src={profile.selectedImage}
                  alt={`${profile?.firstName} ${profile?.lastName}`}
                />
              ) : (
                <ProfileAvatar>
                  <Typography
                    sx={{
                      fontSize: '3rem',
                      fontWeight: 'bold',
                      color: mainYellowColor
                    }}
                  >
                    {profile?.firstName?.[0]}{profile?.lastName?.[0]}
                  </Typography>
                </ProfileAvatar>
              )
            )}
          </Box>

          <Box sx={{ color: 'white', flex: 1 }}>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: { xs: '1.8rem', md: '2.2rem' },
                fontWeight: 'bold',
                color: mainYellowColor
              }}
            >
              {`${profile?.firstName || ''} ${profile?.lastName || ''}`}
            </Typography>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1.2rem',
                color: '#fff',
                mb: 1
              }}
            >
              {profile?.occupation || 'Lawyer'} {profile?.address ? `| ${profile?.address}` : '| 3311 Ann Passage North Zachary, NM 40102'}
            </Typography>

            {/* Stats in small format under occupation */}
            <Box sx={{
              display: 'flex',
              gap: 5,
              mt: 2,
              color: 'white'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify icon="codicon:project" width={20} height={20} sx={{ color: mainYellowColor }} />
                <Typography sx={{ fontWeight: 'bold', fontSize: '1.2rem', ml: 1 }}>{projectCount}</Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify icon="fluent-mdl2:add-friend" width={20} height={20} sx={{ color: mainYellowColor }} />
                <Typography sx={{ fontWeight: 'bold', fontSize: '1.2rem', ml: 1 }}>{connectionCount}</Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Iconify icon="material-symbols:task-alt" width={20} height={20} sx={{ color: mainYellowColor }} />
                <Typography sx={{ fontWeight: 'bold', fontSize: '1.2rem', ml: 1 }}>{taskCount}</Typography>
              </Box>
            </Box>
          </Box>

          <SaveButton
            variant="contained"
          >
            Save
          </SaveButton>
        </Box>

        {/* Tabs Section */}
        <Box sx={{
          px: { xs: 1, sm: 2 },
          backgroundColor: '#f9f9f9'
        }}>
          <StyledTabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ minHeight: 50 }}
          >
            <StyledTab label="PROFILE INFORMATION" />
            <StyledTab label="CONTACT INFORMATION" />
            <StyledTab label="SKILLS" />
          </StyledTabs>
        </Box>

        {/* Tab Content */}
        <Box sx={{ p: { xs: 2, sm: 3, md: 4 }, minHeight: '400px' }}>
          {activeTab === 0 && (
            <ProfileComponent
              profile={profile}
              loading={loading}
              StyledInputBase={StyledInputBase}
            />
          )}
          {activeTab === 1 && (
            <ContactComponent
              profile={profile}
              loading={loading}
              StyledInputBase={StyledInputBase}
            />
          )}
          {activeTab === 2 && (
            <ProfileSkills
              skills={skills}
              skillsLoading={loading}
              getOrCreateSkillColor={getOrCreateSkillColor}
            />
          )}
        </Box>
      </Paper>
    </Container>
  );
};

export default Profile;
