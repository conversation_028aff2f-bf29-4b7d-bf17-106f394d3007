import React from 'react';
import { Box, Grid, Typography, Divider, InputAdornment } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import dayjs from 'dayjs';

//--------------------------------------------------------------------------------------------------

const SectionTitle = ({ children, ...props }) => (
  <Typography 
    sx={{
      fontFamily: 'Recursive Variable',
      fontSize: '1.5rem',
      fontWeight: 'bold',
      color: '#0F52BA',
      marginBottom: 2,
      ...props.sx
    }}
    {...props}
  >
    {children}
  </Typography>
);

const InputWithLabel = ({ label, InputComponent, ...props }) => {
  return (
    <Box sx={{ mb: 3 }}>
      <Typography
        sx={{
          fontFamily: 'Recursive Variable',
          fontSize: '1rem',
          fontWeight: 'bold',
          mb: 0.5,
          color: '#333'
        }}>
        {label}
      </Typography>
      <InputComponent 
        {...props} 
        disabled 
        fullWidth 
        className="darkDisabledText"
        sx={{ 
          '& .MuiInputBase-input': { 
            fontWeight: '700 !important',
            color: '#000 !important',
            WebkitTextFillColor: '#000 !important',
            opacity: '1 !important',
          },
          '&.Mui-disabled': {
            background: 'rgba(0, 0, 0, 0.02) !important',
            '& .MuiInputBase-input': {
              color: '#000 !important',
              '-webkit-text-fill-color': '#000 !important',
              opacity: '1 !important',
              fontWeight: '700 !important',
              cursor: 'default !important',
            },
            '& .MuiInputAdornment-root': {
              color: '#555 !important',
            },
          },
          ...props.sx
        }}
      />
    </Box>
  );
};

export const ContactComponent = ({ profile, loading, StyledInputBase }) => {
  return (
    <Box>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <SectionTitle>Primary Contact Information</SectionTitle>
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Email Address"
            value={profile?.email || ''}
            placeholder="Email Address"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:email-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Phone Number"
            value={profile?.phoneNumber || ''}
            placeholder="Phone Number"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:phone-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>Contact Address</SectionTitle>
        </Grid>

        <Grid item xs={12} md={12}>
          <InputWithLabel
            label="Full Address"
            value={profile?.address || ''}
            multiline={true}
            minRows={2}
            InputComponent={StyledInputBase}
            placeholder="Full Address"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:map-marker-outline" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <SectionTitle>Social Media</SectionTitle>
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="LinkedIn"
            value={profile?.linkedin || ''}
            placeholder="https://linkedin.com/in/username"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:linkedin" width={24} height={24} color="#0077B5" />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="GitHub"
            value={profile?.github || ''}
            placeholder="https://github.com/username"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:github" width={24} height={24} />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Twitter/X"
            value={profile?.twitter || ''}
            placeholder="https://twitter.com/username"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:twitter" width={24} height={24} color="#1DA1F2" />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <InputWithLabel
            label="Personal Website"
            value={profile?.website || ''}
            placeholder="https://yourwebsite.com"
            InputComponent={StyledInputBase}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Iconify icon="mdi:web" width={24} height={24} color="#4CAF50" />
                </InputAdornment>
              ),
            }}
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
            <Iconify
              icon="mdi:calendar-check"
              width={24}
              height={24}
              sx={{ color: mainYellowColor, mr: 2 }}
            />
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1rem',
                fontWeight: 'bold',
                mr: 2
              }}
            >
              Join Date:
            </Typography>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1.125rem'
              }}
            >
              {profile?.dateJoined ? dayjs(profile.dateJoined).format('MMMM DD, YYYY') : 'March 27, 2025'}
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};
