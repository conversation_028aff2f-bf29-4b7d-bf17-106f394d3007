import React, { useState, useEffect } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Box } from '@mui/material';
import CustomDatePicker from 'components/Input/CustomDatePicker';
import dayjs from 'dayjs';

const DateSelectionDialog = ({ open, handleClose, task, handleDateUpdate, section }) => {
  const [startDate, setStartDate] = useState(dayjs(task?.start_date) || dayjs());
  const [endDate, setEndDate] = useState(task?.end_date ? dayjs(task.end_date) : null);

  useEffect(() => {
    if (task) {
      setStartDate(dayjs(task.start_date) || dayjs());
      setEndDate(task.end_date ? dayjs(task.end_date) : null);
    }
  }, [task]);

  const handleSave = () => {
    const formattedStartDate = startDate ? startDate.format('YYYY-MM-DD') : null;
    const formattedEndDate = endDate ? endDate.format('YYYY-MM-DD') : null;

    handleDateUpdate(formattedStartDate, formattedEndDate);
    handleClose();
  };

  const today = dayjs();

  const getStartDateConstraints = () => {
    switch (section) {
      case 'current':
        return { maxDate: today };
      case 'upcoming':
        return { minDate: today.add(1, 'day') };
      case 'overdue':
        return { maxDate: today.subtract(1, 'day') };
      default:
        return { maxDate: today };
    }
  };

  const getEndDateConstraints = () => {
    const minDate = startDate || today;
    switch (section) {
      case 'current':
        return { minDate: today };
      case 'upcoming':
        return { minDate: today.add(1, 'day') };
      case 'overdue':
        return { minDate: today.subtract(1, 'day') };
      default:
        return { minDate };
    }
  };

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle>Edit Task Dates</DialogTitle>
      <DialogContent>
        <Box>
          <CustomDatePicker
            name="startDate"
            values={{ startDate }}
            setValues={({ startDate }) => setStartDate(startDate)}
            size="small"
            labelName="Task's Start Date"
            {...getStartDateConstraints()}
          />
        </Box>
        <Box>
          <CustomDatePicker
            name="endDate"
            values={{ endDate }}
            setValues={({ endDate }) => setEndDate(endDate)}
            size="small"
            labelName="Task's End Date"
            {...getEndDateConstraints()}
          />
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} color="secondary">
          Cancel
        </Button>
        <Button onClick={handleSave} color="primary">
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DateSelectionDialog;
