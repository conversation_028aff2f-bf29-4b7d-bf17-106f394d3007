import React from 'react';
import { Dialog, DialogActions, DialogContent, DialogTitle, Button, TextField } from '@mui/material';
import CustomDatePicker from 'components/Input/CustomDatePicker';
import dayjs from 'dayjs';

const AddTaskDialog = ({ open, handleClose, newTask, setNewTask, handleAddTask }) => {
  const handleStartDateChange = (date) => {
    setNewTask({
      ...newTask,
      start_date: date,
      // Ensure end date is always after or equal to start date
      end_date: newTask.end_date && dayjs(newTask.end_date).isBefore(date) ? date : newTask.end_date,
    });
  };

  const handleEndDateChange = (date) => {
    setNewTask({ ...newTask, end_date: date });
  };

  const handleDeadlineChange = (date) => {
    setNewTask({ ...newTask, deadline: date });
  };

  return (
    <Dialog open={open} onClose={handleClose}>
      <DialogTitle>Thê<PERSON>c</DialogTitle>
      <DialogContent>
        <TextField
          autoFocus
          margin="dense"
          name="title"
          label="Tên Công Việc"
          type="text"
          fullWidth
          variant="outlined"
          value={newTask.title}
          onChange={(e) => setNewTask({ ...newTask, name: e.target.value })}
        />
        {/* Start Date Picker */}
        <CustomDatePicker
          values={newTask}
          setValues={setNewTask}
          name="start_date"
          labelName="Ngày Bắt Đầu"
          size="small"
          minDate={dayjs()}  // Limit start date to today onward
          value={newTask.start_date}
          onChange={handleStartDateChange}
        />
        {/* End Date Picker */}
        <CustomDatePicker
          values={newTask}
          setValues={setNewTask}
          name="end_date"
          labelName="Ngày Kết Thúc"
          size="small"
          minDate={newTask.start_date || dayjs()}  // Limit end date to start date onward
          value={newTask.end_date}
          onChange={handleEndDateChange}
        />
        {/* Deadline Picker */}
        <CustomDatePicker
          values={newTask}
          setValues={setNewTask}
          name="deadline"
          labelName="Hạn Chót"
          size="small"
          minDate={dayjs()}  // Limit deadline to today onward
          value={newTask.deadline}
          onChange={handleDeadlineChange}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Hủy</Button>
        <Button onClick={handleAddTask}>Thêm Công Việc</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddTaskDialog;
