import dayjs from 'dayjs';

export const TASK_SECTIONS = [
  {
    id: 'current',
    title: 'Current',
    backgroundColor: '#FFFBE6'
  },
  {
    id: 'upcoming',
    title: 'Upcoming',
    backgroundColor: '#F0F7FF'
  },
  {
    id: 'completed',
    title: 'Completed',
    backgroundColor: '#F0FFF4'
  },
  {
    id: 'overdue',
    title: 'Overdue',
    backgroundColor: '#FFF0F0'
  }
];

export const dateFormatter = 'YYYY-MM-DD';
export const dateFormatterWithName = 'MMM DD';

export const TASK_CURRENT = 'current';
export const TASK_UPCOMING = 'upcoming';
export const TASK_COMPLETED = 'completed';
export const TASK_OVERDUE = 'overdue';
export const TODO_STATUS = 1;
export const INPROGRESS_STATUS = 2;
export const DONE_STATUS = 3;

export const isTaskCurrent = (task) => {
  const today = dayjs();
  const startDate = task.start_date ? dayjs(task.start_date) : null;
  const endDate = task.end_date ? dayjs(task.end_date) : null;
  const isStatus1 = task.status === TODO_STATUS;
  const isTodayInRange = startDate && endDate ? today.isBetween(startDate, endDate, 'day', '[]') : false;
  const isStartOrEndToday = (startDate && startDate.isSame(today, 'day')) || (endDate && endDate.isSame(today, 'day'));
  const isStatus2 = task.status === INPROGRESS_STATUS;
  return (isStatus1 && (isTodayInRange || isStartOrEndToday)) || isStatus2;
};

export const isTaskUpComming = (task) => {
  const today = dayjs();
  const startDate = task.start_date ? dayjs(task.start_date) : null;
  const endDate = task.end_date ? dayjs(task.end_date) : null;
  const isStatus1 = task.status === TODO_STATUS;
  const isStartAfterToday = startDate ? startDate.isAfter(today, 'day') : false;
  const isEndAfterToday = endDate ? endDate.isAfter(today, 'day') : false;
  const hasBothDates = startDate && endDate;
  const isAlreadyInCurrent = isTaskCurrent(task);
  return isStatus1 && (isStartAfterToday || isEndAfterToday) && !hasBothDates && !isAlreadyInCurrent;
};

export const isTaskDone = (task) => {
  return task.status === DONE_STATUS;
}

export const isTaskOverdue = (task) => {
  const today = dayjs();
  const startDate = task.start_date ? dayjs(task.start_date) : null;
  const endDate = task.end_date ? dayjs(task.end_date) : null;
  const isStatus1 = task.status === 1;

  const isStartBeforeToday = startDate ? startDate.isBefore(today, 'day') : false;
  const isEndBeforeToday = endDate ? endDate.isBefore(today, 'day') : false;

  const isDatesNull = !startDate && !endDate;

  const isNotCurrentOrUpcoming = !(
    (startDate && endDate && today.isBetween(startDate, endDate, 'day', '[]')) ||
    (startDate && startDate.isAfter(today, 'day')) || (endDate && endDate.isAfter(today, 'day'))
  );

  return isStatus1 && ((isStartBeforeToday || isEndBeforeToday) || isDatesNull) && isNotCurrentOrUpcoming;
};