:root {
  --main-yellow-color: #ffc107; /* <PERSON><PERSON><PERSON> trị mặc định, sẽ được ghi đè bởi giá trị từ JS */
}

.taskListContainer {
  padding: 16px;
  width: 100%;
  height: 92vh;
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-sizing: border-box;
}

.columnsContainer {
  display: flex;
  gap: 16px;
  width: 100%;
  justify-content: space-between;
  flex-grow: 1;
  overflow: hidden;
}

.taskBox {
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.08) !important;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  height: 100%;
  overflow: hidden;
  
  &[data-section="current"] {
    border-top: 4px solid var(--main-yellow-color);
  }
  
  &[data-section="upcoming"] {
    border-top: 4px solid rgba(255, 193, 7, 0.6);
  }
  
  &[data-section="completed"] {
    border-top: 4px solid rgba(255, 193, 7, 0.8);
  }
  
  &[data-section="overdue"] {
    border-top: 4px solid #ef4444;
  }
}

.header {
  font-size: 16px !important;
  font-weight: 600 !important;
  margin-bottom: 12px !important;
  color: #1e293b;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0,0,0,0.08);
  white-space: nowrap;
  font-family: 'Recursive Variable', sans-serif !important;
  flex-shrink: 0;
  
  &[data-section="current"] {
    color: var(--main-yellow-color);
  }
  
  &[data-section="upcoming"] {
    color: rgba(255, 193, 7, 0.6);
  }
  
  &[data-section="completed"] {
    color: rgba(255, 193, 7, 0.8);
  }
  
  &[data-section="overdue"] {
    color: #ef4444;
  }
}

.section {
  margin-top: 16px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: calc(100% - 50px);
  padding-right: 4px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }
}

.taskItem {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  word-break: break-word;
  border: 1px solid #e2e8f0;
  
  &:hover {
    box-shadow: 0 3px 6px rgba(0,0,0,0.08);
    transform: translateY(-1px);
    border-color: var(--main-yellow-color);
  }
}

.taskTitle {
  font-size: 15px !important;
  font-weight: 600 !important;
  color: #1e293b;
  margin-bottom: 4px !important;
  line-height: 1.4 !important;
  font-family: 'Recursive Variable', sans-serif !important;
}

.planName {
  font-size: 13px !important;
  color: var(--main-yellow-color);
  margin-bottom: 12px !important;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400 !important;
  font-family: 'Recursive Variable', sans-serif !important;
  
  &:hover {
    opacity: 0.8;
  }
}

.taskDateCover {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #64748b;
  flex-wrap: wrap;
  font-family: 'Recursive Variable', sans-serif !important;
}

.taskDate {
  font-size: 13px !important;
  color: #64748b;
  font-family: 'Recursive Variable', sans-serif !important;
}

.taskActions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.taskIcon, .taskCheckbox, .taskDelete {
  padding: 4px !important;
  margin-left: 2px !important;
  color: #64748b !important;
  
  &:hover {
    background-color: rgba(255, 193, 7, 0.08);
    color: var(--main-yellow-color) !important;
  }
}

.addBtn {
  position: fixed !important;
  bottom: 24px;
  right: 24px;
  background: var(--main-yellow-color) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3) !important;
  border: none !important;
  
  &:hover {
    background: rgba(255, 193, 7, 0.9) !important;
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(255, 193, 7, 0.4) !important;
  }
}

.iconAdd {
  transition: transform 0.2s ease;
  color: white !important;
}

.noContent {
  text-align: center;
  color: #94a3b8;
  padding: 24px 0;
  font-style: italic;
  margin: auto;
  font-family: 'Recursive Variable', sans-serif !important;
  font-size: 0.8rem !important;
}

.pageTitle {
  font-weight: 600 !important;
  color: #1e293b !important;
  font-family: 'Recursive Variable', sans-serif !important;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px !important;
  flex-shrink: 0;
}

.pageContainer {
  background-color: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}

.completedSection {
  background-color: #f8fafc;
}

.taskBox {
  box-shadow: 0 0 10px rgba(0,0,0,0.05) !important;
}