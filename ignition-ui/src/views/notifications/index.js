/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  CircularProgress,
  Container,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Divider,
  Tabs,
  Tab
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import Iconify from 'components/Iconify';
import { successSnackbar, errorSnackbar } from 'components/Snackbar';
import { mockNotifications, getNotificationConfig } from './mockData';
import { updateProfileService } from '../users/services';
import { mainYellowColor } from "helpers/constants";
import useProfile from 'hooks/useProfile';
import styles from './styles.module.scss';

dayjs.extend(relativeTime);

//-------------------------------------------------------------------------------------------------

const Notifications = () => {
  const navigate = useNavigate();
  const { profile, setProfile, loading: profileLoading } = useProfile();
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  const [savingPreferences, setSavingPreferences] = useState(false);
  const observerRef = useRef(null);
  const observer = useRef(null);
  const pageSize = 8;
  const totalNotifications = mockNotifications.length;

  const handleObserver = useCallback((entries) => {
    const target = entries[0];
    if (target.isIntersecting && !loadingMore && notifications.length < totalNotifications) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [loadingMore, notifications.length, totalNotifications]);

  const fetchNotifications = useCallback((append = false) => {
    const start = (page - 1) * pageSize;
    const newNotifications = mockNotifications.slice(start, start + pageSize);

    if (append) {
      setNotifications((prevNotifications) => [...prevNotifications, ...newNotifications]);
    } else {
      setNotifications(newNotifications);
    }

    setLoading(false);
    setLoadingMore(false);
  }, [page]);

  useEffect(() => {
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver(handleObserver, { threshold: 0.1 });

    if (observerRef.current) {
      observer.current.observe(observerRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [handleObserver]);

  useEffect(() => {
    setLoading(true);
    setPage(1);
    fetchNotifications();
  }, [fetchNotifications]);

  useEffect(() => {
    if (page > 1) {
      setLoadingMore(true);
      fetchNotifications(true);
    }
  }, [page, fetchNotifications]);

  const handleRead = (id) => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification =>
        notification.id === id ? { ...notification, is_read: true } : notification
      )
    );
    successSnackbar('Notification marked as read');
  };

  const handleReadAll = () => {
    setNotifications(prevNotifications =>
      prevNotifications.map(notification => ({ ...notification, is_read: true }))
    );
    successSnackbar('All notifications marked as read');
  };

  const handleDelete = (id) => {
    setNotifications(prevNotifications =>
      prevNotifications.filter(notification => notification.id !== id)
    );
    successSnackbar('Notification deleted');
  };

  const handleAction = (actionUrl) => {
    navigate(actionUrl);
  };

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handlePreferenceChange = (field, value) => {
    setProfile((prevProfile) => ({
      ...prevProfile,
      [field]: value,
    }));
  };

  const handleSavePreferences = async () => {
    setSavingPreferences(true);

    const formData = new FormData();
    formData.append('first_name', profile?.firstName || '');
    formData.append('last_name', profile?.lastName || '');
    formData.append('description', profile?.desc || '');
    formData.append('address', profile?.address || '');
    formData.append('occupation', profile?.occupation || '');
    formData.append('phone_number', profile?.phoneNumber || '');
    formData.append('country_code', profile?.countryCode || '');
    formData.append('company', profile?.company || '');
    formData.append('position', profile?.position || '');
    formData.append('full_address', profile?.fullAddress || '');
    formData.append('linkedin', profile?.linkedin || '');
    formData.append('github', profile?.github || '');
    formData.append('twitter', profile?.twitter || '');
    formData.append('website', profile?.website || '');
    formData.append('email_notifications_enabled', profile?.emailNotificationsEnabled ?? true);
    formData.append('sms_notifications_enabled', profile?.smsNotificationsEnabled ?? false);

    try {
      const response = await updateProfileService(formData);
      if (response.status === 201) {
        successSnackbar("Notification preferences updated successfully");
      }
    } catch (error) {
      errorSnackbar("Failed to update notification preferences");
    } finally {
      setSavingPreferences(false);
    }
  };

  const renderNotificationCard = (notification) => {
    const config = getNotificationConfig(notification.type);

    return (
      <Paper
        key={notification.id}
        className={`${styles.notificationCard} ${notification.is_read ? styles.read : ''}`}
        elevation={notification.is_read ? 0 : 1}
      >
        <Box className={styles.notificationContent}>
          <Box
            className={styles.iconBox}
            sx={{ backgroundColor: `${config.color}15` }}
          >
            <Iconify
              icon={config.icon}
              width={24}
              height={24}
              sx={{ color: config.color }}
            />
          </Box>

          <Box className={styles.messageContent}>
            <Typography className={styles.message}>
              {notification.message}
            </Typography>
            <Typography className={styles.timestamp}>
              {dayjs(notification.created_at).fromNow()}
            </Typography>
          </Box>
        </Box>

        <Box className={styles.actionButtons}>
          {!notification.is_read && (
            <Button
              size="small"
              className={styles.markAsReadButton}
              onClick={() => handleRead(notification.id)}
              startIcon={<Iconify icon="mdi:check" />}
            >
              Mark as read
            </Button>
          )}

          <Button
            size="small"
            className={styles.actionButton}
            onClick={() => handleAction(notification.action_url)}
            sx={{ backgroundColor: config.color }}
            startIcon={<Iconify icon="mdi:arrow-right" />}
          >
            {config.actionText}
          </Button>

          <Button
            size="small"
            variant="outlined"
            className={styles.deleteButton}
            onClick={() => handleDelete(notification.id)}
            startIcon={<Iconify icon="mdi:delete-outline" />}
          >
            Delete
          </Button>
        </Box>
      </Paper>
    );
  };

  const renderNotificationsContent = () => {
    if (loading) {
      return Array(3).fill(0).map((_, index) => (
        <Paper key={index} className={styles.notificationSkeleton} />
      ));
    }

    if (notifications.length === 0) {
      return (
        <Box className={styles.emptyState}>
          <Iconify
            icon="mdi:bell-off-outline"
            className={styles.emptyIcon}
          />
          <Typography className={styles.emptyText}>
            No notifications to display
          </Typography>
        </Box>
      );
    }

    return (
      <>
        {notifications.map(renderNotificationCard)}
        <Box ref={observerRef} />
        {loadingMore && (
          <Box className={styles.loadingContainer}>
            <CircularProgress size={32} />
          </Box>
        )}
      </>
    );
  };

  const renderPreferencesContent = () => (
    <Box>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography
            sx={{
              fontFamily: 'Recursive Variable',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: '#0F52BA',
              mb: 1
            }}
          >
            Notification Preferences
          </Typography>
          <Typography
            sx={{
              fontFamily: 'Recursive Variable',
              fontSize: '1rem',
              color: '#666',
              mb: 3
            }}
          >
            Choose how you want to receive notifications about your projects, tasks, and activities.
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ boxShadow: 2, borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Iconify
                  icon="mdi:email-outline"
                  width={32}
                  height={32}
                  sx={{ color: mainYellowColor, mr: 2 }}
                />
                <Typography
                  sx={{
                    fontFamily: 'Recursive Variable',
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    color: '#333'
                  }}
                >
                  Email Notifications
                </Typography>
              </Box>

              <Typography
                sx={{
                  fontFamily: 'Recursive Variable',
                  fontSize: '0.95rem',
                  color: '#666',
                  mb: 2,
                  lineHeight: 1.5
                }}
              >
                Receive notifications via email about task assignments, project updates, deadlines, and important announcements.
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={profile?.emailNotificationsEnabled ?? true}
                    onChange={(event) =>
                      handlePreferenceChange('emailNotificationsEnabled', event.target.checked)
                    }
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: mainYellowColor,
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: mainYellowColor,
                      },
                    }}
                  />
                }
                label={
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '1rem',
                      fontWeight: 'bold',
                      color: profile?.emailNotificationsEnabled ? '#333' : '#999'
                    }}
                  >
                    {profile?.emailNotificationsEnabled ? 'Enabled' : 'Disabled'}
                  </Typography>
                }
                sx={{ mt: 1 }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ boxShadow: 2, borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Iconify
                  icon="mdi:message-text-outline"
                  width={32}
                  height={32}
                  sx={{ color: mainYellowColor, mr: 2 }}
                />
                <Typography
                  sx={{
                    fontFamily: 'Recursive Variable',
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    color: '#333'
                  }}
                >
                  SMS Notifications
                </Typography>
              </Box>

              <Typography
                sx={{
                  fontFamily: 'Recursive Variable',
                  fontSize: '0.95rem',
                  color: '#666',
                  mb: 2,
                  lineHeight: 1.5
                }}
              >
                Get instant SMS alerts for urgent tasks, critical deadlines, and high-priority project updates directly to your phone.
              </Typography>

              <FormControlLabel
                control={
                  <Switch
                    checked={profile?.smsNotificationsEnabled ?? false}
                    onChange={(event) =>
                      handlePreferenceChange('smsNotificationsEnabled', event.target.checked)
                    }
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: mainYellowColor,
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: mainYellowColor,
                      },
                    }}
                  />
                }
                label={
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '1rem',
                      fontWeight: 'bold',
                      color: profile?.smsNotificationsEnabled ? '#333' : '#999'
                    }}
                  >
                    {profile?.smsNotificationsEnabled ? 'Enabled' : 'Disabled'}
                  </Typography>
                }
                sx={{ mt: 1 }}
              />

              {profile?.smsNotificationsEnabled && !profile?.phoneNumber && (
                <Box
                  sx={{
                    mt: 2,
                    p: 2,
                    backgroundColor: '#fff3cd',
                    border: '1px solid #ffeaa7',
                    borderRadius: 1
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: 'Recursive Variable',
                      fontSize: '0.9rem',
                      color: '#856404',
                      display: 'flex',
                      alignItems: 'center'
                    }}
                  >
                    <Iconify icon="mdi:alert" width={16} height={16} sx={{ mr: 1 }} />
                    Please add your phone number in your profile to receive SMS notifications.
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography
              sx={{
                fontFamily: 'Recursive Variable',
                fontSize: '1.25rem',
                fontWeight: 'bold',
                color: '#0F52BA'
              }}
            >
              Notification Types
            </Typography>
            <Button
              variant="contained"
              onClick={handleSavePreferences}
              disabled={savingPreferences || profileLoading}
              startIcon={savingPreferences ? <CircularProgress size={20} color="inherit" /> : <Iconify icon="mdi:content-save" />}
              sx={{
                backgroundColor: mainYellowColor,
                color: '#333',
                fontFamily: 'Recursive Variable',
                fontSize: '1rem',
                fontWeight: 'bold',
                '&:hover': {
                  backgroundColor: '#d89400',
                },
              }}
            >
              {savingPreferences ? "Saving..." : "Save Preferences"}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );

  return (
    <Container maxWidth="lg">
      <Box className={styles.mainPage}>
        <Box className={styles.notificationHeader}>
          <Typography className={styles.title}>
            Notifications & Preferences
          </Typography>
        </Box>

        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            sx={{
              '& .MuiTab-root': {
                fontFamily: 'Recursive Variable',
                fontSize: '1.1rem',
                fontWeight: 'bold',
                textTransform: 'none',
              },
              '& .MuiTabs-indicator': {
                backgroundColor: mainYellowColor,
                height: 3,
              },
              '& .Mui-selected': {
                color: mainYellowColor,
              },
            }}
          >
            <Tab
              label="Notifications"
              icon={<Iconify icon="mdi:bell-outline" />}
              iconPosition="start"
              sx={{ gap: 1 }}
            />
            <Tab
              label="Preferences"
              icon={<Iconify icon="mdi:cog-outline" />}
              iconPosition="start"
              sx={{ gap: 1 }}
            />
          </Tabs>
        </Box>

        {/* Tab Content */}
        {activeTab === 0 && (
          <Box>
            {notifications.length > 0 && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Button
                  variant="contained"
                  onClick={handleReadAll}
                  startIcon={<Iconify icon="mdi:check-all" />}
                  sx={{
                    backgroundColor: '#333',
                    '&:hover': { backgroundColor: '#444' }
                  }}
                >
                  Mark all as read
                </Button>
              </Box>
            )}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {renderNotificationsContent()}
            </Box>
          </Box>
        )}

        {activeTab === 1 && renderPreferencesContent()}
      </Box>
    </Container>
  );
};

export default Notifications;
