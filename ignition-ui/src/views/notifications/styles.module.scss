.mainPage {
  padding: 24px;
  min-height: calc(100vh - 100px);
}

.notificationHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  .title {
    font-family: 'Recursive Variable';
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
  }
}

.notificationCard {
  padding: 16px;
  transition: all 0.2s ease-in-out;
  border: 1px solid #eaeaea;
  background-color: white;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  &.read {
    background-color: #f8f9fa;
    opacity: 0.8;
  }
}

.notificationContent {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 12px;
}

.iconBox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  flex-shrink: 0;
}

.messageContent {
  flex: 1;

  .message {
    font-family: 'Recursive Variable';
    font-size: 1rem;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.5;
  }

  .timestamp {
    font-family: 'Recursive Variable';
    font-size: 0.875rem;
    color: #666;
  }
}

.actionButtons {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;

  button {
    font-family: 'Recursive Variable';
    font-size: 0.875rem;
    font-weight: bold;
    padding: 6px 16px;
    border-radius: 6px;
    text-transform: none;

    &.actionButton {
      color: white;
    }

    &.markAsReadButton {
      color: #333;
      background-color: #f0f0f0;

      &:hover {
        background-color: #e0e0e0;
      }
    }

    &.deleteButton {
      color: #dc3545;
      border-color: #dc3545;

      &:hover {
        background-color: rgba(220, 53, 69, 0.04);
      }
    }
  }
}

.loadingContainer {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

.emptyState {
  text-align: center;
  padding: 48px 0;

  .emptyIcon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
  }

  .emptyText {
    font-family: 'Recursive Variable';
    font-size: 1.125rem;
    color: #666;
  }
}

.notificationSkeleton {
  height: 100px;
  animation: pulse 1.5s infinite;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 8px;
}

@keyframes pulse {
  0% {
    background-position: 200% 0;
  }

  100% {
    background-position: -200% 0;
  }
}
