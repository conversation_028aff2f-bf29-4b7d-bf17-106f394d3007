/* eslint-disable import/no-anonymous-default-export */
/**
 * Simple markdown parser for basic formatting
 * Handles: **bold**, *italic*, `code`, and line breaks
 */

export const parseMarkdown = (text) => {
  if (!text) return '';

  let parsed = text;

  // Handle line breaks
  parsed = parsed.replace(/\n/g, '<br />');

  // Handle bold text **text**
  parsed = parsed.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // Handle italic text *text*
  parsed = parsed.replace(/\*(.*?)\*/g, '<em>$1</em>');

  // Handle inline code `code`
  parsed = parsed.replace(/`(.*?)`/g, '<code style="background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; font-family: monospace;">$1</code>');

  // Handle headers ### Header
  parsed = parsed.replace(/^### (.*$)/gim, '<h3 style="margin: 16px 0 8px 0; font-weight: 600;">$1</h3>');
  parsed = parsed.replace(/^## (.*$)/gim, '<h2 style="margin: 20px 0 10px 0; font-weight: 600;">$1</h2>');
  parsed = parsed.replace(/^# (.*$)/gim, '<h1 style="margin: 24px 0 12px 0; font-weight: 600;">$1</h1>');

  // Handle bullet points - Item
  parsed = parsed.replace(/^- (.*$)/gim, '<li style="margin: 4px 0;">$1</li>');

  // Wrap consecutive list items in ul tags
  parsed = parsed.replace(/(<li.*?<\/li>(\s*<br\s*\/?>)*)+/g, (match) => {
    const cleanMatch = match.replace(/<br\s*\/?>/g, '');
    return `<ul style="margin: 8px 0; padding-left: 20px;">${cleanMatch}</ul>`;
  });

  return parsed;
};

/**
 * Component to render markdown text
 */
export const MarkdownText = ({ children, sx = {}, ...props }) => {
  const parsedContent = parseMarkdown(children);

  return (
    <div
      {...props}
      sx={{
        fontFamily: '"Recursive Variable", sans-serif',
        lineHeight: 1.5,
        '& strong': {
          fontWeight: 600
        },
        '& em': {
          fontStyle: 'italic'
        },
        '& h1, & h2, & h3': {
          fontFamily: '"Recursive Variable", sans-serif'
        },
        '& ul': {
          listStyleType: 'disc'
        },
        '& li': {
          listStyleType: 'disc'
        },
        ...sx
      }}
      dangerouslySetInnerHTML={{ __html: parsedContent }}
    />
  );
};

export default { parseMarkdown, MarkdownText };
