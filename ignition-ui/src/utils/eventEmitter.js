// Simple event emitter for global app events
class EventEmitter {
  constructor() {
    this.events = {};
  }

  on(event, callback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  off(event, callback) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(cb => cb !== callback);
  }

  emit(event, data) {
    if (!this.events[event]) return;
    this.events[event].forEach(callback => callback(data));
  }
}

// Create a global instance
const eventEmitter = new EventEmitter();

// Define event constants
export const EVENTS = {
  PLAN_DELETED: 'plan_deleted',
  PLAN_OPTED_OUT: 'plan_opted_out',
  TASK_UPDATED: 'task_updated',
  TASK_DELETED: 'task_deleted'
};

export default eventEmitter;
