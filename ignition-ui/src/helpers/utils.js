// API Configuration
export const APIURL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// Get headers for API requests
export const getHeaders = () => {
  const token = localStorage.getItem('access_token');
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Date formatting utilities
export const formatDate = (date) => {
  if (!date) return '';
  
  if (typeof date === 'string') {
    date = new Date(date);
  }
  
  if (!(date instanceof Date) || isNaN(date)) {
    return '';
  }
  
  return date.toISOString().split('T')[0];
};

// Parse date from string
export const parseDate = (dateString) => {
  if (!dateString) return null;
  
  const date = new Date(dateString);
  return isNaN(date) ? null : date;
};

// Format date for display
export const formatDisplayDate = (date) => {
  if (!date) return '';
  
  const parsedDate = typeof date === 'string' ? new Date(date) : date;
  
  if (!(parsedDate instanceof Date) || isNaN(parsedDate)) {
    return '';
  }
  
  return parsedDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Calculate date difference in days
export const dateDiffInDays = (date1, date2) => {
  const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
  const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
  
  if (!(d1 instanceof Date) || !(d2 instanceof Date) || isNaN(d1) || isNaN(d2)) {
    return 0;
  }
  
  const diffTime = Math.abs(d2 - d1);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// Add days to date
export const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

// Check if date is valid
export const isValidDate = (date) => {
  const d = typeof date === 'string' ? new Date(date) : date;
  return d instanceof Date && !isNaN(d);
};
