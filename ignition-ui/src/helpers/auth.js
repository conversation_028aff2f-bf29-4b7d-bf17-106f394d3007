import React from "react";
import Cookies from 'js-cookie';
import RouteWrapper from "RouteWrapper";
import { Route } from "react-router-dom";

export const isAuthenticated = () => {
  const accessToken = !!Cookies.get('accessToken');
  const refreshToken = !!Cookies.get("refreshToken");
  return accessToken && refreshToken;
};

export const getRoutes = (routes, layoutKey) => {
  return routes.map((prop, key) => {
    if (prop.layout === layoutKey) {
      return (
        <Route path={prop.path} key={key}
          element={
            <RouteWrapper
              element={prop.component}
              privateRoute={prop.private}
              layout={prop.layout}
            />
          }
        />
      );
    }
    return null;
  });
};
