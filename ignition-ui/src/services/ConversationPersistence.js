/**
 * ConversationPersistence Service
 * Manages chatbot conversation history using cookies for better persistence
 */

import Cookies from 'js-cookie';

class ConversationPersistence {
  constructor() {
    this.cookieName = 'chatbot_conversations';
    this.maxConversations = 50; // Limit to prevent cookie size issues
    this.maxAge = 30; // Days to keep conversations
  }

  /**
   * Get all conversations from cookies
   * @returns {Array} Array of conversation objects
   */
  getAllConversations() {
    try {
      const cookieData = Cookies.get(this.cookieName);
      if (!cookieData) return [];
      
      const conversations = JSON.parse(cookieData);
      
      // Filter out expired conversations
      const now = new Date();
      const validConversations = conversations.filter(conv => {
        const conversationDate = new Date(conv.timestamp);
        const daysDiff = (now - conversationDate) / (1000 * 60 * 60 * 24);
        return daysDiff <= this.maxAge;
      });

      return validConversations;
    } catch (error) {
      console.error('Error reading conversations from cookies:', error);
      return [];
    }
  }

  /**
   * Get conversations for a specific plan
   * @param {string|number} planId - The plan ID
   * @returns {Array} Array of conversation objects for the plan
   */
  getConversationsForPlan(planId) {
    const allConversations = this.getAllConversations();
    return allConversations.filter(conv => conv.planId === planId);
  }

  /**
   * Save a new conversation
   * @param {Object} conversation - Conversation object
   * @param {string|number} conversation.planId - Plan ID
   * @param {string} conversation.planName - Plan name
   * @param {string} conversation.message - User message
   * @param {string} conversation.response - AI response (optional)
   * @param {string} conversation.timestamp - ISO timestamp
   */
  saveConversation(conversation) {
    try {
      const allConversations = this.getAllConversations();
      
      // Add new conversation with unique ID
      const newConversation = {
        id: Date.now() + Math.random(),
        planId: conversation.planId,
        planName: conversation.planName,
        message: conversation.message,
        response: conversation.response || null,
        timestamp: conversation.timestamp || new Date().toISOString(),
        metadata: conversation.metadata || {}
      };

      allConversations.push(newConversation);

      // Keep only the most recent conversations to prevent cookie size issues
      const recentConversations = allConversations
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, this.maxConversations);

      // Save to cookies
      Cookies.set(this.cookieName, JSON.stringify(recentConversations), {
        expires: this.maxAge,
        secure: window.location.protocol === 'https:',
        sameSite: 'strict'
      });

      return newConversation;
    } catch (error) {
      console.error('Error saving conversation to cookies:', error);
      return null;
    }
  }

  /**
   * Update an existing conversation (e.g., add AI response)
   * @param {string|number} conversationId - Conversation ID
   * @param {Object} updates - Updates to apply
   */
  updateConversation(conversationId, updates) {
    try {
      const allConversations = this.getAllConversations();
      const conversationIndex = allConversations.findIndex(conv => conv.id === conversationId);
      
      if (conversationIndex !== -1) {
        allConversations[conversationIndex] = {
          ...allConversations[conversationIndex],
          ...updates,
          updatedAt: new Date().toISOString()
        };

        Cookies.set(this.cookieName, JSON.stringify(allConversations), {
          expires: this.maxAge,
          secure: window.location.protocol === 'https:',
          sameSite: 'strict'
        });

        return allConversations[conversationIndex];
      }
      
      return null;
    } catch (error) {
      console.error('Error updating conversation in cookies:', error);
      return null;
    }
  }

  /**
   * Delete conversations for a specific plan
   * @param {string|number} planId - Plan ID
   */
  deleteConversationsForPlan(planId) {
    try {
      const allConversations = this.getAllConversations();
      const filteredConversations = allConversations.filter(conv => conv.planId !== planId);
      
      Cookies.set(this.cookieName, JSON.stringify(filteredConversations), {
        expires: this.maxAge,
        secure: window.location.protocol === 'https:',
        sameSite: 'strict'
      });

      return true;
    } catch (error) {
      console.error('Error deleting conversations from cookies:', error);
      return false;
    }
  }

  /**
   * Clear all conversations
   */
  clearAllConversations() {
    try {
      Cookies.remove(this.cookieName);
      return true;
    } catch (error) {
      console.error('Error clearing conversations from cookies:', error);
      return false;
    }
  }

  /**
   * Get conversation statistics
   * @returns {Object} Statistics object
   */
  getStatistics() {
    const allConversations = this.getAllConversations();
    const planCounts = {};
    
    allConversations.forEach(conv => {
      planCounts[conv.planId] = (planCounts[conv.planId] || 0) + 1;
    });

    return {
      totalConversations: allConversations.length,
      planCounts,
      oldestConversation: allConversations.length > 0 
        ? Math.min(...allConversations.map(conv => new Date(conv.timestamp)))
        : null,
      newestConversation: allConversations.length > 0
        ? Math.max(...allConversations.map(conv => new Date(conv.timestamp)))
        : null
    };
  }

  /**
   * Export conversations for backup
   * @returns {string} JSON string of all conversations
   */
  exportConversations() {
    const allConversations = this.getAllConversations();
    return JSON.stringify(allConversations, null, 2);
  }

  /**
   * Import conversations from backup
   * @param {string} jsonData - JSON string of conversations
   * @returns {boolean} Success status
   */
  importConversations(jsonData) {
    try {
      const conversations = JSON.parse(jsonData);
      
      if (!Array.isArray(conversations)) {
        throw new Error('Invalid conversation data format');
      }

      // Validate conversation structure
      const validConversations = conversations.filter(conv => 
        conv.planId && conv.message && conv.timestamp
      );

      // Merge with existing conversations and remove duplicates
      const existingConversations = this.getAllConversations();
      const allConversations = [...existingConversations, ...validConversations];
      
      // Remove duplicates based on ID
      const uniqueConversations = allConversations.filter((conv, index, self) =>
        index === self.findIndex(c => c.id === conv.id)
      );

      // Keep only recent conversations
      const recentConversations = uniqueConversations
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, this.maxConversations);

      Cookies.set(this.cookieName, JSON.stringify(recentConversations), {
        expires: this.maxAge,
        secure: window.location.protocol === 'https:',
        sameSite: 'strict'
      });

      return true;
    } catch (error) {
      console.error('Error importing conversations:', error);
      return false;
    }
  }

  /**
   * Migrate from localStorage to cookies
   * This helps users transition from the old localStorage system
   */
  migrateFromLocalStorage() {
    try {
      // Check if there are conversations in localStorage
      const localStorageData = localStorage.getItem('agent_conversations');
      if (!localStorageData) return false;

      const localConversations = JSON.parse(localStorageData);
      if (!Array.isArray(localConversations) || localConversations.length === 0) {
        return false;
      }

      // Get existing cookie conversations
      const existingConversations = this.getAllConversations();
      
      // Merge and deduplicate
      const allConversations = [...existingConversations, ...localConversations];
      const uniqueConversations = allConversations.filter((conv, index, self) =>
        index === self.findIndex(c => 
          c.planId === conv.planId && 
          c.message === conv.message && 
          c.timestamp === conv.timestamp
        )
      );

      // Keep only recent conversations
      const recentConversations = uniqueConversations
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, this.maxConversations);

      // Save to cookies
      Cookies.set(this.cookieName, JSON.stringify(recentConversations), {
        expires: this.maxAge,
        secure: window.location.protocol === 'https:',
        sameSite: 'strict'
      });

      // Optionally clear localStorage after successful migration
      localStorage.removeItem('agent_conversations');
      
      console.log(`Migrated ${localConversations.length} conversations from localStorage to cookies`);
      return true;
    } catch (error) {
      console.error('Error migrating conversations from localStorage:', error);
      return false;
    }
  }
}

// Create singleton instance
const conversationPersistence = new ConversationPersistence();

// Auto-migrate from localStorage on first load
conversationPersistence.migrateFromLocalStorage();

export default conversationPersistence;
