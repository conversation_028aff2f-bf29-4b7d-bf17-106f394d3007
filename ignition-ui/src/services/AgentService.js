import axios from 'axios';
import { APIURL } from "helpers/constants";
import { getHeaders } from "helpers/functions";
import conversationPersistence from './ConversationPersistence';

class AgentService {
  constructor() {
    this.activeRequests = new Map(); // Track active requests by plan ID
    this.conversations = new Map(); // Cache conversations by plan ID
    this.listeners = new Map(); // Event listeners by plan ID
  }

  // Get conversations for a specific plan
  getConversations(planId) {
    if (this.conversations.has(planId)) {
      return this.conversations.get(planId);
    }

    // Load from cookies using the new persistence service
    const planConversations = conversationPersistence.getConversationsForPlan(planId);
    this.conversations.set(planId, planConversations);
    return planConversations;
  }

  // Update conversations for a specific plan
  updateConversations(planId, conversations) {
    this.conversations.set(planId, conversations);

    // Save each conversation using the new persistence service
    conversations.forEach(conv => {
      if (!conv.saved) {
        conversationPersistence.saveConversation({
          planId: planId,
          planName: conv.planName || 'Unknown Plan',
          message: conv.content || conv.message,
          response: conv.response,
          timestamp: conv.timestamp,
          metadata: {
            type: conv.type,
            id: conv.id,
            ...conv.metadata
          }
        });
        conv.saved = true; // Mark as saved to avoid duplicates
      }
    });

    // Notify listeners
    this.notifyListeners(planId, conversations);
  }

  // Add a conversation message
  addMessage(planId, message) {
    const conversations = this.getConversations(planId);
    const updatedConversations = [...conversations, message];
    this.updateConversations(planId, updatedConversations);
    return updatedConversations;
  }

  // Subscribe to conversation updates
  subscribe(planId, callback) {
    if (!this.listeners.has(planId)) {
      this.listeners.set(planId, new Set());
    }
    this.listeners.get(planId).add(callback);

    // Return unsubscribe function
    return () => {
      const planListeners = this.listeners.get(planId);
      if (planListeners) {
        planListeners.delete(callback);
        if (planListeners.size === 0) {
          this.listeners.delete(planId);
        }
      }
    };
  }

  // Notify all listeners for a plan
  notifyListeners(planId, conversations) {
    const planListeners = this.listeners.get(planId);
    if (planListeners) {
      planListeners.forEach(callback => {
        try {
          callback(conversations);
        } catch (error) {
          console.error('Error in agent service listener:', error);
        }
      });
    }
  }

  // Send message with background processing
  async sendMessage(planId, planSlug, message, onPlanUpdate) {
    // Check if there's already an active request for this plan
    if (this.activeRequests.has(planId)) {
      console.log('Request already in progress for plan:', planId);
      return false; // Request already in progress
    }

    try {
      // Mark request as active
      this.activeRequests.set(planId, true);

      // Add user message immediately
      const userMessage = {
        id: Date.now(),
        type: 'user',
        content: message.trim(),
        timestamp: new Date().toISOString()
      };
      this.addMessage(planId, userMessage);

      // Add loading message
      const loadingMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: 'Thinking...',
        timestamp: new Date().toISOString(),
        isLoading: true
      };
      this.addMessage(planId, loadingMessage);

      // Make API request
      const response = await axios.post(
        `${APIURL}/api/assistant/agent-chat`,
        {
          message: message.trim(),
          plan_slug: planSlug
        },
        {
          headers: getHeaders(),
          timeout: 30000 // 30 second timeout for faster responses
        }
      );

      const aiResponseData = response.data;

      // Remove loading message and add actual response
      const conversations = this.getConversations(planId);
      const conversationsWithoutLoading = conversations.filter(conv => !conv.isLoading);
      
      // Clean the AI response content by removing JSON blocks
      const cleanedContent = this.cleanAIResponseContent(aiResponseData.message || 'I received your message but had trouble generating a response.');

      const aiResponse = {
        id: Date.now() + 2,
        type: 'assistant',
        content: cleanedContent,
        timestamp: new Date().toISOString(),
        actions: aiResponseData.actions || [],
        metadata: aiResponseData.metadata || {}
      };

      this.updateConversations(planId, [...conversationsWithoutLoading, aiResponse]);

      // Actions are already executed in the backend during chat response
      // No need to re-execute them here to avoid duplicate execution
      if (aiResponseData.actions && aiResponseData.actions.length > 0) {
        console.log(`✅ ${aiResponseData.actions.length} actions were already executed in the backend`);

        // Just trigger plan update to refresh the UI
        if (onPlanUpdate) {
          console.log('Triggering plan update to refresh UI...');
          onPlanUpdate();
        }
      }

      return true;

    } catch (error) {
      console.error('Error in agent service sendMessage:', error);
      
      // Remove loading message and add error message
      const conversations = this.getConversations(planId);
      const conversationsWithoutLoading = conversations.filter(conv => !conv.isLoading);
      
      const errorResponse = {
        id: Date.now() + 3,
        type: 'assistant',
        content: `Sorry, I encountered an error while processing your request: ${error.response?.data?.error || error.message || 'Unknown error'}. Please try again.`,
        timestamp: new Date().toISOString(),
        isError: true
      };

      this.updateConversations(planId, [...conversationsWithoutLoading, errorResponse]);
      return false;

    } finally {
      // Mark request as complete
      this.activeRequests.delete(planId);
    }
  }

  // Execute AI actions
  async executeActions(planId, planSlug, actions, onPlanUpdate) {
    console.log(`Executing ${actions.length} AI actions:`, actions);
    
    let actionResults = [];
    for (const action of actions) {
      try {
        console.log('Executing action:', action);
        const result = await this.executeAIAction(planSlug, action);
        console.log('Action result:', result);
        actionResults.push(result);
      } catch (error) {
        console.error('Error executing AI action:', error);
        actionResults.push({
          success: false,
          error: error.message || 'Unknown error',
          action: action
        });
      }
    }

    // Process results
    const successfulActions = actionResults.filter(result => result.success);
    console.log(`${successfulActions.length} successful actions out of ${actionResults.length} total`);
    
    if (successfulActions.length > 0) {
      // Trigger plan refresh
      if (onPlanUpdate) {
        console.log('Triggering plan update');
        onPlanUpdate();
      }

      // Add success confirmation message
      const successMessage = {
        id: Date.now() + 4,
        type: 'assistant',
        content: this.formatSuccessMessage(successfulActions),
        timestamp: new Date().toISOString(),
        isSuccess: true
      };
      this.addMessage(planId, successMessage);
    }

    // Handle failed actions
    const failedActions = actionResults.filter(result => !result.success);
    if (failedActions.length > 0) {
      console.error('Failed actions:', failedActions);
      const errorMessage = {
        id: Date.now() + 5,
        type: 'assistant',
        content: `⚠️ **Note:** I encountered some issues executing the requested actions:\n\n${failedActions.map(result => `• ${result.error}`).join('\n')}\n\nPlease try rephrasing your request or check the project permissions.`,
        timestamp: new Date().toISOString(),
        isError: true
      };
      this.addMessage(planId, errorMessage);
    }
  }

  // Execute individual AI action
  async executeAIAction(planSlug, action) {
    try {
      console.log('Executing AI action:', {
        action: action.action || action.type,
        plan_slug: planSlug,
        data: action.data
      });

      const response = await axios.post(
        `${APIURL}/api/assistant/plan-action`,
        {
          action: action.action || action.type,
          plan_slug: planSlug,
          data: action.data || {},
          message: `AI-generated action: ${action.action || action.type}`
        },
        { headers: getHeaders() }
      );

      console.log('AI action response:', response.data);

      return {
        success: true,
        data: response.data,
        action: action
      };
    } catch (error) {
      console.error('Error executing AI action:', error);
      
      let errorMessage = 'Unknown error occurred';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage,
        action: action,
        statusCode: error.response?.status
      };
    }
  }

  // Format success message (clean, user-friendly)
  formatSuccessMessage(successfulActions) {
    const changes = successfulActions.map(result => {
      const action = result.action;
      const actionType = action.action || action.type;
      const resultData = result.data?.result || {};

      switch(actionType) {
        case 'add_milestone':
          return `Added milestone: "${resultData.milestone_name || action.data?.name}"`;
        case 'add_task':
          return `Added task: "${resultData.task_name || action.data?.name}"`;
        case 'add_subtask':
          return `Added subtask: "${resultData.subtask_name || action.data?.name}"`;
        case 'update_milestone':
          return `Updated milestone: "${resultData.milestone_name || action.data?.name}"`;
        case 'update_task':
          return `Updated task: "${resultData.task_name || action.data?.name}"`;
        case 'complete_task':
          return `Completed task: "${resultData.task_name || action.data?.name}"`;
        case 'delete_milestone':
          return `Deleted milestone: "${resultData.milestone_name || action.data?.name}"`;
        case 'delete_task':
          return `Deleted task: "${resultData.task_name || action.data?.name}"`;
        case 'delete_subtask':
          return `Deleted subtask: "${resultData.subtask_name || action.data?.name}"`;
        default:
          return `Completed action: ${actionType}`;
      }
    });

    return `✅ **All done!** I've successfully made the following changes to your project:\n\n${changes.map(change => `• ${change}`).join('\n')}\n\n**Check it out** in your project details above! 🎉`;
  }

  // Clean AI response content by removing technical JSON blocks
  cleanAIResponseContent(content) {
    if (!content) return content;

    // Remove JSON code blocks (```json ... ```)
    let cleanedContent = content.replace(/```json\s*\{[\s\S]*?\}\s*```/gi, '');

    // Remove standalone JSON objects that might appear in responses
    cleanedContent = cleanedContent.replace(/\{[\s\S]*?"actions"[\s\S]*?\}/gi, '');

    // Remove any remaining code blocks
    cleanedContent = cleanedContent.replace(/```[\s\S]*?```/gi, '');

    // Clean up extra whitespace and newlines
    cleanedContent = cleanedContent.replace(/\n\s*\n\s*\n/g, '\n\n');
    cleanedContent = cleanedContent.trim();

    // If the content is now empty or too short, provide a default message
    if (!cleanedContent || cleanedContent.length < 10) {
      return "I've analyzed your request and will make the necessary changes to your project.";
    }

    return cleanedContent;
  }

  // Check if request is in progress
  isRequestInProgress(planId) {
    return this.activeRequests.has(planId);
  }

  // Get loading state
  getLoadingState(planId) {
    const conversations = this.getConversations(planId);
    return conversations.some(conv => conv.isLoading);
  }
}

// Create singleton instance
const agentService = new AgentService();
export default agentService;
