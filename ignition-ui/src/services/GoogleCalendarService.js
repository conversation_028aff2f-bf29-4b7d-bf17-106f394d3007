import { APIURL, getHeaders } from 'helpers/utils';

class GoogleCalendarService {
  constructor() {
    this.isInitialized = false;
    this.gapi = null;
  }

  // Initialize Google Calendar API
  async initialize() {
    if (this.isInitialized) return true;

    try {
      // Load Google API script if not already loaded
      if (!window.gapi) {
        await this.loadGoogleAPI();
      }

      await new Promise((resolve) => {
        window.gapi.load('client:auth2', resolve);
      });

      await window.gapi.client.init({
        apiKey: process.env.REACT_APP_GOOGLE_API_KEY,
        clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID,
        discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest'],
        scope: 'https://www.googleapis.com/auth/calendar.readonly https://www.googleapis.com/auth/calendar.events'
      });

      this.gapi = window.gapi;
      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize Google Calendar API:', error);
      return false;
    }
  }

  // Load Google API script dynamically
  loadGoogleAPI() {
    return new Promise((resolve, reject) => {
      if (window.gapi) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  // Check if user is signed in
  isSignedIn() {
    if (!this.isInitialized || !this.gapi) return false;
    return this.gapi.auth2.getAuthInstance().isSignedIn.get();
  }

  // Sign in to Google Calendar
  async signIn() {
    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) return false;
    }

    try {
      const authInstance = this.gapi.auth2.getAuthInstance();
      await authInstance.signIn();
      return true;
    } catch (error) {
      console.error('Google Calendar sign-in failed:', error);
      return false;
    }
  }

  // Sign out from Google Calendar
  async signOut() {
    if (!this.isInitialized || !this.gapi) return;

    try {
      const authInstance = this.gapi.auth2.getAuthInstance();
      await authInstance.signOut();
    } catch (error) {
      console.error('Google Calendar sign-out failed:', error);
    }
  }

  // Get calendar events for a date range
  async getEvents(startDate, endDate) {
    if (!this.isSignedIn()) {
      throw new Error('Not signed in to Google Calendar');
    }

    try {
      const response = await this.gapi.client.calendar.events.list({
        calendarId: 'primary',
        timeMin: startDate.toISOString(),
        timeMax: endDate.toISOString(),
        showDeleted: false,
        singleEvents: true,
        orderBy: 'startTime'
      });

      return response.result.items || [];
    } catch (error) {
      console.error('Failed to fetch calendar events:', error);
      return [];
    }
  }

  // Get free time slots for a specific date
  async getFreeTimeSlots(date, workingHours = { start: 9, end: 17 }) {
    if (!this.isSignedIn()) {
      return this.generateDefaultTimeSlots(date, workingHours);
    }

    try {
      const startOfDay = date.startOf('day').add(workingHours.start, 'hour');
      const endOfDay = date.startOf('day').add(workingHours.end, 'hour');

      const events = await this.getEvents(startOfDay.toDate(), endOfDay.toDate());
      
      // Generate time slots (1-hour intervals)
      const timeSlots = [];
      let currentTime = startOfDay;

      // Helper function to check event conflicts
      const checkEventConflict = (events, slotStart, slotEnd) => {
        return events.some(event => {
          const eventStart = new Date(event.start.dateTime || event.start.date);
          const eventEnd = new Date(event.end.dateTime || event.end.date);

          return (
            slotStart.toDate() < eventEnd &&
            slotEnd.toDate() > eventStart
          );
        });
      };

      // Helper function to find conflicting event
      const findConflictingEvent = (events, slotStart, slotEnd) => {
        return events.find(event => {
          const eventStart = new Date(event.start.dateTime || event.start.date);
          const eventEnd = new Date(event.end.dateTime || event.end.date);
          return slotStart.toDate() < eventEnd && slotEnd.toDate() > eventStart;
        });
      };

      while (currentTime.isBefore(endOfDay)) {
        const slotEnd = currentTime.add(1, 'hour');

        // Check if this slot conflicts with any calendar event
        const hasConflict = checkEventConflict(events, currentTime, slotEnd);

        timeSlots.push({
          start: currentTime.format('HH:mm'),
          end: slotEnd.format('HH:mm'),
          startTime: currentTime,
          endTime: slotEnd,
          available: !hasConflict,
          event: hasConflict ? findConflictingEvent(events, currentTime, slotEnd) : null
        });

        currentTime = slotEnd;
      }

      return timeSlots;
    } catch (error) {
      console.error('Failed to get free time slots:', error);
      return this.generateDefaultTimeSlots(date, workingHours);
    }
  }

  // Generate default time slots when Google Calendar is not available
  generateDefaultTimeSlots(date, workingHours = { start: 9, end: 17 }) {
    const timeSlots = [];
    const startOfDay = date.startOf('day').add(workingHours.start, 'hour');
    const endOfDay = date.startOf('day').add(workingHours.end, 'hour');
    
    let currentTime = startOfDay;

    while (currentTime.isBefore(endOfDay)) {
      const slotEnd = currentTime.add(1, 'hour');
      
      timeSlots.push({
        start: currentTime.format('HH:mm'),
        end: slotEnd.format('HH:mm'),
        startTime: currentTime,
        endTime: slotEnd,
        available: true,
        event: null
      });

      currentTime = slotEnd;
    }

    return timeSlots;
  }

  // Create a calendar event for a task
  async createTaskEvent(task, startTime, endTime) {
    if (!this.isSignedIn()) {
      throw new Error('Not signed in to Google Calendar');
    }

    try {
      const event = {
        summary: task.name,
        description: `Task from project: ${task.milestone_name}\n\nTask Details:\n${task.description || 'No description available'}`,
        start: {
          dateTime: startTime.toISOString(),
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
        },
        colorId: this.getTaskColorId(task.status)
      };

      const response = await this.gapi.client.calendar.events.insert({
        calendarId: 'primary',
        resource: event
      });

      return response.result;
    } catch (error) {
      console.error('Failed to create calendar event:', error);
      throw error;
    }
  }

  // Get color ID based on task status
  getTaskColorId(status) {
    const colorMap = {
      1: '5', // Pending - Yellow
      2: '9', // In Progress - Blue
      3: '10', // Completed - Green
      4: '11'  // On Hold - Red
    };
    return colorMap[status] || '5';
  }

  // Update task dates in the backend
  async updateTaskSchedule(taskSlug, startDate, endDate, startTime = null, endTime = null) {
    try {
      const updateData = {
        start_date: startDate.format('YYYY-MM-DD'),
        end_date: endDate.format('YYYY-MM-DD')
      };

      if (startTime && endTime) {
        updateData.start_time = startTime.format('HH:mm:ss');
        updateData.end_time = endTime.format('HH:mm:ss');
      }

      const response = await fetch(`${APIURL}/api/tasks/${taskSlug}/update`, {
        method: 'PUT',
        headers: {
          ...getHeaders(),
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updateData)
      });

      if (!response.ok) {
        throw new Error('Failed to update task schedule');
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to update task schedule:', error);
      throw error;
    }
  }

  // Schedule task to a specific time slot
  async scheduleTaskToTimeSlot(task, date, timeSlot) {
    try {
      // Update task dates in backend
      await this.updateTaskSchedule(
        task.slug,
        date,
        date,
        timeSlot.startTime,
        timeSlot.endTime
      );

      // Create Google Calendar event if signed in
      if (this.isSignedIn()) {
        await this.createTaskEvent(task, timeSlot.startTime, timeSlot.endTime);
      }

      return true;
    } catch (error) {
      console.error('Failed to schedule task:', error);
      throw error;
    }
  }

  // Get calendar integration status
  getIntegrationStatus() {
    return {
      initialized: this.isInitialized,
      signedIn: this.isSignedIn(),
      available: !!window.gapi
    };
  }
}

// Create singleton instance
const googleCalendarService = new GoogleCalendarService();

export default googleCalendarService;
