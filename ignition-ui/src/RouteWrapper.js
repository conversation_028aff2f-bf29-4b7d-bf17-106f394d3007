import React from "react";
import PropTypes from "prop-types";
import { Navigate } from "react-router-dom";
import { isAuthenticated } from "helpers/auth";
import { errorSnackbar } from 'components/Snackbar/index';
import { PUBLIC_PAGE_KEY } from "helpers/constants";

//--------------------------------------------------------------------------------------------------

const RouteWrapper = ({ element, privateRoute, layout }) => {
  const loggedIn = isAuthenticated();

  if (layout === PUBLIC_PAGE_KEY) {
    return element;
  }

  if (privateRoute && !loggedIn) {
    errorSnackbar("You need to be logged in to access this page. Redirecting to login...");
    return <Navigate to="/login" replace />;
  }

  if (!privateRoute && loggedIn) {
    return <Navigate to="/d/" replace />;
  }

  return element;
};

RouteWrapper.propTypes = {
  element: PropTypes.node.isRequired,
  privateRoute: PropTypes.bool,
  optionalAuth: PropTypes.bool,
};

export default RouteWrapper;
