import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  Divider,
  Modal,
  IconButton,
  Paper,
  CircularProgress
} from '@mui/material';
import { styled } from '@mui/material/styles';
import Iconify from 'components/Iconify/index';

const StyledCard = styled(Card)(({ theme, selected }) => ({
  cursor: 'pointer',
  transition: 'transform 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease',
  border: selected ? `3px solid #F0A500` : `2px solid ${theme.palette.divider}`,
  borderRadius: '16px',
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  boxShadow: selected
    ? '0 8px 24px rgba(240, 165, 0, 0.15)'
    : '0 2px 8px rgba(0, 0, 0, 0.08)',
  transform: selected ? 'translateY(-3px)' : 'none',
  willChange: 'transform, box-shadow',
  '&:hover': {
    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.12)',
    transform: 'translateY(-2px)',
    borderColor: selected ? '#F0A500' : '#bbb',
  }
}));

const SelectButton = styled(Button)(({ theme }) => ({
  backgroundColor: '#F0A500',
  color: 'white',
  fontWeight: 700,
  padding: '16px 32px',
  borderRadius: '12px',
  fontSize: '1.1rem',
  fontFamily: '"Recursive Variable", sans-serif',
  textTransform: 'none',
  letterSpacing: '0.5px',
  boxShadow: '0 4px 16px rgba(240, 165, 0, 0.3)',
  '&:hover': {
    backgroundColor: '#D4940A',
    boxShadow: '0 6px 16px rgba(240, 165, 0, 0.35)',
  },
  transition: 'background-color 0.2s ease, box-shadow 0.2s ease',
  willChange: 'background-color, box-shadow'
}));

const ModalContainer = styled(Paper)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '86vw',
  maxWidth: '900px',
  maxHeight: '90vh',
  overflow: 'auto',
  borderRadius: '20px',
  padding: '3vw',
  paddingRight: '1vw',
  outline: 'none',
  boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
}));

const PlanOptionsSelector = ({
  planOptions,
  selectedOption,
  onSelectOption,
  onConfirmSelection,
  loading = false
}) => {
  const [detailModalOpen, setDetailModalOpen] = useState(false);
  const [modalOption, setModalOption] = useState(null);

  const handleCardClick = (option) => {
    console.log("🃏 Plan option card clicked:", option);
    setModalOption(option);
    setDetailModalOpen(true);
  };

  const handleSelectFromModal = () => {
    console.log("🎯 Plan option selected from modal:", modalOption);
    onSelectOption(modalOption);
    setDetailModalOpen(false);
    // Directly trigger plan creation with the modalOption
    console.log("⏱️ Triggering plan creation in 100ms...");
    setTimeout(() => {
      console.log("🚀 Calling onConfirmSelection with modalOption...");
      // Pass the modalOption directly to avoid state timing issues
      if (typeof onConfirmSelection === 'function') {
        onConfirmSelection(modalOption);
      }
    }, 100);
  };

  const handleCloseModal = () => {
    setDetailModalOpen(false);
    setModalOption(null);
  };

  if (!planOptions || planOptions.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <Typography variant="h6" color="text.secondary">
          No plan options available
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      width: '100vw',
      maxWidth: 'none',
      mx: 0,
      px: 2,
      position: 'relative',
      left: '50%',
      right: '50%',
      marginLeft: '-50vw',
      marginRight: '-50vw',
      fontFamily: '"Recursive Variable", sans-serif'
    }}>
      <Box sx={{ textAlign: 'center', mb: 5 }}>
        <Typography
          variant="h3"
          sx={{
            fontWeight: 700,
            mb: 2,
            fontSize: '2.5rem',
            fontFamily: '"Recursive Variable", sans-serif',
            color: '#333',
            letterSpacing: '-0.5px'
          }}
        >
          Choose Your Plan Approach
        </Typography>
        <Typography
          variant="h6"
          color="text.secondary"
          sx={{
            fontSize: '1.1rem',
            maxWidth: '800px',
            mx: 'auto',
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 400,
            lineHeight: 1.5,
            color: '#666'
          }}
        >
          Click on any plan to view detailed milestones and tasks, then select your preferred approach
        </Typography>
      </Box>

      <Box
        className="responsive-container layout-stable"
        sx={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'center',
          alignItems: 'stretch',
          gap: 3,
          mb: 4,
          flexWrap: 'nowrap',
          width: '100%',
          maxWidth: '1200px',
          mx: 'auto',
          // Mobile responsiveness
          '@media (max-width: 768px)': {
            flexDirection: 'column',
            gap: 2,
            alignItems: 'center'
          },
          '@media (max-width: 480px)': {
            gap: 1.5,
            px: 1
          }
        }}>
        {planOptions.map((option, index) => (
          <Box key={option.option_number || index} sx={{
            width: 'calc(33.333% - 16px)',
            minWidth: '280px',
            maxWidth: '360px',
            flex: '0 1 auto',
            // Mobile responsiveness
            '@media (max-width: 768px)': {
              width: '100%',
              maxWidth: '500px',
              minWidth: 'auto'
            },
            '@media (max-width: 480px)': {
              maxWidth: '100%'
            }
          }}>
            <StyledCard
              selected={selectedOption?.option_number === option.option_number}
              onClick={() => handleCardClick(option)}
              className="plan-option-card performance-optimized"
            >
              <CardContent sx={{ p: 3, height: '100%', display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="h6" sx={{
                    fontWeight: 700,
                    mb: 2,
                    minHeight: '3rem',
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: '1.3rem',
                    lineHeight: 1.2,
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: '#333'
                  }}>
                    {option.name}
                  </Typography>
                  <Chip
                    label={option.strategy || 'Strategy'}
                    size="small"
                    sx={{
                      backgroundColor: '#F0A500',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '0.85rem',
                      height: '28px',
                      px: 1.5,
                      fontFamily: '"Recursive Variable", sans-serif',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px'
                    }}
                  />
                </Box>

                <Typography variant="body2" color="text.secondary" sx={{
                  mb: 2,
                  minHeight: '3.5rem',
                  lineHeight: 1.6,
                  fontSize: '0.95rem',
                  fontFamily: '"Recursive Variable", sans-serif',
                  color: '#666'
                }}>
                  {option.description}
                </Typography>

                <Divider sx={{ mb: 2 }} />

                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle1" sx={{
                    fontWeight: 600,
                    mb: 1.5,
                    fontSize: '1rem',
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: '#333'
                  }}>
                    Milestones Overview:
                  </Typography>

                  <List dense sx={{ py: 0 }}>
                    {option.milestones?.slice(0, 5).map((milestone, idx) => (
                      <ListItem key={idx} sx={{ px: 0, py: 0.5 }}>
                        <ListItemText
                          primary={milestone.name}
                          primaryTypographyProps={{
                            variant: 'body2',
                            sx: {
                              fontSize: '0.85rem',
                              fontWeight: 500,
                              lineHeight: 1.2,
                              fontFamily: '"Recursive Variable", sans-serif',
                              color: '#555'
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>

                <Box sx={{ mt: 2, textAlign: 'center', pt: 2, borderTop: '1px solid #f0f0f0' }}>
                  <Typography variant="caption" color="text.secondary" sx={{
                    fontWeight: 500,
                    fontSize: '0.8rem',
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: '#888'
                  }}>
                    {option.milestones?.length || 0} milestones • {
                      option.milestones?.reduce((total, m) => total + (m.tasks?.length || 0), 0) || 0
                    } tasks
                  </Typography>
                  <Typography variant="caption" display="block" color="primary" sx={{
                    mt: 1,
                    fontWeight: 600,
                    fontSize: '0.85rem',
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: '#F0A500'
                  }}>
                    Click to view details
                  </Typography>
                </Box>
              </CardContent>
            </StyledCard>
          </Box>
        ))}
      </Box>

      {selectedOption && (
        <Box sx={{ textAlign: 'center', mt: 2 }}>
          <SelectButton
            onClick={() => {
              console.log("🎯 Create Plan button clicked!");
              console.log("📋 Selected option:", selectedOption);
              console.log("🔄 Loading state:", loading);
              onConfirmSelection();
            }}
            disabled={loading}
            size="large"
            startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <Iconify icon="material-symbols:rocket-launch" width={24} height={24} />}
          >
            {loading ? 'Creating Plan...' : `Create Plan: ${selectedOption.name}`}
          </SelectButton>
          <Typography variant="body2" color="text.secondary" sx={{
            mt: 3,
            maxWidth: '600px',
            mx: 'auto',
            fontFamily: '"Recursive Variable", sans-serif',
            fontSize: '1rem',
            lineHeight: 1.5,
            color: '#666'
          }}>
            We'll generate detailed subtasks for each task in your selected plan approach
          </Typography>
        </Box>
      )}

      {/* Detailed Plan Modal */}
      <Modal
        open={detailModalOpen}
        onClose={handleCloseModal}
        aria-labelledby="plan-detail-modal"
      >
        <ModalContainer sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
          {modalOption && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 6, gap: 2 }}>
                <Box sx={{ flex: 1, minWidth: 0 }}> {/* Allow shrinking and prevent overflow */}
                  <Typography variant="h4" sx={{
                    fontWeight: 700,
                    mb: 2,
                    fontSize: '2rem',
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: '#333',
                    wordWrap: 'break-word',
                    overflowWrap: 'break-word'
                  }}>
                    {modalOption.name}
                  </Typography>
                  <Chip
                    label={modalOption.strategy || 'Strategy'}
                    sx={{
                      backgroundColor: '#F0A500',
                      color: 'white',
                      fontWeight: 600,
                      fontSize: '1rem',
                      height: 'auto', // Allow height to adjust
                      minHeight: '36px',
                      px: 2,
                      py: 1,
                      fontFamily: '"Recursive Variable", sans-serif',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                      maxWidth: '100%',
                      '& .MuiChip-label': {
                        whiteSpace: 'normal',
                        wordWrap: 'break-word',
                        overflowWrap: 'break-word'
                      }
                    }}
                  />
                </Box>
                <IconButton onClick={handleCloseModal} sx={{ color: '#666', p: 2, flexShrink: 0 }}>
                  <Iconify icon="material-symbols:close" width={28} height={28} />
                </IconButton>
              </Box>

              <Typography variant="h6" color="text.secondary" sx={{
                mb: 6,
                lineHeight: 1.6,
                fontSize: '1.2rem',
                fontFamily: '"Recursive Variable", sans-serif',
                color: '#666'
              }}>
                {modalOption.description}
              </Typography>

              <Divider sx={{ mb: 6 }} />

              <Typography variant="h5" sx={{
                fontWeight: 600,
                mb: 4,
                fontSize: '1.5rem',
                fontFamily: '"Recursive Variable", sans-serif',
                color: '#333'
              }}>
                Complete Plan Structure
              </Typography>

              {modalOption.milestones?.map((milestone, milestoneIndex) => (
                <Box key={milestoneIndex} sx={{ mb: 6 }}>
                  <Typography variant="h5" sx={{
                    fontWeight: 600,
                    mb: 3,
                    color: '#F0A500',
                    fontSize: '1.4rem',
                    fontFamily: '"Recursive Variable", sans-serif'
                  }}>
                    Milestone {milestoneIndex + 1}: {milestone.name}
                  </Typography>
                  <Typography variant="body1" color="text.secondary" sx={{
                    mb: 3,
                    fontSize: '1.1rem',
                    lineHeight: 1.6,
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: '#666'
                  }}>
                    {milestone.description}
                  </Typography>
                  <Typography variant="body2" sx={{
                    display: 'inline-block',
                    backgroundColor: '#f5f5f5',
                    px: 3,
                    py: 1,
                    borderRadius: '16px',
                    mb: 4,
                    fontSize: '1rem',
                    fontWeight: 500,
                    fontFamily: '"Recursive Variable", sans-serif',
                    color: '#555'
                  }}>
                    Duration: {milestone.estimated_duration}
                  </Typography>

                  <List sx={{ pl: 3 }}>
                    {milestone.tasks?.map((task, taskIndex) => (
                      <ListItem key={taskIndex} sx={{ py: 1.5 }}>
                        <ListItemText
                          primary={`${taskIndex + 1}. ${task.name}`}
                          secondary={task.description}
                          primaryTypographyProps={{
                            variant: 'body1',
                            sx: {
                              fontWeight: 500,
                              fontSize: '1.1rem',
                              mb: 1,
                              fontFamily: '"Recursive Variable", sans-serif',
                              color: '#333'
                            }
                          }}
                          secondaryTypographyProps={{
                            variant: 'body2',
                            sx: {
                              color: '#666',
                              fontSize: '1rem',
                              lineHeight: 1.5,
                              fontFamily: '"Recursive Variable", sans-serif'
                            }
                          }}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              ))}

              <Box sx={{
                display: 'flex',
                justifyContent: 'center',
                gap: 3,
                mt: 6,
                pt: 4,
                borderTop: '2px solid #f0f0f0'
              }}>
                <Button
                  variant="outlined"
                  onClick={handleCloseModal}
                  sx={{
                    borderColor: '#ccc',
                    color: '#666',
                    px: 4,
                    py: 2,
                    fontSize: '1.1rem',
                    borderRadius: '12px',
                    fontFamily: '"Recursive Variable", sans-serif',
                    fontWeight: 600,
                    textTransform: 'none'
                  }}
                >
                  Close
                </Button>
                <SelectButton
                  onClick={handleSelectFromModal}
                  startIcon={<Iconify icon="material-symbols:check-circle" width={24} height={24} />}
                  sx={{ fontSize: '1.1rem', px: 4, py: 2 }}
                >
                  Select This Plan
                </SelectButton>
              </Box>
            </>
          )}
        </ModalContainer>
      </Modal>
    </Box>
  );
};

export default PlanOptionsSelector;
