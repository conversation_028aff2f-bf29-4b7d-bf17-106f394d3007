import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { Box, Dialog, DialogTitle, Button, Grid, IconButton } from '@mui/material';
import { successSnackbar, errorSnackbar } from 'components/Snackbar/index';
import { mainYellowColor } from "helpers/constants";
import { assignUserToTask } from './services';
import InputSelectBase from 'components/Input/InputSelectBase';
import Iconify from 'components/Iconify/index';
import styles from './styles.module.scss';

const AssignComponent = ({ open, onClose, users, selectedUserIds = [], onUsersSelect, task }) => {
  const [selectedUsers, setSelectedUsers] = useState(selectedUserIds);
  const currentUser = useSelector((state) => state.user);

  const uniqueUsers = [
    currentUser,
    ...users.filter((user) => user.invited_user_info?.id !== currentUser.id)
  ];

  const handleAddUser = () => {
    setSelectedUsers([...selectedUsers, '']);
  };

  const handleRemoveUser = (index) => {
    const newSelectedUsers = selectedUsers.filter((_, i) => i !== index);
    setSelectedUsers(newSelectedUsers);
  };

  const handleUserChange = (index, userId) => {
    const newSelectedUsers = [...selectedUsers];
    newSelectedUsers[index] = userId;
    setSelectedUsers(newSelectedUsers);
  };

  const handleSave = async () => {
    console.log("Inside handleSave, selectedUsers:", selectedUsers);
    try {
      for (const userId of selectedUsers.filter((userId) => userId)) {
        await assignUserToTask(task.slug, userId);
      }
      onUsersSelect(selectedUsers.filter((userId) => userId));
      onClose();
      successSnackbar('Users assigned successfully!');
    } catch (error) {
      console.error('Failed to assign users to task:', error);
      errorSnackbar('Failed to assign users.');
    }
  };

  const getFilteredOptions = () => {
    return uniqueUsers.map((user) => ({
      value: user.invited_user_info?.id || user.id,
      label: `${user?.invited_user_info?.first_name || user.first_name} ${user?.invited_user_info?.last_name || user.last_name}`,
      avatar: user.invited_user_info?.avatar || user.avatar,
    }));
  };

  return (
    <Dialog open={open} onClose={onClose} fullWidth maxWidth="sm">
      <DialogTitle sx={{ paddingBottom: 0, fontSize: '1.5rem', fontFamily: 'Recursive Variable' }}>
        Select Assign Members
      </DialogTitle>
      <Box sx={{ padding: '0 24px' }}>
        {selectedUsers.map((userId, index) => (
          <Grid container spacing={0} alignItems="center" key={userId || `new-${index}`}>
            <Grid item xs={11}>
              <Box sx={{ marginTop: '10px' }}>Assign Member {index + 1}</Box>
              <InputSelectBase
                name={`user_select_${index}`}
                keyword={`user_select_${index}`}
                handleChange={(keyword, value) => handleUserChange(index, value)}
                value={userId}
                options={getFilteredOptions()}
                showAvatar={true}
              />
            </Grid>
            <Grid item xs={1} className='d-flex justify-content-end'>
              {selectedUsers.length > 1 && (
                <IconButton onClick={() => handleRemoveUser(index)} aria-label="delete" sx={{ color: 'black' }}>
                  <Iconify icon="ant-design:delete-outlined" width={24} height={24} color={mainYellowColor} />
                </IconButton>
              )}
            </Grid>
          </Grid>
        ))}
      </Box>
      <Box className='d-flex justify-content-between align-items-center' sx={{ padding: '18px 24px 24px' }}>
        <Box>
          <Button onClick={handleAddUser} variant="outlined" className={styles.addBtnBase}>
            <span><Iconify icon="gala:add" width={24} height={24} color={mainYellowColor} /></span>
            Add
          </Button>
        </Box>
        <Box>
          <Button onClick={handleSave} variant="outlined" className={styles.addBtnBase}>
            <span><Iconify icon="material-symbols:save" width={24} height={24} color={mainYellowColor} /></span>
            Save
          </Button>
        </Box>
      </Box>
    </Dialog>
  );
};

export default AssignComponent;
