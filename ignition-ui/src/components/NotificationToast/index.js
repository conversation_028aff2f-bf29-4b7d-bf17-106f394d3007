import React, { useState, useEffect } from 'react';
import { Box, Typography, IconButton } from '@mui/material';
import Iconify from 'components/Iconify';
import { mainYellowColor } from 'helpers/constants';

const NotificationToast = ({ message, type = 'success', duration = 5000, onClose }) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      if (onClose) onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const handleClose = () => {
    setVisible(false);
    if (onClose) onClose();
  };

  if (!visible) return null;

  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          backgroundColor: '#4caf50',
          icon: 'material-symbols:check-circle',
          color: 'white'
        };
      case 'error':
        return {
          backgroundColor: '#f44336',
          icon: 'material-symbols:error',
          color: 'white'
        };
      case 'warning':
        return {
          backgroundColor: mainYellowColor,
          icon: 'material-symbols:warning',
          color: '#333'
        };
      default:
        return {
          backgroundColor: '#2196f3',
          icon: 'material-symbols:info',
          color: 'white'
        };
    }
  };

  const config = getTypeConfig();

  return (
    <Box
      sx={{
        position: 'fixed',
        top: '20px',
        right: '20px',
        zIndex: 9999,
        backgroundColor: config.backgroundColor,
        color: config.color,
        borderRadius: '12px',
        padding: '16px 20px',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        minWidth: '300px',
        maxWidth: '500px',
        animation: 'slideInRight 0.3s ease-out',
        '@keyframes slideInRight': {
          from: {
            transform: 'translateX(100%)',
            opacity: 0
          },
          to: {
            transform: 'translateX(0)',
            opacity: 1
          }
        }
      }}
    >
      <Iconify 
        icon={config.icon} 
        width={24} 
        height={24} 
        color={config.color}
      />
      
      <Typography
        variant="body1"
        sx={{
          fontFamily: '"Recursive Variable", sans-serif',
          fontWeight: 600,
          flex: 1,
          fontSize: '0.95rem'
        }}
      >
        {message}
      </Typography>
      
      <IconButton
        size="small"
        onClick={handleClose}
        sx={{
          color: config.color,
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.1)'
          }
        }}
      >
        <Iconify icon="material-symbols:close" width={18} height={18} />
      </IconButton>
    </Box>
  );
};

// Helper function to show toast notifications
export const showToast = (message, type = 'success', duration = 5000) => {
  // Create a container for the toast
  const toastContainer = document.createElement('div');
  document.body.appendChild(toastContainer);

  // Import React and ReactDOM
  import('react-dom/client').then(({ createRoot }) => {
    const root = createRoot(toastContainer);
    
    const handleClose = () => {
      root.unmount();
      document.body.removeChild(toastContainer);
    };

    root.render(
      <NotificationToast
        message={message}
        type={type}
        duration={duration}
        onClose={handleClose}
      />
    );
  });
};

export default NotificationToast;
