/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  TextField,
  InputAdornment,
  Alert
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from 'helpers/constants';
import conversationPersistence from 'services/ConversationPersistence';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const ConversationHistory = ({ open, onClose, planId = null }) => {
  const [conversations, setConversations] = useState([]);
  const [filteredConversations, setFilteredConversations] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [statistics, setStatistics] = useState({});

  useEffect(() => {
    loadConversations();
  }, [planId]);

  useEffect(() => {
    filterConversations();
  }, [conversations, searchQuery]);

  const loadConversations = () => {
    try {
      const allConversations = planId
        ? conversationPersistence.getConversationsForPlan(planId)
        : conversationPersistence.getAllConversations();

      setConversations(allConversations);
      setStatistics(conversationPersistence.getStatistics());
    } catch (error) {
      console.error('Error loading conversations:', error);
      setConversations([]);
    }
  };

  const filterConversations = () => {
    if (!searchQuery.trim()) {
      setFilteredConversations(conversations);
      return;
    }

    const filtered = conversations.filter(conv =>
      conv.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv.planName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (conv.response && conv.response.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    setFilteredConversations(filtered);
  };

  const handleClearAllConversations = () => {
    if (window.confirm('Are you sure you want to clear all conversation history? This action cannot be undone.')) {
      if (planId) {
        conversationPersistence.deleteConversationsForPlan(planId);
      } else {
        conversationPersistence.clearAllConversations();
      }
      loadConversations();
    }
  };

  const handleExportConversations = () => {
    const exportData = conversationPersistence.exportConversations();
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `chatbot-conversations-${dayjs().format('YYYY-MM-DD')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const groupConversationsByDate = (conversations) => {
    const groups = {};
    conversations.forEach(conv => {
      const date = dayjs(conv.timestamp).format('YYYY-MM-DD');
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(conv);
    });
    return groups;
  };

  const conversationGroups = groupConversationsByDate(filteredConversations);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px',
          maxHeight: '80vh'
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="material-symbols:chat-bubble-outline" width={24} height={24} color={mainYellowColor} />
            <Typography variant="h6" sx={{ fontFamily: '"Recursive Variable", sans-serif', fontWeight: 600 }}>
              {planId ? 'Plan Conversation History' : 'All Conversation History'}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Export conversations">
              <IconButton onClick={handleExportConversations} size="small">
                <Iconify icon="material-symbols:download" width={20} height={20} />
              </IconButton>
            </Tooltip>
            <Tooltip title="Clear all conversations">
              <IconButton onClick={handleClearAllConversations} size="small" color="error">
                <Iconify icon="material-symbols:delete-outline" width={20} height={20} />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent>
        {/* Statistics */}
        <Box sx={{ mb: 2 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              Total conversations: {statistics.totalConversations} | 
              Plans with conversations: {Object.keys(statistics.planCounts || {}).length}
            </Typography>
          </Alert>
        </Box>

        {/* Search */}
        <TextField
          fullWidth
          placeholder="Search conversations..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Iconify icon="material-symbols:search" width={20} height={20} />
              </InputAdornment>
            ),
          }}
          sx={{ mb: 2 }}
        />

        {/* Conversations List */}
        <Box sx={{ maxHeight: '400px', overflow: 'auto' }}>
          {Object.keys(conversationGroups).length === 0 ? (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <Iconify icon="material-symbols:chat-bubble-outline" width={48} height={48} color="#ccc" />
              <Typography variant="body1" color="text.secondary" sx={{ mt: 2 }}>
                {searchQuery ? 'No conversations found matching your search.' : 'No conversations yet.'}
              </Typography>
            </Box>
          ) : (
            Object.keys(conversationGroups)
              .sort((a, b) => dayjs(b).diff(dayjs(a)))
              .map(date => (
                <Box key={date} sx={{ mb: 3 }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontWeight: 600,
                      color: mainYellowColor,
                      mb: 1
                    }}
                  >
                    {dayjs(date).format('MMMM DD, YYYY')}
                  </Typography>
                  <List dense>
                    {conversationGroups[date].map((conversation) => (
                      <ListItem
                        key={conversation.id}
                        sx={{
                          border: '1px solid #f0f0f0',
                          borderRadius: '8px',
                          mb: 1,
                          backgroundColor: '#fafafa'
                        }}
                      >
                        <ListItemText
                          primary={
                            <Box>
                              <Typography
                                variant="body2"
                                sx={{
                                  fontFamily: '"Recursive Variable", sans-serif',
                                  fontWeight: 500,
                                  mb: 0.5
                                }}
                              >
                                {conversation.message}
                              </Typography>
                              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                                <Chip
                                  label={conversation.planName}
                                  size="small"
                                  sx={{
                                    backgroundColor: `${mainYellowColor}20`,
                                    color: mainYellowColor,
                                    fontSize: '0.7rem'
                                  }}
                                />
                                <Typography variant="caption" color="text.secondary">
                                  {dayjs(conversation.timestamp).fromNow()}
                                </Typography>
                              </Box>
                            </Box>
                          }
                        />
                        <ListItemSecondaryAction>
                          <Tooltip title="View details">
                            <IconButton
                              size="small"
                              onClick={() => setSelectedConversation(conversation)}
                            >
                              <Iconify icon="material-symbols:visibility" width={16} height={16} />
                            </IconButton>
                          </Tooltip>
                        </ListItemSecondaryAction>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              ))
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} variant="outlined">
          Close
        </Button>
      </DialogActions>

      {/* Conversation Detail Dialog */}
      {selectedConversation && (
        <Dialog
          open={!!selectedConversation}
          onClose={() => setSelectedConversation(null)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            <Typography variant="h6" sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
              Conversation Details
            </Typography>
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">Plan:</Typography>
              <Typography variant="body1">{selectedConversation.planName}</Typography>
            </Box>
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">Message:</Typography>
              <Typography variant="body1">{selectedConversation.message}</Typography>
            </Box>
            {selectedConversation.response && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">Response:</Typography>
                <Typography variant="body1">{selectedConversation.response}</Typography>
              </Box>
            )}
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">Timestamp:</Typography>
              <Typography variant="body1">
                {dayjs(selectedConversation.timestamp).format('MMMM DD, YYYY [at] HH:mm')}
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSelectedConversation(null)}>Close</Button>
          </DialogActions>
        </Dialog>
      )}
    </Dialog>
  );
};

export default ConversationHistory;
