/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useRef, useEffect } from 'react';
import { FormControl, Typography, Box, TextField, InputAdornment, Popper, Paper, List, ListItem, ListItemText, ClickAwayListener } from '@mui/material';
import { parsePhoneNumber, getCountries, getCountryCallingCode } from 'libphonenumber-js';
import Iconify from 'components/Iconify/index';
import styles from './PhoneNumberInput.module.scss';

// Popular countries to show at the top
const POPULAR_COUNTRIES = ['VN', 'US', 'GB', 'CN', 'JP', 'KR', 'TH', 'SG', 'MY', 'ID'];

const PhoneNumberInput = ({
  value,
  onChange,
  error,
  placeholder = "Phone Number",
  required = false,
  disabled = false,
  className = ""
}) => {
  const primaryColor = '#9E9991';
  const [selectedCountry, setSelectedCountry] = useState('VN');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [initialized, setInitialized] = useState(false);
  const anchorRef = useRef(null);
  const inputRef = useRef(null);

  // Get country data
  const getCountryData = () => {
    const countries = getCountries();
    const countryData = countries.map(country => {
      try {
        const callingCode = getCountryCallingCode(country);
        return {
          code: country,
          callingCode: `+${callingCode}`,
          name: getCountryName(country),
          flag: getCountryFlag(country)
        };
      } catch (error) {
        return null;
      }
    }).filter(Boolean);

    // Sort: popular countries first, then alphabetically
    const popular = countryData.filter(c => POPULAR_COUNTRIES.includes(c.code));
    const others = countryData.filter(c => !POPULAR_COUNTRIES.includes(c.code))
      .sort((a, b) => a.name.localeCompare(b.name));

    return [...popular, ...others];
  };

  const getCountryName = (countryCode) => {
    const names = {
      'VN': 'Vietnam',
      'US': 'United States',
      'GB': 'United Kingdom',
      'CN': 'China',
      'JP': 'Japan',
      'KR': 'South Korea',
      'TH': 'Thailand',
      'SG': 'Singapore',
      'MY': 'Malaysia',
      'ID': 'Indonesia',
      'AU': 'Australia',
      'CA': 'Canada',
      'DE': 'Germany',
      'FR': 'France',
      'IT': 'Italy',
      'ES': 'Spain',
      'BR': 'Brazil',
      'IN': 'India',
      'RU': 'Russia'
    };
    return names[countryCode] || countryCode;
  };

  const getCountryFlag = (countryCode) => {
    const flags = {
      'VN': '🇻🇳',
      'US': '🇺🇸',
      'GB': '🇬🇧',
      'CN': '🇨🇳',
      'JP': '🇯🇵',
      'KR': '🇰🇷',
      'TH': '🇹🇭',
      'SG': '🇸🇬',
      'MY': '🇲🇾',
      'ID': '🇮🇩',
      'AU': '🇦🇺',
      'CA': '🇨🇦',
      'DE': '🇩🇪',
      'FR': '🇫🇷',
      'IT': '🇮🇹',
      'ES': '🇪🇸',
      'BR': '🇧🇷',
      'IN': '🇮🇳',
      'RU': '🇷🇺'
    };
    return flags[countryCode] || '🌍';
  };

  const countries = getCountryData();
  const selectedCountryData = countries.find(c => c.code === selectedCountry);

  // Filter countries based on search term
  const filteredCountries = countries.filter(country =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    country.callingCode.includes(searchTerm) ||
    country.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Initialize with default country code and parse existing value
  useEffect(() => {
    if (!initialized) {
      // Auto-populate with default country code on first load
      const defaultCountryData = getCountryData().find(c => c.code === 'VN');
      if (!value && defaultCountryData) {
        onChange(defaultCountryData.callingCode);
      }
      setInitialized(true);
    }

    if (value) {
      try {
        const parsed = parsePhoneNumber(value);
        if (parsed) {
          setSelectedCountry(parsed.country || 'VN');
          setPhoneNumber(parsed.nationalNumber || '');
        } else {
          // If value is just a country code, don't set phone number
          if (value.startsWith('+') && value.length <= 4) {
            const countryData = getCountryData().find(c => c.callingCode === value);
            if (countryData) {
              setSelectedCountry(countryData.code);
              setPhoneNumber('');
            }
          } else {
            setPhoneNumber(value);
          }
        }
      } catch (error) {
        // If parsing fails, keep the raw value
        setPhoneNumber(value);
      }
    }
  }, [value, initialized, onChange]);

  const handleCountrySelect = (country) => {
    setSelectedCountry(country.code);
    setDropdownOpen(false);
    setSearchTerm('');

    // Update the full phone number
    const fullNumber = phoneNumber ? `${country.callingCode}${phoneNumber}` : '';
    onChange(fullNumber);

    // Focus back to phone input
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handlePhoneNumberChange = (event) => {
    const newNumber = event.target.value.replace(/\D/g, ''); // Only digits
    setPhoneNumber(newNumber);

    // Update the full phone number
    const fullNumber = newNumber ? `${selectedCountryData?.callingCode || '+84'}${newNumber}` : '';
    onChange(fullNumber);
  };

  const handleDropdownToggle = () => {
    if (!disabled) {
      setDropdownOpen(!dropdownOpen);
      setSearchTerm('');
    }
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  return (
    <FormControl fullWidth className={className}>
      <Box className={`${styles.phoneInputContainer} ${error ? styles.error : ''}`}>
        {/* Country Code Selector */}
        <Box
          ref={anchorRef}
          className={styles.countrySelector}
          onClick={handleDropdownToggle}
        >
          <span className={styles.countryFlag}>
            {selectedCountryData?.flag || '🌍'}
          </span>
          <span className={styles.countryCode}>
            {selectedCountryData?.callingCode || '+84'}
          </span>
          <Iconify
            icon={dropdownOpen ? "mdi:chevron-up" : "mdi:chevron-down"}
            width={16}
            color={primaryColor}
            className={styles.dropdownArrow}
          />
        </Box>

        {/* Phone Number Input */}
        <Box className={styles.phoneNumberInput}>
          <Box className={styles.phoneIconContainer}>
            <Iconify
              icon="material-symbols:phone"
              width={24}
              color={primaryColor}
            />
          </Box>
          <TextField
            ref={inputRef}
            value={phoneNumber}
            onChange={handlePhoneNumberChange}
            placeholder={placeholder}
            disabled={disabled}
            variant="standard"
            className={styles.phoneNumberField}
            InputProps={{
              disableUnderline: true,
            }}
            sx={{
              flex: 1,
              '& .MuiInputBase-input': {
                padding: '0 14px',
                fontSize: '1.125rem',
                fontFamily: 'Recursive Variable, sans-serif',
                height: '40px',
                display: 'flex',
                alignItems: 'center',
                border: 'none',
                outline: 'none',
                '&::placeholder': {
                  color: '#888888',
                  opacity: 1,
                }
              }
            }}
          />
        </Box>
      </Box>

      {/* Custom Select2-style Dropdown */}
      <Popper
        open={dropdownOpen}
        anchorEl={anchorRef.current}
        placement="bottom-start"
        className={styles.dropdownPopper}
        modifiers={[
          {
            name: 'offset',
            options: {
              offset: [0, 4],
            },
          },
        ]}
      >
        <ClickAwayListener onClickAway={() => setDropdownOpen(false)}>
          <Paper className={styles.dropdownPaper}>
            {/* Search Input */}
            <Box className={styles.searchContainer}>
              <TextField
                value={searchTerm}
                onChange={handleSearchChange}
                placeholder="Search countries..."
                variant="standard"
                size="small"
                InputProps={{
                  disableUnderline: true,
                  startAdornment: (
                    <InputAdornment position="start">
                      <Iconify icon="mdi:magnify" width={16} color="#888" />
                    </InputAdornment>
                  ),
                }}
                className={styles.searchInput}
                sx={{
                  '& .MuiInputBase-input': {
                    padding: '8px 12px',
                    fontSize: '0.875rem',
                    fontFamily: 'Recursive Variable, sans-serif',
                  }
                }}
              />
            </Box>

            {/* Countries List */}
            <List className={styles.countriesList}>
              {filteredCountries.slice(0, 10).map((country) => (
                <ListItem
                  key={country.code}
                  button
                  onClick={() => handleCountrySelect(country)}
                  className={`${styles.countryItem} ${selectedCountry === country.code ? styles.selected : ''}`}
                >
                  <span className={styles.countryFlag}>{country.flag}</span>
                  <ListItemText
                    primary={
                      <Box className={styles.countryInfo}>
                        <span className={styles.countryName}>{country.name}</span>
                        <span className={styles.countryCallingCode}>{country.callingCode}</span>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
              {filteredCountries.length === 0 && (
                <ListItem className={styles.noResults}>
                  <ListItemText primary="No countries found" />
                </ListItem>
              )}
              {filteredCountries.length > 10 && (
                <ListItem className={styles.moreResults}>
                  <ListItemText primary={`${filteredCountries.length - 10} more results...`} />
                </ListItem>
              )}
            </List>
          </Paper>
        </ClickAwayListener>
      </Popper>

      {error && (
        <Typography className={styles.errorMessage} variant="body2">
          {error}
        </Typography>
      )}
      <Typography className={styles.helperText} variant="caption">
        Please enter your phone number in international format (e.g., +84 123 456 789)
      </Typography>
    </FormControl>
  );
};

export default PhoneNumberInput;
