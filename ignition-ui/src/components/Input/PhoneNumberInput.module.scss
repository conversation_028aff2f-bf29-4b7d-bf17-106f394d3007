.phoneInputContainer {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
  height: 42px;

  &.error {
    .phoneNumberInput {
      border-color: #d32f2f;

      &:focus-within {
        border-color: #d32f2f;
        border-width: 1px;
      }
    }
  }
}

.phoneNumberInput {
  flex: 1;
  display: flex;
  align-items: center;
  border: 1px solid #9E9991;
  border-radius: 5px;
  background-color: #FFFFFF;
  transition: border-color 0.3s ease;
  height: 100%;

  &:hover {
    border-color: #9E9991;
  }

  &:focus-within {
    border-color: #9E9991;
    border-width: 1px;
  }
}

.phoneIconContainer {
  display: flex;
  align-items: center;
  padding: 0 8px 0 12px;
  border-right: none;
  background-color: rgba(158, 153, 145, 0.05);
  border-radius: 5px 0 0 5px;
  height: 100%;
}

/* Country Selector */
.countrySelector {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border: 1px solid #9E9991;
  border-radius: 5px;
  background-color: #FFFFFF;
  transition: all 0.2s ease;
  min-width: 100px;
  height: 100%;

  &:hover {
    border-color: #9E9991;
    background-color: rgba(158, 153, 145, 0.05);
  }
}

.countryFlag {
  font-size: 16px;
  margin-right: 6px;
  line-height: 1;
}

.countryCode {
  font-family: 'Recursive Variable', sans-serif;
  font-size: 0.95rem;
  color: #2b2b2b;
  margin-right: 4px;
  font-weight: 500;
}

.dropdownArrow {
  transition: transform 0.2s ease;
}

.phoneNumberField {
  flex: 1;
  border: none !important;
}

.errorMessage {
  color: #d32f2f !important;
  font-size: 1rem !important;
  margin-top: 5px !important;
  margin-left: 5px !important;
  font-family: 'Recursive Variable', sans-serif !important;
}

.helperText {
  color: #888888 !important;
  font-size: 0.875rem !important;
  margin-top: 4px !important;
  margin-left: 5px !important;
  font-family: 'Recursive Variable', sans-serif !important;
  font-style: italic;
}

/* Select2-style Dropdown */
.dropdownPopper {
  z-index: 1300 !important;
  width: 320px !important;
}

.dropdownPaper {
  border: 1px solid #9E9991 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 20px rgba(158, 153, 145, 0.15) !important;
  background-color: #FFFFFF !important;
  overflow: hidden;
  max-height: 280px;
}

.searchContainer {
  padding: 8px 12px;
  border-bottom: 1px solid rgba(158, 153, 145, 0.15);
  background-color: rgba(158, 153, 145, 0.02);
}

.searchInput {
  width: 100%;

  .MuiInputBase-root {
    background-color: #FFFFFF;
    border: 1px solid rgba(158, 153, 145, 0.3);
    border-radius: 4px;
    padding: 4px 8px;

    &:hover {
      border-color: #9E9991;
    }

    &.Mui-focused {
      border-color: #9E9991;
      box-shadow: 0 0 0 2px rgba(158, 153, 145, 0.1);
    }
  }
}

.countriesList {
  padding: 0 !important;
  max-height: 200px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(158, 153, 145, 0.05);
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(158, 153, 145, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(158, 153, 145, 0.5);
    }
  }
}

.countryItem {
  padding: 10px 16px !important;
  border-bottom: 1px solid rgba(158, 153, 145, 0.08);
  cursor: pointer !important;
  transition: background-color 0.15s ease;

  &:hover {
    background-color: rgba(158, 153, 145, 0.08) !important;
  }

  &.selected {
    background-color: rgba(158, 153, 145, 0.12) !important;
    border-left: 3px solid #9E9991;
  }

  &:last-child {
    border-bottom: none;
  }
}

.countryInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.countryName {
  font-family: 'Recursive Variable', sans-serif;
  font-size: 0.9rem;
  color: #2b2b2b;
  font-weight: 400;
}

.countryCallingCode {
  font-family: 'Recursive Variable', sans-serif;
  font-size: 0.85rem;
  color: #9E9991;
  font-weight: 500;
}

.noResults {
  padding: 16px !important;
  text-align: center;
  color: #888888;
  font-style: italic;
}

.moreResults {
  padding: 8px 16px !important;
  background-color: rgba(158, 153, 145, 0.05);
  color: #9E9991;
  font-size: 0.8rem;
  text-align: center;
  border-top: 1px solid rgba(158, 153, 145, 0.1);
}
