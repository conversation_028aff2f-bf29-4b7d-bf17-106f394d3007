import { TextField } from '@mui/material';
import React from 'react';

const CustomDatePicker = ({
  value,
  onChange,
  label,
  error,
  helperText,
  name,
  values,
  setValues,
  labelName,
  size,
  ...props
}) => {
  // Handle the case where props are passed as values/setValues pattern
  const currentValue = values ? values[name] : value;

  const handleChange = (newValue) => {
    if (setValues && name) {
      setValues(prev => ({
        ...prev,
        [name]: newValue
      }));
    } else if (onChange) {
      onChange(newValue);
    }
  };

  return (
    <TextField
      type="date"
      value={currentValue || ''}
      onChange={(e) => handleChange(e.target.value)}
      label={labelName || label}
      error={error}
      helperText={helperText}
      size={size}
      fullWidth
      InputLabelProps={{
        shrink: true,
      }}
      sx={{
        '& .MuiOutlinedInput-root': {
          '& fieldset': {
            borderWidth: '1px',
            borderColor: '#9E9991',
            borderRadius: '5px',
            fontFamily: 'Recursive Variable',
          },
          '&:hover fieldset': {
            borderWidth: '1px',
            borderColor: '#9E9991',
            fontFamily: 'Recursive Variable',
          },
          '&.Mui-focused fieldset': {
            borderWidth: '1px',
            borderColor: '#9E9991',
            fontFamily: 'Recursive Variable',
          },
        },
        '& .MuiInputBase-input': {
          fontFamily: 'Recursive Variable',
          fontSize: '1rem',
        },
        '& .MuiInputLabel-root': {
          fontFamily: 'Recursive Variable',
        },
        backgroundColor: '#FFFFFF'
      }}
      {...props}
    />
  );
};

export default CustomDatePicker;
