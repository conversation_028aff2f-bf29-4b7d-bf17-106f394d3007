/* DHTMLX Gantt Custom Styling */

/* Container styling */
.gantt_container {
  font-family: 'Recursive Variable', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif !important;
  border-radius: 12px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

/* Grid styling */
.gantt_grid_scale {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-bottom: 2px solid #dee2e6 !important;
  font-weight: 600 !important;
  color: #495057 !important;
}



.gantt_row {
  border-bottom: 1px solid #f1f3f4 !important;
}

.gantt_row:hover {
  background-color: #f8f9fa !important;
}

.gantt_row.odd {
  background-color: #fdfdfd !important;
}

.gantt_row.odd:hover {
  background-color: #f8f9fa !important;
}

/* Timeline styling */
.gantt_task_scale {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #5a67d8 !important;
  height: 60px !important; /* Tăng height để chứa 2 dòng */
}

.gantt_scale_cell {
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
  text-align: center !important;
  padding: 2px 4px !important; /* Giảm padding để chứa 2 dòng */
  min-width: 30px !important; /* Tăng từ 20px lên 30px */
  width: 45px !important; /* Đặt width cố định */
  max-width: 50px !important; /* Tăng max-width */
  font-size: 12px !important; /* Tăng font size lên */
  vertical-align: middle !important; /* Căn giữa theo chiều dọc */
  height: 60px !important; /* Đặt height để chứa 2 dòng */
}

/* Task bars styling */
.gantt_task_line {
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  border: none !important;
  height: 24px !important;
  line-height: 24px !important;
}

/* Milestone tasks (project type) */
.milestone-task .gantt_task_line {
  background: linear-gradient(135deg, #FFD700, #FFA500) !important;
  border: 2px solid #FF8C00 !important;
  border-radius: 8px !important;
  font-weight: 700 !important;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4) !important;
  height: 28px !important;
  line-height: 28px !important;
}

.milestone-task .gantt_task_line::before {
  content: "🏆";
  margin-right: 4px;
}

/* Regular tasks */
.regular-task .gantt_task_line {
  border-radius: 6px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.regular-task .gantt_task_line::before {
  content: "📋";
  margin-right: 4px;
}

/* Task status colors */
.task-pending .gantt_task_line {
  background: linear-gradient(135deg, #FFA726, #FF9800) !important;
  color: white !important;
}

.task-in_progress .gantt_task_line {
  background: linear-gradient(135deg, #42A5F5, #2196F3) !important;
  color: white !important;
}

.task-completed .gantt_task_line {
  background: linear-gradient(135deg, #66BB6A, #4CAF50) !important;
  color: white !important;
}

.task-on_hold .gantt_task_line {
  background: linear-gradient(135deg, #EF5350, #F44336) !important;
  color: white !important;
}

/* Progress bar styling */
.gantt_task_progress {
  background: rgba(255, 255, 255, 0.3) !important;
  border-radius: 4px !important;
  height: 20px !important;
  top: 2px !important;
}

.gantt_task_progress_text {
  color: white !important;
  font-weight: 600 !important;
  font-size: 11px !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Tree icons */
.gantt_tree_icon {
  color: #667eea !important;
  font-weight: bold !important;
}

.gantt_tree_icon.gantt_open {
  color: #764ba2 !important;
}

/* Grid cells */
.gantt_cell {
  padding: 8px 12px !important;
  border-right: 1px solid #f1f3f4 !important;
  vertical-align: middle !important;
  line-height: 2 !important;
}

.gantt_tree_content {
  font-weight: 600 !important; /* Tăng font weight để rõ hơn */
  color: #2c3e50 !important; /* Đổi màu đậm hơn để rõ hơn */
  text-shadow: none !important; /* Loại bỏ text shadow */
}

/* Milestone rows in grid */
.milestone-task .gantt_tree_content {
  font-weight: 700 !important;
  color: #FF8C00 !important;
  text-shadow: none !important; /* Loại bỏ text shadow */
}

/* Task text styling */
.gantt_task_text {
  font-size: 12px !important;
  font-weight: 500 !important;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2) !important;
  padding: 0 8px !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Scrollbars */
.gantt_layout_cell::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.gantt_layout_cell::-webkit-scrollbar-track {
  background: #f1f3f4 !important;
  border-radius: 4px !important;
}

.gantt_layout_cell::-webkit-scrollbar-thumb {
  background: #c1c7cd !important;
  border-radius: 4px !important;
}

.gantt_layout_cell::-webkit-scrollbar-thumb:hover {
  background: #a8b2ba !important;
}

/* Today line */
.gantt_today {
  background: rgba(255, 87, 34, 0.3) !important;
  border-left: 2px solid #FF5722 !important;
}

/* Weekend styling */
.gantt_task_cell.weekend {
  background-color: rgba(0, 0, 0, 0.02) !important;
}

/* Timeline cells - tăng width */
.gantt_task_cell {
  min-width: 30px !important; /* Tăng từ 20px lên 30px */
  width: 45px !important; /* Đặt width cố định */
  max-width: 50px !important; /* Tăng từ 30px lên 50px */
}

/* Grid header cells - cải thiện text visibility */
.gantt_grid_head_cell {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  border-right: 1px solid #dee2e6 !important;
  font-weight: 700 !important; /* Tăng font weight */
  color: #2c3e50 !important; /* Màu đậm hơn */
  text-align: center !important;
  padding: 8px !important;
  text-shadow: none !important;
}

/* Hover effects */
.gantt_task_line:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  transition: all 0.2s ease !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .gantt_grid_head_cell {
    font-size: 12px !important;
    padding: 6px 4px !important;
  }
  
  .gantt_cell {
    padding: 6px 8px !important;
    font-size: 12px !important;
  }
  
  .gantt_task_text {
    font-size: 11px !important;
  }
}

/* Animation for task bars */
.gantt_task_line {
  transition: all 0.2s ease !important;
}

/* Focus states */
.gantt_selected .gantt_task_line {
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3) !important;
}

/* Loading state */
.gantt_loading {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(4px) !important;
}

.gantt_loading_text {
  color: #667eea !important;
  font-weight: 600 !important;
  font-family: 'Recursive Variable' !important;
}
