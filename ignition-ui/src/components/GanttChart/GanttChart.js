import React from 'react';
import { Box } from '@mui/material';
import DHtmlxGanttChart from './DHtmlxGanttChart';

const GanttChart = ({ projectData, onTaskClick, calculateMilestoneProgress, calculateTaskProgress }) => {
  return (
    <Box sx={{ width: '100%' }}>
      <DHtmlxGanttChart
        projectData={projectData}
        onTaskClick={onTaskClick}
        calculateMilestoneProgress={calculateMilestoneProgress}
        calculateTaskProgress={calculateTaskProgress}
      />
    </Box>
  );
};

export default GanttChart;
