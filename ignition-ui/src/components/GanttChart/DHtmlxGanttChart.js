import React, { useRef, useEffect, useMemo, useCallback } from 'react';
import { gantt } from 'dhtmlx-gantt';
import 'dhtmlx-gantt/codebase/dhtmlxgantt.css';
import './DHtmlxGanttChart.css';
import { Box, Card, CardContent, Typography, Chip } from '@mui/material';

const DHtmlxGanttChart = ({ projectData, onTaskClick, calculateMilestoneProgress, calculateTaskProgress }) => {
  const ganttContainer = useRef();

  // Get task color based on status
  const getTaskColor = useCallback((status) => {
    const colorMap = {
      'pending': '#FFA726',
      'in_progress': '#42A5F5',
      'completed': '#66BB6A',
      'on_hold': '#EF5350'
    };
    return colorMap[status] || '#90A4AE';
  }, []);

  // Convert project data to DHTMLX Gantt format
  const ganttData = useMemo(() => {
    if (!projectData) return { data: [], links: [] };

    const tasks = [];
    const links = [];
    let taskId = 1;

    // Helper function to format dates
    const formatDate = (dateStr) => {
      if (!dateStr) return new Date();

      // Parse date string and create date at local midnight to avoid timezone issues
      const dateParts = dateStr.split('-');
      if (dateParts.length === 3) {
        const year = parseInt(dateParts[0]);
        const month = parseInt(dateParts[1]) - 1; // Month is 0-indexed
        const day = parseInt(dateParts[2]);
        return new Date(year, month, day);
      }

      const date = new Date(dateStr);
      return isNaN(date.getTime()) ? new Date() : date;
    };

    // Process milestones and their tasks
    if (projectData.milestones && projectData.milestones.length > 0) {
      projectData.milestones.forEach((milestone, milestoneIndex) => {
        // Add milestone as parent task
        const milestoneStartDate = milestone.tasks && milestone.tasks.length > 0 
          ? new Date(Math.min(...milestone.tasks.map(t => formatDate(t.start_date).getTime())))
          : formatDate(milestone.start_date);
        
        const milestoneEndDate = milestone.tasks && milestone.tasks.length > 0
          ? new Date(Math.max(...milestone.tasks.map(t => formatDate(t.end_date).getTime())))
          : formatDate(milestone.end_date);

        // Calculate duration correctly: inclusive of both start and end dates
        const milestoneDurationDays = Math.ceil((milestoneEndDate - milestoneStartDate) / (1000 * 60 * 60 * 24)) + 1;

        // Calculate milestone progress using the provided function
        const milestoneProgress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : (milestone.progress || 0);

        const milestoneTask = {
          id: taskId++,
          text: `${milestone.name || milestone.title}`,
          start_date: milestoneStartDate,
          end_date: milestoneEndDate,
          duration: Math.max(1, milestoneDurationDays),
          progress: milestoneProgress / 100, // Convert to decimal for DHTMLX Gantt
          type: gantt.config.types.project,
          open: true,
          color: '#FFD700',
          originalData: milestone
        };

        tasks.push(milestoneTask);
        const milestoneParentId = milestoneTask.id;

        // Add tasks under milestone
        if (milestone.tasks && milestone.tasks.length > 0) {
          milestone.tasks.forEach((task, taskIndex) => {
            const taskStartDate = formatDate(task.start_date);
            const taskEndDate = formatDate(task.end_date);

            // Calculate duration correctly: inclusive of both start and end dates
            // Same day = 1 day, next day = 2 days, etc.
            const durationDays = Math.ceil((taskEndDate - taskStartDate) / (1000 * 60 * 60 * 24)) + 1;

            // Calculate task progress using the provided function
            const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : (task.progress || 0);

            const ganttTask = {
              id: taskId++,
              text: `${task.name || task.title}`,
              start_date: taskStartDate,
              end_date: taskEndDate,
              duration: Math.max(1, durationDays),
              progress: taskProgress / 100, // Convert to decimal for DHTMLX Gantt
              parent: milestoneParentId,
              type: gantt.config.types.task,
              color: getTaskColor(task.status),
              originalData: task
            };

            tasks.push(ganttTask);
          });
        }
      });
    }

    return { data: tasks, links: links };
  }, [projectData, getTaskColor, calculateMilestoneProgress, calculateTaskProgress]);

  // Initialize DHTMLX Gantt
  useEffect(() => {
    // Configure DHTMLX Gantt
    gantt.config.date_format = "%Y-%m-%d";
    gantt.config.scale_unit = "day";
    gantt.config.date_scale = "%d %M";
    gantt.config.scale_height = 60; // Tăng height để chứa 2 dòng
    gantt.config.row_height = 40;
    gantt.config.bar_height = 24;
    gantt.config.grid_width = 800; // Tăng gấp đôi từ 400 lên 800
    gantt.config.drag_links = false;
    gantt.config.drag_progress = false;
    gantt.config.drag_resize = false;
    gantt.config.drag_move = false;
    gantt.config.details_on_dblclick = false;
    gantt.config.show_progress = true;
    gantt.config.open_tree_initially = true;
    gantt.config.autofit = false; // Tắt autofit để kiểm soát width
    gantt.config.min_column_width = 30; // Tăng width tối thiểu từ 15 lên 30 (tăng 100%)
    gantt.config.column_width = 45; // Đặt width mặc định cho timeline columns
    gantt.config.autosize = "y"; // Tự động điều chỉnh height theo nội dung
    gantt.config.autosize_min_width = 300;

    // Configure columns - Scientific compact layout
    gantt.config.columns = [
      {
        name: "text",
        label: "Task",
        width: 500, // Reduced from 500 to 200 for compact view
        tree: true,
        template: function(task) {
          const taskName = task.text || task.name || '';
          const typeIcon = task.type === gantt.config.types.project ? '🎯' : '📋';
          return `${typeIcon} ${taskName}`;
        }
      },
      {
        name: "start_date",
        label: "Start",
        width: 100,
        align: "center",
        template: function(task) {
          if (task.start_date) {
            return gantt.date.date_to_str("%m/%d")(task.start_date);
          }
          return "";
        }
      },
      {
        name: "duration",
        label: "Days",
        width: 60,
        align: "center",
        template: function(task) {
          // Compact duration display
          if (task.start_date && task.end_date) {
            const startDate = new Date(task.start_date);
            const endDate = new Date(task.end_date);
            const timeDiff = endDate.getTime() - startDate.getTime();
            const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
            const duration = Math.max(1, daysDiff + 1);
            return `${duration}d`;
          }
          return task.duration ? `${task.duration}d` : '1d';
        }
      },
      {
        name: "progress",
        label: "Progress",
        width: 100,
        align: "center",
        template: function(task) {
          const progress = Math.round((task.progress || 0) * 100);
          return `${progress}%`;
        }
      }
    ];

    // Custom task template
    gantt.templates.task_class = function(start, end, task) {
      let css = "";
      if (task.type === gantt.config.types.project) {
        css += " milestone-task";
      } else {
        css += " regular-task";
      }

      if (task.originalData && task.originalData.status) {
        css += " task-" + task.originalData.status;
      }

      return css;
    };

    // Custom progress bar template - hide progress text on timeline bars
    gantt.templates.progress_text = function(start, end, task) {
      return ""; // Return empty string to hide progress text on timeline bars
    };

    // Hide task text on timeline bars to prevent overlap
    gantt.templates.task_text = function(start, end, task) {
      return ""; // Return empty string to hide task text on timeline bars
    };

    // Custom duration template to ensure correct inclusive calculation
    gantt.templates.grid_date_format = function(date, column) {
      if (column === "duration") {
        return gantt.date.date_to_str("%d %M")(date);
      }
      return gantt.date.date_to_str(gantt.config.date_format)(date);
    };

    // Override duration calculation to be inclusive
    gantt.attachEvent("onTaskLoading", function(task) {
      if (task.start_date && task.end_date) {
        const startDate = new Date(task.start_date);
        const endDate = new Date(task.end_date);

        // Calculate inclusive duration: same day = 1, next day = 2, etc.
        const timeDiff = endDate.getTime() - startDate.getTime();
        const daysDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));

        // Ensure minimum 1 day duration for same-day tasks
        task.duration = Math.max(1, daysDiff + 1);
      }
      return true;
    });

    // Custom date scale template để hiển thị ngày thành 2 dòng
    gantt.templates.date_scale = function(date) {
      const day = gantt.date.date_part(new Date(date)).getDate();
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
        "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const month = monthNames[date.getMonth()];

      return `<div style="text-align: center; line-height: 1.2; padding: 4px 0;">
                <div style="font-weight: 600; font-size: 13px;">${day}</div>
                <div style="font-size: 11px; margin-top: 2px;">${month}</div>
              </div>`;
    };

    // Task click handler
    gantt.attachEvent("onTaskClick", function(id, e) {
      const task = gantt.getTask(id);
      if (onTaskClick && task.originalData) {
        onTaskClick({
          id: task.originalData.id || task.id,
          name: task.originalData.name || task.originalData.title,
          start: task.start_date,
          end: task.end_date,
          progress: Math.round(task.progress * 100),
          status: task.originalData.status,
          type: task.type === gantt.config.types.project ? 'milestone' : 'task'
        });
      }
      return true;
    });

    // Initialize gantt
    gantt.init(ganttContainer.current);

    // Load data
    gantt.parse(ganttData);

    // Cleanup function
    return () => {
      if (gantt.$container) {
        gantt.clearAll();
      }
    };
  }, [ganttData, onTaskClick]);

  return (
    <Box sx={{ width: '100%', mt: 2 }}>
      {/* Project Header */}
      <Card
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
          boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)'
        }}
      >
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
            <Box>
              <Typography
                variant="h5"
                sx={{
                  fontFamily: 'Recursive Variable',
                  fontWeight: 700,
                  mb: 1,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                📊 {projectData?.name || projectData?.title || 'Project Timeline'}
              </Typography>
              <Typography
                variant="body2"
                sx={{
                  opacity: 0.9,
                  fontFamily: 'Recursive Variable'
                }}
              >
                {projectData?.description || 'Interactive project timeline with milestones and tasks'}
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {projectData?.milestones && (
                <Chip
                  label={`${projectData.milestones.length} Milestones`}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontFamily: 'Recursive Variable',
                    fontWeight: 600
                  }}
                />
              )}
              {projectData?.milestones && (
                <Chip
                  label={`${projectData.milestones.reduce((acc, m) => acc + (m.tasks?.length || 0), 0)} Tasks`}
                  sx={{
                    backgroundColor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontFamily: 'Recursive Variable',
                    fontWeight: 600
                  }}
                />
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* DHTMLX Gantt Chart */}
      <Card sx={{ boxShadow: 3, borderRadius: 2 }}>
        <CardContent sx={{ p: 0 }}>
          <Box
            ref={ganttContainer}
            sx={{
              minHeight: '400px', // Thay đổi từ height cố định sang minHeight
              width: '100%',
              '& .gantt_container': {
                fontFamily: 'Recursive Variable, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
              },
              // Header styling
              '& .gantt_grid_head_cell': {
                backgroundColor: '#f8f9fa !important',
                borderColor: '#e9ecef !important',
                fontWeight: '600 !important',
                fontSize: '16px !important',
                color: '#495057 !important'
              },
              // Grid styling
              '& .gantt_grid_data .gantt_cell': {
                borderColor: '#f1f3f4 !important',
                fontSize: '14px !important',
                padding: '4px 8px !important'
              },
              // Timeline header
              '& .gantt_scale_cell': {
                backgroundColor: '#f8f9fa !important',
                borderColor: '#e9ecef !important',
                fontSize: '10px !important'
              },
              // Milestone tasks - using app theme colors
              '& .milestone-task .gantt_task_line': {
                background: 'linear-gradient(135deg, #FFC107, #FF9800) !important',
                border: '1px solid #FF8F00 !important',
                borderRadius: '4px !important',
                fontWeight: '600 !important',
                height: '20px !important',
                boxShadow: '0 2px 4px rgba(255, 193, 7, 0.3) !important'
              },
              // Regular tasks - compact and clean
              '& .regular-task .gantt_task_line': {
                borderRadius: '3px !important',
                height: '18px !important',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1) !important',
                border: '1px solid rgba(0, 0, 0, 0.1) !important'
              },
              // Status-based colors matching app theme
              '& .task-pending .gantt_task_line': {
                background: '#FF9800 !important'
              },
              '& .task-in_progress .gantt_task_line': {
                background: '#2196F3 !important'
              },
              '& .task-completed .gantt_task_line': {
                background: '#4CAF50 !important'
              },
              '& .task-on_hold .gantt_task_line': {
                background: '#F44336 !important'
              },
              // Progress text styling
              '& .gantt_task_progress': {
                fontSize: '10px !important',
                fontWeight: '500 !important'
              },
              // Compact row height
              '& .gantt_row': {
                minHeight: '28px !important'
              },
              '& .gantt_grid_data .gantt_row': {
                minHeight: '28px !important'
              }
            }}
          />
        </CardContent>
      </Card>

      {/* Feature Highlights */}
      <Card sx={{ mt: 2, background: '#f8f9fa', border: '1px solid #e9ecef' }}>
        <CardContent sx={{ p: 2 }}>
          <Typography
            variant="body2"
            sx={{
              textAlign: 'center',
              fontFamily: 'Recursive Variable',
              color: '#6c757d',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 2,
              flexWrap: 'wrap'
            }}
          >
            <span>🎯 Milestone Tracking</span>
            <span>📋 Task Management</span>
            <span>📊 Progress Visualization</span>
            <span>📅 Timeline View</span>
          </Typography>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DHtmlxGanttChart;
