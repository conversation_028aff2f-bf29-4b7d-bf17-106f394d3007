# Ignition - Example Environment Variables
# Copy this file to `.env` and fill in real values.
# NEVER commit your real `.env` file.

# =====================
# Email (Mailjet - current)
# =====================
# Mailjet API key (used as SMTP user and for Web API auth)
MAILJET_API_KEY=your_mailjet_api_key
# Mailjet secret key (used as SMTP password and for Web API auth)
MAILJET_SECRET_KEY=your_mailjet_secret_key
# Verified sender email in Mailjet (Sender & Domains)
FROM_EMAIL=<EMAIL>
# Display name for outgoing emails
FROM_NAME=Ignition App

# (Deprecated in this setup) Previous SendGrid vars
# SENDGRID_API_KEY=
# SENDGRID_FROM_NAME=

# Optional: Django default from email (will fallback to FROM_EMAIL if set in settings)
# DEFAULT_FROM_EMAIL=
