#!/usr/bin/env python3
"""
Debug script specifically for Agent 2 - Structure Optimizer
Investigate why Agent 2 returns success=True but milestones=0
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# Add the ignition-api directory to Python path
sys.path.append('/Users/<USER>/GATE/ignition/ignition-api')

# Load environment variables
from dotenv import load_dotenv
load_dotenv('/Users/<USER>/GATE/ignition/ignition-api/.env')

print(f"🔑 Loaded environment from: {os.path.abspath('/Users/<USER>/GATE/ignition/ignition-api/.env')}")

# Import agents
from multi_agent.agents.domain_classifier import DomainClassificationAgent
from multi_agent.agents.structure_optimizer import StructureOptimizationAgent  
from multi_agent.services.shared_memory import create_initial_state

async def debug_agent2_detailed():
    """Debug Agent 2 with detailed logging"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    print(f"🐛 AGENT 2 DEBUG TEST - {timestamp}")
    print("=" * 80)
    
    # Test input
    test_input = "I want to learn machine learning and data science from scratch. I have basic Python knowledge but no experience with ML algorithms, statistics, or data analysis. My goal is to become proficient enough to work on real ML projects. I can dedicate 15-20 hours per week for the next 6 months."
    
    print(f"📝 Test Input: {test_input}")
    print()
    
    # Initialize agents
    print("🤖 Initializing agents...")
    agent1 = DomainClassificationAgent()
    agent2 = StructureOptimizationAgent()
    print("✅ Agents initialized\n")
    
    # Create initial state
    state = create_initial_state(
        user_input=test_input,
        duration="6 months",
        language="english"
    )
    
    try:
        # STEP 1: Run Agent 1 first
        print("🔍 STEP 1: Running Agent 1 to get domain analysis...")
        print("-" * 60)
        
        result1 = await agent1.process(state)
        
        if result1.get('success'):
            state['domain_analysis'] = result1['domain_analysis']
            print("✅ Agent 1 completed successfully")
            
            # Print domain analysis details
            domain_analysis = result1['domain_analysis']
            print(f"📊 Domain Analysis:")
            print(f"   Primary Domain: {domain_analysis.get('primary_domain', 'N/A')}")
            print(f"   Confidence: {domain_analysis.get('confidence', 'N/A')}")
            print(f"   Category: {domain_analysis.get('category', 'N/A')}")
            print(f"   Description: {domain_analysis.get('description', 'N/A')[:100]}...")
            
            print("\n📋 Full Domain Analysis JSON:")
            print(json.dumps(domain_analysis, indent=2, ensure_ascii=False))
            
        else:
            print(f"❌ Agent 1 failed: {result1.get('error', 'Unknown error')}")
            return
        
        # STEP 2: Debug Agent 2 in detail
        print("\n" + "🏗️ STEP 2: Debugging Agent 2 - Structure Optimization")
        print("=" * 80)
        
        print("📥 Input state for Agent 2:")
        print(f"   User Input: {state['user_input'][:100]}...")
        print(f"   Duration: {state['duration']}")
        print(f"   Language: {state['language']}")
        print(f"   Domain Analysis Available: {bool(state.get('domain_analysis'))}")
        
        # Let's manually inspect Agent 2's process method
        print("\n🔍 Calling Agent 2 process method...")
        
        # Add debugging to Agent 2 by monkey-patching
        original_process = agent2.process
        
        async def debug_process(state):
            print("🚀 Agent 2 process method called")
            print(f"📋 State keys: {list(state.keys())}")
            
            domain_analysis = state.get("domain_analysis", {})
            print(f"🎯 Domain analysis received: {bool(domain_analysis)}")
            
            if domain_analysis:
                print(f"   Primary domain: {domain_analysis.get('primary_domain', 'N/A')}")
                print(f"   Confidence: {domain_analysis.get('confidence', 'N/A')}")
            else:
                print("⚠️ No domain analysis found in state!")
                return {"success": False, "error": "No domain analysis found"}
            
            # Call original method but with more logging
            try:
                print("🤖 Calling Gemini AI for structure generation...")
                
                # Let's inspect the prompt being sent
                user_input = state["user_input"]
                duration = state.get("duration", "3 months")
                primary_domain = domain_analysis.get("primary_domain", "general")
                
                print(f"📝 Prompt parameters:")
                print(f"   User Input: {user_input[:100]}...")
                print(f"   Duration: {duration}")
                print(f"   Primary Domain: {primary_domain}")
                
                # Call the original process method
                result = await original_process(state)
                
                print(f"📤 Agent 2 result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                print(f"✅ Success: {result.get('success', 'N/A')}")
                
                if result.get('success'):
                    structure_design = result.get('structure_design', {})
                    print(f"🏗️ Structure design keys: {list(structure_design.keys()) if isinstance(structure_design, dict) else 'Not a dict'}")
                    
                    milestones = structure_design.get('milestones', [])
                    print(f"📊 Milestones count: {len(milestones)}")
                    print(f"📊 Milestones type: {type(milestones)}")
                    
                    if milestones:
                        print(f"📋 First milestone: {milestones[0]}")
                    else:
                        print("⚠️ No milestones found!")
                        
                        # Let's check if there's raw data we can inspect
                        print("\n🔍 Full structure_design content:")
                        print(json.dumps(structure_design, indent=2, ensure_ascii=False))
                        
                else:
                    print(f"❌ Agent 2 failed: {result.get('error', 'Unknown error')}")
                
                return result
                
            except Exception as e:
                import traceback
                print(f"💥 Exception in Agent 2: {str(e)}")
                print(f"🔍 Traceback:")
                traceback.print_exc()
                return {"success": False, "error": str(e)}
        
        # Replace the process method temporarily
        agent2.process = debug_process
        
        # Now run Agent 2 with debugging
        result2 = await agent2.process(state)
        
        print("\n📊 FINAL AGENT 2 RESULT:")
        print("=" * 60)
        print(json.dumps(result2, indent=2, ensure_ascii=False))
        
    except Exception as e:
        import traceback
        print(f"💥 Debug failed: {str(e)}")
        print(f"🔍 Full traceback:")
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_agent2_detailed())
