# Byte-compiled / optimized / DLL files
*__pycache__
*.py[cod]
*$py.class

# Local development settings
.env
.env.local
.env.*.local
venv/
env/
*.sqlite3
uwsgi.ini
*.pid
dump.rdb

# Log files
*.log

# Ignore the media folder & static
*media
*static

# Compiled Python modules
*.pyc

# Django migration files
**/migrations/*.bak
**/migrations/*.swp
**/migrations/*.pyc
**/migrations/__pycache__/

# Ignore VS Code settings
.vscode/

# Ignore the Mac OS filesystem
*.DS_Store
.DS_Store
