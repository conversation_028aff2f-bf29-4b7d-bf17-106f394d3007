from rest_framework import serializers
from plans.models import Task

class TaskSerializer(serializers.ModelSerializer):
    class Meta:
        model = Task
        fields = ['id', 'name', 'status', 'start_date', 'end_date', 'deadline']
        extra_kwargs = {
            'start_date': {'required': True, 'format': '%Y-%m-%d'},
            'end_date': {'required': True, 'format': '%Y-%m-%d'},
            'deadline': {'required': False, 'format': '%Y-%m-%d'}
        }
