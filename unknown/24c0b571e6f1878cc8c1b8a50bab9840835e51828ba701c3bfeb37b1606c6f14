import os
import re
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from users.models import User
from skills.models import Skill
from django.contrib.auth.hashers import make_password


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'email', 'username', 'password', 'avatar', 'address', 'phone_number', 'country_code', 'occupation', 'email_notifications_enabled', 'sms_notifications_enabled', 'sequential_task_completion', 'discoverable_on_platform']


class SkillSerializer(serializers.ModelSerializer):
    class Meta:
        model = Skill
        fields = ['name', 'id']

class ProfileSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()
    skills = SkillSerializer(many=True)

    class Meta:
        model = User
        fields = [
            'id', 'first_name', 'last_name', 'email', 'avatar', 'description',
            'address', 'phone_number', 'country_code', 'occupation', 'skills', 'date_joined',
            'email_notifications_enabled', 'sms_notifications_enabled', 'sequential_task_completion',
            'discoverable_on_platform'
        ]

    def get_avatar(self, instance):
        url_backend = os.getenv('URL_BACKEND', 'http://127.0.0.1:8000')
        try:
            if instance.avatar and instance.avatar.url:
                return url_backend + instance.avatar.url
            else:
                return url_backend + '/media/avatars/default.png?ver=1'
        except AttributeError:
            return url_backend + '/media/avatars/default.png?ver=1'


class UserConnectSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()
    skills = SkillSerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'first_name', 'last_name', 'email', 'avatar', 'description',
            'address', 'occupation', 'phone_number', 'country_code', 'skills'
        ]

    def get_avatar(self, instance):
        url_backend = os.getenv('URL_BACKEND', 'http://127.0.0.1:8000')
        try:
            if instance.avatar and instance.avatar.url:
                return url_backend + instance.avatar.url
            else:
                return url_backend + '/media/avatars/default.png?ver=1'
        except AttributeError:
            return url_backend + '/media/avatars/default.png?ver=1'


class UserLoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True)


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['is_superadmin'] = user.is_superuser
        return token

    def validate(self, attrs):
        data = super().validate(attrs)
        data['is_superuser'] = self.user.is_superuser
        return data


class CreateAccountSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True)
    first_name = serializers.CharField(required=True)
    last_name = serializers.CharField(required=True)
    phone_number = serializers.CharField(required=False, max_length=20, allow_blank=True, allow_null=True)
    country_code = serializers.CharField(required=False, max_length=5, allow_blank=True, allow_null=True, default='+84')

    def validate_email(self, value):
        if User.objects.filter(email=value, is_active=True).exists():
            raise serializers.ValidationError(
                'Email address already exists. Try again.')
        return value

    def validate_phone_number(self, value):
        if value and value.strip():
            # Basic phone number validation - digits, spaces, hyphens, parentheses
            phone_pattern = r'^[\d\s\-\(\)\+]+$'
            if not re.match(phone_pattern, value):
                raise serializers.ValidationError(
                    'Please enter a valid phone number format.')
            # Remove all non-digit characters for length check
            digits_only = re.sub(r'\D', '', value)
            if len(digits_only) < 7 or len(digits_only) > 15:
                raise serializers.ValidationError(
                    'Phone number must be between 7 and 15 digits.')
        return value

    def create(self, validated_data):
        validated_data['password'] = make_password(validated_data['password'])
        return User.objects.create(**validated_data)


class ActivateUserSerializer(serializers.Serializer):
    uid = serializers.CharField()
    token = serializers.CharField()


class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)


class PasswordResetSerializer(serializers.Serializer):
    password = serializers.CharField(write_only=True)
    password_confirm = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError(
                {"password_confirm": "Password confirm not match with new password"})
        return attrs


class UpdateProfileSerializer(serializers.Serializer):
    first_name = serializers.CharField(required=True, max_length=75)
    last_name = serializers.CharField(required=True, max_length=75)
    avatar = serializers.ImageField(required=False, allow_null=True)
    description = serializers.CharField(required=False, max_length=512)
    address = serializers.CharField(required=False, max_length=255)
    occupation = serializers.CharField(required=False, max_length=75)
    phone_number = serializers.CharField(required=False, max_length=20)
    country_code = serializers.CharField(required=False, max_length=5)
    email_notifications_enabled = serializers.BooleanField(required=False)
    sms_notifications_enabled = serializers.BooleanField(required=False)
    sequential_task_completion = serializers.BooleanField(required=False)
    discoverable_on_platform = serializers.BooleanField(required=False)


class GoogleInputSerializer(serializers.Serializer):
    code = serializers.CharField(required=False)
    error = serializers.CharField(required=False)

class CheckInvitationSerializer(serializers.Serializer):
    signed_id = serializers.CharField(required=True)
    email = serializers.EmailField(required=True)
