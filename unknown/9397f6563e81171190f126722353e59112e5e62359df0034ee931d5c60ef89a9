from django.core.management.base import BaseCommand
from users.models import User
from django.db import connection
from django.utils import timezone
import random

class Command(BaseCommand):
    help = 'Seed data for plans_invitation table using emails from existing users in the database'

    def add_arguments(self, parser):
        parser.add_argument('--invitations', type=int, default=5, help='Number of invitations to create')
        parser.add_argument('--invited_by_id', type=int, default=1, help='ID of the inviter')
        parser.add_argument('--plan_id', type=int, default=1, help='ID of the plan')
        parser.add_argument('--clear', action='store_true', help='Clear all existing invitations')

    def handle(self, *args, **options):
        # Get list of emails from User table
        emails = list(User.objects.values_list('email', flat=True))

        if not emails:
            self.stdout.write(self.style.ERROR('No users found in the database!'))
            self.stdout.write('Please add users to the database before running this command.')
            return

        if options['clear']:
            self.stdout.write('Deleting all existing invitations...')
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM plans_invitation;")
            self.stdout.write(self.style.SUCCESS('All invitations deleted!'))

        number_of_invitations = options['invitations']
        invited_by_id = options['invited_by_id']
        plan_id = options['plan_id']
        
        self.stdout.write(f'Creating {number_of_invitations} sample invitations...')

        # Get current datetime
        now = timezone.now().strftime('%Y-%m-%d %H:%M:%S.%f')

        # Randomly select emails
        # If more invitations than available emails, an email can be used multiple times
        selected_emails = random.choices(emails, k=number_of_invitations)
        
        for email in selected_emails:
            # Create INSERT statement
            sql = f"""
            INSERT INTO `plans_invitation` (`email`, `accepted`, `created_at`, `accepted_at`, `invited_by_id`, `plan_id`)
            VALUES ('{email}', '1', '{now}', '{now}', '{invited_by_id}', '{plan_id}');
            """
            
            try:
                with connection.cursor() as cursor:
                    cursor.execute(sql)
                self.stdout.write(f'Created invitation for email: {email}')
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error creating invitation for email {email}: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully created {number_of_invitations} invitations!')) 