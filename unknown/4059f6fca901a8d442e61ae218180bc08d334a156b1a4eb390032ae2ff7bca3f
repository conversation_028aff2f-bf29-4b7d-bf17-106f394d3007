#!/bin/bash

# Script to start Celery Worker and Beat for Daily Task Reminders
# Run this script on the server to activate the notification system

echo "🚀 Starting Celery Services for Daily Task Reminders..."
echo "📅 This will enable automatic notifications at 8:00 AM and 8:00 PM daily"
echo ""

# Change to project directory
cd ~/ignition-be

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Check if Celery is already running
echo "🔍 Checking existing Celery processes..."
EXISTING_WORKER=$(ps aux | grep "celery.*worker" | grep -v grep)
EXISTING_BEAT=$(ps aux | grep "celery.*beat" | grep -v grep)

if [ ! -z "$EXISTING_WORKER" ]; then
    echo "⚠️  Celery Worker is already running:"
    echo "$EXISTING_WORKER"
    echo ""
fi

if [ ! -z "$EXISTING_BEAT" ]; then
    echo "⚠️  Celery Beat is already running:"
    echo "$EXISTING_BEAT"
    echo ""
fi

# Create logs directory if it doesn't exist
mkdir -p logs

# Start Celery Worker in background
echo "🔄 Starting Celery Worker..."
celery -A main worker --loglevel=info --detach \
    --pidfile=logs/celery_worker.pid \
    --logfile=logs/celery_worker.log

if [ $? -eq 0 ]; then
    echo "✅ Celery Worker started successfully"
else
    echo "❌ Failed to start Celery Worker"
    exit 1
fi

# Wait a moment for worker to initialize
sleep 2

# Start Celery Beat in background
echo "⏰ Starting Celery Beat Scheduler..."
celery -A main beat --loglevel=info --detach \
    --scheduler django_celery_beat.schedulers:DatabaseScheduler \
    --pidfile=logs/celery_beat.pid \
    --logfile=logs/celery_beat.log

if [ $? -eq 0 ]; then
    echo "✅ Celery Beat started successfully"
else
    echo "❌ Failed to start Celery Beat"
    exit 1
fi

echo ""
echo "🎉 Daily Task Reminder System is now ACTIVE!"
echo ""
echo "📋 Schedule:"
echo "   🌅 Morning reminders: 8:00 AM daily (tasks starting today)"
echo "   🌆 Evening reminders: 8:00 PM daily (tasks ending today)"
echo ""
echo "📊 Status check:"
ps aux | grep "celery" | grep -v grep

echo ""
echo "📝 Log files:"
echo "   Worker log:  ~/ignition-be/logs/celery_worker.log"
echo "   Beat log:    ~/ignition-be/logs/celery_beat.log"
echo ""
echo "🧪 Test the system:"
echo "   python manage.py send_task_reminders --dry-run --verbose"
echo ""
echo "🛑 To stop services:"
echo "   pkill -f 'celery.*worker'"
echo "   pkill -f 'celery.*beat'"
