from rest_framework import serializers
import os
import re
from .models import Notification


class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = '__all__'


class SendSMSSerializer(serializers.Serializer):
    """
    Serializer for sending SMS messages via AWS SNS
    """
    phone_number = serializers.CharField(
        max_length=20,
        help_text="Recipient phone number in international format (e.g., +1234567890)"
    )
    message = serializers.CharField(
        max_length=1600,  # AWS SNS limit is 1600 characters
        help_text="SMS message content (max 1600 characters)"
    )

    def validate_phone_number(self, value):
        """
        Validate phone number format
        """
        # Remove any spaces, dashes, or parentheses
        cleaned_number = re.sub(r'[\s\-\(\)]', '', value)

        # Check if it starts with + and contains only digits after that
        if not re.match(r'^\+[1-9]\d{1,14}$', cleaned_number):
            raise serializers.ValidationError(
                "Phone number must be in international format (e.g., +1234567890) "
                "and contain 2-15 digits after the country code."
            )

        return cleaned_number

    def validate_message(self, value):
        """
        Validate message content
        """
        if not value.strip():
            raise serializers.ValidationError("Message cannot be empty.")

        if len(value) > 1600:
            raise serializers.ValidationError(
                "Message is too long. AWS SNS supports maximum 1600 characters."
            )

        return value.strip()


class SMSResponseSerializer(serializers.Serializer):
    """
    Serializer for SMS sending response
    """
    success = serializers.BooleanField()
    message_id = serializers.CharField(required=False, allow_null=True)
    to = serializers.CharField(required=False)
    estimated_cost = serializers.FloatField(required=False, allow_null=True)
    provider = serializers.CharField(required=False)
    error = serializers.CharField(required=False, allow_null=True)
    message = serializers.CharField(required=False)
