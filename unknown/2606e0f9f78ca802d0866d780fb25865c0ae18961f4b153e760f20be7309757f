# 🚀 **LANGGRAPH IMPLEMENTATION GUIDE - IGNITION MULTI-AGENT SYSTEM**

## 📋 **TỔNG QUAN**

LangGraph được chọn làm framework chính cho Ignition's multi-agent plan generation system. Document này cung cấp hướng dẫn chi tiết từ setup đến production deployment.

---

## 🎯 **TẠI SAO CHỌN LANGGRAPH?**

### **Lý do chính:**
1. **Perfect fit cho 6-agent pipeline**: Graph structure phù hợp với sequential/parallel workflow
2. **Advanced debugging**: Time travel và replay capabilities
3. **Production-ready**: Mature framework với comprehensive tooling
4. **State management**: Built-in persistence và checkpointing
5. **Flexibility**: Fine-grained control over agent flow

### **So với các framework khác:**
- **vs CrewAI**: Nhiều control hơn, phù hợp production
- **vs AutoGen**: Structured workflow thay vì conversation-based
- **vs Custom**: Đã có sẵn best practices và tooling

---

## 🏗️ **KIẾN TRÚC LANGGRAPH CHO IGNITION**

### **Core Components:**

```python
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
from typing import TypedDict, Annotated, List
import operator

# 1. Define State Structure
class PlanGenerationState(TypedDict):
    # Input data
    user_input: str
    duration: str
    language: str
    
    # Agent outputs
    domain_analysis: dict
    structure_design: dict
    content_data: dict
    timeline_data: dict
    validation_results: dict
    final_plan: dict
    
    # Metadata
    messages: Annotated[List[str], operator.add]
    current_step: str
    progress: float
    errors: List[dict]
```

### **Graph Architecture:**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Domain Agent   │───▶│Structure Agent  │───▶│   Parallel      │
│  (Sequential)   │    │  (Sequential)   │    │   Processing    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                       │
                                              ┌────────┴────────┐
                                              ▼                 ▼
                                    ┌─────────────────┐ ┌─────────────────┐
                                    │ Content Agent   │ │Timeline Agent   │
                                    │   (Parallel)    │ │  (Parallel)     │
                                    └─────────────────┘ └─────────────────┘
                                              │                 │
                                              └────────┬────────┘
                                                       ▼
                                              ┌─────────────────┐
                                              │Validation Agent │
                                              │  (Sequential)   │
                                              └─────────────────┘
                                                       │
                                                       ▼
                                              ┌─────────────────┐
                                              │ Quality Agent   │
                                              │  (Sequential)   │
                                              └─────────────────┘
```

---

## 🤖 **IMPLEMENTATION CHI TIẾT**

### **1. Base Agent Class:**

```python
from abc import ABC, abstractmethod
from langgraph.graph import StateGraph
import asyncio
import logging

class BaseIgnitionAgent(ABC):
    """Base class cho tất cả Ignition agents"""
    
    def __init__(self, agent_name: str, config: dict):
        self.agent_name = agent_name
        self.config = config
        self.logger = logging.getLogger(f"ignition.{agent_name}")
        
    @abstractmethod
    async def process(self, state: PlanGenerationState) -> dict:
        """Main processing method - must be implemented"""
        pass
    
    def validate_input(self, state: PlanGenerationState) -> bool:
        """Validate input state"""
        return True
    
    def validate_output(self, output: dict) -> bool:
        """Validate output quality"""
        return True
    
    async def execute(self, state: PlanGenerationState) -> PlanGenerationState:
        """Standard execution flow với error handling"""
        try:
            self.logger.info(f"Starting {self.agent_name}")
            
            # Input validation
            if not self.validate_input(state):
                raise ValueError(f"Invalid input for {self.agent_name}")
            
            # Process
            result = await self.process(state)
            
            # Output validation
            if not self.validate_output(result):
                raise ValueError(f"Invalid output from {self.agent_name}")
            
            # Update state
            updated_state = state.copy()
            updated_state.update(result)
            updated_state["messages"].append(f"{self.agent_name} completed successfully")
            updated_state["current_step"] = self.agent_name
            
            self.logger.info(f"Completed {self.agent_name}")
            return updated_state
            
        except Exception as e:
            self.logger.error(f"Error in {self.agent_name}: {str(e)}")
            error_state = state.copy()
            error_state["errors"].append({
                "agent": self.agent_name,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            return error_state
```

### **2. Domain Classification Agent:**

```python
class DomainClassificationAgent(BaseIgnitionAgent):
    """Agent 1: Phân tích domain và requirements"""
    
    def __init__(self):
        super().__init__(
            agent_name="domain_classifier",
            config={
                "model": "gpt-4",
                "temperature": 0.1,
                "max_tokens": 1500
            }
        )
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Phân tích domain từ user input"""
        
        # Prepare AI prompt
        system_prompt = """Bạn là chuyên gia phân tích domain với 10+ năm kinh nghiệm.
        Nhiệm vụ: Phân tích user input và xác định:
        1. Primary domain (mobile_app, web_app, data_science, etc.)
        2. Sub-domains liên quan
        3. Complexity level (beginner/intermediate/advanced)
        4. Key requirements
        5. Success metrics
        
        Trả về JSON format với structure rõ ràng."""
        
        user_prompt = f"""
        Phân tích dự án này:
        Input: "{state['user_input']}"
        Duration: {state['duration']}
        Language: {state['language']}
        """
        
        # Call AI
        from assistant.ai_providers import create_chat_completion
        
        ai_response = await create_chat_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            model=self.config["model"],
            temperature=self.config["temperature"]
        )
        
        # Parse response
        import json
        try:
            analysis_result = json.loads(ai_response["content"])
        except json.JSONDecodeError:
            # Fallback parsing logic
            analysis_result = self._parse_fallback(ai_response["content"])
        
        return {
            "domain_analysis": analysis_result,
            "progress": 16.67  # 1/6 agents completed
        }
    
    def validate_output(self, output: dict) -> bool:
        """Validate domain analysis quality"""
        analysis = output.get("domain_analysis", {})
        
        required_fields = ["primary_domain", "complexity_level", "requirements"]
        return all(field in analysis for field in required_fields)
```

### **3. Structure Optimization Agent:**

```python
class StructureOptimizationAgent(BaseIgnitionAgent):
    """Agent 2: Thiết kế cấu trúc milestone và task"""
    
    def __init__(self):
        super().__init__(
            agent_name="structure_optimizer",
            config={
                "model": "claude-3-sonnet",
                "temperature": 0.2,
                "max_tokens": 2000
            }
        )
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Thiết kế optimal structure dựa trên domain analysis"""
        
        domain_analysis = state["domain_analysis"]
        
        system_prompt = """Bạn là kiến trúc sư dự án chuyên nghiệp.
        Nhiệm vụ: Thiết kế cấu trúc tối ưu với:
        1. 5 milestones chính
        2. 5 tasks per milestone
        3. Dependencies rõ ràng
        4. Timeline estimate
        5. Critical path analysis
        
        Output: JSON structure với milestones và tasks."""
        
        user_prompt = f"""
        Dựa trên domain analysis:
        {json.dumps(domain_analysis, indent=2, ensure_ascii=False)}
        
        Thiết kế cấu trúc dự án tối ưu.
        """
        
        ai_response = await create_chat_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            model=self.config["model"],
            temperature=self.config["temperature"]
        )
        
        structure_result = json.loads(ai_response["content"])
        
        return {
            "structure_design": structure_result,
            "progress": 33.33  # 2/6 agents completed
        }
```

### **4. Parallel Processing Agents:**

```python
class ContentGenerationAgent(BaseIgnitionAgent):
    """Agent 3: Generate detailed content"""
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Generate names, descriptions cho milestones/tasks/subtasks"""
        
        structure = state["structure_design"]
        domain_context = state["domain_analysis"]
        
        # AI processing logic here
        content_result = await self._generate_content(structure, domain_context)
        
        return {
            "content_data": content_result,
            "progress": 50.0  # Parallel với Timeline Agent
        }

class TimelineOptimizationAgent(BaseIgnitionAgent):
    """Agent 4: Optimize timeline và scheduling"""
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Calculate realistic timelines và resource allocation"""
        
        structure = state["structure_design"]
        
        # Timeline optimization logic
        timeline_result = await self._optimize_timeline(structure)
        
        return {
            "timeline_data": timeline_result,
            "progress": 50.0  # Parallel với Content Agent
        }
```

---

## 🔄 **GRAPH WORKFLOW SETUP**

### **Main Orchestrator:**

```python
class IgnitionPlanOrchestrator:
    """Main orchestrator cho multi-agent plan generation"""
    
    def __init__(self):
        self.agents = {
            "domain_classifier": DomainClassificationAgent(),
            "structure_optimizer": StructureOptimizationAgent(),
            "content_generator": ContentGenerationAgent(),
            "timeline_optimizer": TimelineOptimizationAgent(),
            "validation_agent": ValidationAgent(),
            "quality_enhancer": QualityEnhancementAgent()
        }
        
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """Build LangGraph workflow"""
        
        workflow = StateGraph(PlanGenerationState)
        
        # Add nodes (agents)
        for agent_name, agent in self.agents.items():
            workflow.add_node(agent_name, agent.execute)
        
        # Define edges (flow)
        workflow.add_edge("domain_classifier", "structure_optimizer")
        workflow.add_edge("structure_optimizer", "content_generator")
        workflow.add_edge("structure_optimizer", "timeline_optimizer")
        
        # Parallel processing merge
        workflow.add_edge(["content_generator", "timeline_optimizer"], "validation_agent")
        workflow.add_edge("validation_agent", "quality_enhancer")
        
        # Set entry và exit points
        workflow.set_entry_point("domain_classifier")
        workflow.set_finish_point("quality_enhancer")
        
        return workflow
    
    async def generate_plan(self, user_input: str, duration: str = "3 months") -> dict:
        """Main method để generate plan"""
        
        # Initialize state
        initial_state = PlanGenerationState(
            user_input=user_input,
            duration=duration,
            language="vietnamese",
            messages=[],
            current_step="initializing",
            progress=0.0,
            errors=[]
        )
        
        # Compile và execute workflow
        app = self.workflow.compile()
        
        try:
            final_state = await app.ainvoke(initial_state)
            return {
                "success": True,
                "plan": final_state["final_plan"],
                "metadata": {
                    "progress": final_state["progress"],
                    "messages": final_state["messages"],
                    "errors": final_state["errors"]
                }
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "partial_state": initial_state
            }
```

---

## 🎯 **NEXT STEPS**

### **Implementation Roadmap:**

1. **Week 1-2**: Setup LangGraph environment và base classes
2. **Week 3-4**: Implement Domain và Structure agents
3. **Week 5-6**: Add Content và Timeline agents với parallel processing
4. **Week 7-8**: Implement Validation và Quality agents
5. **Week 9-10**: Integration testing và optimization
6. **Week 11-12**: Production deployment và monitoring

### **Key Features to Add:**
- ✅ **Error handling và retry logic**
- ✅ **Progress tracking real-time**
- ✅ **Quality gates giữa agents**
- ✅ **Human-in-the-loop interruption**
- ✅ **Caching và performance optimization**

**LangGraph provides the perfect foundation cho Ignition's sophisticated multi-agent system!** 🚀

---

## 🔧 **ADVANCED FEATURES**

### **1. Human-in-the-Loop Integration:**

```python
from langgraph.checkpoint.sqlite import SqliteSaver
from langgraph.prebuilt import ToolExecutor

class HumanInterventionAgent:
    """Handle human intervention points"""

    def __init__(self):
        self.checkpointer = SqliteSaver.from_conn_string(":memory:")

    async def request_human_input(self, state: PlanGenerationState, question: str) -> dict:
        """Pause execution for human input"""

        # Save current state
        checkpoint_id = await self.checkpointer.aput(
            config={"configurable": {"thread_id": "human_intervention"}},
            checkpoint={
                "state": state,
                "question": question,
                "timestamp": datetime.now().isoformat()
            }
        )

        return {
            "human_input_required": True,
            "checkpoint_id": checkpoint_id,
            "question": question,
            "status": "waiting_for_human"
        }

# Usage trong agent
class ValidationAgent(BaseIgnitionAgent):
    def __init__(self):
        super().__init__("validation_agent", {})
        self.human_intervention = HumanInterventionAgent()

    async def process(self, state: PlanGenerationState) -> dict:
        validation_result = await self._validate_plan(state)

        # Nếu quality score thấp, request human input
        if validation_result["quality_score"] < 0.8:
            return await self.human_intervention.request_human_input(
                state,
                f"Plan quality score is {validation_result['quality_score']}. Should we proceed or revise?"
            )

        return {"validation_results": validation_result}
```

### **2. Time Travel & Replay:**

```python
class TimeTravel:
    """Debug và replay capabilities"""

    def __init__(self, workflow: StateGraph):
        self.workflow = workflow
        self.execution_history = []

    async def execute_with_history(self, initial_state: PlanGenerationState):
        """Execute workflow với full history tracking"""

        app = self.workflow.compile(checkpointer=SqliteSaver.from_conn_string(":memory:"))

        config = {"configurable": {"thread_id": "main_execution"}}

        async for event in app.astream(initial_state, config=config):
            # Save each step
            self.execution_history.append({
                "timestamp": datetime.now().isoformat(),
                "event": event,
                "state_snapshot": event.get("state", {})
            })

            yield event

    async def replay_from_step(self, step_index: int, new_input: dict = None):
        """Replay từ một step cụ thể"""

        if step_index >= len(self.execution_history):
            raise ValueError("Invalid step index")

        # Get state at step
        target_state = self.execution_history[step_index]["state_snapshot"]

        # Modify state if needed
        if new_input:
            target_state.update(new_input)

        # Continue execution từ step này
        app = self.workflow.compile()
        return await app.ainvoke(target_state)

    def visualize_execution(self):
        """Tạo visualization của execution flow"""

        steps = []
        for i, history_item in enumerate(self.execution_history):
            steps.append({
                "step": i,
                "agent": history_item["event"].get("agent_name", "unknown"),
                "timestamp": history_item["timestamp"],
                "duration": self._calculate_duration(i),
                "success": "error" not in history_item["event"]
            })

        return steps
```

### **3. Performance Monitoring:**

```python
import time
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class AgentMetrics:
    agent_name: str
    execution_time: float
    memory_usage: float
    api_calls: int
    success_rate: float
    quality_score: float

class PerformanceMonitor:
    """Monitor agent performance và system health"""

    def __init__(self):
        self.metrics: Dict[str, List[AgentMetrics]] = {}
        self.system_metrics = []

    async def track_agent_execution(self, agent_name: str, execution_func):
        """Track performance của một agent"""

        start_time = time.time()
        start_memory = self._get_memory_usage()

        try:
            result = await execution_func()
            success = True
            quality_score = self._calculate_quality_score(result)
        except Exception as e:
            success = False
            quality_score = 0.0
            result = {"error": str(e)}

        end_time = time.time()
        end_memory = self._get_memory_usage()

        metrics = AgentMetrics(
            agent_name=agent_name,
            execution_time=end_time - start_time,
            memory_usage=end_memory - start_memory,
            api_calls=self._count_api_calls(),
            success_rate=1.0 if success else 0.0,
            quality_score=quality_score
        )

        # Store metrics
        if agent_name not in self.metrics:
            self.metrics[agent_name] = []
        self.metrics[agent_name].append(metrics)

        return result

    def get_performance_report(self) -> dict:
        """Generate comprehensive performance report"""

        report = {
            "overall_stats": {
                "total_executions": sum(len(metrics) for metrics in self.metrics.values()),
                "average_execution_time": self._calculate_avg_execution_time(),
                "success_rate": self._calculate_overall_success_rate(),
                "memory_efficiency": self._calculate_memory_efficiency()
            },
            "agent_breakdown": {}
        }

        for agent_name, agent_metrics in self.metrics.items():
            report["agent_breakdown"][agent_name] = {
                "executions": len(agent_metrics),
                "avg_time": sum(m.execution_time for m in agent_metrics) / len(agent_metrics),
                "success_rate": sum(m.success_rate for m in agent_metrics) / len(agent_metrics),
                "avg_quality": sum(m.quality_score for m in agent_metrics) / len(agent_metrics)
            }

        return report
```

### **4. Production Integration:**

```python
from django.http import JsonResponse
from django.views import View
from asgiref.sync import async_to_sync
import json

class LangGraphPlanGenerationView(View):
    """Django view integration với LangGraph orchestrator"""

    def __init__(self):
        super().__init__()
        self.orchestrator = IgnitionPlanOrchestrator()
        self.monitor = PerformanceMonitor()
        self.time_travel = TimeTravel(self.orchestrator.workflow)

    async def post(self, request):
        """Handle plan generation request"""

        try:
            data = json.loads(request.body)
            user_input = data.get("user_input")
            duration = data.get("duration", "3 months")

            # Validate input
            if not user_input:
                return JsonResponse({
                    "success": False,
                    "error": "user_input is required"
                }, status=400)

            # Execute với monitoring
            async def execute_plan():
                return await self.orchestrator.generate_plan(user_input, duration)

            result = await self.monitor.track_agent_execution("full_pipeline", execute_plan)

            # Add performance metrics
            result["performance_metrics"] = self.monitor.get_performance_report()

            return JsonResponse(result)

        except Exception as e:
            return JsonResponse({
                "success": False,
                "error": str(e)
            }, status=500)

    async def get(self, request):
        """Get execution history và debug info"""

        execution_id = request.GET.get("execution_id")

        if execution_id:
            # Return specific execution details
            history = self.time_travel.visualize_execution()
            return JsonResponse({
                "execution_history": history,
                "replay_available": True
            })
        else:
            # Return overall stats
            return JsonResponse(self.monitor.get_performance_report())

# URL configuration
# urls.py
urlpatterns = [
    path('api/langgraph/generate-plan', LangGraphPlanGenerationView.as_view(), name='langgraph-generate-plan'),
]
```

### **5. Error Recovery & Resilience:**

```python
class ErrorRecoveryManager:
    """Handle errors và automatic recovery"""

    def __init__(self, max_retries: int = 3):
        self.max_retries = max_retries
        self.recovery_strategies = {
            "ai_api_error": self._handle_ai_api_error,
            "validation_error": self._handle_validation_error,
            "timeout_error": self._handle_timeout_error
        }

    async def execute_with_recovery(self, agent_func, state: PlanGenerationState):
        """Execute agent với automatic error recovery"""

        for attempt in range(self.max_retries + 1):
            try:
                return await agent_func(state)

            except Exception as e:
                error_type = self._classify_error(e)

                if attempt < self.max_retries:
                    # Try recovery
                    recovery_strategy = self.recovery_strategies.get(error_type)
                    if recovery_strategy:
                        state = await recovery_strategy(state, e, attempt)
                        continue

                # Final attempt failed
                raise e

    async def _handle_ai_api_error(self, state: PlanGenerationState, error: Exception, attempt: int):
        """Handle AI API failures"""

        # Switch to fallback model
        if "gpt-4" in str(error):
            state["fallback_model"] = "claude-3-sonnet"
        elif "claude" in str(error):
            state["fallback_model"] = "gpt-3.5-turbo"

        # Add delay
        await asyncio.sleep(2 ** attempt)  # Exponential backoff

        return state

    async def _handle_validation_error(self, state: PlanGenerationState, error: Exception, attempt: int):
        """Handle validation failures"""

        # Reduce quality threshold
        state["quality_threshold"] = max(0.6, state.get("quality_threshold", 0.8) - 0.1)

        return state
```

---

## 🚀 **DEPLOYMENT CHECKLIST**

### **Pre-Production:**
- [ ] **Unit tests** cho tất cả agents
- [ ] **Integration tests** cho full pipeline
- [ ] **Performance benchmarking**
- [ ] **Error handling validation**
- [ ] **Memory leak testing**

### **Production Setup:**
- [ ] **Database migration** cho LangGraph state storage
- [ ] **Redis setup** cho caching
- [ ] **Monitoring dashboard** setup
- [ ] **Logging configuration**
- [ ] **Backup strategies**

### **Go-Live:**
- [ ] **Blue-green deployment**
- [ ] **Rollback procedures** tested
- [ ] **Performance monitoring** active
- [ ] **Error alerting** configured
- [ ] **Documentation** complete

**LangGraph implementation sẽ transform Ignition thành world-class AI planning platform!** 🎯
