#!/bin/bash

# <PERSON>ript to check Celery services status
# Run this script to monitor the notification system

echo "📊 Celery Services Status Check"
echo "================================"
echo ""

# Check Celery processes
echo "🔍 Celery Processes:"
CELERY_PROCESSES=$(ps aux | grep "celery" | grep -v grep)
if [ -z "$CELERY_PROCESSES" ]; then
    echo "❌ No Celery processes running"
    echo ""
    echo "🚀 To start services:"
    echo "   ./start_celery_services.sh"
else
    echo "✅ Running processes:"
    echo "$CELERY_PROCESSES"
fi

echo ""

# Check log files
echo "📝 Log Files:"
if [ -f "logs/celery_worker.log" ]; then
    echo "✅ Worker log exists: logs/celery_worker.log"
    echo "   Last 3 lines:"
    tail -3 logs/celery_worker.log | sed 's/^/   /'
else
    echo "❌ Worker log not found"
fi

echo ""

if [ -f "logs/celery_beat.log" ]; then
    echo "✅ Beat log exists: logs/celery_beat.log"
    echo "   Last 3 lines:"
    tail -3 logs/celery_beat.log | sed 's/^/   /'
else
    echo "❌ Beat log not found"
fi

echo ""

# Check scheduled tasks
echo "⏰ Scheduled Tasks:"
echo "   🌅 Morning reminders: 8:00 AM daily"
echo "   🌆 Evening reminders: 8:00 PM daily"

echo ""

# Test command
echo "🧪 Test Command:"
echo "   python manage.py send_task_reminders --dry-run --verbose"

echo ""

# Current time
echo "🕐 Current Server Time:"
date

echo ""
echo "📋 Next scheduled notifications:"
CURRENT_HOUR=$(date +%H)
if [ $CURRENT_HOUR -lt 8 ]; then
    echo "   🌅 Next: Today at 8:00 AM"
elif [ $CURRENT_HOUR -lt 20 ]; then
    echo "   🌆 Next: Today at 8:00 PM"
else
    echo "   🌅 Next: Tomorrow at 8:00 AM"
fi
