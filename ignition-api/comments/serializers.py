from rest_framework import serializers
from .models import Comment
from users.models import User
import os


class UserSerializer(serializers.ModelSerializer):
    avatar = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'avatar']

    def get_avatar(self, instance):
        url_backend = os.getenv('URL_BACKEND', 'http://127.0.0.1:8000')
        try:
            if instance.avatar and instance.avatar.url:
                return url_backend + instance.avatar.url
            else:
                return url_backend + '/media/avatars/default.png?ver=1'
        except AttributeError:
            return url_backend + '/media/avatars/default.png?ver=1'


class CommentSerializer(serializers.ModelSerializer):
    content = serializers.CharField(required=True)
    class Meta:
        model = Comment
        fields = '__all__'


class CommentsListWithUserSerializer(serializers.ModelSerializer):
    user = UserSerializer()
    created_at = serializers.SerializerMethodField()

    class Meta:
        model = Comment
        fields = ['id', 'content', 'type', 'user', 'target_id','created_at']

    def get_created_at(self, instance):
        print(instance.created_at)
        return instance.created_at.strftime("%H:%M %d/%m ")

