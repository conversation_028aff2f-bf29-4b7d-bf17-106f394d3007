from django.shortcuts import render
from rest_framework.permissions import IsAuthenticated
from .models import Comment
from .permissions import IsCommentOwner
from .serializers import CommentSerializer, CommentsListWithUserSerializer
from rest_framework.views import APIView
from rest_framework.response import Response
from starlette import status
from drf_yasg.utils import swagger_auto_schema
from django.shortcuts import get_object_or_404

class addCommentView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=CommentSerializer, responses={201: 'Comment created successfully'})
    def post(self, request):
        serializer = CommentSerializer(data=request.data)
        if serializer.is_valid():
            content = serializer.validated_data['content']
            type = serializer.validated_data.get('type')
            target_id = serializer.validated_data.get('target_id')
            comment = Comment.objects.create(
                content=content,
                type = type,
                user=request.user,
                target_id=target_id,
            )
            return Response(CommentSerializer(comment).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class deleteCommentView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, id):
        comment = get_object_or_404(Comment, pk=id)
        self.check_object_permissions(request, comment)
        comment.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class updateCommentView(APIView):
    permission_classes = [IsAuthenticated, IsCommentOwner]

    @swagger_auto_schema(request_body=CommentSerializer, responses={200: 'Login success'})
    def put(self, request, id):
        user = request.user
        comment = get_object_or_404(Comment, id=id)
        serializer = CommentSerializer(comment, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class getCommentView(APIView):
    permission_classes = [IsAuthenticated]
    def get(self, request, target_id, type):
        comments = Comment.objects.filter(target_id=target_id, type=type)
        serializer = CommentsListWithUserSerializer(comments, many=True)
        return Response(serializer.data ,status = status.HTTP_200_OK)
