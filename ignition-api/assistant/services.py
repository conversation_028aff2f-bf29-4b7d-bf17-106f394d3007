"""
Dedicated service for generating plan options using AI.
This service is isolated from other AI operations to prevent interference.
"""

import os
import json
from typing import List, Dict, Any, Optional
from assistant.ai_providers import create_chat_completion


class PlanOptionsService:
    """Service for generating plan options with AI"""

    def __init__(self):
        self.provider = os.getenv('PLAN_OPTIONS_AI_PROVIDER', 'google')
        self.model = os.getenv('PLAN_OPTIONS_AI_MODEL', 'gemini-2.0-flash-exp')

    def generate_plan_options(self, prompt: str, duration: str = "3 months") -> List[Dict[str, Any]]:
        """
        Generate 3 plan options for the given prompt and duration.

        Args:
            prompt: Project description
            duration: Project duration

        Returns:
            List of plan options with milestones and tasks
        """
        try:
            # Generate options directly with AI
            plan_options = self._generate_with_ai(prompt, duration)

            if plan_options:
                return plan_options
            else:
                # If AI fails, raise an exception instead of using fallback
                raise Exception("AI failed to generate plan options")

        except Exception as e:
            print(f"Error in generate_plan_options: {str(e)}")
            # Re-raise the exception instead of returning fallback options
            raise Exception(f"Failed to generate plan options: {str(e)}")



    def _generate_with_ai(self, prompt: str, duration: str) -> Optional[List[Dict[str, Any]]]:
        """Generate plan options using AI"""
        try:
            # Import prompt functions from prompts.py
            from .prompts import get_generate_plan_options_system_prompt, get_generate_plan_options_user_prompt

            system_prompt = get_generate_plan_options_system_prompt()
            user_prompt = get_generate_plan_options_user_prompt(prompt, duration)

            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ]

            # Call AI with specific provider and model
            ai_response = create_chat_completion(
                messages=messages,
                provider=self.provider,
                model=self.model,
                temperature=0.3,
                max_tokens=21020  # Tăng để đảm bảo response đầy đủ và chi tiết
            )

            # Parse response
            print(f"AI response: {ai_response}")
            content = ai_response.get('content', '').strip()
            print(f"AI response for plan options: {content[:500]}...")

            # Extract and validate JSON
            plan_data = self._extract_and_validate_json(content)

            if plan_data and 'plan_options' in plan_data:
                return plan_data['plan_options']

            return None

        except Exception as e:
            print(f"AI generation failed: {str(e)}")
            return None



    def _extract_and_validate_json(self, content: str) -> Optional[Dict[str, Any]]:
        """Extract and validate JSON from AI response"""
        try:
            print(f"🔍 Raw AI content length: {len(content)}")
            print(f"🔍 Raw AI content preview: {content[:200]}...")

            # Try direct JSON parsing first
            try:
                plan_data = json.loads(content)
                if self._validate_plan_options(plan_data):
                    print("✅ Direct JSON parsing successful")
                    return plan_data
            except json.JSONDecodeError as e:
                print(f"❌ Direct JSON parsing failed: {str(e)}")

            # Try extracting JSON from markdown or other formatting
            json_content = self._extract_json_from_content(content)
            if json_content:
                print(f"🔍 Extracted JSON length: {len(json_content)}")
                print(f"🔍 Extracted JSON preview: {json_content[:200]}...")
                try:
                    plan_data = json.loads(json_content)
                    if self._validate_plan_options(plan_data):
                        print("✅ Extracted JSON parsing successful")
                        return plan_data
                    else:
                        print("❌ JSON validation failed")
                except json.JSONDecodeError as e:
                    print(f"❌ Extracted JSON parsing failed: {str(e)}")
                    # Thử repair JSON bị cắt
                    repaired_json = self._try_repair_truncated_json(json_content)
                    if repaired_json:
                        try:
                            plan_data = json.loads(repaired_json)
                            if self._validate_plan_options(plan_data):
                                print("✅ Repaired JSON parsing successful")
                                return plan_data
                        except json.JSONDecodeError:
                            print("❌ Repaired JSON still invalid")

            print("❌ All JSON extraction methods failed")
            return None

        except Exception as e:
            print(f"JSON extraction failed: {str(e)}")
            return None

    def _extract_json_from_content(self, content: str) -> Optional[str]:
        """Extract JSON from content with various formatting"""
        # Method 1: Remove markdown formatting
        if '```json' in content:
            print("🔍 Found markdown JSON formatting")
            start = content.find('```json') + 7
            end = content.find('```', start)
            if end != -1:
                extracted = content[start:end].strip()
                print(f"🔍 Markdown extraction result length: {len(extracted)}")
                return extracted

        # Method 2: Remove any ``` formatting
        if '```' in content:
            print("🔍 Found generic markdown formatting")
            lines = content.split('\n')
            json_lines = []
            in_code_block = False

            for line in lines:
                if line.strip().startswith('```'):
                    in_code_block = not in_code_block
                    continue
                if in_code_block or (not in_code_block and (line.strip().startswith('{') or line.strip().startswith('"'))):
                    json_lines.append(line)

            if json_lines:
                extracted = '\n'.join(json_lines).strip()
                print(f"🔍 Generic markdown extraction result length: {len(extracted)}")
                return extracted

        # Method 3: Find JSON between braces
        print("🔍 Trying brace-based extraction")
        start = content.find('{')
        end = content.rfind('}')
        if start != -1 and end != -1 and end > start:
            extracted = content[start:end + 1]
            print(f"🔍 Brace extraction result length: {len(extracted)}")
            return extracted

        print("🔍 No JSON extraction method worked")
        return None

    def _try_repair_truncated_json(self, json_content: str) -> Optional[str]:
        """Try to repair truncated JSON by closing unclosed structures"""
        try:
            print("🔧 Attempting to repair truncated JSON...")

            # Count open and close braces/brackets
            open_braces = json_content.count('{')
            close_braces = json_content.count('}')
            open_brackets = json_content.count('[')
            close_brackets = json_content.count(']')

            # If JSON is truncated, try to close it properly
            repaired = json_content.strip()

            # Remove any incomplete string at the end
            if repaired.endswith('"'):
                # Find the last complete quote pair
                last_quote = repaired.rfind('"', 0, -1)
                if last_quote != -1:
                    repaired = repaired[:last_quote + 1]

            # Close unclosed strings
            quote_count = repaired.count('"')
            if quote_count % 2 != 0:
                repaired += '"'

            # Close unclosed brackets and braces
            missing_brackets = open_brackets - close_brackets
            missing_braces = open_braces - close_braces

            for _ in range(missing_brackets):
                repaired += ']'

            for _ in range(missing_braces):
                repaired += '}'

            print(f"🔧 Repaired JSON length: {len(repaired)}")
            return repaired

        except Exception as e:
            print(f"🔧 JSON repair failed: {str(e)}")
            return None

    def _validate_plan_options(self, data: Dict[str, Any]) -> bool:
        """Validate the structure of plan options"""
        try:
            print("🔍 Starting validation...")

            if not isinstance(data, dict) or 'plan_options' not in data:
                print("❌ Invalid data structure or missing plan_options")
                return False

            plan_options = data['plan_options']
            if not isinstance(plan_options, list):
                print("❌ plan_options is not a list")
                return False

            if len(plan_options) != 3:
                print(f"❌ Expected 3 options, got {len(plan_options)}")
                return False

            for i, option in enumerate(plan_options):
                print(f"🔍 Validating option {i+1}...")

                if not isinstance(option, dict):
                    print(f"❌ Option {i+1} is not a dict")
                    return False

                required_fields = ['option_number', 'name', 'description', 'strategy', 'milestones']
                missing_fields = [field for field in required_fields if field not in option]
                if missing_fields:
                    print(f"❌ Option {i+1} missing fields: {missing_fields}")
                    return False

                milestones = option['milestones']
                if not isinstance(milestones, list):
                    print(f"❌ Option {i+1} milestones is not a list")
                    return False

                print(f"🔍 Option {i+1} has {len(milestones)} milestones")
                if len(milestones) < 1:  # Flexible: ít nhất 1 milestone
                    print(f"❌ Option {i+1} expected at least 1 milestone, got {len(milestones)}")
                    return False

                for j, milestone in enumerate(milestones):
                    if not isinstance(milestone, dict):
                        print(f"❌ Option {i+1} milestone {j+1} is not a dict")
                        return False

                    # Kiểm tra các field bắt buộc của milestone
                    milestone_required_fields = ['name', 'description']
                    milestone_missing_fields = [field for field in milestone_required_fields if field not in milestone]
                    if milestone_missing_fields:
                        print(f"❌ Option {i+1} milestone {j+1} missing fields: {milestone_missing_fields}")
                        return False

                    if 'tasks' not in milestone or not isinstance(milestone['tasks'], list):
                        print(f"❌ Option {i+1} milestone {j+1} missing or invalid tasks")
                        return False

                    tasks_count = len(milestone['tasks'])
                    print(f"🔍 Option {i+1} milestone {j+1} has {tasks_count} tasks")
                    if tasks_count < 1:  # Flexible: ít nhất 1 task
                        print(f"❌ Option {i+1} milestone {j+1} expected at least 1 task, got {tasks_count}")
                        return False

                    # Kiểm tra structure của từng task
                    for k, task in enumerate(milestone['tasks']):
                        if not isinstance(task, dict):
                            print(f"❌ Option {i+1} milestone {j+1} task {k+1} is not a dict")
                            return False

                        task_required_fields = ['name', 'description']
                        task_missing_fields = [field for field in task_required_fields if field not in task]
                        if task_missing_fields:
                            print(f"❌ Option {i+1} milestone {j+1} task {k+1} missing fields: {task_missing_fields}")
                            return False

            print("✅ Validation successful")
            return True

        except Exception as e:
            print(f"❌ Validation exception: {str(e)}")
            return False


