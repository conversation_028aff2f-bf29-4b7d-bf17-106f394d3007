"""
Clean, optimized prompt templates for AI-powered project planning.
Generates comprehensive 5-5-5 structure plans with detailed descriptions.
"""

from datetime import datetime


def get_subtask_generation_prompt(selected_option, original_prompt):
    """
    Generate user prompt for creating subtasks from a selected plan option.
    Contains only user-specific content, format requirements are in system prompt.

    Args:
        selected_option (dict): The selected plan option structure
        original_prompt (str): Original user project description

    Returns:
        str: User prompt with project context and plan structure
    """
    import json

    return (
        f"ORIGINAL PROJECT CONTEXT:\n"
        f"Project description: {original_prompt}\n"
        f"Selected plan: {selected_option.get('name')}\n"
        f"Plan description: {selected_option.get('description')}\n"
        f"Strategy: {selected_option.get('strategy')}\n\n"

        f"CURRENT PLAN STRUCTURE REQUIRING SUBTASK CREATION:\n"
        f"{json.dumps(selected_option, indent=2, ensure_ascii=False)}\n\n"

        f"Please create detailed subtasks for each task in the above plan according to the requirements specified in the system prompt."
    )


def get_create_plan_from_option_system_prompt():
    """
    Returns the system prompt for create-plan-from-option API subtask generation.
    """
    from datetime import datetime
    today = datetime.now().strftime('%Y-%m-%d')

    return (
        "You are a project planning expert who can create detailed subtasks on existing plan frameworks. "
        "Your task is to create comprehensive subtasks with detailed descriptions from the selected plan option.\n\n"

        "CRITICAL REQUIREMENTS (MUST BE FOLLOWED EXACTLY):\n"
        "- For each task in each milestone, create the exact number of subtasks needed to complete that task\n"
        "- Subtask name: MINIMUM 25 words with detailed description of the subtask\n"
        "- Each subtask should be completable within the estimated hours\n"
        "- Include specific tools, technologies, and methods when possible\n"
        "- Provide step-by-step implementation guidance\n"
        "- Maintain consistency with original project requirements\n"
        "- The goal is to complete the objectives of the tasks and contribute to completing the milestones\n\n"

        "CONTENT GUIDELINES:\n"
        "- Subtask name should be a comprehensive action description (25+ words)\n"
        "- Subtask description should include technical details, expected outcomes, tools used (40+ words)\n"
        "- Logical progression from preparation to implementation to validation\n"
        "- Each subtask should have clear completion criteria\n"
        "- Include specific deliverables and quality standards\n"
        "- Consider dependencies between subtasks\n\n"

        "RESPONSE FORMAT:\n"
        "Return JSON with the following structure:\n"
        "```json\n"
        "{\n"
        '  "enhanced_plan": {\n'
        '    "name": "plan name from selected option",\n'
        '    "description": "enhanced plan description with detailed subtasks",\n'
        '    "milestones": [\n'
        "      {\n"
        '        "name": "milestone name from original plan",\n'
        '        "description": "enhanced milestone description (minimum 60 words including purpose, objectives, deliverables, outcomes, challenges, success criteria)",\n'
        '        "estimated_duration": "estimated time from original plan",\n'
        '        "success_criteria": "detailed success criteria",\n'
        '        "tasks": [\n'
        "          {\n"
        '            "name": "task name from original plan",\n'
        '            "description": "enhanced task description (minimum 40 words explaining implementation approach, methods, tools, expected deliverables)",\n'
        '            "estimated_duration": "1-2 days",\n'
        '            "subtasks": [\n'
        "              {\n"
        '                "name": "comprehensive subtask action description (minimum 25 words detailing specific steps, technical implementation, tools used, expected results)",\n'
        '                "description": "detailed implementation guidance (minimum 40 words including step-by-step instructions, technical details, quality criteria, completion standards, dependencies, and deliverables)"\n'
        "              }\n"
        "            ]\n"
        "          }\n"
        "        ]\n"
        "      }\n"
        "    ]\n"
        "  }\n"
        "}\n"
        "```\n\n"

        "PLAN LANGUAGE: follow the input language from the user\n\n"


        "VALIDATION RULES:\n"
        "- Maintain original milestone and task structure\n"
        "- Milestone names: 7-10 words (concise but descriptive)\n"
        "- Task names: around 15 words (detailed but not overly long)\n"
        "- Subtask name: 10-15 words with clear action description\n"
        "- Subtask description: 25-40 words with detailed implementation guidance\n"
        "- Each task must have a certain number of subtasks to complete that task\n"
        "- Include technical details, tools, methods and expected results\n"
        "- Valid JSON with correct formatting and brackets\n"
        "- No markdown formatting in response\n"
        f"- Consider project timeline starting from {today}\n\n"

        "IMPORTANT: Only return valid JSON, no markdown formatting or additional explanations."
    )


def get_generate_plan_options_system_prompt():
    """
    Returns the system prompt for generate-plan-options API.
    """
    return """You are a professional project planning expert.

TASK: Create exactly 3 different project plan options with structure:
- Each option has an appropriate number of important milestones suitable for that option
- Each milestone has an appropriate number of tasks to complete that milestone
- Focus on 3 options to give users diverse choices. These options must be truly rich and diverse to give users better choices
- The number of milestones and tasks in each option must be sufficient to achieve the objectives set out in the user's requirements.
- The options must have content to achieve the objectives set out in the user's requirements.
- The descriptions of plans, milestones and tasks must be detailed, clear and easy to understand.
- The names of milestones and tasks must also have content in the description of the corresponding milestone and task
- In each plan name, there should not be words like "option 1", "option 2", "option 3" or equivalent, but must be natural plan names
- Each milestone needs to be described with a name of 7-10 words (concise but descriptive)
- Each task needs to be described with a name of around 15 words (detailed but not overly long)

FORMAT: Return valid JSON with structure:
{"plan_options": [{"option_number": 1, "name": "Option Name", "description": "Approach description", "strategy": "Special point of the option", "milestones": [{"name": "Milestone Name", "description": "Milestone description", "estimated_duration": "1-2 weeks", "tasks": [{"name": "Task Name", "description": "Task description"}]}]}]}

PLAN LANGUAGE: follow the input language from the user

IMPORTANT RULES:
- Only return pure JSON
- DO NOT use ```json or ```
- NO markdown formatting
- NO explanations or additional text
- Start immediately with { and end with }"""


def get_generate_plan_options_user_prompt(prompt: str, duration: str):
    """
    Returns the user prompt for generate-plan-options API.
    """
    return f"""Project: "{prompt}"
Implementation time: {duration}
Output language of results: follow the input language from the user

Create 3 plan options with different approaches. Each option should have its own strategy and implementation style."""


def get_enhanced_plan_name_prompt(original_name: str, original_prompt: str, plan_description: str):
    """
    Generate prompt for creating a more detailed and descriptive plan name.

    Args:
        original_name (str): Original plan name from option
        original_prompt (str): Original user project description
        plan_description (str): Plan description

    Returns:
        str: User prompt for enhanced plan name generation
    """
    return (
        f"ORIGINAL PLAN NAME: {original_name}\n"
        f"PROJECT CONTEXT: {original_prompt}\n"
        f"PLAN DESCRIPTION: {plan_description}\n\n"

        f"Please create a more detailed and descriptive plan name based on the above information. "
        f"The enhanced name should:\n"
        f"- Be more specific and descriptive than the original name\n"
        f"- Include key aspects or focus areas of the plan\n"
        f"- Be around 10-15 words long\n"
        f"- Clearly convey what the plan will accomplish\n"
        f"- Use the same language as the original content\n\n"

        f"Return only the enhanced plan name, no additional text or formatting."
    )
