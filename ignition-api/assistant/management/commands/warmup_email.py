from django.core.management.base import BaseCommand
from django.conf import settings
import os
import time
import random

from utils.email import send_email_with_name


class Command(BaseCommand):
    help = "Warm up email reputation by sending legitimate emails to improve deliverability"

    def add_arguments(self, parser):
        parser.add_argument(
            "--count",
            dest="count",
            default=5,
            type=int,
            help="Number of warm-up emails to send (default: 5)",
        )
        parser.add_argument(
            "--to",
            dest="to",
            default="<EMAIL>",
            help="Recipient email for warm-up (default: <EMAIL>)",
        )

    def handle(self, *args, **options):
        count = options["count"]
        to_email = options["to"]

        self.stdout.write(self.style.HTTP_INFO(f"--- Email Warm-up Process ---"))
        self.stdout.write(f"Sending {count} warm-up emails to {to_email}")
        
        # Professional email templates
        templates = [
            {
                "subject": "Welcome to Ignition AI - Account Created",
                "message": """Dear User,

Welcome to Ignition AI Platform! Your account has been successfully created.

Getting Started:
1. Complete your profile setup
2. Explore our AI planning tools
3. Create your first project
4. Invite team members

Our platform offers advanced AI-powered project planning, real-time collaboration, and comprehensive analytics.

Need help? Contact our support team.

Best regards,
The Ignition AI Team

---
Ignition AI Platform
https://ignitionai.site
© 2025 All rights reserved."""
            },
            {
                "subject": "Your Ignition AI Account - Next Steps",
                "message": """Hello,

Thank you for joining Ignition AI Platform.

Here's what you can do next:
• Set up your workspace
• Import existing projects
• Configure team settings
• Explore AI features

Our AI assistant is ready to help you plan and manage your projects more efficiently.

Questions? We're here to <NAME_EMAIL>

Best regards,
The Ignition AI Team

---
This email was sent to confirm your account setup.
Ignition AI Platform | https://ignitionai.site"""
            },
            {
                "subject": "Ignition AI - Account Verification Complete",
                "message": """Dear User,

Your Ignition AI account verification is complete.

Account Status: Active
Plan: Professional
Features Unlocked: All

You now have access to:
- AI-powered project planning
- Team collaboration tools
- Advanced analytics
- Priority support

Start creating your first project today!

Best regards,
The Ignition AI Team

---
Ignition AI Platform
Support: <EMAIL>
Unsubscribe: <EMAIL>"""
            }
        ]

        success_count = 0
        
        for i in range(count):
            template = random.choice(templates)
            
            # Add unique elements to avoid duplicate detection
            subject = f"{template['subject']} #{i+1001}"
            message = template['message'] + f"\n\nEmail ID: #{i+1001}\nSent: January 8, 2025"
            
            self.stdout.write(f"\nSending email {i+1}/{count}...")
            self.stdout.write(f"Subject: {subject}")
            
            try:
                result = send_email_with_name(
                    subject=subject,
                    message=message,
                    recipient_list=[to_email],
                    fail_silently=False,
                )
                
                if result == 1:
                    success_count += 1
                    self.stdout.write(self.style.SUCCESS(f"✅ Email {i+1} sent successfully"))
                else:
                    self.stdout.write(self.style.ERROR(f"❌ Email {i+1} failed"))
                    
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"❌ Email {i+1} exception: {e}"))
            
            # Random delay between emails (1-3 seconds)
            if i < count - 1:  # Don't delay after last email
                delay = random.uniform(1, 3)
                self.stdout.write(f"Waiting {delay:.1f} seconds...")
                time.sleep(delay)

        self.stdout.write(self.style.HTTP_INFO(f"\n--- Warm-up Complete ---"))
        self.stdout.write(f"Successfully sent: {success_count}/{count} emails")
        self.stdout.write("Check your email inbox - these should have better deliverability!")
        
        if success_count == count:
            self.stdout.write(self.style.SUCCESS("🎉 All emails sent successfully! Email reputation should improve."))
        else:
            self.stdout.write(self.style.WARNING(f"⚠️ {count - success_count} emails failed. Check configuration."))
