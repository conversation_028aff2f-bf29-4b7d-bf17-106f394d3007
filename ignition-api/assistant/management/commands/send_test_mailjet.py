from django.core.management.base import BaseCommand
from django.conf import settings
import os

from utils.email import send_email_with_name, get_formatted_from_email


class Command(BaseCommand):
    help = "Send a test email using current Mailjet SMTP settings (with API fallback)."

    def add_arguments(self, parser):
        parser.add_argument(
            "--to",
            dest="to",
            default="<EMAIL>",
            help="Recipient email to send the test message to (default: <EMAIL>)",
        )
        parser.add_argument(
            "--subject",
            dest="subject",
            default="Ignition Mailjet Test",
            help="Email subject (default: 'Ignition Mailjet Test')",
        )

    def handle(self, *args, **options):
        to_email = options["to"]
        subject = options["subject"]

        # Resolve sender formatting
        sender_formatted = get_formatted_from_email()
        from_email_env = os.getenv("FROM_EMAIL")
        from_name_env = os.getenv("FROM_NAME")

        # Show effective SMTP configuration
        self.stdout.write(self.style.HTTP_INFO("--- SMTP Configuration ---"))
        self.stdout.write(f"EMAIL_BACKEND: {getattr(settings, 'EMAIL_BACKEND', None)}")
        self.stdout.write(f"EMAIL_HOST: {getattr(settings, 'EMAIL_HOST', None)}")
        self.stdout.write(f"EMAIL_PORT: {getattr(settings, 'EMAIL_PORT', None)}")
        self.stdout.write(f"EMAIL_USE_TLS: {getattr(settings, 'EMAIL_USE_TLS', None)}")
        self.stdout.write(
            f"MAILJET_API_KEY present: {bool(os.getenv('MAILJET_API_KEY') or os.getenv('MAIL_JET_API_KEY'))}"
        )
        self.stdout.write(
            f"MAILJET_SECRET_KEY present: {bool(os.getenv('MAILJET_SECRET_KEY') or os.getenv('MAIL_JET_API_SECRET'))}"
        )

        # Show sender details
        self.stdout.write(self.style.HTTP_INFO("--- Sender ---"))
        self.stdout.write(f"FROM_EMAIL (env): {from_email_env}")
        self.stdout.write(f"FROM_NAME (env): {from_name_env}")
        self.stdout.write(f"Effective sender used: {sender_formatted}")

        if not from_email_env:
            self.stdout.write(self.style.WARNING("FROM_EMAIL is not set. Please set FROM_EMAIL in your .env"))

        # Compose professional message
        message = (
            "Dear User,\n\n"
            "Welcome to Ignition AI - Your Intelligent Planning Assistant.\n\n"
            "This email confirms that our notification system is working properly. "
            "You will receive important updates about your account and projects through this email address.\n\n"
            "Key Features:\n"
            "• AI-powered project planning\n"
            "• Real-time collaboration\n"
            "• Smart task management\n"
            "• Progress tracking\n\n"
            "If you have any questions, please contact our support team.\n\n"
            "Best regards,\n"
            "The Ignition AI Team\n\n"
            "---\n"
            "This is an automated message from Ignition AI Platform.\n"
            "Visit us at: https://ignitionai.site\n"
        )

        self.stdout.write(self.style.HTTP_INFO("--- Sending ---"))
        self.stdout.write(f"To: {to_email}")

        try:
            result = send_email_with_name(
                subject=subject,
                message=message,
                recipient_list=[to_email],
                fail_silently=False,
            )
            self.stdout.write(self.style.SUCCESS(f"send_email_with_name result: {result}"))
            self.stdout.write(self.style.SUCCESS("If result=1, SMTP send succeeded. If SMTP failed, util attempted Mailjet Web API fallback."))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Sending failed: {e}"))
            raise
