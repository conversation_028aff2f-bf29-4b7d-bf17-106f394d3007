"""
AI Provider abstraction layer for supporting multiple AI services.
Currently supports: OpenAI, OpenRouter
"""

import os
import json
import requests
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

load_dotenv()

try:
    import google.generativeai as genai
    GOOGLE_AI_AVAILABLE = True
except ImportError:
    GOOGLE_AI_AVAILABLE = False


class AIProvider(ABC):
    """Abstract base class for AI providers"""

    @abstractmethod
    def create_chat_completion(self, messages: List[Dict], model: str = None, **kwargs) -> Dict[str, Any]:
        """Create a chat completion"""
        pass

    @abstractmethod
    def get_default_model(self) -> str:
        """Get the default model for this provider"""
        pass

    @abstractmethod
    def is_configured(self) -> bool:
        """Check if the provider is properly configured"""
        pass


class OpenAIProvider(AIProvider):
    """OpenAI provider implementation"""

    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY')
        self.base_url = "https://api.openai.com/v1"
        self.default_model = os.getenv('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini')

    def create_chat_completion(self, messages: List[Dict], model: str = None, **kwargs) -> Dict[str, Any]:
        """Create chat completion using OpenAI API"""
        if not self.is_configured():
            raise ValueError("OpenAI API key not configured")

        try:
            from openai import OpenAI
            client = OpenAI(api_key=self.api_key)

            response = client.chat.completions.create(
                model=model or self.default_model,
                messages=messages,
                **kwargs
            )

            return {
                'content': response.choices[0].message.content,
                'model': response.model,
                'usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                },
                'provider': 'openai'
            }
        except Exception as e:
            raise Exception(f"OpenAI API error: {str(e)}")

    def get_default_model(self) -> str:
        return self.default_model

    def is_configured(self) -> bool:
        return bool(self.api_key)


class OpenRouterProvider(AIProvider):
    """OpenRouter provider implementation"""

    def __init__(self):
        self.api_key = os.getenv('OPENROUTER_API_KEY')
        self.base_url = "https://openrouter.ai/api/v1"
        self.default_model = os.getenv('OPENROUTER_DEFAULT_MODEL')
        self.app_name = os.getenv('OPENROUTER_APP_NAME', 'Ignition')
        self.site_url = os.getenv('OPENROUTER_SITE_URL', 'https://ignition.app')

    def create_chat_completion(self, messages: List[Dict], model: str = None, **kwargs) -> Dict[str, Any]:
        """Create chat completion using OpenRouter API"""
        if not self.is_configured():
            raise ValueError("OpenRouter API key not configured")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "HTTP-Referer": self.site_url,
            "X-Title": self.app_name,
            "Content-Type": "application/json"
        }

        payload = {
            "model": model or self.default_model,
            "messages": messages,
            **kwargs
        }

        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=60  # 1 minute timeout - much faster
            )
            response.raise_for_status()

            # Log response details for debugging
            print(f"OpenRouter response status: {response.status_code}")
            print(f"OpenRouter response length: {len(response.text)}")

            # Check if response is empty or too short
            if not response.text or len(response.text) < 10:
                raise Exception(f"OpenRouter returned empty or too short response: {len(response.text)} characters")

            # Try to parse JSON with better error handling
            try:
                data = response.json()
            except json.JSONDecodeError as e:
                print(f"JSON decode error at position {e.pos}: {e.msg}")
                print(f"Response text (first 500 chars): {response.text[:500]}")
                print(f"Response text (last 500 chars): {response.text[-500:]}")

                # Try to find and extract valid JSON from response
                response_text = response.text.strip()

                # Look for JSON in response
                start_idx = response_text.find('{')
                end_idx = response_text.rfind('}')

                if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                    try:
                        json_candidate = response_text[start_idx:end_idx + 1]
                        data = json.loads(json_candidate)
                        print("Successfully extracted JSON from partial response")
                    except json.JSONDecodeError:
                        raise Exception(f"OpenRouter API error: Invalid JSON response. Error: {e.msg} at position {e.pos}")
                else:
                    raise Exception(f"OpenRouter API error: No valid JSON found in response")

            # Validate response structure
            if not isinstance(data, dict):
                raise Exception(f"OpenRouter API error: Response is not a JSON object")

            if 'choices' not in data or not data['choices']:
                raise Exception(f"OpenRouter API error: No choices in response")

            if 'message' not in data['choices'][0] or 'content' not in data['choices'][0]['message']:
                raise Exception(f"OpenRouter API error: Invalid response structure")

            return {
                'content': data['choices'][0]['message']['content'],
                'model': data.get('model', 'unknown'),
                'usage': data.get('usage', {}),
                'provider': 'openrouter'
            }
        except requests.exceptions.RequestException as e:
            raise Exception(f"OpenRouter API error: {str(e)}")
        except (KeyError, IndexError) as e:
            raise Exception(f"OpenRouter response parsing error: {str(e)}")

    def get_default_model(self) -> str:
        return self.default_model

    def is_configured(self) -> bool:
        return bool(self.api_key)


class GoogleGenerativeAIProvider(AIProvider):
    """Google Generative AI provider implementation"""

    def __init__(self):
        # Support both GEMINI_API_KEY and GOOGLE_AI_API_KEY for backward compatibility
        self.api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_AI_API_KEY')
        self.default_model = os.getenv('GEMINI_DEFAULT_MODEL') or os.getenv('GOOGLE_AI_DEFAULT_MODEL', 'gemini-2.0-flash-exp')
        if self.api_key and GOOGLE_AI_AVAILABLE:
            genai.configure(api_key=self.api_key)

    def create_chat_completion(self, messages: List[Dict], model: str = None, **kwargs) -> Dict[str, Any]:
        """Create chat completion using Google Generative AI"""
        if not self.is_configured():
            raise ValueError("Google AI API key not configured or library not available")

        try:
            # Convert messages to Google AI format
            google_messages = self._convert_messages_to_google_format(messages)

            # Initialize the model
            model_name = model or self.default_model
            google_model = genai.GenerativeModel(model_name)

            # Extract generation config from kwargs
            generation_config = {}
            if 'temperature' in kwargs:
                generation_config['temperature'] = kwargs['temperature']
            if 'max_tokens' in kwargs:
                generation_config['max_output_tokens'] = kwargs['max_tokens']

            # Generate response
            response = google_model.generate_content(
                google_messages,
                generation_config=genai.types.GenerationConfig(**generation_config) if generation_config else None
            )

            # Check if response was blocked
            if response.candidates[0].finish_reason.name in ['SAFETY', 'RECITATION']:
                raise Exception(f"Google AI response was blocked: {response.candidates[0].finish_reason.name}")

            return {
                'content': response.text,
                'model': model_name,
                'usage': {
                    'prompt_tokens': response.usage_metadata.prompt_token_count if response.usage_metadata else 0,
                    'completion_tokens': response.usage_metadata.candidates_token_count if response.usage_metadata else 0,
                    'total_tokens': response.usage_metadata.total_token_count if response.usage_metadata else 0
                },
                'provider': 'google'
            }
        except Exception as e:
            raise Exception(f"Google AI API error: {str(e)}")

    def _convert_messages_to_google_format(self, messages: List[Dict]) -> str:
        """Convert OpenAI-style messages to Google AI format"""
        converted_parts = []

        for message in messages:
            role = message.get('role', '')
            content = message.get('content', '')

            if role == 'system':
                converted_parts.append(f"System: {content}")
            elif role == 'user':
                converted_parts.append(f"User: {content}")
            elif role == 'assistant':
                converted_parts.append(f"Assistant: {content}")

        return "\n\n".join(converted_parts)

    def get_default_model(self) -> str:
        return self.default_model

    def is_configured(self) -> bool:
        return bool(self.api_key and GOOGLE_AI_AVAILABLE)


class AIProviderManager:
    """Manager class for handling multiple AI providers"""

    def __init__(self):
        google_provider = GoogleGenerativeAIProvider()
        self.providers = {
            'openai': OpenAIProvider(),
            'openrouter': OpenRouterProvider(),
            'google': google_provider,
            'gemini': google_provider  # Alias for Google provider
        }
        self.default_provider = os.getenv('AI_PROVIDER', 'openrouter').lower()

    def get_provider(self, provider_name: str = None) -> AIProvider:
        """Get a specific provider or the default one"""
        provider_name = provider_name or self.default_provider

        if provider_name not in self.providers:
            raise ValueError(f"Unknown provider: {provider_name}")

        provider = self.providers[provider_name]
        if not provider.is_configured():
            raise ValueError(f"Provider {provider_name} is not properly configured")

        return provider

    def create_chat_completion(self, messages: List[Dict], provider: str = None, model: str = None, **kwargs) -> Dict[str, Any]:
        """Create chat completion using specified or default provider"""
        ai_provider = self.get_provider(provider)
        return ai_provider.create_chat_completion(messages, model, **kwargs)




# Global instance
ai_manager = AIProviderManager()


def create_chat_completion(messages: List[Dict], provider: str = None, model: str = None, **kwargs) -> Dict[str, Any]:
    """
    Convenience function to create chat completion.

    Args:
        messages: List of message dictionaries
        provider: AI provider name ('openai', 'openrouter', 'google')
        model: Model name (optional, uses provider default)
        **kwargs: Additional parameters for the API

    Returns:
        Dictionary with response content and metadata
    """
    # Log the AI call details
    ai_provider = ai_manager.get_provider(provider)
    actual_model = model or ai_provider.get_default_model()
    provider_name = provider or os.getenv('AI_PROVIDER', 'openrouter')

    print(f"🤖 AI API Call - Provider: {provider_name}, Model: {actual_model}")

    result = ai_manager.create_chat_completion(messages, provider, model, **kwargs)

    # Log response info if available
    if result and 'model' in result:
        print(f"✅ AI Response received - Model used: {result['model']}")
    else:
        print(f"✅ AI Response received from {provider_name}")

    return result