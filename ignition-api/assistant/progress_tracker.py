"""
Progress tracking utility for AI plan generation.
Provides real-time feedback to users during the plan creation process.
"""

import uuid
import re
from datetime import datetime, timedelta
from django.utils import timezone
from plans.models import PlanCreationProgress


class PlanCreationProgressTracker:
    """
    Tracks and updates progress during AI plan generation.
    Provides detailed, user-friendly status updates.
    """

    def __init__(self, user):
        self.user = user
        self.session_id = str(uuid.uuid4())
        self.progress = None

    def start(self, prompt):
        """Initialize progress tracking for a new plan creation session"""
        self.progress = PlanCreationProgress.objects.create(
            user=self.user,
            session_id=self.session_id,
            current_step='initializing',
            status_message='Starting to create your plan...',
            step_details={
                'prompt': prompt
            },
            status='initializing'
        )
        return self.session_id

    def update_step(self, step, message, details=None):
        """Update the current progress step"""
        if not self.progress:
            return

        step_details = self.progress.step_details.copy()
        if details:
            step_details.update(details)

        self.progress.current_step = step
        self.progress.status_message = message
        self.progress.step_details = step_details
        self.progress.status = step
        self.progress.save()

        print(f"Progress Update: {message}")

    def update_ai_processing(self):
        """Update when AI is processing the request"""
        self.update_step(
            step='generating_structure',
            message='AI is analyzing your requirements and generating plan structure...'
        )

    def update_parsing_response(self):
        """Update when parsing AI response"""
        self.update_step(
            step='generating_structure',
            message='AI has generated the plan! Now parsing and validating the structure...'
        )

    def update_creating_milestones(self, milestone_count=0, total_milestones=5):
        """Update when creating milestones"""
        self.update_step(
            step='creating_milestones',
            message='Creating milestones...'
        )

    def update_creating_tasks(self, task_count=0, total_tasks=25):
        """Update when creating tasks"""
        self.update_step(
            step='creating_tasks',
            message='Creating tasks...'
        )

    def update_creating_subtasks(self, subtask_count=0, total_subtasks=125):
        """Update when creating subtasks"""
        self.update_step(
            step='creating_subtasks',
            message='Creating detailed subtasks...'
        )

    def update_finalizing(self):
        """Update when finalizing the plan"""
        self.update_step(
            step='finalizing',
            message='Finalizing your plan...'
        )

    def complete(self, plan):
        """Mark the progress as completed"""
        if not self.progress:
            return

        # Update basic fields first
        self.progress.current_step = 'completed'
        self.progress.status_message = f'Your plan "{plan.name}" has been created successfully!'
        self.progress.status = 'completed'
        self.progress.completed_at = timezone.now()

        # Ensure all step_details values are JSON serializable
        step_details = self.progress.step_details.copy() if self.progress.step_details else {}

        # Convert any UUID objects to strings recursively
        def convert_uuids(obj):
            if hasattr(obj, 'hex'):  # UUID object
                return str(obj)
            elif isinstance(obj, dict):
                return {k: convert_uuids(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_uuids(item) for item in obj]
            else:
                return obj

        step_details = convert_uuids(step_details)

        step_details.update({
            'plan_id': str(plan.id),  # Convert UUID to string
            'plan_slug': str(plan.slug),  # Convert UUID slug to string
            'plan_name': plan.name,
            'completion_time': timezone.now().isoformat()
        })
        self.progress.step_details = step_details

        # Set plan_id instead of plan object to avoid UUID serialization issues
        self.progress.plan_id = plan.id
        self.progress.save()

        print(f"Plan creation completed: {plan.name}")

        # Calculate and assign dates to tasks
        self.calculate_plan_dates(plan)

    def calculate_plan_dates(self, plan):
        """Calculate and assign start_date and end_date to all tasks in the plan"""
        try:
            from plans.models import Milestone, Task

            print(f"Calculating dates for plan: {plan.name}")

            # Start from today
            current_date = timezone.now().date()

            # Get all milestones ordered by creation
            milestones = Milestone.objects.filter(plan=plan).order_by('id')

            for milestone in milestones:
                print(f"Processing milestone: {milestone.name}")

                # Parse estimated duration (e.g., "3 weeks", "2 weeks")
                duration_weeks = self.parse_duration_to_weeks(milestone.estimated_duration)
                milestone_days = duration_weeks * 7

                # Get tasks for this milestone
                tasks = Task.objects.filter(milestone=milestone).order_by('id')
                task_count = tasks.count()

                if task_count > 0:
                    # Distribute milestone duration among tasks
                    days_per_task = max(1, milestone_days // task_count)

                    for i, task in enumerate(tasks):
                        # Calculate task start and end dates
                        task_start = current_date + timedelta(days=i * days_per_task)
                        task_end = task_start + timedelta(days=days_per_task - 1)

                        # Update task dates
                        task.start_date = task_start
                        task.end_date = task_end
                        task.save()

                        print(f"  Task: {task.name[:50]}... -> {task_start} to {task_end}")

                # Move to next milestone start date
                current_date = current_date + timedelta(days=milestone_days)

            print(f"Date calculation completed for plan: {plan.name}")

        except Exception as e:
            print(f"Error calculating plan dates: {str(e)}")

    def parse_duration_to_weeks(self, duration_str):
        """Parse duration string like '3 weeks', '2 weeks' to number of weeks"""
        if not duration_str:
            return 1  # Default to 1 week

        # Extract number from string like "3 weeks", "2 weeks"
        import re
        match = re.search(r'(\d+)\s*week', duration_str.lower())
        if match:
            return int(match.group(1))

        # If no weeks found, try days
        match = re.search(r'(\d+)\s*day', duration_str.lower())
        if match:
            return max(1, int(match.group(1)) // 7)  # Convert days to weeks

        # Default to 1 week if can't parse
        return 1

    def fail(self, error_message):
        """Mark the progress as failed"""
        if not self.progress:
            return

        self.progress.current_step = 'failed'
        self.progress.status_message = f'Plan creation failed: {error_message}'
        self.progress.status = 'failed'
        self.progress.error_message = error_message
        self.progress.completed_at = timezone.now()

        # Ensure step_details is JSON serializable before saving
        if self.progress.step_details:
            def convert_uuids(obj):
                if hasattr(obj, 'hex'):  # UUID object
                    return str(obj)
                elif isinstance(obj, dict):
                    return {k: convert_uuids(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_uuids(item) for item in obj]
                else:
                    return obj

            self.progress.step_details = convert_uuids(self.progress.step_details)

        self.progress.save()

        print(f"Plan creation failed: {error_message}")

    @classmethod
    def get_latest_progress(cls, user):
        """Get the latest progress for a user"""
        try:
            return PlanCreationProgress.objects.filter(user=user).latest('started_at')
        except PlanCreationProgress.DoesNotExist:
            return None

    @classmethod
    def cleanup_old_progress(cls, user, keep_recent=5):
        """Clean up old progress records, keeping only the most recent ones"""
        old_progress = PlanCreationProgress.objects.filter(user=user).order_by('-started_at')[keep_recent:]
        for progress in old_progress:
            progress.delete()
