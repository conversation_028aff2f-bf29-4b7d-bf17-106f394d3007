import os
import json
import asyncio
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from assistant.serializers import PlanPromptSerializer
from rest_framework import status
from assistant.ai_providers import create_chat_completion
from assistant.services import PlanOptionsService
from dotenv import load_dotenv
from django.shortcuts import get_object_or_404
from django.db import transaction
from plans.models import Plan, Milestone, Task, Subtask
from plans.serializers import PlanViewSerializer, MilestoneSerializer, TaskUpdateSerializer, SubtaskSerializer
from assistant.progress_tracker import PlanCreationProgressTracker
import threading
load_dotenv()


class GeneratePlanOptionsView(APIView):
    """Generate 3 simple plan options for user to choose from"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=PlanPromptSerializer, responses={200: 'Plan options generated successfully'})
    def post(self, request):
        serializer = PlanPromptSerializer(data=request.data)
        if serializer.is_valid():
            prompt = serializer.validated_data['prompt']
            duration = serializer.validated_data.get('duration', '3 months')
            user = request.user

            try:
                # Use dedicated plan options service
                plan_options_service = PlanOptionsService()
                plan_options = plan_options_service.generate_plan_options(prompt, duration)

                return Response({
                    "message": "Plan options generated successfully",
                    "plan_options": plan_options,
                    "status": "options_ready"
                }, status=status.HTTP_200_OK)

            except Exception as e:
                return Response({
                    "error": f"Failed to generate plan options: {str(e)}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)


class MultiAgentPipelineView(APIView):
    """
    API endpoint để chạy pipeline Agent 1→2→3 và trả về kết quả chi tiết
    Input: user_input, duration, language
    Output: Kết quả từ Agent 3 (enhanced content với milestones và subtasks)
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'user_input': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='Mô tả dự án/kế hoạch học tập của user',
                    example='Tôi muốn học lập trình web từ cơ bản. Tôi chưa biết gì về HTML, CSS, JavaScript. Mục tiêu là tạo được website cá nhân và hiểu được cách web hoạt động.'
                ),
                'duration': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='Thời gian dự kiến hoàn thành',
                    example='3 months'
                ),
                'language': openapi.Schema(
                    type=openapi.TYPE_STRING, 
                    description='Ngôn ngữ phản hồi',
                    example='vietnamese'
                ),
            },
            required=['user_input']
        ),
        responses={
            200: openapi.Response(
                description='Pipeline thành công',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'pipeline_result': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'domain_analysis': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'structure_design': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'enhanced_content': openapi.Schema(type=openapi.TYPE_OBJECT),
                            }
                        ),
                        'execution_time': openapi.Schema(type=openapi.TYPE_NUMBER),
                        'agents_status': openapi.Schema(type=openapi.TYPE_OBJECT),
                    }
                )
            ),
            400: 'Bad Request',
            500: 'Internal Server Error'
        }
    )
    def post(self, request):
        """
        Chạy pipeline Agent 1→2→3 với input từ user
        """
        try:
            # Validate input
            user_input = request.data.get('user_input')
            duration = request.data.get('duration', '3 months')
            language = request.data.get('language', 'english')

            if not user_input:
                return Response({
                    'success': False,
                    'error': 'user_input is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Log API call
            user = request.user
            print(f"🚀 Multi-Agent Pipeline API called by user {user.id}")
            print(f"📝 Input: {user_input[:100]}...")
            print(f"⏱️ Duration: {duration}")
            print(f"🌐 Language: {language}")

            # Run pipeline synchronously
            pipeline_result = asyncio.run(self._run_agent_pipeline(user_input, duration, language))

            return Response({
                'success': True,
                'message': 'Multi-agent pipeline completed successfully',
                'pipeline_result': pipeline_result['data'],
                'execution_time': pipeline_result['execution_time'],
                'agents_status': pipeline_result['agents_status']
            }, status=status.HTTP_200_OK)

        except Exception as e:
            import traceback
            error_message = str(e)
            print(f"❌ Multi-Agent Pipeline API failed: {error_message}")
            print(f"🔍 Full traceback: {traceback.format_exc()}")

            return Response({
                'success': False,
                'error': f'Pipeline failed: {error_message}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    async def _run_agent_pipeline(self, user_input, duration, language):
        """
        Chạy pipeline Agent 1→2→3 và trả về kết quả
        """
        import time
        from multi_agent.agents.domain_classifier import DomainClassificationAgent
        from multi_agent.agents.structure_optimizer import StructureOptimizationAgent  
        from multi_agent.agents.content_generator import ContentGenerationAgent
        from multi_agent.services.shared_memory import create_initial_state

        start_time = time.time()

        # Initialize agents
        print("🤖 Initializing agents...")
        agent1 = DomainClassificationAgent()
        agent2 = StructureOptimizationAgent()
        agent3 = ContentGenerationAgent()

        # Create initial state
        state = create_initial_state(
            user_input=user_input,
            duration=duration,
            language=language
        )

        # Track agents status
        agents_status = {
            'agent1_domain_classifier': {'status': 'pending', 'execution_time': 0},
            'agent2_structure_optimizer': {'status': 'pending', 'execution_time': 0},
            'agent3_content_generator': {'status': 'pending', 'execution_time': 0}
        }

        try:
            # AGENT 1: Domain Classification
            print("🔍 Running Agent 1: Domain Classification...")
            agent1_start = time.time()

            result1 = await agent1.process(state)
            agent1_time = time.time() - agent1_start

            if result1.get('success'):
                state['domain_analysis'] = result1['domain_analysis']
                agents_status['agent1_domain_classifier'] = {
                    'status': 'success',
                    'execution_time': round(agent1_time, 2),
                    'domain': result1['domain_analysis'].get('primary_domain', 'N/A')
                }
                print(f"✅ Agent 1 completed in {agent1_time:.2f}s")
            else:
                agents_status['agent1_domain_classifier'] = {
                    'status': 'failed',
                    'execution_time': round(agent1_time, 2),
                    'error': result1.get('error', 'Unknown error')
                }
                raise Exception(f"Agent 1 failed: {result1.get('error', 'Unknown error')}")

            # AGENT 2: Structure Optimization
            print("🏗️ Running Agent 2: Structure Optimization...")
            agent2_start = time.time()

            result2 = await agent2.process(state)
            agent2_time = time.time() - agent2_start

            if result2.get('success'):
                state['structure_design'] = result2['structure_design']
                milestones_count = len(result2['structure_design'].get('milestones', []))
                agents_status['agent2_structure_optimizer'] = {
                    'status': 'success',
                    'execution_time': round(agent2_time, 2),
                    'milestones_generated': milestones_count
                }
                print(f"✅ Agent 2 completed in {agent2_time:.2f}s - {milestones_count} milestones")
            else:
                agents_status['agent2_structure_optimizer'] = {
                    'status': 'failed',
                    'execution_time': round(agent2_time, 2),
                    'error': result2.get('error', 'Unknown error')
                }
                raise Exception(f"Agent 2 failed: {result2.get('error', 'Unknown error')}")

            # AGENT 3: Content Generation
            print("📝 Running Agent 3: Content Generation...")
            agent3_start = time.time()

            result3 = await agent3.process(state)
            agent3_time = time.time() - agent3_start

            if result3.get('success'):
                state['content_data'] = result3['enhanced_content']
                enhanced_milestones = len(result3['enhanced_content'].get('enhanced_milestones', []))
                agents_status['agent3_content_generator'] = {
                    'status': 'success',
                    'execution_time': round(agent3_time, 2),
                    'enhanced_milestones': enhanced_milestones
                }
                print(f"✅ Agent 3 completed in {agent3_time:.2f}s - {enhanced_milestones} enhanced milestones")
            else:
                agents_status['agent3_content_generator'] = {
                    'status': 'failed',
                    'execution_time': round(agent3_time, 2),
                    'error': result3.get('error', 'Unknown error')
                }
                raise Exception(f"Agent 3 failed: {result3.get('error', 'Unknown error')}")

            # Calculate total execution time
            total_time = time.time() - start_time

            # Prepare final result
            pipeline_data = {
                'domain_analysis': state.get('domain_analysis', {}),
                'structure_design': state.get('structure_design', {}),
                'enhanced_content': state.get('content_data', {})
            }

            print(f"🏁 Pipeline completed successfully in {total_time:.2f}s")

            return {
                'data': pipeline_data,
                'execution_time': round(total_time, 2),
                'agents_status': agents_status
            }

        except Exception as e:
            # Update failed agent status
            total_time = time.time() - start_time
            print(f"💥 Pipeline failed after {total_time:.2f}s: {str(e)}")
            raise e


class CreatePlanFromOptionView(APIView):
    """Create a full plan from a selected option and generate subtasks"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'selected_option': openapi.Schema(type=openapi.TYPE_OBJECT, description='Selected plan option'),
                'original_prompt': openapi.Schema(type=openapi.TYPE_STRING, description='Original user prompt'),
            },
            required=['selected_option', 'original_prompt']
        ),
        responses={202: 'Plan creation from option started'}
    )
    def post(self, request):
        try:
            selected_option = request.data.get('selected_option')
            original_prompt = request.data.get('original_prompt', '')
            user = request.user

            # Log API call with AI provider information
            ai_provider = os.getenv('AI_PROVIDER', 'openrouter')
            if ai_provider == 'openrouter':
                model_name = os.getenv('OPENROUTER_DEFAULT_MODEL', 'anthropic/claude-3.5-sonnet')
                print(f"🚀 create-plan-from-option API called by user {user.id} using OpenRouter model: {model_name}")
            elif ai_provider == 'openai':
                model_name = os.getenv('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini')
                print(f"🚀 create-plan-from-option API called by user {user.id} using OpenAI model: {model_name}")
            else:
                print(f"🚀 create-plan-from-option API called by user {user.id} using AI provider: {ai_provider}")

            if not selected_option:
                return Response({
                    'error': 'Selected option is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Initialize progress tracker
            progress_tracker = PlanCreationProgressTracker(user)
            session_id = progress_tracker.start(f"Creating plan from selected option: {selected_option.get('name', 'Unknown')}")

            # Start plan creation with subtask generation
            thread = threading.Thread(
                target=self.process_plan_creation_from_option,
                args=(selected_option, original_prompt, user.id, progress_tracker)
            )
            thread.daemon = True
            thread.start()

            return Response({
                "message": "Plan creation from selected option started",
                "status": "processing",
                "session_id": session_id
            }, status=status.HTTP_202_ACCEPTED)

        except Exception as e:
            return Response({
                "error": f"Failed to start plan creation from option: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def process_plan_creation_from_option(self, selected_option, original_prompt, user_id, progress_tracker):
        """Process plan creation from selected option with subtask generation"""
        try:
            from users.models import User
            user = User.objects.get(id=user_id)

            # Log background processing start with AI model info
            ai_provider = os.getenv('AI_PROVIDER', 'openrouter')
            if ai_provider == 'openrouter':
                model_name = os.getenv('OPENROUTER_DEFAULT_MODEL', 'anthropic/claude-3.5-sonnet')
                print(f"⚙️ Background processing started for user {user_id} - Plan: '{selected_option.get('name', 'Unknown')}' using OpenRouter model: {model_name}")
            elif ai_provider == 'openai':
                model_name = os.getenv('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini')
                print(f"⚙️ Background processing started for user {user_id} - Plan: '{selected_option.get('name', 'Unknown')}' using OpenAI model: {model_name}")
            else:
                print(f"⚙️ Background processing started for user {user_id} - Plan: '{selected_option.get('name', 'Unknown')}' using AI provider: {ai_provider}")

            # Step 1: Update progress - Creating plan structure
            progress_tracker.update_step('creating_structure', 'Creating plan structure from selected option...')

            # Step 2: Generate subtasks for each task
            progress_tracker.update_step('generating_subtasks', 'Generating detailed subtasks for each task...')

            enhanced_option = self._generate_subtasks_for_option(selected_option, original_prompt)

            # Step 3: Create plan in database
            progress_tracker.update_step('saving_plan', 'Saving plan to database...')

            plan = self._create_plan_from_enhanced_option(enhanced_option, user, progress_tracker)

            # Step 4: Complete
            progress_tracker.complete(plan)

        except Exception as e:
            import traceback
            error_message = str(e)
            print(f"Plan creation from option failed for user {user_id}: {error_message}")
            print(f"Full traceback: {traceback.format_exc()}")
            progress_tracker.fail(error_message)

    def _generate_subtasks_for_option(self, selected_option, original_prompt):
        """Generate subtasks for each task in the selected option using detailed prompts"""
        try:
            # Import the detailed prompt functions
            from .prompts import get_subtask_generation_prompt, get_create_plan_from_option_system_prompt

            # Check if we should use Gemini Pro 2.5 for plan creation
            use_gemini_pro = os.getenv('USE_GEMINI_PRO_FOR_PLAN_CREATION', 'false').lower() == 'true'

            if use_gemini_pro:
                # Use Gemini Pro 2.5 for plan creation
                ai_provider = 'google'
                model_name = os.getenv('GOOGLE_AI_PLAN_CREATION_MODEL', 'gemini-2.5-pro')
                print(f"🤖 create-plan-from-option: Using Gemini Pro 2.5 model: {model_name}")
            else:
                # Use default AI provider
                ai_provider = os.getenv('AI_PROVIDER', 'openrouter')
                if ai_provider == 'openrouter':
                    model_name = os.getenv('OPENROUTER_DEFAULT_MODEL', 'anthropic/claude-3.5-sonnet')
                    print(f"🤖 create-plan-from-option: Using OpenRouter model: {model_name}")
                elif ai_provider == 'openai':
                    model_name = os.getenv('OPENAI_DEFAULT_MODEL', 'gpt-4o-mini')
                    print(f"🤖 create-plan-from-option: Using OpenAI model: {model_name}")
                elif ai_provider == 'google':
                    model_name = os.getenv('GOOGLE_AI_DEFAULT_MODEL', 'gemini-2.0-flash-exp')
                    print(f"🤖 create-plan-from-option: Using Google AI model: {model_name}")
                else:
                    print(f"🤖 create-plan-from-option: Using AI provider: {ai_provider}")
                    model_name = None

            # Generate comprehensive prompt using prompts.py approach
            ai_prompt = get_subtask_generation_prompt(selected_option, original_prompt)

            # Use system prompt from prompts.py for consistency
            system_prompt = get_create_plan_from_option_system_prompt()

            # Call AI API with enhanced prompts
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": ai_prompt}
            ]

            # Log AI API call details
            print(f"🔄 Calling AI API for subtask generation - Provider: {ai_provider}, Model: {model_name}")

            # Call AI with specific provider and model
            if use_gemini_pro:
                ai_response = create_chat_completion(
                    messages=messages,
                    provider=ai_provider,
                    model=model_name,
                    temperature=0.3  # Optimized temperature for consistency
                )
            else:
                ai_response = create_chat_completion(
                    messages=messages,
                    provider=ai_provider,
                    temperature=0.3  # Optimized temperature for consistency
                )

            print(f"✅ AI API response received for subtask generation")

            # Parse the response
            content = ai_response.get('content', '').strip()
            print(f"AI response for subtasks: {content[:500]}...")

            # Try to parse JSON
            try:
                enhanced_data = json.loads(content)
                if 'enhanced_plan' in enhanced_data:
                    enhanced_plan = enhanced_data['enhanced_plan']
                    # Add original_prompt to enhanced_plan for name generation
                    enhanced_plan['original_prompt'] = original_prompt
                    return enhanced_plan
            except json.JSONDecodeError:
                pass

            # Try to extract JSON from response
            enhanced_data = self._extract_enhanced_plan_json(content)
            if enhanced_data and 'enhanced_plan' in enhanced_data:
                enhanced_plan = enhanced_data['enhanced_plan']
                # Add original_prompt to enhanced_plan for name generation
                enhanced_plan['original_prompt'] = original_prompt
                return enhanced_plan

            # If parsing fails, raise exception instead of fallback
            raise Exception("Failed to parse AI response for subtask generation")

        except Exception as e:
            print(f"Error generating subtasks: {str(e)}")
            raise Exception(f"Subtask generation failed: {str(e)}")

    def _extract_enhanced_plan_json(self, content):
        """Extract JSON from AI response"""
        try:
            start_idx = content.find('{')
            end_idx = content.rfind('}')

            if start_idx != -1 and end_idx != -1:
                json_candidate = content[start_idx:end_idx + 1]
                return json.loads(json_candidate)

            return None
        except Exception as e:
            print(f"Error extracting enhanced plan JSON: {str(e)}")
            return None



    def _create_plan_from_enhanced_option(self, enhanced_option, user, progress_tracker):
        """Create plan in database from enhanced option"""
        try:
            with transaction.atomic():
                # Generate enhanced plan name
                enhanced_name = self._generate_enhanced_plan_name(
                    enhanced_option.get('name', 'Generated Plan'),
                    enhanced_option.get('original_prompt', ''),
                    enhanced_option.get('description', 'AI-generated project plan')
                )

                # Create the plan
                plan = Plan.objects.create(
                    name=enhanced_name,
                    description=enhanced_option.get('description', 'AI-generated project plan'),
                    user=user,
                    status='completed'
                )

                # Create milestones, tasks, and subtasks
                for milestone_data in enhanced_option.get('milestones', []):
                    milestone = Milestone.objects.create(
                        name=milestone_data.get('name', 'Milestone'),
                        description=milestone_data.get('description', ''),
                        estimated_duration=milestone_data.get('estimated_duration', '1-2 weeks'),
                        success_criteria=milestone_data.get('success_criteria', ''),
                        plan=plan
                    )

                    for task_data in milestone_data.get('tasks', []):
                        task = Task.objects.create(
                            name=task_data.get('name', 'Task'),
                            description=task_data.get('description', ''),
                            estimated_duration=task_data.get('estimated_duration', '1-2 days'),
                            milestone=milestone,
                            user=user  # Set task creator
                        )

                        # Auto-assign the plan creator to this task
                        task.assignees.add(user)
                        print(f"✅ Auto-assigned user {user.id} to task: {task.name}")

                        for subtask_data in task_data.get('subtasks', []):
                            Subtask.objects.create(
                                name=subtask_data.get('name', 'Subtask'),
                                description=subtask_data.get('description', ''),
                                task=task
                            )

                print(f"Successfully created plan '{plan.name}' (ID: {plan.id}) from selected option")
                return plan

        except Exception as e:
            print(f"Error creating plan from enhanced option: {str(e)}")
            raise

    def _generate_enhanced_plan_name(self, original_name, original_prompt, plan_description):
        """Generate a more detailed and descriptive plan name using AI"""
        try:
            from .prompts import get_enhanced_plan_name_prompt

            # Create prompt for enhanced name generation
            user_prompt = get_enhanced_plan_name_prompt(original_name, original_prompt, plan_description)

            # Simple system prompt for name enhancement
            system_prompt = (
                "You are a professional project naming expert. "
                "Create clear, descriptive, and professional project plan names that accurately reflect the project scope and objectives. "
                "Return only the enhanced plan name without any additional formatting or explanations."
            )

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            # Call AI with optimized settings for name generation
            # Use Google AI for better name generation results
            ai_response = create_chat_completion(
                messages=messages,
                provider='google',
                temperature=0.3,  # Lower temperature for more consistent naming
                max_tokens=100    # Short response for just the name
            )

            enhanced_name = ai_response.get('content', '').strip()

            # Validate the enhanced name
            if enhanced_name and len(enhanced_name) > 10 and len(enhanced_name) <= 200:
                print(f"✅ Enhanced plan name: '{enhanced_name}' (from '{original_name}')")
                return enhanced_name
            else:
                print(f"⚠️ AI generated invalid name, using original: '{original_name}'")
                return original_name

        except Exception as e:
            print(f"❌ Error generating enhanced plan name: {str(e)}")
            print(f"🔄 Falling back to original name: '{original_name}'")
            return original_name


class PlanCreationStatusView(APIView):
    """
    Get detailed plan creation progress with real-time updates.
    Provides useful feedback about what the AI is currently working on.
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(responses={200: 'Plan creation status retrieved successfully'})
    def get(self, request):
        try:
            # Get the latest progress for this user
            latest_progress = PlanCreationProgressTracker.get_latest_progress(request.user)

            if not latest_progress:
                return Response({
                    'status': 'no_active_creation',
                    'message': 'No active plan creation found'
                })

            # Check if progress is older than 10 minutes (likely stale)
            from django.utils import timezone
            from datetime import timedelta

            ten_minutes_ago = timezone.now() - timedelta(minutes=10)
            if latest_progress.started_at < ten_minutes_ago and latest_progress.status != 'completed':
                return Response({
                    'status': 'timeout',
                    'message': 'Plan creation timed out. Please try again.',
                    'error': 'Creation process took too long'
                })

            # Return detailed progress information
            response_data = {
                'status': latest_progress.status,
                'message': latest_progress.status_message,
                'current_step': latest_progress.current_step,
                'session_id': latest_progress.session_id,
                'started_at': latest_progress.started_at,
                'step_details': latest_progress.step_details
            }

            # If completed, include plan data
            if latest_progress.status == 'completed' and latest_progress.plan:
                serializer = PlanViewSerializer(latest_progress.plan, context={'request': request})
                response_data['plan'] = serializer.data
                response_data['plan_url'] = f'/d/plan/{latest_progress.plan.slug}'

            # If failed, include error information
            elif latest_progress.status == 'failed':
                response_data['error'] = latest_progress.error_message

            return Response(response_data)

        except Exception as e:
            return Response({
                'status': 'error',
                'error': f'Error checking plan creation status: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _cleanup_failed_plan(self, plan):
        """Mark plan as failed and clean up any partial data, but keep the plan record"""
        try:
            if plan and hasattr(plan, 'id') and plan.id:
                # Mark the plan as failed so frontend can handle it
                plan.status = 'failed'
                plan.name = "Plan creation failed"
                plan.description = "Plan creation failed due to AI response processing error"
                plan.save()

                # Clean up any partial milestone/task data that might have been created
                try:
                    # Delete any milestones and their related objects that might have been partially created
                    for milestone in plan.milestone_set.all():
                        # Delete tasks and their subtasks
                        for task in milestone.task_set.all():
                            task.subtask_set.all().delete()
                            task.assignees.clear()  # Clear many-to-many relationships
                            task.delete()
                        # Delete milestone risks
                        milestone.risks.all().delete()
                        milestone.delete()

                    print(f"Cleaned up failed plan data for plan_id: {plan.id}")
                except Exception as delete_error:
                    print(f"Error cleaning up plan objects: {delete_error}")
                    # If cleanup fails, just ensure plan is marked as failed
                    plan.status = 'failed'
                    plan.save()
        except Exception as cleanup_error:
            print(f"Error during plan cleanup: {cleanup_error}")


class AIAgentChatView(APIView):
    """API endpoint for AI agent chat with plan context"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='User message'),
                'plan_slug': openapi.Schema(type=openapi.TYPE_STRING, description='Plan slug'),
            },
            required=['message', 'plan_slug']
        ),
        responses={
            200: openapi.Response(description="AI response generated successfully"),
            400: openapi.Response(description="Invalid input"),
            404: openapi.Response(description="Plan not found")
        }
    )
    def post(self, request):
        try:
            message = request.data.get('message', '').strip()
            plan_slug = request.data.get('plan_slug')

            if not message or not plan_slug:
                return Response({
                    'error': 'Message and plan_slug are required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the plan with full context
            plan = get_object_or_404(Plan, slug=plan_slug)

            # Check if user has permission to access this plan
            if plan.user != request.user:
                from plans.models import PlanAccess
                if not PlanAccess.objects.filter(user=request.user, plan=plan).exists():
                    return Response({
                        'error': 'You do not have permission to access this plan'
                    }, status=status.HTTP_403_FORBIDDEN)

            # Get complete plan context
            plan_context = self._get_plan_context(plan)

            # Generate AI response with plan context
            ai_response = self._generate_ai_response(message, plan_context)

            # Execute actions if any were generated
            executed_actions = []
            action_results = []

            if ai_response.get('actions'):
                print(f"Executing {len(ai_response['actions'])} actions...")
                for action in ai_response['actions']:
                    try:
                        result = self._execute_action(action, plan, request.user)
                        if result:
                            executed_actions.append(action)
                            action_results.append(result)
                            print(f"Successfully executed action: {action['action']}")
                        else:
                            print(f"Failed to execute action: {action['action']}")
                    except Exception as e:
                        print(f"Error executing action {action['action']}: {str(e)}")
                        continue

            # Clean the response content for better UX
            cleaned_content = self._clean_ai_response_content(ai_response.get('content', ''))

            return Response({
                'message': cleaned_content,
                'actions': executed_actions,
                'action_results': action_results,
                'metadata': {
                    'model': ai_response.get('model'),
                    'provider': ai_response.get('provider'),
                    'plan_context_included': True,
                    'actions_executed': len(executed_actions)
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _get_plan_context(self, plan):
        """Get complete plan context for AI"""
        try:
            milestones = []
            for milestone in plan.milestone_set.all().order_by('id'):
                milestone_data = {
                    'id': milestone.id,
                    'name': milestone.name or '',
                    'description': milestone.description or '',
                    'estimated_duration': milestone.estimated_duration or '',
                    'success_criteria': milestone.success_criteria or '',
                    'tasks': []
                }

                for task in milestone.task_set.all().order_by('id'):
                    task_data = {
                        'id': task.id,
                        'slug': task.slug,
                        'name': task.name or '',
                        'description': task.description or '',
                        'status': task.status,
                        'priority': task.priority,
                        'start_date': task.start_date.isoformat() if task.start_date else None,
                        'end_date': task.end_date.isoformat() if task.end_date else None,
                        'progress': task.progress or 0,
                        'estimated_duration': getattr(task, 'estimated_duration', '') or '',
                        'order': getattr(task, 'order', None),
                        'subtasks': []
                    }

                    for subtask in task.subtask_set.all().order_by('id'):
                        subtask_data = {
                            'id': subtask.id,
                            'slug': subtask.slug,
                            'name': subtask.name or '',
                            'description': subtask.description or '',
                            'status': subtask.status,
                            'progress': subtask.progress or 0,
                            'start_date': subtask.start_date.isoformat() if subtask.start_date else None,
                            'end_date': subtask.end_date.isoformat() if subtask.end_date else None,
                            'order': getattr(subtask, 'order', None)
                        }
                        task_data['subtasks'].append(subtask_data)

                    milestone_data['tasks'].append(task_data)

                milestones.append(milestone_data)
        except Exception as e:
            # Log the error but don't fail completely
            print(f"Error building plan context: {e}")
            milestones = []

        return {
            'plan': {
                'id': plan.id,
                'slug': plan.slug,
                'name': plan.name,
                'description': plan.description,
                'priority': plan.priority,
                'status': plan.status,
                'created_at': plan.created_at.isoformat()
            },
            'milestones': milestones,
            'statistics': {
                'total_milestones': len(milestones),
                'total_tasks': sum(len(m['tasks']) for m in milestones),
                'total_subtasks': sum(len(t['subtasks']) for m in milestones for t in m['tasks']),
                'completed_tasks': sum(1 for m in milestones for t in m['tasks'] if t['status'] == 3),
                'in_progress_tasks': sum(1 for m in milestones for t in m['tasks'] if t['status'] == 2)
            }
        }

    def _generate_ai_response(self, user_message, plan_context):
        """Generate AI response using external API with plan context"""
        try:
            print(f"Generating AI response for message: {user_message}")

            # Create system prompt with plan context
            system_prompt = self._create_system_prompt(plan_context)
            print(f"System prompt created: {len(system_prompt)} characters")

            # Create user prompt with context
            user_prompt = f"""
User message: {user_message}

🎯 **EXECUTION MODE - TAKE IMMEDIATE ACTION:**

1. **DUPLICATE CHECK**: Verify if similar content exists first
2. **IMMEDIATE EXECUTION**: Don't explain what you would do - just do it with JSON actions
3. **CREATE COMPLETE HIERARCHY**: When adding milestones, include tasks AND subtasks in your actions
4. **FOLLOW 5-5-5 STRUCTURE**: 5 tasks per milestone, 5 subtasks per task for comprehensive coverage
5. **SIMPLE CONFIRMATION**: Respond with brief confirmation like "**Done!** I've added [what you added]. Check it out!"
6. **NO TECHNICAL DETAILS**: Hide JSON blocks and implementation details from user

📝 **RESPONSE EXAMPLES:**
- Request: "add milestone for X" → "**Perfect!** I've added the 'Learning X' milestone with 5 comprehensive tasks and 25 detailed subtasks. All set!"
- Request: "create tasks for Y" → "**Done!** I've created 5 detailed tasks with subtasks for Y. Ready to go!"

⚡ **MANDATORY JSON ACTIONS:**
Every request that modifies the plan MUST include executable JSON actions.
When creating milestones, ALWAYS include both add_task AND add_subtask actions for complete structure.

COMPLETE MILESTONE CREATION EXAMPLE:
If adding a milestone that needs tasks and subtasks, create a comprehensive structure:
```json
{{
    "actions": [
        {{
            "action": "add_milestone",
            "data": {{
                "name": "Advanced Car Setup Techniques",
                "description": "Master advanced car setup techniques including aerodynamics, suspension tuning, and brake balance optimization for competitive racing",
                "estimated_duration": "4 weeks",
                "success_criteria": "Demonstrate proficiency in all advanced setup techniques with measurable lap time improvements"
            }}
        }},
        {{
            "action": "add_task",
            "data": {{
                "name": "Aerodynamics Setup and Optimization",
                "description": "Learn and master aerodynamic setup principles including front/rear wing angles, ride height, and downforce balance for different track types"
            }}
        }},
        {{
            "action": "add_task",
            "data": {{
                "name": "Suspension Tuning Fundamentals",
                "description": "Master suspension setup including spring rates, damper settings, anti-roll bars, and geometry adjustments for optimal handling"
            }}
        }}
    ]
}}
```

DELETE MILESTONE EXAMPLE:
If deleting milestones, use the correct format with milestone_id in data:
```json
{{
    "actions": [
        {{
            "action": "delete_milestone",
            "data": {{
                "milestone_id": 6
            }}
        }},
        {{
            "action": "delete_milestone",
            "data": {{
                "milestone_id": 7
            }}
        }}
    ]
}}
```

CRITICAL: Always put parameters inside the "data" object, not directly in the action!

RESPONSE STRUCTURE:
1. **Brief Acknowledgment**: Simple confirmation of what you're doing
2. **JSON Actions**: Provide complete JSON actions for immediate execution
3. **User-Friendly Confirmation**: "Done! I've added/changed... check it out!"

Available action types:
- add_milestone: Add a new milestone (name, description, estimated_duration, success_criteria)
- add_task: Add a task to a milestone (name, description, milestone_id - optional)
- add_subtask: Add a subtask to a task (name, description, task_slug)
- update_milestone: Update milestone details (milestone_id, name, description, etc.)
- update_task: Update task details (task_slug, name, description, priority, etc.)
- update_subtask: Update subtask details (subtask_slug, name, description, status, etc.)
- update_task_status: Update task status (task_slug, status: 1=pending, 2=in_progress, 3=completed)
- complete_task: Mark task as completed (task_slug)
- delete_milestone: Delete milestone and all tasks (milestone_id)
- delete_task: Delete task and all subtasks (task_slug)
- delete_subtask: Delete subtask (subtask_slug)

REMEMBER: Your JSON actions will be automatically executed. Always include them when making recommendations.
"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]

            print("Calling AI API with OpenRouter...")

            # Use environment-specified provider with optimized settings
            ai_response = create_chat_completion(
                messages=messages,
                provider=os.getenv('AI_PROVIDER', 'openrouter'),
                temperature=0.3  # Optimized temperature for consistency
            )

            print(f"AI response received: {len(ai_response.get('content', ''))} characters")

            # Parse actions from response
            actions = self._parse_actions_from_response(ai_response.get('content', ''))
            print(f"Parsed {len(actions)} actions from response")
            print(f"AI response content preview: {ai_response.get('content', '')[:500]}...")
            if actions:
                print(f"Actions found: {json.dumps(actions, indent=2)}")
            else:
                print("No actions found in response - checking parsing logic")
                # If user explicitly requested adding something but no actions found, try to create one
                user_message_lower = user_message.lower()
                if any(keyword in user_message_lower for keyword in ['add', 'create', 'new', 'milestone', 'task']):
                    print("User requested adding something but no actions found - creating fallback action")
                    if 'milestone' in user_message_lower:
                        # Check for duplicates first
                        existing_names = [m['name'].lower() for m in plan_context['milestones']]

                        # Extract milestone name from user message
                        import re
                        milestone_match = re.search(r'(?:add|create).*?milestone.*?(?:for|called|named)?\s*([^.!?]+)', user_message_lower)
                        if milestone_match:
                            milestone_name = milestone_match.group(1).strip()

                            # Check if similar milestone already exists
                            if any(name in milestone_name.lower() or milestone_name.lower() in name for name in existing_names):
                                print(f"Similar milestone already exists, skipping fallback creation")
                                actions = []
                            else:
                                # Create comprehensive milestone with tasks
                                actions = [{
                                    'action': 'add_milestone',
                                    'data': {
                                        'name': milestone_name.title(),
                                        'description': f'Comprehensive milestone covering {milestone_name} fundamentals and advanced techniques',
                                        'estimated_duration': '3-4 weeks',
                                        'success_criteria': f'Master all aspects of {milestone_name} including theory and practical application'
                                    }
                                }]

                                # Add basic tasks for the milestone (following 15-word limit)
                                basic_tasks = [
                                    f"Learn and understand fundamental concepts and theoretical principles of {milestone_name} for solid foundation building",
                                    f"Practice essential {milestone_name} techniques through hands-on exercises and guided implementation of core methods",
                                    f"Apply {milestone_name} knowledge in realistic scenarios to demonstrate practical understanding and problem-solving capabilities",
                                    f"Master advanced {milestone_name} concepts through comprehensive study and implementation of complex techniques and strategies",
                                    f"Validate {milestone_name} skills through assessment testing and demonstrate proficiency in all learned concepts"
                                ]

                                for task_name in basic_tasks:
                                    actions.append({
                                        'action': 'add_task',
                                        'data': {
                                            'name': task_name,
                                            'description': f'Detailed task focusing on {task_name.lower()} with step-by-step guidance and practical exercises'
                                        }
                                    })

                                print(f"Created comprehensive fallback milestone with {len(actions)} actions")

            return {
                'content': ai_response.get('content', ''),
                'actions': actions,
                'model': ai_response.get('model'),
                'provider': ai_response.get('provider')
            }

        except Exception as e:
            print(f"Error in _generate_ai_response: {str(e)}")
            # Fallback to basic response if AI fails
            return {
                'content': f"Sorry, I encountered an error while processing your request: Network Error. Please try again.",
                'actions': [],
                'model': 'fallback',
                'provider': 'local',
                'error': str(e)
            }

    def _create_system_prompt(self, plan_context):
        """Create system prompt with plan context"""
        plan = plan_context['plan']
        stats = plan_context['statistics']

        # Build detailed milestone analysis
        milestones_analysis = []
        existing_milestone_names = []
        for milestone in plan_context['milestones']:
            task_count = len(milestone['tasks'])
            completed_tasks = sum(1 for task in milestone['tasks'] if task['status'] == 3)
            in_progress_tasks = sum(1 for task in milestone['tasks'] if task['status'] == 2)

            existing_milestone_names.append(milestone['name'].lower())

            milestone_details = f"- **{milestone['name']}**: {completed_tasks}/{task_count} tasks completed"
            if milestone['description']:
                milestone_details += f"\n  Description: {milestone['description']}"
            if milestone['estimated_duration']:
                milestone_details += f"\n  Duration: {milestone['estimated_duration']}"

            # Add task details
            if milestone['tasks']:
                milestone_details += f"\n  Tasks:"
                for task in milestone['tasks'][:3]:  # Show first 3 tasks
                    status_text = {1: 'Pending', 2: 'In Progress', 3: 'Completed'}.get(task['status'], 'Unknown')
                    milestone_details += f"\n    • {task['name']} ({status_text})"
                if len(milestone['tasks']) > 3:
                    milestone_details += f"\n    • ... and {len(milestone['tasks']) - 3} more tasks"
            else:
                milestone_details += f"\n  ⚠️ **NO TASKS DEFINED** - This milestone needs tasks and subtasks!"

            milestones_analysis.append(milestone_details)

        # Build the prompt using string concatenation to avoid f-string nesting issues
        project_overview = f"""You are an intelligent AI Project Management Assistant with comprehensive capabilities to analyze, understand, and modify the project "{plan['name']}".

🎯 **PROJECT OVERVIEW:**
- **Project**: {plan['name']}
- **Description**: {plan['description']}
- **Status**: {plan['status']}
- **Progress**: {stats['completed_tasks']}/{stats['total_tasks']} tasks completed
- **Structure**: {stats['total_milestones']} milestones, {stats['total_tasks']} tasks, {stats['total_subtasks']} subtasks

📊 **CURRENT PROJECT ANALYSIS:**
{chr(10).join(milestones_analysis)}

🔍 **EXISTING MILESTONE NAMES** (avoid duplicates):
{', '.join(existing_milestone_names)}

📋 **COMPLETE PROJECT DATA:**
{json.dumps(plan_context, indent=2)}

🧠 **YOUR CORE CAPABILITIES:**

**1. INTELLIGENT ANALYSIS & INSIGHTS:**
- Answer detailed questions about project status, progress, and content
- Provide strategic recommendations and identify potential issues
- Analyze task dependencies, resource allocation, and timeline feasibility
- Compare milestones and suggest optimizations
- Generate progress reports and summaries
- Identify gaps, bottlenecks, and improvement opportunities

**2. DIRECT PROJECT MODIFICATIONS:**
- Create, modify, and delete milestones, tasks, and subtasks
- Update project structure and content intelligently
- Manage task assignments, priorities, and deadlines
- Reorganize project hierarchy for better workflow
- Implement suggested improvements automatically

**3. CONVERSATIONAL ASSISTANCE:**
- Answer questions about any aspect of the project
- Explain project concepts, progress, and recommendations
- Provide guidance on project management best practices
- Help break down complex requirements into manageable tasks
- Offer multiple solutions and alternatives

🎯 **INTERACTION GUIDELINES:**
- **BE DIRECT AND ACTION-ORIENTED**: Take immediate action instead of explaining what you would do
- **CONCISE RESPONSES**: Keep responses brief and focused on results
- **MARKDOWN FORMATTING**: Use **bold text** and clear formatting
- **IMMEDIATE EXECUTION**: Execute actions automatically and confirm completion
- **USER-FRIENDLY**: Hide technical details and JSON from users
- **COMPLETE HIERARCHY**: Always create full milestone → task → subtask structures

⚠️ **CRITICAL RULES:**
1. **TAKE DIRECT ACTION**: Don't explain what you would do - just do it immediately
2. **CREATE COMPLETE STRUCTURES**: When adding milestones, automatically include tasks AND subtasks
3. **FOLLOW 5-5-5 PATTERN**: 5 tasks per milestone, 5 subtasks per task for comprehensive coverage
4. **DUPLICATE CHECK**: Always verify if similar content already exists before adding
5. **CONTEXT AWARENESS**: Reference existing milestones/tasks when relevant
6. **CONFIRMATION MESSAGES**: Provide simple confirmations like "I've added/changed... all done! check it out"
7. **HIDE TECHNICAL DETAILS**: Never show JSON blocks or implementation details to users
8. **NAMING CONVENTIONS**:
   - Milestone names: 7-10 words (concise but descriptive)
   - Task names: around 15 words (detailed but not overly long)
   - Subtask names: 10-15 words (clear action description)

🔧 **RESPONSE FORMAT:**
- **Brief acknowledgment** of the request
- **Immediate action execution** with JSON actions
- **Simple confirmation** message with results
- **NO technical explanations** or JSON blocks visible to user
- Use this EXACT format for actions:"""

        # JSON template without f-string to avoid nesting issues
        json_template = '''
```json
{
  "actions": [
    {
      "action": "add_milestone",
      "data": {
        "name": "Milestone Name",
        "description": "Detailed description",
        "estimated_duration": "2 weeks",
        "success_criteria": "Clear success criteria"
      }
    }
  ]
}
```

MANDATORY RULES:
1. When you identify missing milestones, tasks, or improvements - ALWAYS include JSON actions
2. Your JSON will be automatically executed to modify the project
3. Be specific and actionable in your recommendations
4. Focus on gaps in the current project structure
5. Provide both analysis AND executable actions

🔧 **AVAILABLE ACTIONS:**
- **add_milestone**: Create new milestone (name, description, estimated_duration, success_criteria)
- **add_task**: Add task to milestone (name, description, milestone_id, priority, estimated_duration)
- **add_subtask**: Create subtask for task (name, description, task_slug, estimated_duration)
- **update_milestone**: Modify milestone (milestone_id, name, description, success_criteria)
- **update_task**: Update task details (task_slug, name, description, priority, status, progress)
- **update_subtask**: Modify subtask (subtask_slug, name, description, status, progress)
- **update_task_status**: Change task status (task_slug, status: 1=todo, 2=in_progress, 3=completed)
- **complete_task**: Mark task as done (task_slug)
- **set_task_priority**: Update task priority (task_slug, priority: 1=low, 2=medium, 3=high)
- **bulk_update_status**: Update multiple items (items: [{type, id, status}])
- **reorder_tasks**: Reorganize task order (milestone_id, task_order: [task_slugs])
- **analyze_project**: Generate detailed project analysis and statistics
- **suggest_improvements**: Provide actionable improvement recommendations
- **delete_milestone**: Remove milestone and all contents (milestone_id)
- **delete_task**: Delete task and all subtasks (task_slug)
- **delete_subtask**: Remove subtask (subtask_slug)

REMEMBER: If you recommend changes, you MUST include the JSON actions block. Your recommendations will be automatically implemented.

RESPONSE STYLE:
- Provide conversational, user-friendly analysis first
- Include JSON actions at the end for execution
- Keep explanations clear and non-technical
- Focus on what you're doing for the user, not how the system works'''

        return project_overview + json_template

    def _parse_actions_from_response(self, response_content):
        """Parse actions from AI response - handles both JSON and structured markdown"""
        actions = []
        try:
            import re

            print(f"Parsing actions from response content (length: {len(response_content)})")
            print(f"Response preview: {response_content[:300]}...")

            # First, try to find JSON blocks in the response - multiple patterns
            json_patterns = [
                r'```json\s*(\{.*?\})\s*```',  # Standard markdown JSON blocks
                r'```\s*(\{.*?"actions".*?\})\s*```',  # JSON blocks without json label
                r'(\{[^{}]*"actions"[^{}]*\[[^\]]*\][^{}]*\})',  # Inline JSON with actions array
            ]

            for i, pattern in enumerate(json_patterns):
                matches = re.findall(pattern, response_content, re.DOTALL | re.IGNORECASE)
                print(f"Pattern {i+1} found {len(matches)} matches")

                for match in matches:
                    try:
                        parsed_json = json.loads(match)
                        print(f"Successfully parsed JSON: {parsed_json}")
                        if 'actions' in parsed_json and isinstance(parsed_json['actions'], list):
                            actions.extend(parsed_json['actions'])
                            print(f"Added {len(parsed_json['actions'])} actions from JSON")
                            # Break after finding first valid actions to avoid duplicates
                            break
                    except json.JSONDecodeError as e:
                        print(f"JSON decode error: {e}")
                        continue

                # If we found actions, stop trying other patterns
                if actions:
                    break

            # If no JSON actions found, try to parse structured recommendations
            if not actions:
                print("No JSON actions found, trying structured parsing")
                actions = self._parse_structured_recommendations(response_content)

        except Exception as e:
            print(f"Error parsing actions from AI response: {e}")

        print(f"Final actions count: {len(actions)}")
        return actions

    def _parse_structured_recommendations(self, response_content):
        """Parse structured recommendations from AI response and convert to actions"""
        actions = []
        try:
            import re

            # Enhanced patterns for milestone detection
            milestone_patterns = [
                # Pattern 1: "Insert the new milestone..." format
                r'Insert the new milestone.*?"([^"]+)".*?after Milestone (\d+).*?before Milestone (\d+)',
                # Pattern 2: "add a milestone for..." format
                r'add a milestone for.*?([^.]+?)(?:\.|$)',
                # Pattern 3: "new milestone should be..." format
                r'new milestone should be.*?"([^"]+)"',
                # Pattern 4: "milestone for..." format
                r'milestone for ([^.]+?)(?:\.|$)',
                # Pattern 5: "**Positioning**: The new milestone..." format
                r'\*\*Positioning\*\*.*?milestone.*?"([^"]+)"',
                # Pattern 6: General milestone addition
                r'add.*?milestone.*?"([^"]+)"',
                # Pattern 7: Milestone in recommendations
                r'### Recommendations:.*?milestone.*?"([^"]+)"',
                # Pattern 8: Milestone with specific focus
                r'dedicated focus on ([^.]+?)(?:\.|$)'
            ]

            for pattern in milestone_patterns:
                matches = re.findall(pattern, response_content, re.IGNORECASE | re.DOTALL)
                for match in matches:
                    if isinstance(match, tuple):
                        milestone_name = match[0].strip()
                        # If we have position info, use it
                        if len(match) > 2:
                            after_milestone = int(match[1])
                            position = after_milestone
                        else:
                            position = None
                    else:
                        milestone_name = match.strip()
                        position = None

                    # Clean up the milestone name
                    milestone_name = re.sub(r'^(only |just |simply )', '', milestone_name, re.IGNORECASE)
                    milestone_name = milestone_name.strip('"\'.,!?')

                    if milestone_name and len(milestone_name) > 3:  # Avoid very short matches
                        actions.append({
                            'action': 'add_milestone',
                            'data': {
                                'name': milestone_name.title(),
                                'description': f'Milestone focused on {milestone_name.lower()}',
                                'position': position,
                                'estimated_duration': '2 weeks',
                                'success_criteria': f'Complete all objectives related to {milestone_name.lower()}'
                            }
                        })
                        break  # Only add one milestone per pattern to avoid duplicates

            # Enhanced patterns for task detection
            task_patterns = [
                r'add.*?task.*?"([^"]+)"',
                r'new task.*?"([^"]+)"',
                r'task for ([^.]+?)(?:\.|$)',
                r'create.*?task.*?"([^"]+)"'
            ]

            for pattern in task_patterns:
                matches = re.findall(pattern, response_content, re.IGNORECASE)
                for task_name in matches:
                    task_name = task_name.strip('"\'.,!?')
                    if task_name and len(task_name) > 3:
                        actions.append({
                            'action': 'add_task',
                            'data': {
                                'name': task_name.title(),
                                'description': f'Task for {task_name.lower()}',
                                'milestone_id': None  # Will be resolved to first available milestone
                            }
                        })

            # Look for specific action keywords in the response
            action_keywords = {
                'car setup': 'Car Setup and Configuration',
                'setup optimization': 'Setup Optimization',
                'vehicle setup': 'Vehicle Setup',
                'car configuration': 'Car Configuration',
                'setup theory': 'Setup Theory and Practice',
                'setup practice': 'Setup Theory and Practice'
            }

            for keyword, milestone_name in action_keywords.items():
                if keyword.lower() in response_content.lower() and not any(action['data']['name'] == milestone_name for action in actions if action['action'] == 'add_milestone'):
                    actions.append({
                        'action': 'add_milestone',
                        'data': {
                            'name': milestone_name,
                            'description': f'Comprehensive milestone covering {keyword} fundamentals and advanced techniques',
                            'estimated_duration': '3 weeks',
                            'success_criteria': f'Master all aspects of {keyword} including theory and practical application'
                        }
                    })

        except Exception as e:
            print(f"Error parsing structured recommendations: {e}")

        return actions

    def _execute_action(self, action, plan, user):
        """Execute a single action on the plan"""
        try:
            action_type = action.get('action')
            data = action.get('data', {})

            # Handle legacy format where parameters are directly in action instead of data
            if not data:
                # Copy all non-action keys to data
                data = {k: v for k, v in action.items() if k != 'action'}
                print(f"Using legacy action format, extracted data: {data}")

            if action_type == 'add_milestone':
                return self._execute_add_milestone(plan, data, user)
            elif action_type == 'add_task':
                return self._execute_add_task(plan, data, user)
            elif action_type == 'add_subtask':
                return self._execute_add_subtask(plan, data, user)
            elif action_type == 'update_milestone':
                return self._execute_update_milestone(plan, data, user)
            elif action_type == 'update_task':
                return self._execute_update_task(plan, data, user)
            elif action_type == 'update_subtask':
                return self._execute_update_subtask(plan, data, user)
            elif action_type == 'update_task_status':
                return self._execute_update_task_status(plan, data, user)
            elif action_type == 'complete_task':
                return self._execute_complete_task(plan, data, user)
            elif action_type == 'delete_milestone':
                return self._execute_delete_milestone(plan, data, user)
            elif action_type == 'delete_task':
                return self._execute_delete_task(plan, data, user)
            elif action_type == 'delete_subtask':
                return self._execute_delete_subtask(plan, data, user)
            elif action_type == 'set_task_priority':
                return self._execute_set_task_priority(plan, data, user)
            elif action_type == 'bulk_update_status':
                return self._execute_bulk_update_status(plan, data, user)
            elif action_type == 'reorder_tasks':
                return self._execute_reorder_tasks(plan, data, user)
            elif action_type == 'analyze_project':
                return self._execute_analyze_project(plan, data, user)
            elif action_type == 'suggest_improvements':
                return self._execute_suggest_improvements(plan, data, user)
            else:
                print(f"Unknown action type: {action_type}")
                return None

        except Exception as e:
            print(f"Error executing action {action_type}: {str(e)}")
            return None

    def _execute_add_milestone(self, plan, data, user):
        """Execute add_milestone action"""
        try:
            from plans.serializers import MilestoneSerializer, TaskSerializer

            milestone_data = {
                'name': data.get('name', 'New Milestone'),
                'description': data.get('description', ''),
                'estimated_duration': data.get('estimated_duration', ''),
                'success_criteria': data.get('success_criteria', ''),
                'plan': plan.id
            }

            serializer = MilestoneSerializer(data=milestone_data)
            if serializer.is_valid():
                milestone = serializer.save()

                # Auto-create basic tasks for the milestone if none specified
                milestone_name_lower = milestone.name.lower()
                basic_tasks = []

                if 'car setup' in milestone_name_lower or 'setup' in milestone_name_lower:
                    basic_tasks = [
                        'Understanding Basic Setup Parameters',
                        'Learning Suspension and Damper Settings',
                        'Mastering Aerodynamics Configuration',
                        'Brake Balance and Pressure Optimization',
                        'Testing and Data Analysis Methods'
                    ]
                elif 'learning' in milestone_name_lower or 'fundamentals' in milestone_name_lower:
                    basic_tasks = [
                        'Study Core Concepts and Theory',
                        'Practice Basic Techniques',
                        'Apply Knowledge in Real Scenarios',
                        'Review and Refine Understanding',
                        'Master Advanced Applications'
                    ]
                else:
                    # Generic tasks for any milestone
                    basic_tasks = [
                        f'Research and Planning for {milestone.name}',
                        f'Initial Implementation of {milestone.name}',
                        f'Testing and Validation of {milestone.name}',
                        f'Optimization and Refinement of {milestone.name}',
                        f'Final Review and Documentation of {milestone.name}'
                    ]

                # Create tasks for the milestone with subtasks
                created_tasks = 0
                created_subtasks = 0

                for task_name in basic_tasks:
                    try:
                        task_data = {
                            'name': task_name,
                            'description': f'Detailed task focusing on {task_name.lower()} with step-by-step guidance and practical exercises',
                            'milestone': milestone.id,
                            'priority': 2,  # Medium priority
                            'status': 1  # Pending
                        }

                        task_serializer = TaskSerializer(data=task_data)
                        if task_serializer.is_valid():
                            task = task_serializer.save()
                            created_tasks += 1

                            # Auto-create subtasks for each task
                            subtasks = self._generate_subtasks_for_task(task_name, milestone_name_lower)

                            for subtask_name in subtasks:
                                try:
                                    from plans.serializers import SubtaskSerializer
                                    subtask_data = {
                                        'name': subtask_name,
                                        'description': f'Step-by-step guidance for {subtask_name.lower()}',
                                        'task': task.id,
                                        'status': 1,  # Pending
                                        'progress': 0
                                    }

                                    subtask_serializer = SubtaskSerializer(data=subtask_data)
                                    if subtask_serializer.is_valid():
                                        subtask_serializer.save()
                                        created_subtasks += 1
                                    else:
                                        print(f"Subtask serializer errors for '{subtask_name}': {subtask_serializer.errors}")
                                except Exception as subtask_error:
                                    print(f"Error creating subtask '{subtask_name}': {str(subtask_error)}")
                                    continue
                        else:
                            print(f"Task serializer errors for '{task_name}': {task_serializer.errors}")
                    except Exception as task_error:
                        print(f"Error creating task '{task_name}': {str(task_error)}")
                        continue

                return {
                    'milestone_id': milestone.id,
                    'milestone_name': milestone.name,
                    'created_tasks': created_tasks,
                    'created_subtasks': created_subtasks,
                    'message': f'Successfully added milestone "{milestone.name}" with {created_tasks} tasks and {created_subtasks} subtasks'
                }
            else:
                print(f"Milestone serializer errors: {serializer.errors}")
                return None

        except Exception as e:
            print(f"Error in _execute_add_milestone: {str(e)}")
            return None

    def _generate_subtasks_for_task(self, task_name, milestone_context):
        """Generate contextual subtasks for a given task"""
        task_lower = task_name.lower()

        if 'setup' in milestone_context and 'parameters' in task_lower:
            return [
                'Study suspension geometry basics',
                'Learn about spring rates and damping',
                'Understand anti-roll bar effects',
                'Practice reading telemetry data',
                'Test different parameter combinations'
            ]
        elif 'setup' in milestone_context and 'suspension' in task_lower:
            return [
                'Adjust front suspension settings',
                'Fine-tune rear suspension balance',
                'Test ride height variations',
                'Optimize damper settings',
                'Validate handling characteristics'
            ]
        elif 'setup' in milestone_context and 'aerodynamics' in task_lower:
            return [
                'Configure front wing angle',
                'Adjust rear wing settings',
                'Balance downforce distribution',
                'Test drag vs downforce trade-offs',
                'Validate cornering performance'
            ]
        elif 'setup' in milestone_context and 'brake' in task_lower:
            return [
                'Set brake balance distribution',
                'Adjust brake pressure settings',
                'Test brake temperature management',
                'Optimize brake bias for different corners',
                'Validate braking consistency'
            ]
        elif 'setup' in milestone_context and 'testing' in task_lower:
            return [
                'Plan systematic testing approach',
                'Record baseline performance data',
                'Execute controlled setup changes',
                'Analyze performance differences',
                'Document optimal configurations'
            ]
        elif 'learning' in milestone_context or 'study' in task_lower:
            return [
                'Research theoretical foundations',
                'Review expert tutorials and guides',
                'Take detailed notes on key concepts',
                'Create summary reference materials',
                'Quiz yourself on important topics'
            ]
        elif 'practice' in task_lower or 'implementation' in task_lower:
            return [
                'Start with basic exercises',
                'Progress to intermediate challenges',
                'Apply concepts in real scenarios',
                'Get feedback on performance',
                'Refine technique based on results'
            ]
        elif 'testing' in task_lower or 'validation' in task_lower:
            return [
                'Design test scenarios',
                'Execute systematic tests',
                'Collect and analyze data',
                'Compare against benchmarks',
                'Document findings and insights'
            ]
        elif 'optimization' in task_lower or 'refinement' in task_lower:
            return [
                'Identify improvement opportunities',
                'Implement incremental changes',
                'Measure performance impact',
                'Fine-tune based on results',
                'Achieve optimal configuration'
            ]
        else:
            # Generic subtasks for any task
            task_base = task_name.replace('Research and Planning for ', '').replace('Initial Implementation of ', '').replace('Testing and Validation of ', '').replace('Optimization and Refinement of ', '').replace('Final Review and Documentation of ', '')
            return [
                f'Gather resources and materials for {task_base}',
                f'Create detailed plan for {task_base}',
                f'Execute core activities for {task_base}',
                f'Review and validate {task_base} results',
                f'Document learnings from {task_base}'
            ]

    def _clean_ai_response_content(self, content):
        """Clean AI response content for better UX - hide technical details"""
        if not content:
            return "I've processed your request successfully!"

        import re

        # Remove all JSON blocks and technical explanations
        cleaned = re.sub(r'```json.*?```', '', content, flags=re.DOTALL)
        cleaned = re.sub(r'```.*?```', '', cleaned, flags=re.DOTALL)

        # Remove technical explanations and implementation details
        cleaned = re.sub(r'Here\'s the plan:.*?(?=\n\n|\Z)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'Explanation of Actions:.*?(?=\n\n|\Z)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'Important Considerations:.*?(?=\n\n|\Z)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'After adding.*?(?=\n\n|\Z)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'The milestone_id fields.*?(?=\n\n|\Z)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'This comprehensive approach.*?(?=\n\n|\Z)', '', cleaned, flags=re.DOTALL)

        # Remove excessive whitespace
        cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)
        cleaned = cleaned.strip()

        # If content is mostly technical explanations, provide a simple confirmation
        if len(cleaned) < 100 or 'milestone_id' in cleaned or 'task_slug' in cleaned:
            return "**Done!** I've made the changes to your plan. Check it out!"

        return cleaned

    def _execute_add_task(self, plan, data, user):
        """Execute add_task action"""
        try:
            from plans.serializers import TaskUpdateSerializer

            # Find milestone to add task to
            milestone_id = data.get('milestone_id')
            if not milestone_id:
                # Use first milestone if none specified
                milestone = plan.milestone_set.first()
                if not milestone:
                    return None
                milestone_id = milestone.id

            task_data = {
                'name': data.get('name', 'New Task'),
                'description': data.get('description', ''),
                'milestone': milestone_id,
                'priority': data.get('priority', 2),  # Default to medium priority
                'status': data.get('status', 1)  # Default to pending
            }

            serializer = TaskUpdateSerializer(data=task_data)
            if serializer.is_valid():
                task = serializer.save()
                return {
                    'task_id': task.id,
                    'task_slug': task.slug,
                    'task_name': task.name,
                    'milestone_name': task.milestone.name,
                    'message': f'Successfully added task "{task.name}" to milestone "{task.milestone.name}"'
                }
            else:
                print(f"Task serializer errors: {serializer.errors}")
                return None

        except Exception as e:
            print(f"Error in _execute_add_task: {str(e)}")
            return None

    def _execute_add_subtask(self, plan, data, user):
        """Execute add_subtask action"""
        try:
            from plans.serializers import SubtaskSerializer
            from plans.models import Task

            # Find task to add subtask to
            task_slug = data.get('task_slug')
            if not task_slug:
                # Use first task if none specified
                task = Task.objects.filter(milestone__plan=plan).first()
                if not task:
                    return None
            else:
                task = Task.objects.filter(slug=task_slug, milestone__plan=plan).first()
                if not task:
                    return None

            subtask_data = {
                'name': data.get('name', 'New Subtask'),
                'description': data.get('description', ''),
                'task': task.id,
                'status': data.get('status', 1),  # Default to pending
                'progress': data.get('progress', 0)  # Default to 0% progress
            }

            serializer = SubtaskSerializer(data=subtask_data)
            if serializer.is_valid():
                subtask = serializer.save()
                return {
                    'subtask_id': subtask.id,
                    'subtask_slug': subtask.slug,
                    'subtask_name': subtask.name,
                    'task_name': subtask.task.name,
                    'message': f'Successfully added subtask "{subtask.name}" to task "{subtask.task.name}"'
                }
            else:
                print(f"Subtask serializer errors: {serializer.errors}")
                return None

        except Exception as e:
            print(f"Error in _execute_add_subtask: {str(e)}")
            return None

    def _execute_update_task_status(self, plan, data, user):
        """Execute update_task_status action"""
        try:
            from plans.models import Task

            task_slug = data.get('task_slug')
            new_status = data.get('status')

            if not task_slug or new_status is None:
                return None

            task = Task.objects.filter(slug=task_slug, milestone__plan=plan).first()
            if not task:
                return None

            old_status = task.status
            task.status = new_status
            task.save()

            status_names = {1: 'Pending', 2: 'In Progress', 3: 'Completed'}

            return {
                'task_id': task.id,
                'task_slug': task.slug,
                'task_name': task.name,
                'old_status': status_names.get(old_status, 'Unknown'),
                'new_status': status_names.get(new_status, 'Unknown'),
                'message': f'Successfully updated task "{task.name}" status to {status_names.get(new_status, "Unknown")}'
            }

        except Exception as e:
            print(f"Error in _execute_update_task_status: {str(e)}")
            return None

    def _execute_complete_task(self, plan, data, user):
        """Execute complete_task action"""
        try:
            from plans.models import Task

            task_slug = data.get('task_slug')
            if not task_slug:
                return None

            task = Task.objects.filter(slug=task_slug, milestone__plan=plan).first()
            if not task:
                return None

            # Check for sequential completion validation
            if user.sequential_task_completion and task.milestone:
                # Check if there are any incomplete tasks with lower order in the same milestone
                incomplete_previous_tasks = Task.objects.filter(
                    milestone=task.milestone,
                    order__lt=task.order,
                    status__in=[1, 2]  # TODO_STATUS, INPROGRESS_STATUS
                ).exists()

                if incomplete_previous_tasks:
                    return {
                        'error': True,
                        'message': f'Cannot complete task "{task.name}". Sequential task completion is enabled - please complete previous tasks first.'
                    }

            task.status = 3  # Completed
            task.progress = 100
            task.save()

            return {
                'task_id': task.id,
                'task_slug': task.slug,
                'task_name': task.name,
                'message': f'Successfully marked task "{task.name}" as completed'
            }

        except Exception as e:
            print(f"Error in _execute_complete_task: {str(e)}")
            return None

    # Update methods for other actions
    def _execute_update_milestone(self, plan, data, user):
        """Execute update_milestone action"""
        try:
            from plans.models import Milestone

            milestone_id = data.get('milestone_id')
            if not milestone_id:
                print("No milestone_id provided for update")
                return None

            milestone = Milestone.objects.filter(id=milestone_id, plan=plan).first()
            if not milestone:
                print(f"Milestone with id {milestone_id} not found")
                return None

            # Update fields if provided
            updated_fields = []
            if 'name' in data:
                milestone.name = data['name']
                updated_fields.append('name')
            if 'description' in data:
                milestone.description = data['description']
                updated_fields.append('description')
            if 'estimated_duration' in data:
                milestone.estimated_duration = data['estimated_duration']
                updated_fields.append('estimated_duration')
            if 'success_criteria' in data:
                milestone.success_criteria = data['success_criteria']
                updated_fields.append('success_criteria')

            milestone.save()

            return {
                'milestone_id': milestone_id,
                'milestone_name': milestone.name,
                'updated_fields': updated_fields,
                'message': f'Successfully updated milestone "{milestone.name}"'
            }

        except Exception as e:
            print(f"Error in _execute_update_milestone: {str(e)}")
            return None

    def _execute_update_task(self, plan, data, user):
        """Execute update_task action"""
        try:
            from plans.models import Task

            task_slug = data.get('task_slug')
            if not task_slug:
                print("No task_slug provided for update")
                return None

            task = Task.objects.filter(slug=task_slug, milestone__plan=plan).first()
            if not task:
                print(f"Task with slug {task_slug} not found")
                return None

            # Update fields if provided
            updated_fields = []
            if 'name' in data:
                task.name = data['name']
                updated_fields.append('name')
            if 'description' in data:
                task.description = data['description']
                updated_fields.append('description')
            if 'priority' in data:
                task.priority = data['priority']
                updated_fields.append('priority')
            if 'status' in data:
                task.status = data['status']
                updated_fields.append('status')
            if 'progress' in data:
                task.progress = data['progress']
                updated_fields.append('progress')

            task.save()

            return {
                'task_slug': task_slug,
                'task_name': task.name,
                'milestone_name': task.milestone.name,
                'updated_fields': updated_fields,
                'message': f'Successfully updated task "{task.name}"'
            }

        except Exception as e:
            print(f"Error in _execute_update_task: {str(e)}")
            return None

    def _execute_update_subtask(self, plan, data, user):
        """Execute update_subtask action"""
        try:
            from plans.models import Subtask

            subtask_slug = data.get('subtask_slug')
            if not subtask_slug:
                print("No subtask_slug provided for update")
                return None

            subtask = Subtask.objects.filter(slug=subtask_slug, task__milestone__plan=plan).first()
            if not subtask:
                print(f"Subtask with slug {subtask_slug} not found")
                return None

            # Update fields if provided
            updated_fields = []
            if 'name' in data:
                subtask.name = data['name']
                updated_fields.append('name')
            if 'description' in data:
                subtask.description = data['description']
                updated_fields.append('description')
            if 'status' in data:
                subtask.status = data['status']
                updated_fields.append('status')
            if 'progress' in data:
                subtask.progress = data['progress']
                updated_fields.append('progress')

            subtask.save()

            return {
                'subtask_slug': subtask_slug,
                'subtask_name': subtask.name,
                'task_name': subtask.task.name,
                'updated_fields': updated_fields,
                'message': f'Successfully updated subtask "{subtask.name}"'
            }

        except Exception as e:
            print(f"Error in _execute_update_subtask: {str(e)}")
            return None

    def _execute_delete_milestone(self, plan, data, user):
        """Execute delete_milestone action"""
        try:
            from plans.models import Milestone

            milestone_id = data.get('milestone_id')
            if not milestone_id:
                print("No milestone_id provided for deletion")
                return None

            milestone = Milestone.objects.filter(id=milestone_id, plan=plan).first()
            if not milestone:
                print(f"Milestone with id {milestone_id} not found")
                return None

            milestone_name = milestone.name
            task_count = milestone.task_set.count()

            # Delete the milestone (this will cascade delete tasks and subtasks)
            milestone.delete()

            return {
                'milestone_id': milestone_id,
                'milestone_name': milestone_name,
                'deleted_tasks': task_count,
                'message': f'Successfully deleted milestone "{milestone_name}" and {task_count} associated tasks'
            }

        except Exception as e:
            print(f"Error in _execute_delete_milestone: {str(e)}")
            return None

    def _execute_delete_task(self, plan, data, user):
        """Execute delete_task action"""
        try:
            from plans.models import Task

            task_slug = data.get('task_slug')
            if not task_slug:
                print("No task_slug provided for deletion")
                return None

            task = Task.objects.filter(slug=task_slug, milestone__plan=plan).first()
            if not task:
                print(f"Task with slug {task_slug} not found")
                return None

            task_name = task.name
            subtask_count = task.subtask_set.count()
            milestone_name = task.milestone.name

            # Delete the task (this will cascade delete subtasks)
            task.delete()

            return {
                'task_slug': task_slug,
                'task_name': task_name,
                'milestone_name': milestone_name,
                'deleted_subtasks': subtask_count,
                'message': f'Successfully deleted task "{task_name}" from milestone "{milestone_name}" and {subtask_count} associated subtasks'
            }

        except Exception as e:
            print(f"Error in _execute_delete_task: {str(e)}")
            return None

    def _execute_delete_subtask(self, plan, data, user):
        """Execute delete_subtask action"""
        try:
            from plans.models import Subtask

            subtask_slug = data.get('subtask_slug')
            if not subtask_slug:
                print("No subtask_slug provided for deletion")
                return None

            subtask = Subtask.objects.filter(slug=subtask_slug, task__milestone__plan=plan).first()
            if not subtask:
                print(f"Subtask with slug {subtask_slug} not found")
                return None

            subtask_name = subtask.name
            task_name = subtask.task.name

            # Delete the subtask
            subtask.delete()

            return {
                'subtask_slug': subtask_slug,
                'subtask_name': subtask_name,
                'task_name': task_name,
                'message': f'Successfully deleted subtask "{subtask_name}" from task "{task_name}"'
            }

        except Exception as e:
            print(f"Error in _execute_delete_subtask: {str(e)}")
            return None

    def _execute_set_task_priority(self, plan, data, user):
        """Execute set_task_priority action"""
        try:
            from plans.models import Task

            task_slug = data.get('task_slug')
            priority = data.get('priority', 2)  # Default to medium priority

            if not task_slug:
                return None

            task = Task.objects.filter(slug=task_slug, milestone__plan=plan).first()
            if not task:
                return None

            old_priority = task.priority
            task.priority = priority
            task.save()

            priority_names = {1: 'Low', 2: 'Medium', 3: 'High'}

            return {
                'task_id': task.id,
                'task_slug': task.slug,
                'task_name': task.name,
                'old_priority': priority_names.get(old_priority, 'Unknown'),
                'new_priority': priority_names.get(priority, 'Unknown'),
                'message': f'Successfully updated task "{task.name}" priority to {priority_names.get(priority, "Unknown")}'
            }

        except Exception as e:
            print(f"Error in _execute_set_task_priority: {str(e)}")
            return None

    def _execute_bulk_update_status(self, plan, data, user):
        """Execute bulk_update_status action"""
        try:
            from plans.models import Task, Subtask

            items = data.get('items', [])
            if not items:
                return None

            updated_items = []

            for item in items:
                item_type = item.get('type')  # 'task' or 'subtask'
                item_id = item.get('id')
                status = item.get('status')

                if item_type == 'task':
                    task = Task.objects.filter(slug=item_id, milestone__plan=plan).first()
                    if task:
                        # Check for sequential completion validation
                        if user.sequential_task_completion and status == 3 and task.milestone:
                            incomplete_previous_tasks = Task.objects.filter(
                                milestone=task.milestone,
                                order__lt=task.order,
                                status__in=[1, 2]  # TODO_STATUS, INPROGRESS_STATUS
                            ).exists()

                            if incomplete_previous_tasks:
                                continue  # Skip this task, don't update it

                        task.status = status
                        if status == 3:  # Completed
                            task.progress = 100
                        task.save()
                        updated_items.append(f"Task: {task.name}")

                elif item_type == 'subtask':
                    subtask = Subtask.objects.filter(slug=item_id, task__milestone__plan=plan).first()
                    if subtask:
                        # Check for sequential completion validation
                        if user.sequential_task_completion and status == 3 and subtask.task:
                            incomplete_previous_subtasks = Subtask.objects.filter(
                                task=subtask.task,
                                order__lt=subtask.order,
                                status__in=[1, 2]  # TODO_STATUS, INPROGRESS_STATUS
                            ).exists()

                            if incomplete_previous_subtasks:
                                continue  # Skip this subtask, don't update it

                        subtask.status = status
                        if status == 3:  # Completed
                            subtask.progress = 100
                        subtask.save()
                        updated_items.append(f"Subtask: {subtask.name}")

            return {
                'updated_count': len(updated_items),
                'updated_items': updated_items,
                'message': f'Successfully updated {len(updated_items)} items'
            }

        except Exception as e:
            print(f"Error in _execute_bulk_update_status: {str(e)}")
            return None

    def _execute_reorder_tasks(self, plan, data, user):
        """Execute reorder_tasks action"""
        try:
            from plans.models import Milestone, Task

            milestone_id = data.get('milestone_id')
            task_order = data.get('task_order', [])  # List of task slugs in desired order

            if not milestone_id or not task_order:
                return None

            milestone = Milestone.objects.filter(id=milestone_id, plan=plan).first()
            if not milestone:
                return None

            # Update task order
            for index, task_slug in enumerate(task_order):
                Task.objects.filter(slug=task_slug, milestone=milestone).update(order=index)

            return {
                'milestone_id': milestone_id,
                'milestone_name': milestone.name,
                'reordered_tasks': len(task_order),
                'message': f'Successfully reordered {len(task_order)} tasks in milestone "{milestone.name}"'
            }

        except Exception as e:
            print(f"Error in _execute_reorder_tasks: {str(e)}")
            return None

    def _execute_analyze_project(self, plan, data, user):
        """Execute analyze_project action - provides detailed project analysis"""
        try:
            from plans.models import Milestone, Task, Subtask

            # Get project statistics
            milestones = Milestone.objects.filter(plan=plan)
            total_milestones = milestones.count()

            total_tasks = Task.objects.filter(milestone__plan=plan).count()
            completed_tasks = Task.objects.filter(milestone__plan=plan, status=3).count()
            in_progress_tasks = Task.objects.filter(milestone__plan=plan, status=2).count()

            total_subtasks = Subtask.objects.filter(task__milestone__plan=plan).count()
            completed_subtasks = Subtask.objects.filter(task__milestone__plan=plan, status=3).count()

            # Calculate progress
            task_progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            subtask_progress = (completed_subtasks / total_subtasks * 100) if total_subtasks > 0 else 0

            # Identify issues
            issues = []
            if total_tasks == 0:
                issues.append("No tasks defined - project needs task breakdown")
            if total_subtasks == 0:
                issues.append("No subtasks defined - tasks need detailed breakdown")
            if task_progress < 10:
                issues.append("Very low task completion rate - consider reviewing priorities")

            # Identify milestones without tasks
            empty_milestones = []
            for milestone in milestones:
                if milestone.task_set.count() == 0:
                    empty_milestones.append(milestone.name)

            analysis = {
                'project_name': plan.name,
                'total_milestones': total_milestones,
                'total_tasks': total_tasks,
                'completed_tasks': completed_tasks,
                'in_progress_tasks': in_progress_tasks,
                'total_subtasks': total_subtasks,
                'completed_subtasks': completed_subtasks,
                'task_progress_percentage': round(task_progress, 1),
                'subtask_progress_percentage': round(subtask_progress, 1),
                'issues_identified': issues,
                'empty_milestones': empty_milestones,
                'message': f'Project analysis complete: {completed_tasks}/{total_tasks} tasks completed ({round(task_progress, 1)}%)'
            }

            return analysis

        except Exception as e:
            print(f"Error in _execute_analyze_project: {str(e)}")
            return None

    def _execute_suggest_improvements(self, plan, data, user):
        """Execute suggest_improvements action - provides actionable improvement suggestions"""
        try:
            from plans.models import Milestone, Task, Subtask

            suggestions = []

            # Check for milestones without tasks
            milestones = Milestone.objects.filter(plan=plan)
            for milestone in milestones:
                if milestone.task_set.count() == 0:
                    suggestions.append({
                        'type': 'add_tasks',
                        'milestone': milestone.name,
                        'suggestion': f'Add tasks to milestone "{milestone.name}" to make it actionable'
                    })

            # Check for tasks without subtasks
            tasks = Task.objects.filter(milestone__plan=plan)
            for task in tasks:
                if task.subtask_set.count() == 0:
                    suggestions.append({
                        'type': 'add_subtasks',
                        'task': task.name,
                        'suggestion': f'Break down task "{task.name}" into specific subtasks'
                    })

            # Check for stalled tasks (in progress for too long)
            stalled_tasks = Task.objects.filter(milestone__plan=plan, status=2)
            if stalled_tasks.count() > 3:
                suggestions.append({
                    'type': 'review_progress',
                    'suggestion': f'Review {stalled_tasks.count()} in-progress tasks - some may need attention'
                })

            # Check overall progress
            total_tasks = tasks.count()
            completed_tasks = Task.objects.filter(milestone__plan=plan, status=3).count()

            if total_tasks > 0:
                progress = completed_tasks / total_tasks * 100
                if progress < 20:
                    suggestions.append({
                        'type': 'boost_progress',
                        'suggestion': 'Consider focusing on completing some quick wins to build momentum'
                    })

            return {
                'suggestions_count': len(suggestions),
                'suggestions': suggestions,
                'message': f'Generated {len(suggestions)} improvement suggestions for the project'
            }

        except Exception as e:
            print(f"Error in _execute_suggest_improvements: {str(e)}")
            return None


class AIAgentActionView(APIView):
    """API endpoint for AI agent to perform actions on plans"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'action': openapi.Schema(type=openapi.TYPE_STRING, description='Action type: add_milestone, add_task, add_subtask, etc.'),
                'plan_slug': openapi.Schema(type=openapi.TYPE_STRING, description='Plan slug'),
                'data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Action data'),
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='User message for context')
            },
            required=['action', 'plan_slug', 'data']
        ),
        responses={
            200: openapi.Response(description="Action completed successfully"),
            400: openapi.Response(description="Invalid input"),
            404: openapi.Response(description="Plan not found")
        }
    )
    def post(self, request):
        try:
            action = request.data.get('action')
            plan_slug = request.data.get('plan_slug')
            data = request.data.get('data', {})
            message = request.data.get('message', '')

            # Get the plan
            plan = get_object_or_404(Plan, slug=plan_slug)

            # Check if user has permission to modify this plan
            # For now, we'll check if user is the owner or has access
            if plan.user != request.user:
                # Check if user has access through PlanAccess
                from plans.models import PlanAccess
                if not PlanAccess.objects.filter(user=request.user, plan=plan).exists():
                    return Response({
                        'error': 'You do not have permission to modify this plan'
                    }, status=status.HTTP_403_FORBIDDEN)

            result = None

            if action == 'add_milestone':
                result = self._add_milestone(plan, data)
            elif action == 'add_task':
                result = self._add_task(plan, data)
            elif action == 'add_subtask':
                result = self._add_subtask(plan, data)
            elif action == 'update_subtask':
                result = self._update_subtask(plan, data)
            elif action == 'update_task_status':
                result = self._update_task_status(plan, data)
            elif action == 'complete_task':
                result = self._complete_task(plan, data)
            elif action == 'update_milestone':
                result = self._update_milestone(plan, data)
            elif action == 'update_task':
                result = self._update_task(plan, data)
            elif action == 'delete_milestone':
                result = self._delete_milestone(plan, data)
            elif action == 'delete_task':
                result = self._delete_task(plan, data)
            elif action == 'delete_subtask':
                result = self._delete_subtask(plan, data)
            elif action == 'set_task_priority':
                result = self._set_task_priority(plan, data)
            elif action == 'bulk_update_status':
                result = self._bulk_update_status(plan, data)
            elif action == 'reorder_tasks':
                result = self._reorder_tasks(plan, data)
            elif action == 'analyze_project':
                result = self._analyze_project(plan, data)
            elif action == 'suggest_improvements':
                result = self._suggest_improvements(plan, data)
            else:
                return Response({
                    'error': f'Unknown action: {action}'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'message': 'Action completed successfully',
                'action': action,
                'result': result
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def _add_milestone(self, plan, data):
        """Add a new milestone to the plan"""
        # Handle success_criteria properly - ensure it's a string
        success_criteria = data.get('success_criteria', '')
        if isinstance(success_criteria, list):
            success_criteria = '\n'.join(str(item) for item in success_criteria)
        elif not isinstance(success_criteria, str):
            success_criteria = str(success_criteria) if success_criteria else ''

        # Handle estimated_duration properly
        estimated_duration = data.get('estimated_duration', '')
        if not isinstance(estimated_duration, str):
            estimated_duration = str(estimated_duration) if estimated_duration else ''

        milestone_data = {
            'name': data.get('name', 'New Milestone'),
            'description': data.get('description', ''),
            'plan': plan.id,
            'estimated_duration': estimated_duration,
            'success_criteria': success_criteria
        }

        serializer = MilestoneSerializer(data=milestone_data)
        if serializer.is_valid():
            milestone = serializer.save()

            # Handle positioning if specified
            position = data.get('position')
            if position is not None:
                try:
                    # Get all milestones for this plan ordered by id
                    milestones = list(plan.milestone_set.all().order_by('id'))
                    if position < len(milestones):
                        # Move the new milestone to the specified position
                        # This is a simple approach - in a more complex system you'd use proper ordering fields
                        print(f"Milestone positioned at {position} (basic implementation)")
                except Exception as e:
                    print(f"Error positioning milestone: {e}")

            return {
                'milestone_id': milestone.id,
                'milestone_name': milestone.name,
                'milestone_data': serializer.data,
                'message': f'Successfully added milestone: {milestone.name}'
            }
        else:
            raise ValueError(f'Invalid milestone data: {serializer.errors}')

    def _add_task(self, plan, data):
        """Add a new task to a milestone"""
        milestone_id = data.get('milestone_id')

        # If no milestone_id provided, try to find the most appropriate milestone
        if not milestone_id:
            # Strategy 1: Look for a milestone that matches the task context
            task_name = data.get('name', '').lower()
            milestones = plan.milestone_set.all()

            # Try to match by keywords
            for milestone in milestones:
                milestone_name = milestone.name.lower()
                if any(word in milestone_name for word in task_name.split() if len(word) > 3):
                    milestone_id = milestone.id
                    break

            # Strategy 2: Use the first milestone if no match found
            if not milestone_id and milestones.exists():
                milestone_id = milestones.first().id

            # Strategy 3: Create a default milestone if none exist
            if not milestone_id:
                default_milestone_data = {
                    'name': 'General Tasks',
                    'description': 'General milestone for organizing tasks',
                    'plan': plan.id,
                    'estimated_duration': '2 weeks',
                    'success_criteria': 'Complete all general tasks'
                }
                milestone_serializer = MilestoneSerializer(data=default_milestone_data)
                if milestone_serializer.is_valid():
                    milestone = milestone_serializer.save()
                    milestone_id = milestone.id
                else:
                    raise ValueError('Could not create default milestone for task')

        milestone = get_object_or_404(Milestone, id=milestone_id, plan=plan)

        task_data = {
            'name': data.get('name', 'New Task'),
            'description': data.get('description', ''),
            'milestone': milestone.id,
            'priority': data.get('priority', 2),  # Default to medium priority
            'status': data.get('status', 1)  # Default to pending
        }

        serializer = TaskUpdateSerializer(data=task_data)
        if serializer.is_valid():
            task = serializer.save()

            # Set order if not provided
            if 'order' not in task_data:
                task.order = Task.objects.filter(milestone=milestone).count()
                task.save()

            return {
                'task_id': task.id,
                'task_slug': task.slug,
                'task_name': task.name,
                'milestone_name': milestone.name,
                'task_data': serializer.data,
                'message': f'Successfully added task "{task.name}" to milestone "{milestone.name}"'
            }
        else:
            raise ValueError(f'Invalid task data: {serializer.errors}')

    def _add_subtask(self, plan, data):
        """Add a new subtask to a task"""
        task_slug = data.get('task_slug')
        if not task_slug:
            raise ValueError('task_slug is required for adding subtasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)

        subtask_data = {
            'name': data.get('name', 'New Subtask'),
            'description': data.get('description', ''),
            'task': task.id,
            'status': data.get('status', 1),  # Default to pending
            'progress': data.get('progress', 0)  # Default to 0% progress
        }

        serializer = SubtaskSerializer(data=subtask_data)
        if serializer.is_valid():
            subtask = serializer.save()

            # Set order if not provided
            if 'order' not in subtask_data:
                subtask.order = Subtask.objects.filter(task=task).count()
                subtask.save()

            return {
                'subtask_id': subtask.id,
                'subtask_slug': subtask.slug,
                'subtask_name': subtask.name,
                'task_name': task.name,
                'subtask_data': serializer.data,
                'message': f'Successfully added subtask "{subtask.name}" to task "{task.name}"'
            }
        else:
            raise ValueError(f'Invalid subtask data: {serializer.errors}')

    def _update_subtask(self, plan, data):
        """Update subtask details"""
        subtask_slug = data.get('subtask_slug')
        if not subtask_slug:
            raise ValueError('subtask_slug is required for updating subtasks')

        subtask = get_object_or_404(Subtask, slug=subtask_slug, task__milestone__plan=plan)

        # Update fields if provided
        if 'name' in data:
            subtask.name = data['name']
        if 'description' in data:
            subtask.description = data['description']
        if 'status' in data:
            subtask.status = data['status']
        if 'progress' in data:
            subtask.progress = data['progress']
        if 'start_date' in data:
            from datetime import datetime
            subtask.start_date = datetime.fromisoformat(data['start_date']).date()
        if 'end_date' in data:
            from datetime import datetime
            subtask.end_date = datetime.fromisoformat(data['end_date']).date()

        subtask.save()

        return {
            'subtask_id': subtask.id,
            'subtask_slug': subtask.slug,
            'subtask_name': subtask.name,
            'task_name': subtask.task.name,
            'updated_fields': list(data.keys()),
            'message': f'Successfully updated subtask "{subtask.name}"'
        }

    def _update_task_status(self, plan, data):
        """Update task status"""
        task_slug = data.get('task_slug')
        new_status = data.get('status')

        if not task_slug or new_status is None:
            raise ValueError('task_slug and status are required for updating task status')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)
        task.status = new_status
        task.save()

        return {
            'task_id': task.id,
            'task_slug': task.slug,
            'task_name': task.name,
            'new_status': new_status
        }

    def _complete_task(self, plan, data, user=None):
        """Mark task as completed"""
        task_slug = data.get('task_slug')

        if not task_slug:
            raise ValueError('task_slug is required for completing tasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)

        # Check for sequential completion validation if user is provided
        if user and user.sequential_task_completion and task.milestone:
            incomplete_previous_tasks = Task.objects.filter(
                milestone=task.milestone,
                order__lt=task.order,
                status__in=[1, 2]  # TODO_STATUS, INPROGRESS_STATUS
            ).exists()

            if incomplete_previous_tasks:
                raise ValueError(f'Cannot complete task "{task.name}". Sequential task completion is enabled - please complete previous tasks first.')

        task.status = 3  # Completed status
        task.progress = 100
        task.save()

        return {
            'task_id': task.id,
            'task_slug': task.slug,
            'task_name': task.name,
            'status': 'completed',
            'progress': 100
        }

    def _update_milestone(self, plan, data):
        """Update milestone details"""
        milestone_id = data.get('milestone_id')
        if not milestone_id:
            raise ValueError('milestone_id is required for updating milestones')

        milestone = get_object_or_404(Milestone, id=milestone_id, plan=plan)

        # Update fields if provided
        if 'name' in data:
            milestone.name = data['name']
        if 'description' in data:
            milestone.description = data['description']
        if 'estimated_duration' in data:
            milestone.estimated_duration = data['estimated_duration']
        if 'success_criteria' in data:
            milestone.success_criteria = data['success_criteria']

        milestone.save()

        return {
            'milestone_id': milestone.id,
            'milestone_name': milestone.name,
            'updated_fields': list(data.keys())
        }

    def _update_task(self, plan, data):
        """Update task details"""
        task_slug = data.get('task_slug')
        if not task_slug:
            raise ValueError('task_slug is required for updating tasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)

        # Update fields if provided
        if 'name' in data:
            task.name = data['name']
        if 'description' in data:
            task.description = data['description']
        if 'priority' in data:
            task.priority = data['priority']
        if 'start_date' in data:
            from datetime import datetime
            task.start_date = datetime.fromisoformat(data['start_date']).date()
        if 'end_date' in data:
            from datetime import datetime
            task.end_date = datetime.fromisoformat(data['end_date']).date()
        if 'progress' in data:
            task.progress = data['progress']

        task.save()

        return {
            'task_id': task.id,
            'task_slug': task.slug,
            'task_name': task.name,
            'updated_fields': list(data.keys())
        }

    def _delete_milestone(self, plan, data):
        """Delete a milestone and all its tasks"""
        milestone_id = data.get('milestone_id')
        if not milestone_id:
            raise ValueError('milestone_id is required for deleting milestones')

        milestone = get_object_or_404(Milestone, id=milestone_id, plan=plan)
        milestone_name = milestone.name
        task_count = milestone.task_set.count()

        milestone.delete()

        return {
            'milestone_id': milestone_id,
            'milestone_name': milestone_name,
            'deleted_tasks': task_count,
            'message': f'Successfully deleted milestone "{milestone_name}" and {task_count} associated tasks'
        }

    def _delete_task(self, plan, data):
        """Delete a task and all its subtasks"""
        task_slug = data.get('task_slug')
        if not task_slug:
            raise ValueError('task_slug is required for deleting tasks')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)
        task_name = task.name
        subtask_count = task.subtask_set.count()
        milestone_name = task.milestone.name

        task.delete()

        return {
            'task_slug': task_slug,
            'task_name': task_name,
            'milestone_name': milestone_name,
            'deleted_subtasks': subtask_count,
            'message': f'Successfully deleted task "{task_name}" from milestone "{milestone_name}" along with {subtask_count} subtasks'
        }

    def _delete_subtask(self, plan, data):
        """Delete a subtask"""
        subtask_slug = data.get('subtask_slug')
        if not subtask_slug:
            raise ValueError('subtask_slug is required for deleting subtasks')

        subtask = get_object_or_404(Subtask, slug=subtask_slug, task__milestone__plan=plan)
        subtask_name = subtask.name
        task_name = subtask.task.name

        subtask.delete()

        return {
            'subtask_slug': subtask_slug,
            'subtask_name': subtask_name,
            'task_name': task_name,
            'message': f'Successfully deleted subtask "{subtask_name}" from task "{task_name}"'
        }



    def _set_task_priority(self, plan, data):
        """Set task priority"""
        task_slug = data.get('task_slug')
        priority = data.get('priority', 2)

        if not task_slug:
            raise ValueError('task_slug is required for setting task priority')

        task = get_object_or_404(Task, slug=task_slug, milestone__plan=plan)
        old_priority = task.priority
        task.priority = priority
        task.save()

        priority_names = {1: 'Low', 2: 'Medium', 3: 'High'}
        return {
            'task_slug': task_slug,
            'task_name': task.name,
            'old_priority': priority_names.get(old_priority, 'Unknown'),
            'new_priority': priority_names.get(priority, 'Unknown'),
            'message': f'Updated task "{task.name}" priority to {priority_names.get(priority, "Unknown")}'
        }

    def _bulk_update_status(self, plan, data):
        """Bulk update status for multiple items"""
        items = data.get('items', [])
        if not items:
            raise ValueError('items list is required for bulk status update')

        updated_items = []
        for item in items:
            item_type = item.get('type')
            item_id = item.get('id')
            status = item.get('status')

            if item_type == 'task':
                task = Task.objects.filter(slug=item_id, milestone__plan=plan).first()
                if task:
                    task.status = status
                    if status == 3:
                        task.progress = 100
                    task.save()
                    updated_items.append(f"Task: {task.name}")

            elif item_type == 'subtask':
                subtask = Subtask.objects.filter(slug=item_id, task__milestone__plan=plan).first()
                if subtask:
                    subtask.status = status
                    if status == 3:
                        subtask.progress = 100
                    subtask.save()
                    updated_items.append(f"Subtask: {subtask.name}")

        return {
            'updated_count': len(updated_items),
            'updated_items': updated_items,
            'message': f'Successfully updated {len(updated_items)} items'
        }

    def _reorder_tasks(self, plan, data):
        """Reorder tasks within a milestone"""
        milestone_id = data.get('milestone_id')
        task_order = data.get('task_order', [])

        if not milestone_id or not task_order:
            raise ValueError('milestone_id and task_order are required for reordering tasks')

        milestone = get_object_or_404(Milestone, id=milestone_id, plan=plan)

        for index, task_slug in enumerate(task_order):
            Task.objects.filter(slug=task_slug, milestone=milestone).update(order=index)

        return {
            'milestone_id': milestone_id,
            'milestone_name': milestone.name,
            'reordered_tasks': len(task_order),
            'message': f'Reordered {len(task_order)} tasks in milestone "{milestone.name}"'
        }

    def _analyze_project(self, plan, data):
        """Analyze project and provide detailed insights"""
        milestones = Milestone.objects.filter(plan=plan)
        total_milestones = milestones.count()

        total_tasks = Task.objects.filter(milestone__plan=plan).count()
        completed_tasks = Task.objects.filter(milestone__plan=plan, status=3).count()
        in_progress_tasks = Task.objects.filter(milestone__plan=plan, status=2).count()

        total_subtasks = Subtask.objects.filter(task__milestone__plan=plan).count()
        completed_subtasks = Subtask.objects.filter(task__milestone__plan=plan, status=3).count()

        task_progress = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
        subtask_progress = (completed_subtasks / total_subtasks * 100) if total_subtasks > 0 else 0

        return {
            'project_name': plan.name,
            'total_milestones': total_milestones,
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks,
            'in_progress_tasks': in_progress_tasks,
            'total_subtasks': total_subtasks,
            'completed_subtasks': completed_subtasks,
            'task_progress_percentage': round(task_progress, 1),
            'subtask_progress_percentage': round(subtask_progress, 1),
            'message': f'Analysis complete: {completed_tasks}/{total_tasks} tasks completed ({round(task_progress, 1)}%)'
        }

    def _suggest_improvements(self, plan, data):
        """Generate improvement suggestions for the project"""
        suggestions = []

        # Check for milestones without tasks
        milestones = Milestone.objects.filter(plan=plan)
        for milestone in milestones:
            if milestone.task_set.count() == 0:
                suggestions.append({
                    'type': 'add_tasks',
                    'milestone': milestone.name,
                    'suggestion': f'Add tasks to milestone "{milestone.name}"'
                })

        # Check for tasks without subtasks
        tasks = Task.objects.filter(milestone__plan=plan)
        for task in tasks:
            if task.subtask_set.count() == 0:
                suggestions.append({
                    'type': 'add_subtasks',
                    'task': task.name,
                    'suggestion': f'Break down task "{task.name}" into subtasks'
                })

        return {
            'suggestions_count': len(suggestions),
            'suggestions': suggestions,
            'message': f'Generated {len(suggestions)} improvement suggestions'
        }
