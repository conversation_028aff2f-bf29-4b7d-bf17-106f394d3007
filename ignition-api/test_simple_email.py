import requests
import json

api_key = '2b22596d7b6b70d47e7ba4db05cc475e'
api_secret = '1c4907c2cbd644152c87e3c5078bf7af'

print('=== Testing simple email to new recipient ===')

# Very simple, clean email
data = {
    'Messages': [
        {
            'From': {
                'Email': '<EMAIL>',
                'Name': 'Ignition Support'
            },
            'To': [{'Email': '<EMAIL>'}],
            'Subject': 'Account Setup Complete',
            'TextPart': '''Hello,

Your Ignition account has been created successfully.

Account ID: ACC-001
Status: Active
Created: January 8, 2025

You can now log in and start using the platform.

Best regards,
Ignition Support Team

---
Ignition Platform
https://ignitionai.site
''',
            'HTMLPart': '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Account Setup Complete</title>
</head>
<body style="font-family: Arial, sans-serif; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <h2>Account Setup Complete</h2>
    
    <p>Hello,</p>
    
    <p>Your Ignition account has been created successfully.</p>
    
    <ul>
        <li><strong>Account ID:</strong> ACC-001</li>
        <li><strong>Status:</strong> Active</li>
        <li><strong>Created:</strong> January 8, 2025</li>
    </ul>
    
    <p>You can now log in and start using the platform.</p>
    
    <p>Best regards,<br>Ignition Support Team</p>
    
    <hr>
    <p style="font-size: 12px; color: #666;">
        Ignition Platform<br>
        <a href="https://ignitionai.site">https://ignitionai.site</a>
    </p>
</body>
</html>
            '''
        }
    ]
}

try:
    response = requests.post(
        'https://api.mailjet.com/v3.1/send',
        headers={'Content-Type': 'application/json'},
        data=json.dumps(data),
        auth=(api_key, api_secret),
        timeout=30
    )
    
    print(f'Status Code: {response.status_code}')
    if response.status_code in (200, 201):
        result = response.json()
        message_id = result['Messages'][0]['To'][0].get('MessageID', 'N/A')
        print(f'✅ Simple email <NAME_EMAIL>')
        print(f'Message ID: {message_id}')
        print('\n🔍 Please check:')
        print('1. <NAME_EMAIL>')
        print('2. Spam folder if not in inbox')
        print('3. Compare with previous <NAME_EMAIL>')
    else:
        print(f'❌ Failed: {response.text}')
        
except Exception as e:
    print(f'❌ Exception: {e}')

print('\n=== Testing with different sender name ===')

# Try with different sender name
data2 = {
    'Messages': [
        {
            'From': {
                'Email': '<EMAIL>',
                'Name': 'Platform Notifications'
            },
            'To': [{'Email': '<EMAIL>'}],
            'Subject': 'Welcome Message',
            'TextPart': '''Welcome!

Thank you for joining our platform.

Your account is now ready to use.

Regards,
Platform Team
''',
            'HTMLPart': '''
<html>
<body style="font-family: Arial, sans-serif;">
    <h3>Welcome!</h3>
    <p>Thank you for joining our platform.</p>
    <p>Your account is now ready to use.</p>
    <p>Regards,<br>Platform Team</p>
</body>
</html>
            '''
        }
    ]
}

try:
    response2 = requests.post(
        'https://api.mailjet.com/v3.1/send',
        headers={'Content-Type': 'application/json'},
        data=json.dumps(data2),
        auth=(api_key, api_secret),
        timeout=30
    )
    
    print(f'Status Code: {response2.status_code}')
    if response2.status_code in (200, 201):
        result2 = response2.json()
        message_id2 = result2['Messages'][0]['To'][0].get('MessageID', 'N/A')
        print(f'✅ Second email sent with different sender name')
        print(f'Message ID: {message_id2}')
    else:
        print(f'❌ Failed: {response2.text}')
        
except Exception as e:
    print(f'❌ Exception: {e}')

print('\n📧 Check both <NAME_EMAIL> and <NAME_EMAIL>')
