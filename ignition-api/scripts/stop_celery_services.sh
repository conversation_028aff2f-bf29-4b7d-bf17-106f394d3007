#!/bin/bash

# <PERSON><PERSON>t to stop Celery Worker and Beat services
# Run this script to deactivate the notification system

echo "🛑 Stopping Celery Services..."
echo ""

# Check current processes
echo "🔍 Current Celery processes:"
ps aux | grep "celery" | grep -v grep

echo ""

# Stop Celery Worker
echo "🔄 Stopping Celery Worker..."
pkill -f 'celery.*worker'
if [ $? -eq 0 ]; then
    echo "✅ Celery Worker stopped"
else
    echo "⚠️  No Celery Worker process found"
fi

# Stop Celery Beat
echo "⏰ Stopping Celery Beat..."
pkill -f 'celery.*beat'
if [ $? -eq 0 ]; then
    echo "✅ Celery Beat stopped"
else
    echo "⚠️  No Celery Beat process found"
fi

# Wait a moment
sleep 2

# Check if processes are stopped
echo ""
echo "🔍 Remaining Celery processes:"
REMAINING=$(ps aux | grep "celery" | grep -v grep)
if [ -z "$REMAINING" ]; then
    echo "✅ All Celery services stopped successfully"
else
    echo "⚠️  Some processes may still be running:"
    echo "$REMAINING"
    echo ""
    echo "🔧 Force kill if needed:"
    echo "   sudo pkill -9 -f celery"
fi

echo ""
echo "🎯 Daily Task Reminder System is now INACTIVE"
