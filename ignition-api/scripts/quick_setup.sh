#!/bin/bash

# Quick setup script to activate daily task reminders
# Run this script on the server after pulling the latest code

echo "🚀 Quick Setup: Daily Task Reminders"
echo "===================================="
echo ""

# Get current directory
CURRENT_DIR=$(pwd)
echo "📁 Current directory: $CURRENT_DIR"

# Check if we're in the right directory
if [[ ! -f "manage.py" ]]; then
    echo "❌ Error: manage.py not found. Please run this script from the Django project root."
    echo "   Expected location: ~/ignition-be/"
    exit 1
fi

echo "✅ Found Django project"
echo ""

# Check if scripts exist
if [[ ! -f "scripts/start_celery_services.sh" ]]; then
    echo "❌ Error: scripts/start_celery_services.sh not found"
    echo "   Please make sure you've pulled the latest code with: git pull origin main"
    exit 1
fi

echo "✅ Found Celery scripts"
echo ""

# Make scripts executable
echo "🔧 Making scripts executable..."
chmod +x scripts/*.sh

if [[ $? -eq 0 ]]; then
    echo "✅ Scripts are now executable"
else
    echo "❌ Failed to make scripts executable"
    exit 1
fi

echo ""

# Check current Celery status
echo "🔍 Checking current Celery status..."
EXISTING_PROCESSES=$(ps aux | grep "celery" | grep -v grep)
if [[ ! -z "$EXISTING_PROCESSES" ]]; then
    echo "⚠️  Celery processes are already running:"
    echo "$EXISTING_PROCESSES"
    echo ""
    read -p "Do you want to restart Celery services? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🛑 Stopping existing services..."
        ./scripts/stop_celery_services.sh
        sleep 3
    else
        echo "ℹ️  Keeping existing services running"
        echo "   Use './scripts/check_celery_status.sh' to check status"
        exit 0
    fi
fi

# Start Celery services
echo "🚀 Starting Celery services..."
./scripts/start_celery_services.sh

if [[ $? -eq 0 ]]; then
    echo ""
    echo "🎉 Setup completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "   • Check status: ./scripts/check_celery_status.sh"
    echo "   • Test feature: source venv/bin/activate && python manage.py send_task_reminders --dry-run --verbose"
    echo "   • View logs: tail -f logs/celery_worker.log"
    echo ""
    echo "⏰ Daily notifications will be sent at:"
    echo "   🌅 8:00 AM - Tasks starting today"
    echo "   🌆 8:00 PM - Tasks ending today"
else
    echo "❌ Failed to start Celery services"
    echo "   Check the error messages above and try running manually:"
    echo "   ./scripts/start_celery_services.sh"
    exit 1
fi
