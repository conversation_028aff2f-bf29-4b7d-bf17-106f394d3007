from django.shortcuts import render
from django.http import JsonResponse
from django.db import connections
from django.db.utils import OperationalError
import redis
import os
import socket

def health_check(request):
    # Check database connection
    db_conn = True
    try:
        connections['default'].cursor()
    except OperationalError:
        db_conn = False
    
    # Check Redis connection if used
    redis_conn = True
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
    except:
        redis_conn = False
    
    # Check disk space
    disk_usage = os.popen("df -h / | grep -v Filesystem | awk '{print $5}'").read().strip()
    
    # Check memory usage
    memory_usage = os.popen("free | grep Mem | awk '{print $3/$2 * 100.0}'").read().strip()
    
    # Get hostname
    hostname = socket.gethostname()
    
    # Check if Gun<PERSON> is running
    gunicorn_running = os.system("systemctl is-active --quiet gunicorn") == 0
    
    # Check if Nginx is running
    nginx_running = os.system("systemctl is-active --quiet nginx") == 0
    
    status = {
        "status": "healthy" if db_conn and gunicorn_running and nginx_running else "unhealthy",
        "database": "connected" if db_conn else "disconnected",
        "redis": "connected" if redis_conn else "disconnected",
        "disk_usage": disk_usage,
        "memory_usage": memory_usage + "%",
        "hostname": hostname,
        "gunicorn": "running" if gunicorn_running else "stopped",
        "nginx": "running" if nginx_running else "stopped"
    }
    
    status_code = 200 if status["status"] == "healthy" else 500
    
    return JsonResponse(status, status=status_code)
