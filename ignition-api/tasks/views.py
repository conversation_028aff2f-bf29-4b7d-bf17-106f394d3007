from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from django.shortcuts import get_object_or_404
from django.db.models import Q
from plans.models import Task, TODO_STATUS, DONE_STATUS, INPROGRESS_STATUS
from plans.permissions import canAccessTask
from plans.serializers import TaskSerializer, TaskUpdateSerializer, \
    UpdateTaskStatusTodoSerializer, UpdateTaskStartEndDateTodoSerializer

class AddTaskForUserView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=TaskSerializer, responses={201: 'Task created successfully'})
    def post(self, request):
        serializer = TaskSerializer(data=request.data)
        if serializer.is_valid():
            name = serializer.validated_data['name']
            start_date = serializer.validated_data.get('start_date', '')
            end_date = serializer.validated_data.get('end_date', '')
            status_value = serializer.validated_data.get('status', 1)
            task = Task.objects.create(
                name=name,
                start_date=start_date,
                end_date=end_date,
                status=status_value,
                user=request.user
            )
            return Response(TaskSerializer(task).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DeleteTaskForUserView(APIView):
    permission_classes = [IsAuthenticated, canAccessTask]

    def delete(self, request, slug):
        try:
            task = get_object_or_404(Task, slug=slug)
            task.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Task.DoesNotExist:
            return Response({'message': 'Task not found'}, status=status.HTTP_404_NOT_FOUND)


class HandleTaskCompletionForUserView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=TaskUpdateSerializer, responses={200: 'Task updated successfully'})
    def put(self, request, task_id):
        task = get_object_or_404(Task, id=task_id)
        user = request.user

        data = request.data.copy()
        completed = data.get('completed', None)
        if completed is not None:
            new_status = DONE_STATUS if completed else TODO_STATUS

            # Check if sequential task completion is enabled
            if user.sequential_task_completion and completed and task.milestone:
                # Check if there are any incomplete tasks with lower order in the same milestone
                incomplete_previous_tasks = Task.objects.filter(
                    milestone=task.milestone,
                    order__lt=task.order,
                    status__in=[TODO_STATUS, INPROGRESS_STATUS]
                ).exists()

                if incomplete_previous_tasks:
                    return Response({
                        "message": "Sequential task completion is enabled. Please complete previous tasks first."
                    }, status=status.HTTP_400_BAD_REQUEST)

            data['status'] = new_status

        serializer = TaskUpdateSerializer(task, data=data, partial=True)
        if serializer.is_valid():
            if 'start_date' not in data:
                task.start_date = None
            if 'end_date' not in data:
                task.end_date = None
            serializer.save()

            task.subtask_set.update(status=task.status)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response({"message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class getMyTaskView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user

        # Filter tasks that belong to the user or are assigned to them
        # Also ensure the task has a milestone and the milestone has a plan (not orphaned)
        tasks = Task.objects.filter(
            Q(user=user) | Q(assignees=user)
        ).filter(
            milestone__isnull=False,  # Task must have a milestone
            milestone__plan__isnull=False  # Milestone must have a plan
        ).distinct()
        serializer = TaskSerializer(tasks, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)



class getMyTaskForTodoView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        # Filter tasks that belong to the user or are assigned to them
        # Also ensure the task has a milestone and the milestone has a plan (not orphaned)
        tasks = Task.objects.filter(
            Q(user=user) | Q(assignees=user)
        ).filter(
            milestone__isnull=False,  # Task must have a milestone
            milestone__plan__isnull=False  # Milestone must have a plan
        ).distinct()

        serializer = TaskSerializer(tasks, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


class UpdateTaskStatusTodoView(APIView):
    permission_classes = [IsAuthenticated, canAccessTask]

    def post(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        user = request.user

        serializer = UpdateTaskStatusTodoSerializer(task, data=request.data, partial=True)
        if serializer.is_valid():
            if 'status' in serializer.validated_data:
                new_status = serializer.validated_data['status']

                # Check if sequential task completion is enabled and task is being marked as done
                if (user.sequential_task_completion and
                    new_status == DONE_STATUS and
                    task.milestone):

                    # Check if there are any incomplete tasks with lower order in the same milestone
                    incomplete_previous_tasks = Task.objects.filter(
                        milestone=task.milestone,
                        order__lt=task.order,
                        status__in=[TODO_STATUS, INPROGRESS_STATUS]
                    ).exists()

                    if incomplete_previous_tasks:
                        return Response({
                            "message": "Sequential task completion is enabled. Please complete previous tasks first."
                        }, status=status.HTTP_400_BAD_REQUEST)

                task.status = new_status

            if 'name' in serializer.validated_data:
                task.name = serializer.validated_data['name']

            if 'start_date' in serializer.validated_data:
                task.start_date = serializer.validated_data['start_date']

            if 'end_date' in serializer.validated_data:
                task.end_date = serializer.validated_data['end_date']

            task.save()
            return Response(TaskSerializer(task).data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UpdateTaskStartEndDateTodoView(APIView):
    permission_classes = [IsAuthenticated, canAccessTask]

    def post(self, request, slug):
        task = get_object_or_404(Task, slug=slug)

        serializer = UpdateTaskStartEndDateTodoSerializer(task, data=request.data, partial=True)
        if serializer.is_valid():
            if 'start_date' in serializer.validated_data:
                task.start_date = serializer.validated_data['start_date']

            if 'end_date' in serializer.validated_data:
                task.start_date = serializer.validated_data['end_date']

            task.save()
            return Response(TaskSerializer(task).data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
