from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from unittest.mock import patch, MagicMock

User = get_user_model()


class SendSMSViewTestCase(APITestCase):
    """
    Test cases for the SMS sending API endpoint
    """

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.url = reverse('send_sms')

    def test_send_sms_unauthenticated(self):
        """Test that unauthenticated users cannot send SMS"""
        data = {
            'phone_number': '+**********',
            'message': 'Test message'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_send_sms_invalid_phone_number(self):
        """Test SMS sending with invalid phone number"""
        self.client.force_authenticate(user=self.user)

        # Test invalid phone number formats
        invalid_numbers = [
            '**********',  # Missing +
            '+',  # Just +
            '+1',  # Too short
            '+**********12345678',  # Too long
            '+abc**********',  # Contains letters
            '',  # Empty
        ]

        for phone_number in invalid_numbers:
            data = {
                'phone_number': phone_number,
                'message': 'Test message'
            }
            response = self.client.post(self.url, data)
            self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
            self.assertIn('error', response.data)

    def test_send_sms_empty_message(self):
        """Test SMS sending with empty message"""
        self.client.force_authenticate(user=self.user)

        data = {
            'phone_number': '+**********',
            'message': ''
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    def test_send_sms_message_too_long(self):
        """Test SMS sending with message exceeding AWS SNS limit"""
        self.client.force_authenticate(user=self.user)

        data = {
            'phone_number': '+**********',
            'message': 'x' * 1601  # Exceeds 1600 character limit
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)

    @patch('notifications.views.send_sms')
    def test_send_sms_success(self, mock_send_sms):
        """Test successful SMS sending"""
        self.client.force_authenticate(user=self.user)

        # Mock successful SMS sending
        mock_send_sms.return_value = {
            'success': True,
            'message_id': 'test-message-id-123',
            'to': '+**********',
            'estimated_cost': 0.0075,
            'provider': 'aws_sns'
        }

        data = {
            'phone_number': '+**********',
            'message': 'Test SMS message'
        }
        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['message'], 'SMS sent successfully')
        self.assertIn('details', response.data)
        self.assertEqual(response.data['details']['message_id'], 'test-message-id-123')

        # Verify the send_sms function was called with correct parameters
        mock_send_sms.assert_called_once_with('+**********', 'Test SMS message', fail_silently=False)

    @patch('notifications.views.send_sms')
    def test_send_sms_failure(self, mock_send_sms):
        """Test SMS sending failure"""
        self.client.force_authenticate(user=self.user)

        # Mock failed SMS sending
        mock_send_sms.return_value = {
            'success': False,
            'error': 'AWS SNS error: Invalid credentials',
            'provider': 'aws_sns'
        }

        data = {
            'phone_number': '+**********',
            'message': 'Test SMS message'
        }
        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['error'], 'SMS sending failed')
        self.assertIn('details', response.data)

    @patch('notifications.views.send_sms')
    def test_send_sms_disabled(self, mock_send_sms):
        """Test SMS sending when service is disabled"""
        self.client.force_authenticate(user=self.user)

        # Mock disabled SMS service
        mock_send_sms.return_value = {
            'success': False,
            'error': 'SMS disabled via SMS_PROVIDER setting',
            'disabled': True,
            'provider': 'disabled'
        }

        data = {
            'phone_number': '+**********',
            'message': 'Test SMS message'
        }
        response = self.client.post(self.url, data)

        self.assertEqual(response.status_code, status.HTTP_503_SERVICE_UNAVAILABLE)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['error'], 'SMS service is currently disabled')
