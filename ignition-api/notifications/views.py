from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .serializers import Send<PERSON><PERSON>erializer, SMSResponseSerializer
from utils.email import send_sms
import logging

logger = logging.getLogger(__name__)


class SendSMSView(APIView):
    """
    API endpoint for sending SMS messages using AWS SNS
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Send SMS message using AWS SNS",
        request_body=SendSMSSerializer,
        responses={
            200: openapi.Response(
                description="SMS sent successfully",
                schema=SMSResponseSerializer
            ),
            400: openapi.Response(
                description="Bad request - validation errors",
                examples={
                    "application/json": {
                        "error": "Invalid phone number format",
                        "details": {
                            "phone_number": ["Phone number must be in international format"]
                        }
                    }
                }
            ),
            500: openapi.Response(
                description="Internal server error - SMS sending failed",
                examples={
                    "application/json": {
                        "error": "SMS sending failed",
                        "details": "AWS SNS error: Invalid credentials"
                    }
                }
            )
        },
        tags=['SMS']
    )
    def post(self, request):
        """
        Send an SMS message to a phone number using AWS SNS

        Required fields:
        - phone_number: Recipient phone number in international format (e.g., +1234567890)
        - message: SMS message content (max 1600 characters)
        """
        serializer = SendSMSSerializer(data=request.data)

        if not serializer.is_valid():
            return Response({
                "error": "Validation failed",
                "details": serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        phone_number = serializer.validated_data['phone_number']
        message = serializer.validated_data['message']

        # Log the SMS sending attempt
        logger.info(f"User {request.user.email} attempting to send SMS to {phone_number}")

        try:
            # Send SMS using the existing utility function
            result = send_sms(phone_number, message, fail_silently=False)

            if result.get('success'):
                logger.info(f"SMS sent successfully to {phone_number} by user {request.user.email}")

                response_data = {
                    "success": True,
                    "message": "SMS sent successfully",
                    "details": {
                        "message_id": result.get('message_id'),
                        "to": result.get('to', phone_number),
                        "estimated_cost": result.get('estimated_cost'),
                        "provider": result.get('provider', 'aws_sns')
                    }
                }
                return Response(response_data, status=status.HTTP_200_OK)
            else:
                logger.error(f"SMS failed for {phone_number} by user {request.user.email}: {result.get('error')}")

                # Check if SMS is disabled
                if result.get('disabled'):
                    return Response({
                        "success": False,
                        "error": "SMS service is currently disabled",
                        "details": result.get('error')
                    }, status=status.HTTP_503_SERVICE_UNAVAILABLE)

                return Response({
                    "success": False,
                    "error": "SMS sending failed",
                    "details": result.get('error')
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            logger.error(f"SMS sending exception for {phone_number} by user {request.user.email}: {str(e)}")
            return Response({
                "success": False,
                "error": "SMS sending failed",
                "details": str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
