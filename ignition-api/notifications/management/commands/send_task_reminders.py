from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import date, datetime, timedelta
import logging
from notifications.tasks import (
    send_daily_task_reminders_morning,
    send_daily_task_reminders_evening
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Send daily task reminder notifications'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['morning', 'evening', 'both'],
            default='both',
            help='Type of reminder to send (morning, evening, or both)'
        )
        parser.add_argument(
            '--test-date',
            type=str,
            help='Test with specific date (YYYY-MM-DD format)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending notifications'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force send notifications even if outside normal hours'
        )

    def handle(self, *args, **options):
        reminder_type = options['type']
        test_date = options.get('test_date')
        dry_run = options['dry_run']
        force = options['force']

        self.stdout.write(
            self.style.SUCCESS(f'🔔 Starting task reminder notifications...')
        )

        if test_date:
            try:
                test_date_obj = datetime.strptime(test_date, '%Y-%m-%d').date()
                self.stdout.write(f'📅 Using test date: {test_date_obj}')
            except ValueError:
                self.stdout.write(
                    self.style.ERROR('❌ Invalid date format. Use YYYY-MM-DD')
                )
                return

        if dry_run:
            self.stdout.write(
                self.style.WARNING('🧪 DRY RUN MODE - No notifications will be sent')
            )

        # Check if it's appropriate time to send notifications
        current_time = timezone.now()
        current_hour = current_time.hour

        if not force:
            if reminder_type == 'morning' and not (7 <= current_hour <= 10):
                self.stdout.write(
                    self.style.WARNING(
                        f'⏰ Current time ({current_hour}:00) is outside morning reminder window (7-10 AM). '
                        f'Use --force to override.'
                    )
                )
                return
            elif reminder_type == 'evening' and not (19 <= current_hour <= 22):
                self.stdout.write(
                    self.style.WARNING(
                        f'⏰ Current time ({current_hour}:00) is outside evening reminder window (7-10 PM). '
                        f'Use --force to override.'
                    )
                )
                return

        results = []

        try:
            if reminder_type in ['morning', 'both']:
                self.stdout.write('🌅 Sending morning reminders...')
                if not dry_run:
                    morning_result = send_daily_task_reminders_morning.delay()
                    results.append(('morning', morning_result))
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ Morning reminders task queued: {morning_result.id}')
                    )
                else:
                    self.stdout.write('🧪 [DRY RUN] Would send morning reminders')

            if reminder_type in ['evening', 'both']:
                self.stdout.write('🌆 Sending evening reminders...')
                if not dry_run:
                    evening_result = send_daily_task_reminders_evening.delay()
                    results.append(('evening', evening_result))
                    self.stdout.write(
                        self.style.SUCCESS(f'✅ Evening reminders task queued: {evening_result.id}')
                    )
                else:
                    self.stdout.write('🧪 [DRY RUN] Would send evening reminders')

            if not dry_run and results:
                self.stdout.write('\n📊 Task Results:')
                for reminder_type, task_result in results:
                    try:
                        # Wait for task completion (with timeout)
                        result = task_result.get(timeout=60)
                        self.stdout.write(
                            f'✅ {reminder_type.title()} reminders: '
                            f'{result.get("users_processed", 0)} users processed, '
                            f'{result.get("tasks_found", 0)} tasks found'
                        )
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'❌ {reminder_type.title()} reminders failed: {str(e)}')
                        )

            self.stdout.write(
                self.style.SUCCESS('\n🎉 Task reminder notifications completed!')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error sending notifications: {str(e)}')
            )
            logger.error(f"Task reminder command failed: {str(e)}")


class Command(BaseCommand):
    help = 'Send daily task reminder notifications - Manual trigger for testing and backup scheduling'

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['morning', 'evening', 'both'],
            default='both',
            help='Type of reminder to send (morning, evening, or both)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending notifications'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force send notifications even if outside normal hours'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about tasks and users'
        )

    def handle(self, *args, **options):
        reminder_type = options['type']
        dry_run = options['dry_run']
        force = options['force']
        verbose = options['verbose']

        self.stdout.write(
            self.style.SUCCESS(f'🔔 Daily Task Reminder System')
        )
        self.stdout.write(f'📅 Date: {date.today()}')
        self.stdout.write(f'⏰ Time: {timezone.now().strftime("%H:%M:%S")}')
        self.stdout.write(f'🎯 Type: {reminder_type}')

        if dry_run:
            self.stdout.write(
                self.style.WARNING('🧪 DRY RUN MODE - No notifications will be sent')
            )

        # Time validation
        current_hour = timezone.now().hour
        if not force:
            if reminder_type == 'morning' and not (7 <= current_hour <= 10):
                self.stdout.write(
                    self.style.WARNING(
                        f'⏰ Outside morning window (7-10 AM). Current: {current_hour}:00. Use --force to override.'
                    )
                )
                return
            elif reminder_type == 'evening' and not (19 <= current_hour <= 22):
                self.stdout.write(
                    self.style.WARNING(
                        f'⏰ Outside evening window (7-10 PM). Current: {current_hour}:00. Use --force to override.'
                    )
                )
                return

        try:
            results = []

            if reminder_type in ['morning', 'both']:
                self.stdout.write('\n🌅 Processing morning reminders (tasks starting today)...')
                if not dry_run:
                    morning_result = send_daily_task_reminders_morning.delay()
                    results.append(('morning', morning_result))
                    self.stdout.write(f'✅ Morning task queued: {morning_result.id}')
                else:
                    self.stdout.write('🧪 [DRY RUN] Would queue morning reminders')

            if reminder_type in ['evening', 'both']:
                self.stdout.write('\n🌆 Processing evening reminders (tasks ending today)...')
                if not dry_run:
                    evening_result = send_daily_task_reminders_evening.delay()
                    results.append(('evening', evening_result))
                    self.stdout.write(f'✅ Evening task queued: {evening_result.id}')
                else:
                    self.stdout.write('🧪 [DRY RUN] Would queue evening reminders')

            # Wait for results if not dry run
            if not dry_run and results:
                self.stdout.write('\n📊 Waiting for task completion...')
                for reminder_name, task_result in results:
                    try:
                        result = task_result.get(timeout=120)  # 2 minute timeout
                        
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'✅ {reminder_name.title()} completed: '
                                f'{result.get("users_processed", 0)} users, '
                                f'{result.get("tasks_found", 0)} tasks'
                            )
                        )
                        
                        if verbose and result.get('results'):
                            self.stdout.write(f'\n📋 {reminder_name.title()} Details:')
                            for user_result in result['results']:
                                email_success = user_result.get('email_result', {}).get('success', False)
                                sms_success = user_result.get('sms_result', {}).get('success', False)
                                self.stdout.write(
                                    f'  👤 {user_result.get("user", "Unknown")}: '
                                    f'📧 {"✅" if email_success else "❌"} '
                                    f'📱 {"✅" if sms_success else "❌"} '
                                    f'({user_result.get("tasks_count", 0)} tasks)'
                                )
                        
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'❌ {reminder_name.title()} failed: {str(e)}')
                        )

            self.stdout.write(
                self.style.SUCCESS('\n🎉 Daily task reminders completed!')
            )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Command failed: {str(e)}')
            )
            logger.error(f"Daily task reminder command error: {str(e)}")
