import logging
from datetime import date, datetime
from typing import List, Dict, Any
from celery import shared_task
from django.utils import timezone
from django.db.models import Q
from plans.models import Task
from users.models import User
from utils.notifications import send_notification_email, send_notification_sms

logger = logging.getLogger(__name__)


@shared_task
def send_daily_task_reminders_morning():
    """
    Send morning reminders (8:00 AM) for tasks starting today.
    """
    logger.info("🌅 Starting morning task reminders...")

    today = date.today()

    # Find tasks starting today
    starting_tasks = Task.objects.filter(
        start_date=today,
        status__in=[1, 2]  # Todo or In Progress
    ).select_related('user', 'milestone__plan').prefetch_related('assignees')

    logger.info(f"Found {starting_tasks.count()} tasks starting today")

    # Group tasks by user (assignees + task owner)
    user_tasks = {}

    for task in starting_tasks:
        # Add task owner
        if task.user:
            if task.user.id not in user_tasks:
                user_tasks[task.user.id] = {'user': task.user, 'tasks': []}
            user_tasks[task.user.id]['tasks'].append(task)

        # Add assignees
        for assignee in task.assignees.all():
            if assignee.id not in user_tasks:
                user_tasks[assignee.id] = {'user': assignee, 'tasks': []}
            user_tasks[assignee.id]['tasks'].append(task)

    # Send notifications to each user
    results = []
    for user_data in user_tasks.values():
        user = user_data['user']
        tasks = user_data['tasks']

        result = send_task_reminder_notifications(
            user=user,
            tasks=tasks,
            reminder_type='morning_start',
            notification_type='task_reminder'
        )
        results.append(result)

    logger.info(f"🌅 Morning reminders completed. Processed {len(results)} users")
    return {
        'type': 'morning_reminders',
        'date': str(today),
        'users_processed': len(results),
        'tasks_found': starting_tasks.count(),
        'results': results
    }


@shared_task
def send_daily_task_reminders_evening():
    """
    Send evening reminders (8:00 PM) for tasks ending today.
    """
    logger.info("🌆 Starting evening task reminders...")

    today = date.today()

    # Find tasks ending today
    ending_tasks = Task.objects.filter(
        end_date=today,
        status__in=[1, 2]  # Todo or In Progress
    ).select_related('user', 'milestone__plan').prefetch_related('assignees')

    logger.info(f"Found {ending_tasks.count()} tasks ending today")

    # Group tasks by user (assignees + task owner)
    user_tasks = {}

    for task in ending_tasks:
        # Add task owner
        if task.user:
            if task.user.id not in user_tasks:
                user_tasks[task.user.id] = {'user': task.user, 'tasks': []}
            user_tasks[task.user.id]['tasks'].append(task)

        # Add assignees
        for assignee in task.assignees.all():
            if assignee.id not in user_tasks:
                user_tasks[assignee.id] = {'user': assignee, 'tasks': []}
            user_tasks[assignee.id]['tasks'].append(task)

    # Send notifications to each user
    results = []
    for user_data in user_tasks.values():
        user = user_data['user']
        tasks = user_data['tasks']

        result = send_task_reminder_notifications(
            user=user,
            tasks=tasks,
            reminder_type='evening_end',
            notification_type='task_reminder'
        )
        results.append(result)

    logger.info(f"🌆 Evening reminders completed. Processed {len(results)} users")
    return {
        'type': 'evening_reminders',
        'date': str(today),
        'users_processed': len(results),
        'tasks_found': ending_tasks.count(),
        'results': results
    }


def send_task_reminder_notifications(user: User, tasks: List[Task], 
                                   reminder_type: str, notification_type: str) -> Dict[str, Any]:
    """
    Send both email and SMS notifications for task reminders.

    Args:
        user: User to send notifications to
        tasks: List of tasks to remind about
        reminder_type: 'morning_start' or 'evening_end'
        notification_type: Type of notification for preference checking

    Returns:
        dict: Results of notification attempts
    """
    try:
        if not tasks:
            return {
                'user': user.email,
                'success': False,
                'reason': 'No tasks to remind about'
            }

        # Generate notification content
        if reminder_type == 'morning_start':
            subject, email_content, sms_content = generate_morning_reminder_content(user, tasks)
        else:  # evening_end
            subject, email_content, sms_content = generate_evening_reminder_content(user, tasks)

        # Send email notification
        email_result = send_notification_email(
            user=user,
            subject=subject,
            message=email_content,
            notification_type=notification_type
        )

        # Send SMS notification
        sms_result = send_notification_sms(
            user=user,
            message=sms_content,
            notification_type=notification_type
        )

        logger.info(f"Task reminder sent to {user.email} - Email: {email_result.get('success')}, SMS: {sms_result.get('success')}")

        return {
            'user': user.email,
            'reminder_type': reminder_type,
            'tasks_count': len(tasks),
            'email_result': email_result,
            'sms_result': sms_result,
            'success': True
        }

    except Exception as e:
        logger.error(f"Failed to send task reminder to {user.email}: {str(e)}")
        return {
            'user': user.email,
            'success': False,
            'error': str(e)
        }


def generate_morning_reminder_content(user: User, tasks: List[Task]) -> tuple:
    """Generate morning reminder content for tasks starting today."""
    task_count = len(tasks)
    user_name = user.first_name or user.email.split('@')[0]

    # Email subject
    if task_count == 1:
        subject = f"🌅 Good Morning! You have 1 task starting today"
    else:
        subject = f"🌅 Good Morning! You have {task_count} tasks starting today"

    # Email content
    email_content = f"Good morning {user_name}!\n\n"
    email_content += f"You have {task_count} task{'s' if task_count > 1 else ''} starting today:\n\n"

    for i, task in enumerate(tasks, 1):
        plan_name = task.milestone.plan.name if task.milestone and task.milestone.plan else "No Plan"
        email_content += f"{i}. {task.name}\n"
        email_content += f"   Project: {plan_name}\n"
        if task.end_date:
            email_content += f"   Due: {task.end_date.strftime('%B %d, %Y')}\n"
        email_content += "\n"

    email_content += "Have a productive day!\n\n"
    email_content += "Best regards,\n"
    email_content += "The Ignition Team"

    # SMS content (shorter but more informative)
    if task_count == 1:
        task = tasks[0]
        plan_name = task.milestone.plan.name if task.milestone and task.milestone.plan else "No Plan"
        sms_content = f"🌅 Good morning {user_name}! You have 1 task starting today:\n\n"
        sms_content += f"• {task.name}\n"
        sms_content += f"  📋 {plan_name}"
        if task.end_date:
            sms_content += f"\n  📅 Due: {task.end_date.strftime('%m/%d')}"
    else:
        sms_content = f"🌅 Good morning {user_name}! You have {task_count} tasks starting today:\n\n"
        for i, task in enumerate(tasks[:3], 1):  # Limit to first 3 tasks for SMS
            plan_name = task.milestone.plan.name if task.milestone and task.milestone.plan else "No Plan"
            # Add task ID for duplicate names
            task_display = f"{task.name}"
            if len([t for t in tasks if t.name == task.name]) > 1:
                task_display += f" (#{task.id})"

            sms_content += f"{i}. {task_display}\n"
            sms_content += f"   📋 {plan_name[:20]}{'...' if len(plan_name) > 20 else ''}"
            if task.end_date:
                sms_content += f" | 📅 {task.end_date.strftime('%m/%d')}"
            sms_content += "\n"
        if task_count > 3:
            sms_content += f"...and {task_count - 3} more tasks"

    sms_content += "\n\nCheck your Ignition App for details. Have a great day!"

    return subject, email_content, sms_content


def generate_evening_reminder_content(user: User, tasks: List[Task]) -> tuple:
    """Generate evening reminder content for tasks ending today."""
    task_count = len(tasks)
    user_name = user.first_name or user.email.split('@')[0]

    # Email subject
    if task_count == 1:
        subject = f"🌆 Reminder: 1 task is due today"
    else:
        subject = f"🌆 Reminder: {task_count} tasks are due today"

    # Email content
    email_content = f"Good evening {user_name}!\n\n"
    email_content += f"This is a friendly reminder that {task_count} task{'s' if task_count > 1 else ''} {'are' if task_count > 1 else 'is'} due today:\n\n"

    for i, task in enumerate(tasks, 1):
        plan_name = task.milestone.plan.name if task.milestone and task.milestone.plan else "No Plan"
        email_content += f"{i}. {task.name}\n"
        email_content += f"   Project: {plan_name}\n"
        email_content += f"   Status: {'Todo' if task.status == 1 else 'In Progress' if task.status == 2 else 'Done'}\n"
        email_content += "\n"

    email_content += "Don't forget to update your progress or mark them as complete!\n\n"
    email_content += "Best regards,\n"
    email_content += "The Ignition Team"

    # SMS content (shorter but more informative)
    if task_count == 1:
        task = tasks[0]
        plan_name = task.milestone.plan.name if task.milestone and task.milestone.plan else "No Plan"
        status_emoji = "📝" if task.status == 1 else "⏳" if task.status == 2 else "✅"
        status_text = "Todo" if task.status == 1 else "In Progress" if task.status == 2 else "Done"
        sms_content = f"🌆 Hi {user_name}! Reminder: 1 task is due today:\n\n"
        sms_content += f"• {task.name}\n"
        sms_content += f"  📋 {plan_name}\n"
        sms_content += f"  {status_emoji} {status_text}"
    else:
        sms_content = f"🌆 Hi {user_name}! Reminder: {task_count} tasks are due today:\n\n"
        for i, task in enumerate(tasks[:3], 1):  # Limit to first 3 tasks for SMS
            plan_name = task.milestone.plan.name if task.milestone and task.milestone.plan else "No Plan"
            status_emoji = "📝" if task.status == 1 else "⏳" if task.status == 2 else "✅"

            # Add task ID for duplicate names
            task_display = f"{task.name}"
            if len([t for t in tasks if t.name == task.name]) > 1:
                task_display += f" (#{task.id})"

            sms_content += f"{i}. {task_display}\n"
            sms_content += f"   📋 {plan_name[:20]}{'...' if len(plan_name) > 20 else ''} | {status_emoji}\n"
        if task_count > 3:
            sms_content += f"...and {task_count - 3} more tasks"

    sms_content += "\n\nCheck your Ignition App to update progress!"

    return subject, email_content, sms_content
