# Plan planer app for students

[![Python](https://img.shields.io/badge/Python-3776AB?style=for-the-badge&logo=python&logoColor=white)](https://www.python.org/)
[![Django](https://img.shields.io/badge/Django-092E20?style=for-the-badge&logo=django&logoColor=white)](https://www.djangoproject.com/)
[![OpenAI](https://img.shields.io/static/v1?style=for-the-badge&message=OpenAI&color=412991&logo=OpenAI&logoColor=FFFFFF&label=)](https://openai.com/research/overview)

## Development Environment

- **python:** 3.11
- **pip:** 23.2

## How to run this project

Run the following steps

1. Clone project and cd into directory:*

```bash
<NAME_EMAIL>:nghialuutrung/hackademic-be-api.git
cd hackademic-be-api
```

2. Create new virtualenv with the following commands:
(If you have already created a virtual environment, please proceed to step 3.)

```bash
python3.11 -m venv venv
```

3. Active virtualenv

```bash
source venv/bin/activate
```

4. Install python libs:

```bash
pip install --upgrade pip
pip install -r requirements.txt
```

5. Database migration

```bash
python manage.py makemigrations
python manage.py migrate
```

6. Configure environment variables

Copy the example environment file and configure it:
```bash
cp .env.example .env
# Edit .env file with your actual configuration values
```

7. (Optional) Setup Google Generative AI for Plan Options

For enhanced plan options generation using Google's Gemini 2.0 Flash:
```bash
# Quick setup
chmod +x scripts/install_google_ai.sh
./scripts/install_google_ai.sh

# Or manual setup - google-generativeai is already in requirements.txt
# Just add GOOGLE_AI_API_KEY to your .env file
```

Get your Google AI API key from: https://makersuite.google.com/app/apikey

For detailed setup instructions, see: [docs/GOOGLE_AI_SETUP.md](docs/GOOGLE_AI_SETUP.md)

8. Run server on local

```bash
python manage.py runserver
```

## AI Configuration

This project supports multiple AI providers:

### Primary AI (for general plan generation)
- **OpenAI**: GPT-4, GPT-3.5-turbo
- **OpenRouter**: Claude, Llama, and other models

### Plan Options AI (dedicated service)
- **Google Generative AI**: Gemini 2.0 Flash (recommended)
- **Fallback**: Predefined plan templates

Configure in your `.env` file:
```bash
# Primary AI Provider
AI_PROVIDER=openai
OPENAI_API_KEY=your_openai_key

# Plan Options Service (isolated)
PLAN_OPTIONS_AI_PROVIDER=google
GOOGLE_AI_API_KEY=your_google_ai_key
PLAN_OPTIONS_AI_MODEL=gemini-2.0-flash-exp
```
