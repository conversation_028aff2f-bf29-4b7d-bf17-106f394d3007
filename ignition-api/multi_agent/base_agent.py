"""
Base Agent Class for Multi-Agent System

This module provides the abstract base class that all agents inherit from,
ensuring consistent behavior, error handling, and logging across the system.
"""

import logging
import time
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

from .services.shared_memory import PlanGenerationState


class BaseIgnitionAgent(ABC):
    """
    Abstract base class for all Ignition agents.
    
    Provides common functionality including:
    - Standardized execution flow
    - Error handling with retry logic
    - Logging and metrics collection
    - Input/output validation
    - Performance monitoring
    """
    
    def __init__(self, agent_name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the base agent.
        
        Args:
            agent_name: Unique name for this agent
            config: Optional configuration dictionary
        """
        self.agent_name = agent_name
        self.config = config or {}
        
        # Setup logging
        self.logger = logging.getLogger(f"ignition.{agent_name}")
        
        # Initialize metrics
        self.metrics = {
            "calls": 0,
            "errors": 0,
            "total_time": 0.0,
            "avg_time": 0.0,
            "last_execution": None,
            "success_rate": 1.0
        }
        
        # Configuration from config
        self.max_retries = self.config.get('max_retries', 3)
        self.timeout = self.config.get('timeout', 120)
        self.retry_delay = self.config.get('retry_delay', 1.0)
        
        self.logger.info(f"{self.agent_name} initialized with config: {self.config}")
    
    @abstractmethod
    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Main processing logic - each agent implements this differently.
        
        Args:
            state: Current plan generation state
            
        Returns:
            Dict containing the agent's output data
        """
        pass
    
    async def validate_input(self, state: PlanGenerationState) -> bool:
        """
        Validate input state before processing.
        
        Args:
            state: Input state to validate
            
        Returns:
            bool: True if input is valid
        """
        # Basic validation - can be overridden by specific agents
        if not isinstance(state, dict):
            self.logger.error("State must be a dictionary")
            return False
        
        required_fields = ['user_input', 'session_id']
        for field in required_fields:
            if field not in state:
                self.logger.error(f"Missing required field: {field}")
                return False
        
        return True
    
    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """
        Validate output before returning.
        
        Args:
            output: Output data to validate
            
        Returns:
            bool: True if output is valid
        """
        # Basic validation - can be overridden by specific agents
        if not isinstance(output, dict):
            self.logger.error("Output must be a dictionary")
            return False
        
        return True
    
    async def execute(self, state: PlanGenerationState) -> PlanGenerationState:
        """
        Standard execution flow with error handling, logging, and metrics.
        
        Args:
            state: Current plan generation state
            
        Returns:
            Updated state with agent's output
        """
        start_time = time.time()
        self.metrics["calls"] += 1
        
        try:
            self.logger.info(f"Starting execution of {self.agent_name}")
            
            # Input validation
            if not await self.validate_input(state):
                raise ValueError(f"Input validation failed for {self.agent_name}")
            
            # Execute with retry logic
            output = await self._execute_with_retry(state)
            
            # Output validation
            if not await self.validate_output(output):
                raise ValueError(f"Output validation failed for {self.agent_name}")
            
            # Update state with output
            updated_state = state.copy()
            updated_state.update(output)
            
            # Update metrics
            execution_time = time.time() - start_time
            self._update_metrics(execution_time, success=True)
            
            self.logger.info(f"{self.agent_name} completed successfully in {execution_time:.2f}s")
            return updated_state
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_metrics(execution_time, success=False)
            
            self.logger.error(f"{self.agent_name} failed after {execution_time:.2f}s: {str(e)}")
            
            # Add error to state
            if "errors" not in state:
                state["errors"] = []
            
            state["errors"].append({
                "agent": self.agent_name,
                "error": str(e),
                "timestamp": datetime.now().isoformat(),
                "execution_time": execution_time
            })
            
            raise
    
    async def _execute_with_retry(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Execute the agent with retry logic.
        
        Args:
            state: Current plan generation state
            
        Returns:
            Agent output data
        """
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if attempt > 0:
                    self.logger.warning(f"{self.agent_name} retry attempt {attempt}/{self.max_retries}")
                    await asyncio.sleep(self.retry_delay * attempt)
                
                # Execute with timeout
                return await asyncio.wait_for(
                    self.process(state),
                    timeout=self.timeout
                )
                
            except asyncio.TimeoutError as e:
                last_exception = e
                self.logger.warning(f"{self.agent_name} timeout on attempt {attempt + 1}")
                
            except Exception as e:
                last_exception = e
                self.logger.warning(f"{self.agent_name} error on attempt {attempt + 1}: {str(e)}")
        
        # All retries failed
        raise last_exception or Exception(f"{self.agent_name} failed after {self.max_retries} retries")
    
    def _update_metrics(self, execution_time: float, success: bool):
        """Update agent metrics."""
        self.metrics["total_time"] += execution_time
        self.metrics["avg_time"] = self.metrics["total_time"] / self.metrics["calls"]
        self.metrics["last_execution"] = datetime.now().isoformat()
        
        if not success:
            self.metrics["errors"] += 1
        
        self.metrics["success_rate"] = (
            (self.metrics["calls"] - self.metrics["errors"]) / self.metrics["calls"]
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current agent metrics."""
        return self.metrics.copy()
    
    def reset_metrics(self):
        """Reset agent metrics."""
        self.metrics = {
            "calls": 0,
            "errors": 0,
            "total_time": 0.0,
            "avg_time": 0.0,
            "last_execution": None,
            "success_rate": 1.0
        }
