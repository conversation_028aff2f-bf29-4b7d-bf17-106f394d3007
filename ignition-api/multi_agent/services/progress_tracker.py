"""
Progress Tracker Service

This service provides real-time progress tracking and monitoring
for the multi-agent plan generation workflow.
"""

import time
import asyncio
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass


@dataclass
class ProgressEvent:
    """Represents a progress event in the workflow."""
    timestamp: datetime
    agent_name: str
    event_type: str  # 'started', 'progress', 'completed', 'error'
    progress: float  # 0.0 to 1.0
    message: str
    details: Dict[str, Any]


class ProgressTracker:
    """
    Real-time progress tracking for multi-agent workflows.
    
    Features:
    - Track progress across all agents
    - Estimate completion times
    - Monitor performance metrics
    - Support for progress callbacks
    """
    
    def __init__(self):
        """Initialize progress tracker."""
        self.events: List[ProgressEvent] = []
        self.callbacks: List[Callable] = []
        self.start_time: Optional[datetime] = None
        self.agent_progress: Dict[str, float] = {}
        self.agent_timings: Dict[str, Dict[str, datetime]] = {}
        
        # Progress weights for each agent (should sum to 1.0)
        self.agent_weights = {
            'domain_classifier': 0.15,
            'structure_optimizer': 0.20,
            'content_generator': 0.25,
            'timeline_optimizer': 0.15,
            'validation_agent': 0.10,
            'quality_enhancer': 0.15
        }
    
    def start_tracking(self, session_id: str):
        """Start tracking progress for a session."""
        self.start_time = datetime.now()
        self.events.clear()
        self.agent_progress.clear()
        self.agent_timings.clear()
        
        self._add_event(
            agent_name="system",
            event_type="started",
            progress=0.0,
            message=f"Plan generation started for session {session_id}"
        )
    
    def update_agent_progress(self, agent_name: str, progress: float, 
                            message: str = "", details: Dict[str, Any] = None):
        """
        Update progress for a specific agent.
        
        Args:
            agent_name: Name of the agent
            progress: Agent's internal progress (0.0 to 1.0)
            message: Progress message
            details: Additional details
        """
        self.agent_progress[agent_name] = progress
        
        # Track timing
        if agent_name not in self.agent_timings:
            self.agent_timings[agent_name] = {'started': datetime.now()}
        
        if progress >= 1.0:
            self.agent_timings[agent_name]['completed'] = datetime.now()
        
        # Calculate overall progress
        overall_progress = self._calculate_overall_progress()
        
        self._add_event(
            agent_name=agent_name,
            event_type="progress",
            progress=overall_progress,
            message=message,
            details=details or {}
        )
        
        # Notify callbacks
        self._notify_callbacks(agent_name, overall_progress, message)
    
    def mark_agent_started(self, agent_name: str):
        """Mark an agent as started."""
        self.agent_timings[agent_name] = {'started': datetime.now()}
        
        self._add_event(
            agent_name=agent_name,
            event_type="started",
            progress=self._calculate_overall_progress(),
            message=f"{agent_name} started processing"
        )
    
    def mark_agent_completed(self, agent_name: str, message: str = ""):
        """Mark an agent as completed."""
        self.agent_progress[agent_name] = 1.0
        
        if agent_name in self.agent_timings:
            self.agent_timings[agent_name]['completed'] = datetime.now()
        
        overall_progress = self._calculate_overall_progress()
        
        self._add_event(
            agent_name=agent_name,
            event_type="completed",
            progress=overall_progress,
            message=message or f"{agent_name} completed successfully"
        )
        
        self._notify_callbacks(agent_name, overall_progress, message)
    
    def mark_agent_error(self, agent_name: str, error: str, details: Dict[str, Any] = None):
        """Mark an agent as having an error."""
        self._add_event(
            agent_name=agent_name,
            event_type="error",
            progress=self._calculate_overall_progress(),
            message=f"{agent_name} error: {error}",
            details=details or {}
        )
        
        self._notify_callbacks(agent_name, self._calculate_overall_progress(), f"Error: {error}")
    
    def add_callback(self, callback: Callable[[str, float, str], None]):
        """
        Add a progress callback function.
        
        Args:
            callback: Function that takes (agent_name, progress, message)
        """
        self.callbacks.append(callback)
    
    def remove_callback(self, callback: Callable):
        """Remove a progress callback function."""
        if callback in self.callbacks:
            self.callbacks.remove(callback)
    
    def get_current_progress(self) -> float:
        """Get current overall progress (0.0 to 1.0)."""
        return self._calculate_overall_progress()
    
    def get_estimated_completion_time(self) -> Optional[datetime]:
        """Estimate when the workflow will complete."""
        if not self.start_time:
            return None
        
        current_progress = self._calculate_overall_progress()
        if current_progress <= 0:
            return None
        
        elapsed = datetime.now() - self.start_time
        estimated_total = elapsed / current_progress
        return self.start_time + estimated_total
    
    def get_agent_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get performance metrics for each agent."""
        metrics = {}
        
        for agent_name, timings in self.agent_timings.items():
            agent_metrics = {
                'progress': self.agent_progress.get(agent_name, 0.0),
                'started_at': timings.get('started'),
                'completed_at': timings.get('completed'),
                'duration': None,
                'status': 'running'
            }
            
            if 'completed' in timings:
                duration = timings['completed'] - timings['started']
                agent_metrics['duration'] = duration.total_seconds()
                agent_metrics['status'] = 'completed'
            elif 'started' in timings:
                duration = datetime.now() - timings['started']
                agent_metrics['duration'] = duration.total_seconds()
                agent_metrics['status'] = 'running'
            
            metrics[agent_name] = agent_metrics
        
        return metrics
    
    def get_recent_events(self, limit: int = 10) -> List[ProgressEvent]:
        """Get recent progress events."""
        return self.events[-limit:] if self.events else []
    
    def _calculate_overall_progress(self) -> float:
        """Calculate overall progress based on agent weights."""
        total_progress = 0.0
        
        for agent_name, weight in self.agent_weights.items():
            agent_progress = self.agent_progress.get(agent_name, 0.0)
            total_progress += agent_progress * weight
        
        return min(total_progress, 1.0)
    
    def _add_event(self, agent_name: str, event_type: str, progress: float, 
                   message: str, details: Dict[str, Any] = None):
        """Add a progress event."""
        event = ProgressEvent(
            timestamp=datetime.now(),
            agent_name=agent_name,
            event_type=event_type,
            progress=progress,
            message=message,
            details=details or {}
        )
        
        self.events.append(event)
        
        # Keep only last 100 events to prevent memory issues
        if len(self.events) > 100:
            self.events = self.events[-100:]
    
    def _notify_callbacks(self, agent_name: str, progress: float, message: str):
        """Notify all registered callbacks."""
        for callback in self.callbacks:
            try:
                callback(agent_name, progress, message)
            except Exception as e:
                # Don't let callback errors break the tracker
                print(f"Progress callback error: {e}")
