"""
Quality Gates Service

This service provides validation checkpoints between agent stages
to ensure quality and consistency throughout the workflow.
"""

from typing import Dict, Any, List, Optional, Tuple
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class QualityLevel(Enum):
    """Quality levels for validation."""
    FAIL = "fail"
    PASS_WITH_WARNINGS = "pass_with_warnings"
    PASS = "pass"
    EXCELLENT = "excellent"


class QualityGate:
    """Represents a single quality gate with validation rules."""
    
    def __init__(self, name: str, description: str, min_score: float = 0.7):
        self.name = name
        self.description = description
        self.min_score = min_score
        self.rules: List[Callable] = []
    
    def add_rule(self, rule_func: callable, weight: float = 1.0):
        """Add a validation rule to this gate."""
        self.rules.append((rule_func, weight))
    
    def validate(self, data: Dict[str, Any]) -> Tuple[QualityLevel, float, List[str]]:
        """
        Validate data against all rules in this gate.
        
        Returns:
            Tuple of (quality_level, score, issues)
        """
        if not self.rules:
            return QualityLevel.PASS, 1.0, []
        
        total_score = 0.0
        total_weight = 0.0
        issues = []
        
        for rule_func, weight in self.rules:
            try:
                score, rule_issues = rule_func(data)
                total_score += score * weight
                total_weight += weight
                issues.extend(rule_issues)
            except Exception as e:
                logger.error(f"Quality rule error in {self.name}: {e}")
                issues.append(f"Rule validation error: {str(e)}")
        
        if total_weight == 0:
            return QualityLevel.PASS, 1.0, issues
        
        final_score = total_score / total_weight
        
        # Determine quality level
        if final_score < self.min_score:
            level = QualityLevel.FAIL
        elif final_score < 0.85:
            level = QualityLevel.PASS_WITH_WARNINGS
        elif final_score < 0.95:
            level = QualityLevel.PASS
        else:
            level = QualityLevel.EXCELLENT
        
        return level, final_score, issues


class QualityGates:
    """
    Quality gates system for multi-agent workflow validation.
    
    Provides validation checkpoints between agent stages to ensure
    quality and consistency throughout the plan generation process.
    """
    
    def __init__(self):
        """Initialize quality gates system."""
        self.gates: Dict[str, QualityGate] = {}
        self._setup_default_gates()
    
    def _setup_default_gates(self):
        """Setup default quality gates for the workflow."""
        
        # Domain Analysis Quality Gate
        domain_gate = QualityGate(
            name="domain_analysis",
            description="Validates domain classification and requirement extraction",
            min_score=0.8
        )
        domain_gate.add_rule(self._validate_domain_completeness, 1.0)
        domain_gate.add_rule(self._validate_domain_confidence, 0.8)
        self.gates["domain_analysis"] = domain_gate
        
        # Structure Design Quality Gate
        structure_gate = QualityGate(
            name="structure_design",
            description="Validates project structure and milestone organization",
            min_score=0.75
        )
        structure_gate.add_rule(self._validate_structure_completeness, 1.0)
        structure_gate.add_rule(self._validate_milestone_balance, 0.9)
        structure_gate.add_rule(self._validate_dependencies, 0.8)
        self.gates["structure_design"] = structure_gate
        
        # Content Quality Gate
        content_gate = QualityGate(
            name="content_quality",
            description="Validates content quality and completeness",
            min_score=0.8
        )
        content_gate.add_rule(self._validate_content_completeness, 1.0)
        content_gate.add_rule(self._validate_content_quality, 0.9)
        self.gates["content_quality"] = content_gate
        
        # Timeline Quality Gate
        timeline_gate = QualityGate(
            name="timeline_quality",
            description="Validates timeline feasibility and optimization",
            min_score=0.75
        )
        timeline_gate.add_rule(self._validate_timeline_completeness, 1.0)
        timeline_gate.add_rule(self._validate_timeline_feasibility, 0.9)
        self.gates["timeline_quality"] = timeline_gate
        
        # Final Plan Quality Gate
        final_gate = QualityGate(
            name="final_plan",
            description="Validates overall plan quality and enhancement",
            min_score=0.85
        )
        final_gate.add_rule(self._validate_final_completeness, 1.0)
        final_gate.add_rule(self._validate_final_quality, 0.9)
        final_gate.add_rule(self._validate_final_enhancement, 0.8)
        self.gates["final_plan"] = final_gate
    
    def validate_stage(self, stage_name: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a specific stage using its quality gate.
        
        Args:
            stage_name: Name of the stage to validate
            data: Data to validate
            
        Returns:
            Dict containing validation results
        """
        if stage_name not in self.gates:
            logger.warning(f"No quality gate found for stage: {stage_name}")
            return {
                "stage": stage_name,
                "quality_level": QualityLevel.PASS.value,
                "score": 1.0,
                "issues": [],
                "passed": True
            }
        
        gate = self.gates[stage_name]
        quality_level, score, issues = gate.validate(data)
        
        result = {
            "stage": stage_name,
            "quality_level": quality_level.value,
            "score": score,
            "issues": issues,
            "passed": quality_level != QualityLevel.FAIL,
            "description": gate.description
        }
        
        logger.info(f"Quality gate {stage_name}: {quality_level.value} (score: {score:.2f})")
        
        return result
    
    # Validation rule implementations
    def _validate_domain_completeness(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate domain analysis completeness."""
        issues = []
        score = 1.0
        
        domain_analysis = data.get('domain_analysis', {})
        
        required_fields = ['primary_domain', 'sub_domains', 'complexity_level', 'extracted_requirements']
        for field in required_fields:
            if field not in domain_analysis:
                issues.append(f"Missing {field} in domain analysis")
                score -= 0.2
        
        return max(score, 0.0), issues
    
    def _validate_domain_confidence(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate domain analysis confidence."""
        issues = []
        
        domain_analysis = data.get('domain_analysis', {})
        confidence = domain_analysis.get('confidence_score', 0.0)
        
        if confidence < 0.8:
            issues.append(f"Low confidence score: {confidence}")
            return confidence, issues
        
        return 1.0, issues
    
    def _validate_structure_completeness(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate structure design completeness."""
        issues = []
        score = 1.0
        
        structure_design = data.get('structure_design', {})
        milestone_structure = structure_design.get('milestone_structure', [])
        
        if not milestone_structure:
            issues.append("No milestones found in structure design")
            return 0.0, issues
        
        if len(milestone_structure) < 3:
            issues.append("Too few milestones (minimum 3 recommended)")
            score -= 0.3
        
        return max(score, 0.0), issues
    
    def _validate_milestone_balance(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate milestone balance and task distribution."""
        issues = []
        score = 1.0
        
        structure_design = data.get('structure_design', {})
        milestones = structure_design.get('milestone_structure', [])
        
        for milestone in milestones:
            task_count = milestone.get('task_count', 0)
            if task_count < 3:
                issues.append(f"Milestone {milestone.get('milestone_id')} has too few tasks")
                score -= 0.1
            elif task_count > 7:
                issues.append(f"Milestone {milestone.get('milestone_id')} has too many tasks")
                score -= 0.1
        
        return max(score, 0.0), issues
    
    def _validate_dependencies(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate dependency structure."""
        issues = []
        
        structure_design = data.get('structure_design', {})
        dependency_graph = structure_design.get('dependency_graph', {})
        
        if not dependency_graph:
            issues.append("No dependency graph found")
            return 0.5, issues
        
        return 1.0, issues
    
    def _validate_content_completeness(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate content generation completeness."""
        issues = []
        score = 1.0
        
        content_data = data.get('content_data', {})
        detailed_content = content_data.get('detailed_content', {})
        
        if not detailed_content:
            issues.append("No detailed content found")
            return 0.0, issues
        
        return score, issues
    
    def _validate_content_quality(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate content quality metrics."""
        issues = []
        
        content_data = data.get('content_data', {})
        metrics = content_data.get('content_metrics', {})
        
        clarity_score = metrics.get('clarity_score', 0.0)
        if clarity_score < 0.8:
            issues.append(f"Low clarity score: {clarity_score}")
        
        return metrics.get('clarity_score', 0.8), issues
    
    def _validate_timeline_completeness(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate timeline optimization completeness."""
        issues = []
        score = 1.0
        
        timeline_data = data.get('timeline_data', {})
        timeline_optimization = timeline_data.get('timeline_optimization', {})
        
        if not timeline_optimization:
            issues.append("No timeline optimization found")
            return 0.0, issues
        
        return score, issues
    
    def _validate_timeline_feasibility(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate timeline feasibility."""
        issues = []
        
        # TODO: Add specific timeline feasibility checks
        return 1.0, issues
    
    def _validate_final_completeness(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate final plan completeness."""
        issues = []
        score = 1.0
        
        final_plan = data.get('final_plan', {})
        enhanced_plan = final_plan.get('enhanced_plan', {})
        
        if not enhanced_plan:
            issues.append("No enhanced plan found")
            return 0.0, issues
        
        return score, issues
    
    def _validate_final_quality(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate final plan quality."""
        issues = []
        
        final_plan = data.get('final_plan', {})
        metrics = final_plan.get('final_metrics', {})
        
        overall_quality = metrics.get('overall_quality', 0.0)
        if overall_quality < 0.9:
            issues.append(f"Low overall quality score: {overall_quality}")
        
        return overall_quality, issues
    
    def _validate_final_enhancement(self, data: Dict[str, Any]) -> Tuple[float, List[str]]:
        """Validate final plan enhancement level."""
        issues = []
        
        final_plan = data.get('final_plan', {})
        enhanced_plan = final_plan.get('enhanced_plan', {})
        metadata = enhanced_plan.get('plan_metadata', {})
        
        enhancement_level = metadata.get('enhancement_level', '')
        if enhancement_level != 'premium':
            issues.append(f"Enhancement level not premium: {enhancement_level}")
            return 0.8, issues
        
        return 1.0, issues
