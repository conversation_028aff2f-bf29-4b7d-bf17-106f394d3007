"""
Shared Memory and State Management

This module defines the state structure and management functions
for communication between agents in the multi-agent system.
"""

import uuid
from typing import Dict, Any, List, Optional, TypedDict
from datetime import datetime


class PlanGenerationState(TypedDict, total=False):
    """
    TypedDict defining the complete state structure for plan generation.
    
    This state is passed between all agents and contains:
    - Input data from user
    - Output from each agent
    - Metadata for tracking and error handling
    """
    
    # Input data
    user_input: str              # "Tôi muốn tạo app e-commerce bán quần áo"
    duration: str                # "3 months"
    language: str                # "vietnamese"
    
    # Agent outputs
    domain_analysis: Dict[str, Any]     # Agent 1 output
    structure_design: Dict[str, Any]    # Agent 2 output
    content_data: Dict[str, Any]        # Agent 3 output
    timeline_data: Dict[str, Any]       # Agent 4 output
    validation_results: Dict[str, Any]  # Agent 5 output
    final_plan: Dict[str, Any]          # Agent 6 output
    
    # Metadata
    messages: List[str]          # Progress messages
    current_step: str            # Current agent
    progress: float              # Percentage complete (0.0 to 1.0)
    errors: List[Dict[str, Any]] # Error tracking
    session_id: str              # Unique session ID
    
    # Timestamps
    created_at: str              # ISO timestamp when state was created
    updated_at: str              # ISO timestamp when state was last updated
    
    # Configuration
    config: Dict[str, Any]       # Runtime configuration


def create_initial_state(user_input: str, duration: str = "3 months", 
                        language: str = "vietnamese") -> PlanGenerationState:
    """
    Create initial state for plan generation.
    
    Args:
        user_input: User's project description
        duration: Project duration (default: "3 months")
        language: Response language (default: "vietnamese")
        
    Returns:
        PlanGenerationState: Initial state with user input and metadata
    """
    now = datetime.now().isoformat()
    
    return PlanGenerationState(
        # Input data
        user_input=user_input,
        duration=duration,
        language=language,
        
        # Agent outputs (will be populated by agents)
        domain_analysis={},
        structure_design={},
        content_data={},
        timeline_data={},
        validation_results={},
        final_plan={},
        
        # Metadata
        messages=[f"Plan generation started for: {user_input[:50]}..."],
        current_step="initialization",
        progress=0.0,
        errors=[],
        session_id=str(uuid.uuid4()),
        
        # Timestamps
        created_at=now,
        updated_at=now,
        
        # Configuration
        config={}
    )


def update_state_progress(state: PlanGenerationState, current_step: str, 
                         progress: float, message: str = None) -> PlanGenerationState:
    """
    Update state with progress information.
    
    Args:
        state: Current state
        current_step: Name of current processing step
        progress: Progress percentage (0.0 to 1.0)
        message: Optional progress message
        
    Returns:
        Updated state
    """
    state["current_step"] = current_step
    state["progress"] = progress
    state["updated_at"] = datetime.now().isoformat()
    
    if message:
        state["messages"].append(f"[{current_step}] {message}")
    
    return state


def add_error_to_state(state: PlanGenerationState, agent_name: str, 
                      error: str, details: Dict[str, Any] = None) -> PlanGenerationState:
    """
    Add error information to state.
    
    Args:
        state: Current state
        agent_name: Name of agent that encountered error
        error: Error message
        details: Optional additional error details
        
    Returns:
        Updated state with error information
    """
    error_entry = {
        "agent": agent_name,
        "error": error,
        "timestamp": datetime.now().isoformat(),
        "details": details or {}
    }
    
    state["errors"].append(error_entry)
    state["updated_at"] = datetime.now().isoformat()
    
    return state


def validate_state_structure(state: Dict[str, Any]) -> bool:
    """
    Validate that state has required structure.
    
    Args:
        state: State dictionary to validate
        
    Returns:
        bool: True if state structure is valid
    """
    required_fields = [
        'user_input', 'duration', 'language', 'session_id',
        'messages', 'current_step', 'progress', 'errors'
    ]
    
    for field in required_fields:
        if field not in state:
            return False
    
    # Validate data types
    if not isinstance(state['messages'], list):
        return False
    
    if not isinstance(state['errors'], list):
        return False
    
    if not isinstance(state['progress'], (int, float)):
        return False
    
    if not (0.0 <= state['progress'] <= 1.0):
        return False
    
    return True


def serialize_state(state: PlanGenerationState) -> str:
    """
    Serialize state to JSON string.
    
    Args:
        state: State to serialize
        
    Returns:
        JSON string representation of state
    """
    import json
    return json.dumps(state, indent=2, ensure_ascii=False)


def deserialize_state(state_json: str) -> PlanGenerationState:
    """
    Deserialize state from JSON string.
    
    Args:
        state_json: JSON string representation of state
        
    Returns:
        PlanGenerationState object
    """
    import json
    return json.loads(state_json)
