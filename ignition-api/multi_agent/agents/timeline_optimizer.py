"""
Timeline Optimization Agent

This agent calculates realistic timelines, resource allocation,
and identifies bottlenecks and parallel execution opportunities.
"""

import math
from typing import Dict, Any, List, Tuple
from datetime import datetime, timedelta, date
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class TimelineOptimizationAgent(BaseIgnitionAgent):
    """
    Agent responsible for timeline optimization and resource allocation.

    Capabilities:
    - Calculate realistic duration estimates with complexity adjustments
    - Identify bottlenecks and critical path analysis
    - Find parallel execution opportunities across milestones and tasks
    - Add buffer time and comprehensive risk mitigation
    - Resource allocation optimization and team size recommendations
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Timeline Optimization Agent."""
        super().__init__("timeline_optimizer", config)

        # Timeline calculation parameters
        self.working_days_per_week = 5
        self.hours_per_working_day = 8
        self.buffer_percentage = 0.15  # 15% buffer time

        # Optimization weights for scoring
        self.optimization_weights = {
            "time_efficiency": 0.35,
            "resource_utilization": 0.25,
            "risk_mitigation": 0.25,
            "parallel_optimization": 0.15
        }

    def _calculate_task_duration(self, task: Dict[str, Any], milestone_context: Dict[str, Any],
                               domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate realistic duration for individual tasks"""

        # Base duration from structure design
        base_duration = task.get("estimated_duration", "1_week")
        complexity = task.get("complexity", "medium")

        # Complexity multipliers
        complexity_multipliers = {
            "low": 0.8,
            "medium": 1.0,
            "high": 1.4,
            "very_high": 1.8
        }

        # Domain-specific adjustments
        domain_multipliers = {
            "mobile_app_development": 1.2,  # Mobile development typically takes longer
            "web_development": 1.0,
            "e_commerce": 1.3,  # E-commerce has more complexity
            "data_science": 1.1
        }

        # Team size adjustments
        team_size = domain_analysis.get("constraints", {}).get("team_size", "small_team_3_5_people")
        team_multipliers = {
            "solo": 1.5,
            "small_team_3_5_people": 1.0,
            "medium_team_6_10_people": 0.8,
            "large_team": 0.7
        }

        # Parse base duration
        duration_value, duration_unit = self._parse_duration(base_duration)

        # Apply multipliers
        complexity_mult = complexity_multipliers.get(complexity, 1.0)
        domain_mult = domain_multipliers.get(domain_analysis["primary_domain"], 1.0)
        team_mult = team_multipliers.get(team_size, 1.0)

        # Calculate adjusted duration
        adjusted_duration = duration_value * complexity_mult * domain_mult * team_mult

        # Add buffer time
        buffered_duration = adjusted_duration * (1 + self.buffer_percentage)

        # Convert back to appropriate unit
        final_duration = math.ceil(buffered_duration)

        return {
            "original_duration": base_duration,
            "calculated_duration": f"{final_duration}_{duration_unit}",
            "duration_days": self._convert_to_days(final_duration, duration_unit),
            "effort_hours": self._calculate_effort_hours(final_duration, duration_unit),
            "adjustments_applied": {
                "complexity_multiplier": complexity_mult,
                "domain_multiplier": domain_mult,
                "team_multiplier": team_mult,
                "buffer_percentage": self.buffer_percentage
            }
        }

    def _parse_duration(self, duration_str: str) -> Tuple[int, str]:
        """Parse duration string into value and unit"""
        parts = duration_str.split("_")
        if len(parts) == 2:
            try:
                value = int(parts[0])
                unit = parts[1]
                return value, unit
            except ValueError:
                pass

        # Default fallback
        return 1, "weeks"

    def _convert_to_days(self, duration_value: int, duration_unit: str) -> int:
        """Convert duration to working days"""
        if duration_unit == "days":
            return duration_value
        elif duration_unit == "weeks":
            return duration_value * self.working_days_per_week
        elif duration_unit == "months":
            return duration_value * self.working_days_per_week * 4
        else:
            return duration_value * self.working_days_per_week  # Default to weeks

    def _calculate_effort_hours(self, duration_value: int, duration_unit: str) -> int:
        """Calculate total effort hours"""
        days = self._convert_to_days(duration_value, duration_unit)
        return days * self.hours_per_working_day

    def _calculate_milestone_duration(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate milestone duration based on constituent tasks"""

        tasks = milestone.get("tasks", [])

        # Calculate individual task durations
        task_durations = []
        total_effort_hours = 0

        for task in tasks:
            duration_info = self._calculate_task_duration(task, milestone, domain_analysis)
            task_durations.append(duration_info)
            total_effort_hours += duration_info["effort_hours"]

        # Determine if tasks can run in parallel
        parallel_groups = self._identify_parallel_tasks(tasks, milestone)

        # Calculate milestone duration considering parallelization
        if parallel_groups:
            milestone_days = self._calculate_parallel_duration(task_durations, parallel_groups)
        else:
            # Sequential execution
            milestone_days = sum(td["duration_days"] for td in task_durations)

        milestone_weeks = math.ceil(milestone_days / self.working_days_per_week)

        return {
            "milestone_id": milestone["milestone_id"],
            "calculated_duration_days": milestone_days,
            "calculated_duration_weeks": milestone_weeks,
            "total_effort_hours": total_effort_hours,
            "task_durations": task_durations,
            "parallel_opportunities": parallel_groups,
            "resource_intensity": self._calculate_resource_intensity(total_effort_hours, milestone_days)
        }

    def _identify_parallel_tasks(self, tasks: List[Dict[str, Any]], milestone: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify which tasks can be executed in parallel"""

        parallel_groups = []

        # Group tasks by type for potential parallelization
        task_types = {
            "research": [],
            "design": [],
            "development": [],
            "testing": [],
            "documentation": []
        }

        for i, task in enumerate(tasks):
            task_name = task.get("name", "").lower()
            task_with_index = {"task_index": i, "task": task}

            if "research" in task_name or "analysis" in task_name:
                task_types["research"].append(task_with_index)
            elif "design" in task_name or "wireframe" in task_name:
                task_types["design"].append(task_with_index)
            elif "development" in task_name or "implementation" in task_name:
                task_types["development"].append(task_with_index)
            elif "testing" in task_name or "qa" in task_name:
                task_types["testing"].append(task_with_index)
            else:
                task_types["documentation"].append(task_with_index)

        # Create parallel groups for tasks of same type
        for task_type, type_tasks in task_types.items():
            if len(type_tasks) > 1:
                parallel_groups.append({
                    "group_type": task_type,
                    "tasks": [t["task_index"] for t in type_tasks],
                    "parallelization_factor": min(len(type_tasks), 3),  # Max 3 parallel tasks
                    "coordination_overhead": 0.1  # 10% overhead for coordination
                })

        return parallel_groups

    def _calculate_parallel_duration(self, task_durations: List[Dict[str, Any]], parallel_groups: List[Dict[str, Any]]) -> int:
        """Calculate duration considering parallel execution"""

        if not parallel_groups:
            return sum(td["duration_days"] for td in task_durations)

        # Create execution timeline
        task_assignments = {}  # task_index -> execution_group
        sequential_tasks = []

        # Assign tasks to parallel groups
        for group in parallel_groups:
            for task_index in group["tasks"]:
                task_assignments[task_index] = group

        # Identify sequential tasks
        for i, _ in enumerate(task_durations):
            if i not in task_assignments:
                sequential_tasks.append(i)

        # Calculate parallel group durations
        total_duration = 0

        # Sequential tasks duration
        for task_index in sequential_tasks:
            total_duration += task_durations[task_index]["duration_days"]

        # Parallel groups duration (max duration in each group)
        for group in parallel_groups:
            group_durations = [task_durations[i]["duration_days"] for i in group["tasks"]]
            max_group_duration = max(group_durations)

            # Add coordination overhead
            coordination_overhead = max_group_duration * group["coordination_overhead"]
            group_total_duration = max_group_duration + coordination_overhead

            total_duration += group_total_duration

        return math.ceil(total_duration)

    def _calculate_resource_intensity(self, total_effort_hours: int, milestone_days: int) -> str:
        """Calculate resource intensity level for milestone"""

        if milestone_days == 0:
            return "low"

        hours_per_day = total_effort_hours / milestone_days

        if hours_per_day <= 8:
            return "low"
        elif hours_per_day <= 16:
            return "medium"
        elif hours_per_day <= 24:
            return "high"
        else:
            return "very_high"

    def _calculate_project_dates(self, milestones: List[Dict[str, Any]], project_start_date: str = None) -> Dict[str, Any]:
        """Calculate start and end dates for entire project"""

        if not project_start_date:
            # Default to next Monday
            today = date.today()
            days_ahead = 0 - today.weekday()  # Monday is 0
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            project_start_date = (today + timedelta(days_ahead)).isoformat()

        start_date = datetime.fromisoformat(project_start_date).date()
        current_date = start_date

        milestone_schedule = []

        for milestone in milestones:
            milestone_duration_days = milestone["calculated_duration_days"]

            # Calculate milestone dates
            milestone_start = current_date
            milestone_end = self._add_working_days(milestone_start, milestone_duration_days)

            # Calculate task dates within milestone
            task_schedule = self._calculate_task_dates(
                milestone["task_durations"],
                milestone_start,
                milestone.get("parallel_opportunities", [])
            )

            milestone_info = {
                "milestone_id": milestone["milestone_id"],
                "start_date": milestone_start.isoformat(),
                "end_date": milestone_end.isoformat(),
                "duration_days": milestone_duration_days,
                "duration_weeks": milestone["calculated_duration_weeks"],
                "buffer_days": math.ceil(milestone_duration_days * 0.1),  # 10% buffer
                "resource_intensity": milestone["resource_intensity"],
                "tasks": task_schedule
            }

            milestone_schedule.append(milestone_info)

            # Move to next milestone (add 1 day gap)
            current_date = self._add_working_days(milestone_end, 1)

        # Calculate project totals
        project_end_date = milestone_schedule[-1]["end_date"]
        total_duration_days = (datetime.fromisoformat(project_end_date).date() - start_date).days
        total_working_days = sum(m["duration_days"] for m in milestone_schedule)

        return {
            "project_start_date": start_date.isoformat(),
            "project_end_date": project_end_date,
            "total_duration_days": total_duration_days,
            "total_working_days": total_working_days,
            "total_duration_weeks": math.ceil(total_working_days / self.working_days_per_week),
            "milestone_schedule": milestone_schedule
        }

    def _add_working_days(self, start_date: date, working_days: int) -> date:
        """Add working days to a date, skipping weekends"""
        current_date = start_date
        days_added = 0

        while days_added < working_days:
            current_date += timedelta(days=1)
            # Skip weekends (Saturday = 5, Sunday = 6)
            if current_date.weekday() < 5:
                days_added += 1

        return current_date

    def _calculate_task_dates(self, task_durations: List[Dict[str, Any]], milestone_start: date,
                            parallel_opportunities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Calculate start and end dates for tasks within milestone"""

        task_schedule = []
        current_date = milestone_start

        # Create task execution plan
        execution_plan = self._create_task_execution_plan(task_durations, parallel_opportunities)

        for execution_group in execution_plan:
            if execution_group["type"] == "sequential":
                # Sequential execution
                for task_index in execution_group["tasks"]:
                    task_duration = task_durations[task_index]
                    task_start = current_date
                    task_end = self._add_working_days(task_start, task_duration["duration_days"])

                    task_info = {
                        "task_index": task_index,
                        "start_date": task_start.isoformat(),
                        "end_date": task_end.isoformat(),
                        "duration_days": task_duration["duration_days"],
                        "effort_hours": task_duration["effort_hours"],
                        "can_parallel": False,
                        "parallel_with": [],
                        "resource_requirements": self._determine_resource_requirements(task_duration),
                        "dependencies": [],
                        "risk_level": self._assess_task_risk(task_duration)
                    }

                    task_schedule.append(task_info)
                    current_date = self._add_working_days(task_end, 1)  # 1 day gap

            elif execution_group["type"] == "parallel":
                # Parallel execution
                parallel_start = current_date
                max_duration = 0
                parallel_task_indices = execution_group["tasks"]

                for task_index in parallel_task_indices:
                    task_duration = task_durations[task_index]
                    task_start = parallel_start
                    task_end = self._add_working_days(task_start, task_duration["duration_days"])

                    max_duration = max(max_duration, task_duration["duration_days"])

                    task_info = {
                        "task_index": task_index,
                        "start_date": task_start.isoformat(),
                        "end_date": task_end.isoformat(),
                        "duration_days": task_duration["duration_days"],
                        "effort_hours": task_duration["effort_hours"],
                        "can_parallel": True,
                        "parallel_with": [i for i in parallel_task_indices if i != task_index],
                        "resource_requirements": self._determine_resource_requirements(task_duration),
                        "dependencies": [],
                        "risk_level": self._assess_task_risk(task_duration)
                    }

                    task_schedule.append(task_info)

                # Move current date to end of parallel group
                current_date = self._add_working_days(parallel_start, max_duration + 1)

        return task_schedule

    def _create_task_execution_plan(self, task_durations: List[Dict[str, Any]], parallel_opportunities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create execution plan considering parallel opportunities"""

        execution_plan = []
        assigned_tasks = set()

        # Add parallel groups
        for parallel_group in parallel_opportunities:
            execution_plan.append({
                "type": "parallel",
                "tasks": parallel_group["tasks"],
                "group_type": parallel_group["group_type"]
            })
            assigned_tasks.update(parallel_group["tasks"])

        # Add remaining tasks as sequential
        sequential_tasks = [i for i in range(len(task_durations)) if i not in assigned_tasks]
        if sequential_tasks:
            execution_plan.append({
                "type": "sequential",
                "tasks": sequential_tasks
            })

        return execution_plan

    def _determine_resource_requirements(self, task_duration: Dict[str, Any]) -> List[str]:
        """Determine resource requirements based on task characteristics"""

        effort_hours = task_duration["effort_hours"]

        # Basic resource requirements based on effort
        if effort_hours <= 20:
            return ["junior_developer"]
        elif effort_hours <= 40:
            return ["senior_developer"]
        elif effort_hours <= 80:
            return ["senior_developer", "junior_developer"]
        else:
            return ["senior_developer", "junior_developer", "specialist"]

    def _assess_task_risk(self, task_duration: Dict[str, Any]) -> str:
        """Assess risk level of task based on duration and complexity"""

        duration_days = task_duration["duration_days"]
        adjustments = task_duration.get("adjustments_applied", {})
        complexity_mult = adjustments.get("complexity_multiplier", 1.0)

        # Risk assessment logic
        if duration_days > 10 and complexity_mult > 1.2:
            return "high"
        elif duration_days > 5 or complexity_mult > 1.0:
            return "medium"
        else:
            return "low"

    def _detect_bottlenecks(self, timeline_data: Dict[str, Any], structure_design: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect potential bottlenecks in project timeline"""

        bottlenecks = []
        milestone_schedule = timeline_data["milestone_schedule"]

        # 1. Resource bottlenecks
        resource_bottlenecks = self._detect_resource_bottlenecks(milestone_schedule)
        bottlenecks.extend(resource_bottlenecks)

        # 2. Dependency bottlenecks
        dependency_bottlenecks = self._detect_dependency_bottlenecks(
            milestone_schedule, structure_design
        )
        bottlenecks.extend(dependency_bottlenecks)

        # 3. Critical path bottlenecks
        critical_path_bottlenecks = self._detect_critical_path_bottlenecks(
            milestone_schedule, structure_design
        )
        bottlenecks.extend(critical_path_bottlenecks)

        # 4. Skill/expertise bottlenecks
        skill_bottlenecks = self._detect_skill_bottlenecks(milestone_schedule)
        bottlenecks.extend(skill_bottlenecks)

        return bottlenecks

    def _detect_resource_bottlenecks(self, milestone_schedule: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect resource over-allocation bottlenecks"""

        bottlenecks = []

        # Analyze resource intensity across milestones
        for i, milestone in enumerate(milestone_schedule):
            resource_intensity = milestone.get("resource_intensity", "medium")

            if resource_intensity == "very_high":
                bottlenecks.append({
                    "type": "resource_overallocation",
                    "location": f"milestone_{milestone['milestone_id']}",
                    "severity": "high",
                    "description": f"Milestone {milestone['milestone_id']} requires very high resource intensity",
                    "impact": "high",
                    "probability": 0.7,
                    "mitigation": "Consider extending timeline or adding resources",
                    "estimated_delay": "3-5_days"
                })

            # Check for consecutive high-intensity milestones
            if i > 0:
                prev_intensity = milestone_schedule[i-1].get("resource_intensity", "medium")
                if resource_intensity == "high" and prev_intensity == "high":
                    bottlenecks.append({
                        "type": "consecutive_high_intensity",
                        "location": f"milestone_{milestone_schedule[i-1]['milestone_id']}_to_{milestone['milestone_id']}",
                        "severity": "medium",
                        "description": "Two consecutive high-intensity milestones may cause team burnout",
                        "impact": "medium",
                        "probability": 0.5,
                        "mitigation": "Add recovery time between milestones or redistribute tasks",
                        "estimated_delay": "2-3_days"
                    })

        return bottlenecks

    def _detect_dependency_bottlenecks(self, milestone_schedule: List[Dict[str, Any]], structure_design: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect dependency-related bottlenecks"""

        bottlenecks = []
        dependency_graph = structure_design.get("dependency_graph", {})

        # Analyze dependency chains
        for milestone_id, dependencies in dependency_graph.items():
            if len(dependencies) > 1:
                # Multiple dependencies = potential bottleneck
                bottlenecks.append({
                    "type": "multiple_dependencies",
                    "location": f"milestone_{milestone_id}",
                    "severity": "medium",
                    "description": f"Milestone {milestone_id} depends on {len(dependencies)} previous milestones",
                    "impact": "medium",
                    "probability": 0.4,
                    "mitigation": "Ensure clear handoff procedures and buffer time",
                    "estimated_delay": "1-2_days"
                })

        # Check for long dependency chains
        critical_path = structure_design.get("critical_path_analysis", {}).get("critical_milestones", [])
        if len(critical_path) >= 4:
            bottlenecks.append({
                "type": "long_critical_path",
                "location": "entire_project",
                "severity": "high",
                "description": f"Critical path contains {len(critical_path)} sequential milestones",
                "impact": "high",
                "probability": 0.6,
                "mitigation": "Identify opportunities for parallelization or scope reduction",
                "estimated_delay": "1-2_weeks"
            })

        return bottlenecks

    def _detect_critical_path_bottlenecks(self, milestone_schedule: List[Dict[str, Any]], structure_design: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Detect bottlenecks on critical path"""

        bottlenecks = []
        critical_milestones = structure_design.get("critical_path_analysis", {}).get("critical_milestones", [])

        for milestone in milestone_schedule:
            if milestone["milestone_id"] in critical_milestones:
                # Check if milestone has no buffer time
                buffer_days = milestone.get("buffer_days", 0)
                duration_days = milestone["duration_days"]

                if buffer_days < duration_days * 0.1:  # Less than 10% buffer
                    bottlenecks.append({
                        "type": "insufficient_buffer",
                        "location": f"milestone_{milestone['milestone_id']}",
                        "severity": "high",
                        "description": f"Critical path milestone {milestone['milestone_id']} has insufficient buffer time",
                        "impact": "high",
                        "probability": 0.8,
                        "mitigation": "Add 2-3 days buffer time to critical milestones",
                        "estimated_delay": "potential_project_delay"
                    })

        return bottlenecks

    def _detect_skill_bottlenecks(self, milestone_schedule: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Detect skill/expertise bottlenecks"""

        bottlenecks = []

        # Analyze skill requirements across timeline
        skill_timeline = {}

        for milestone in milestone_schedule:
            milestone_start = milestone["start_date"]
            milestone_end = milestone["end_date"]

            for task in milestone.get("tasks", []):
                resource_reqs = task.get("resource_requirements", [])

                for skill in resource_reqs:
                    if skill not in skill_timeline:
                        skill_timeline[skill] = []

                    skill_timeline[skill].append({
                        "start": milestone_start,
                        "end": milestone_end,
                        "milestone": milestone["milestone_id"]
                    })

        # Check for skill over-allocation
        for skill, allocations in skill_timeline.items():
            if len(allocations) > 2:  # Same skill needed in multiple overlapping periods
                bottlenecks.append({
                    "type": "skill_overallocation",
                    "location": f"skill_{skill}",
                    "severity": "medium",
                    "description": f"Skill '{skill}' is required in {len(allocations)} overlapping time periods",
                    "impact": "medium",
                    "probability": 0.6,
                    "mitigation": f"Consider cross-training team members in {skill} or hiring additional expertise",
                    "estimated_delay": "3-7_days"
                })

        return bottlenecks

    def _calculate_timeline_optimization_score(self, timeline_data: Dict[str, Any], bottlenecks: List[Dict[str, Any]],
                                             structure_design: Dict[str, Any]) -> float:
        """Calculate overall timeline optimization score"""

        scores = {
            "time_efficiency": self._score_time_efficiency(timeline_data, structure_design),
            "resource_utilization": self._score_resource_utilization(timeline_data),
            "risk_mitigation": self._score_risk_mitigation(bottlenecks, timeline_data),
            "parallel_optimization": self._score_parallel_optimization(timeline_data, structure_design)
        }

        # Weighted average
        total_score = 0
        for metric, weight in self.optimization_weights.items():
            total_score += scores[metric] * weight

        return round(total_score, 2)

    def _score_time_efficiency(self, timeline_data: Dict[str, Any], structure_design: Dict[str, Any]) -> float:
        """Score time efficiency of the timeline"""

        total_working_days = timeline_data["total_working_days"]
        milestone_count = len(timeline_data["milestone_schedule"])

        # Base efficiency score
        avg_milestone_duration = total_working_days / milestone_count if milestone_count > 0 else 0

        # Optimal milestone duration is 10-15 working days
        if 10 <= avg_milestone_duration <= 15:
            duration_score = 1.0
        elif 8 <= avg_milestone_duration <= 18:
            duration_score = 0.8
        else:
            duration_score = 0.6

        # Buffer time efficiency
        total_buffer_days = sum(m.get("buffer_days", 0) for m in timeline_data["milestone_schedule"])
        buffer_ratio = total_buffer_days / total_working_days if total_working_days > 0 else 0

        # Optimal buffer is 10-15%
        if 0.10 <= buffer_ratio <= 0.15:
            buffer_score = 1.0
        elif 0.05 <= buffer_ratio <= 0.20:
            buffer_score = 0.8
        else:
            buffer_score = 0.6

        return (duration_score + buffer_score) / 2

    def _score_resource_utilization(self, timeline_data: Dict[str, Any]) -> float:
        """Score resource utilization efficiency"""

        milestone_schedule = timeline_data["milestone_schedule"]

        # Analyze resource intensity distribution
        intensity_counts = {"low": 0, "medium": 0, "high": 0, "very_high": 0}

        for milestone in milestone_schedule:
            intensity = milestone.get("resource_intensity", "medium")
            intensity_counts[intensity] += 1

        total_milestones = len(milestone_schedule)
        if total_milestones == 0:
            return 0.5

        # Optimal distribution: mostly medium with some low and high
        medium_ratio = intensity_counts["medium"] / total_milestones
        very_high_ratio = intensity_counts["very_high"] / total_milestones

        # Penalize too many very_high intensity milestones
        if very_high_ratio > 0.4:
            return 0.4
        elif very_high_ratio > 0.2:
            return 0.6
        elif medium_ratio > 0.5:
            return 0.9
        else:
            return 0.7

    def _score_risk_mitigation(self, bottlenecks: List[Dict[str, Any]], timeline_data: Dict[str, Any]) -> float:
        """Score risk mitigation effectiveness"""

        if not bottlenecks:
            return 1.0

        # Analyze bottleneck severity
        high_severity_count = sum(1 for b in bottlenecks if b.get("severity") == "high")
        medium_severity_count = sum(1 for b in bottlenecks if b.get("severity") == "medium")

        total_bottlenecks = len(bottlenecks)

        # Calculate risk score
        if high_severity_count == 0 and medium_severity_count <= 2:
            return 0.9
        elif high_severity_count <= 1 and medium_severity_count <= 4:
            return 0.7
        elif high_severity_count <= 2:
            return 0.5
        else:
            return 0.3

    def _score_parallel_optimization(self, timeline_data: Dict[str, Any], structure_design: Dict[str, Any]) -> float:
        """Score parallel execution optimization"""

        milestone_schedule = timeline_data["milestone_schedule"]

        # Count parallel opportunities utilized
        total_parallel_opportunities = 0
        utilized_opportunities = 0

        for milestone in milestone_schedule:
            for task in milestone.get("tasks", []):
                if task.get("can_parallel", False):
                    utilized_opportunities += 1
                total_parallel_opportunities += 1

        if total_parallel_opportunities == 0:
            return 0.5

        parallel_ratio = utilized_opportunities / total_parallel_opportunities

        # Score based on parallel utilization
        if parallel_ratio >= 0.3:
            return 0.9
        elif parallel_ratio >= 0.2:
            return 0.7
        elif parallel_ratio >= 0.1:
            return 0.5
        else:
            return 0.3

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate timeline optimization output"""

        timeline_data = output.get("timeline_data", {})

        required_fields = [
            "project_start_date", "project_end_date", "total_duration_weeks",
            "milestone_schedule", "bottlenecks", "optimization_score"
        ]

        if not all(field in timeline_data for field in required_fields):
            self.logger.error(f"Missing required fields in timeline data: {required_fields}")
            return False

        # Validate milestone schedule
        milestone_schedule = timeline_data.get("milestone_schedule", [])
        if len(milestone_schedule) != 5:
            self.logger.error(f"Expected 5 milestones in schedule, got {len(milestone_schedule)}")
            return False

        # Validate dates
        try:
            start_date = datetime.fromisoformat(timeline_data["project_start_date"])
            end_date = datetime.fromisoformat(timeline_data["project_end_date"])

            if end_date <= start_date:
                self.logger.error("Project end date must be after start date")
                return False
        except ValueError as e:
            self.logger.error(f"Invalid date format: {e}")
            return False

        # Validate optimization score
        opt_score = timeline_data.get("optimization_score", 0)
        if not isinstance(opt_score, (int, float)) or not (0 <= opt_score <= 1):
            self.logger.error(f"Invalid optimization score: {opt_score}")
            return False

        return True

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Main timeline optimization process

        Args:
            state: Current plan generation state with content_data

        Returns:
            Dict containing timeline optimization data
        """

        content_data = state["content_data"]
        domain_analysis = state["domain_analysis"]
        structure_design = state["structure_design"]

        try:
            self.logger.info("Starting timeline optimization process...")

            # Step 1: Calculate milestone durations
            detailed_content = content_data["detailed_content"]
            milestones = detailed_content["milestones"]

            milestone_durations = []
            for milestone in milestones:
                self.logger.info(f"Calculating duration for {milestone['milestone_id']}")
                duration_info = self._calculate_milestone_duration(milestone, domain_analysis)
                milestone_durations.append(duration_info)

            # Step 2: Calculate project dates and schedule
            project_dates = self._calculate_project_dates(milestone_durations)
            self.logger.info(f"Project timeline: {project_dates['project_start_date']} to {project_dates['project_end_date']}")

            # Step 3: Detect bottlenecks
            bottlenecks = self._detect_bottlenecks(project_dates, structure_design)
            self.logger.info(f"Detected {len(bottlenecks)} potential bottlenecks")

            # Step 4: Calculate optimization score
            optimization_score = self._calculate_timeline_optimization_score(
                project_dates, bottlenecks, structure_design
            )
            self.logger.info(f"Timeline optimization score: {optimization_score}")

            # Step 5: Create comprehensive timeline data
            timeline_data = {
                **project_dates,
                "bottlenecks": bottlenecks,
                "optimization_score": optimization_score,
                "timeline_generation_timestamp": datetime.now().isoformat(),
                "domain_context": domain_analysis["primary_domain"],
                "complexity_level": domain_analysis["complexity_level"],
                "team_size": domain_analysis.get("constraints", {}).get("team_size", "small_team_3_5_people"),
                "buffer_strategy": {
                    "percentage": self.buffer_percentage,
                    "rationale": "15% buffer added to account for unforeseen delays and complexity"
                },
                "resource_recommendations": self._generate_resource_recommendations(project_dates, bottlenecks),
                "risk_assessment": self._generate_risk_assessment(bottlenecks),
                "optimization_recommendations": self._generate_optimization_recommendations(
                    project_dates, bottlenecks, optimization_score
                )
            }

            # Step 6: Validate output
            if not await self.validate_output({"timeline_data": timeline_data}):
                raise ValueError("Timeline optimization validation failed")

            self.logger.info("Timeline optimization completed successfully")
            self.logger.info(f"Total project duration: {timeline_data['total_duration_weeks']} weeks")

            return {
                "timeline_data": timeline_data,
                "progress": 66.67  # 4/6 agents completed
            }

        except Exception as e:
            self.logger.error(f"Timeline optimization failed: {e}")
            raise e

    def _generate_resource_recommendations(self, project_dates: Dict[str, Any], bottlenecks: List[Dict[str, Any]]) -> List[str]:
        """Generate resource allocation recommendations"""

        recommendations = []

        # Analyze resource intensity
        high_intensity_milestones = [
            m for m in project_dates["milestone_schedule"]
            if m.get("resource_intensity") in ["high", "very_high"]
        ]

        if high_intensity_milestones:
            recommendations.append(
                f"Consider adding 1-2 additional team members during {len(high_intensity_milestones)} high-intensity milestones"
            )

        # Analyze bottlenecks
        resource_bottlenecks = [b for b in bottlenecks if "resource" in b.get("type", "")]
        if resource_bottlenecks:
            recommendations.append(
                "Address resource bottlenecks by cross-training team members or hiring specialists"
            )

        # Skill-specific recommendations
        skill_bottlenecks = [b for b in bottlenecks if "skill" in b.get("type", "")]
        if skill_bottlenecks:
            recommendations.append(
                "Invest in skill development or external consultants for specialized tasks"
            )

        if not recommendations:
            recommendations.append("Current resource allocation appears optimal for project scope")

        return recommendations

    def _generate_risk_assessment(self, bottlenecks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive risk assessment"""

        high_risk_count = sum(1 for b in bottlenecks if b.get("severity") == "high")
        medium_risk_count = sum(1 for b in bottlenecks if b.get("severity") == "medium")

        if high_risk_count >= 2:
            overall_risk = "high"
            risk_description = "Multiple high-severity bottlenecks identified"
        elif high_risk_count == 1 or medium_risk_count >= 3:
            overall_risk = "medium"
            risk_description = "Some bottlenecks require attention"
        else:
            overall_risk = "low"
            risk_description = "Timeline appears manageable with current plan"

        return {
            "overall_risk_level": overall_risk,
            "risk_description": risk_description,
            "high_risk_items": high_risk_count,
            "medium_risk_items": medium_risk_count,
            "mitigation_priority": "high" if high_risk_count > 0 else "medium"
        }

    def _generate_optimization_recommendations(self, project_dates: Dict[str, Any], bottlenecks: List[Dict[str, Any]],
                                            optimization_score: float) -> List[str]:
        """Generate optimization recommendations"""

        recommendations = []

        if optimization_score < 0.7:
            recommendations.append("Consider significant timeline adjustments or scope reduction")
        elif optimization_score < 0.8:
            recommendations.append("Minor timeline optimizations recommended")

        # Specific recommendations based on bottlenecks
        for bottleneck in bottlenecks:
            if bottleneck.get("severity") == "high":
                recommendations.append(f"Priority: {bottleneck.get('mitigation', 'Address high-severity bottleneck')}")

        # Parallel execution recommendations
        milestone_schedule = project_dates["milestone_schedule"]
        parallel_opportunities = sum(
            1 for m in milestone_schedule
            for t in m.get("tasks", [])
            if t.get("can_parallel", False)
        )

        if parallel_opportunities < len(milestone_schedule) * 2:  # Less than 2 parallel tasks per milestone
            recommendations.append("Explore additional opportunities for parallel task execution")

        if not recommendations:
            recommendations.append("Timeline is well-optimized - proceed with current plan")

        return recommendations
