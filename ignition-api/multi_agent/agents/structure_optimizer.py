"""
Structure Optimization Agent - Simplified Gemini AI

This agent uses Gemini AI to generate project structure/milestones from domain analysis.
Simplified approach with better error handling and cleaner prompts.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, List
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class StructureOptimizationAgent(BaseIgnitionAgent):
    """
    Simplified Structure Optimization Agent - Clean Gemini AI approach
    
    Purpose: Generate project milestones and tasks from domain analysis
    Input: domain_analysis from Agent 1
    Output: structure_design with milestones and tasks
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Structure Optimization Agent with simplified Gemini AI."""
        super().__init__("structure_optimizer", config)
        
        # Gemini AI configuration
        self.temperature = 0.4
        self.max_tokens = 4000
        self.timeout = 60
        
        # Initialize Gemini client
        self._init_gemini_client()
        
        self.logger.info("🏗️ Structure Optimization Agent initialized - Simplified Gemini AI")

    def _init_gemini_client(self):
        """Initialize Gemini AI client with proper API keys"""
        try:
            import google.generativeai as genai
            
            # Agent 2 uses API keys 3 and 4
            api_key_3 = os.getenv('GOOGLE_AI_API_KEY_3')
            api_key_4 = os.getenv('GOOGLE_AI_API_KEY_4')
            
            if not api_key_3 or not api_key_4:
                raise ValueError("GOOGLE_AI_API_KEY_3 and GOOGLE_AI_API_KEY_4 environment variables not set")
            
            # Configure primary client
            genai.configure(api_key=api_key_3)
            self.primary_model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Store fallback info
            self.fallback_api_key = api_key_4
            self.fallback_model_name = 'gemini-2.5-flash'
            
            self.logger.info("✅ Gemini AI client initialized successfully for Agent 2")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini client: {e}")
            raise e

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Process domain analysis to generate project structure using Gemini AI
        
        Args:
            state: PlanGenerationState with domain_analysis from Agent 1
            
        Returns:
            Dict with success status and structure_design
        """
        domain_analysis = state.get("domain_analysis", {})
        
        if not domain_analysis:
            error_msg = "No domain analysis found in state"
            self.logger.error(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
        
        self.logger.info("🚀 AGENT 2 STARTING: Structure optimization with Gemini AI")
        self.logger.info(f"🎯 Domain: {domain_analysis.get('primary_domain', 'unknown')}")
        
        try:
            # Generate structure using Gemini AI
            structure_design = await self._generate_structure_with_gemini(domain_analysis)
            
            if structure_design and self._validate_structure(structure_design):
                self.logger.info(f"✅ SUCCESS: Generated {len(structure_design.get('milestone_structure', []))} milestones")
                
                return {
                    "success": True,
                    "structure_design": structure_design,
                    "metadata": {
                        "agent": "StructureOptimizationAgent",
                        "method": "gemini_ai_simplified",
                        "timestamp": datetime.now().isoformat()
                    }
                }
            else:
                # Create minimal fallback structure
                self.logger.warning("⚠️ Gemini failed, creating minimal fallback structure")
                fallback_structure = self._create_minimal_fallback_structure(domain_analysis)
                
                return {
                    "success": True,
                    "structure_design": fallback_structure,
                    "metadata": {
                        "agent": "StructureOptimizationAgent",
                        "method": "minimal_fallback",
                        "timestamp": datetime.now().isoformat()
                    }
                }
                
        except Exception as e:
            error_msg = f"Structure optimization error: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            # Return fallback structure even on error
            fallback_structure = self._create_minimal_fallback_structure(domain_analysis)
            
            return {
                "success": True,  # Still return success with fallback
                "structure_design": fallback_structure,
                "metadata": {
                    "agent": "StructureOptimizationAgent",
                    "method": "error_fallback",
                    "timestamp": datetime.now().isoformat(),
                    "error": error_msg
                }
            }

    async def _generate_structure_with_gemini(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate project structure using Gemini AI with simplified prompts
        
        Args:
            domain_analysis: Domain analysis from Agent 1
            
        Returns:
            Dict with structure design or None if failed
        """
        try:
            domain = domain_analysis.get("primary_domain", "general_project")
            complexity = domain_analysis.get("complexity_level", "intermediate")
            requirements = domain_analysis.get("extracted_requirements", {})
            constraints = domain_analysis.get("constraints", {})
            
            # Create simplified, domain-specific prompt
            system_prompt = f"""You are an expert project planner for {domain.replace('_', ' ')} projects.

Create a learning/project plan with milestones and tasks. Adapt the number of milestones and tasks based on complexity and domain.

DOMAIN: {domain}
COMPLEXITY: {complexity}

Return ONLY valid JSON in this format:
{{
    "milestone_structure": [
        {{
            "milestone_id": "M1",
            "name": "Milestone name",
            "description": "What will be accomplished",
            "position": 1,
            "estimated_duration": "2-4 weeks",
            "tasks": [
                {{
                    "task_id": "T1.1",
                    "name": "Task name",
                    "description": "Task description",
                    "estimated_duration": "3-5 days",
                    "complexity": "easy|medium|hard"
                }}
            ]
        }}
    ],
    "dependency_graph": {{"dependencies": []}},
    "critical_path_analysis": {{"critical_milestones": ["M1"]}},
    "optimization_score": 0.8
}}"""

            user_prompt = f"""Create a {complexity} level {domain.replace('_', ' ')} learning plan.

Requirements: {json.dumps(requirements.get('functional', [])[:3])}
Time constraint: {constraints.get('time', '6 months')}

Generate 4-6 milestones with 3-5 tasks each, appropriate for {domain} domain."""

            # Call Gemini AI
            response = await self._call_gemini_api(system_prompt, user_prompt)
            
            if response:
                # Parse and validate response
                structure = self._parse_json_response(response)
                
                if structure and self._validate_structure(structure):
                    self.logger.info("✅ Gemini structure generation successful")
                    return structure
                else:
                    self.logger.warning("⚠️ Invalid Gemini structure format")
                    return None
            else:
                self.logger.warning("⚠️ No response from Gemini AI")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Gemini structure generation error: {e}")
            return None

    async def _call_gemini_api(self, system_prompt: str, user_prompt: str) -> str:
        """
        Call Gemini AI API with error handling and fallback
        
        Args:
            system_prompt: System instructions
            user_prompt: User query
            
        Returns:
            Response text or None if failed
        """
        try:
            # Combine prompts
            full_prompt = f"{system_prompt}\n\n{user_prompt}"
            
            # Try primary model first
            try:
                response = await self.primary_model.generate_content_async(
                    full_prompt,
                    generation_config={
                        'temperature': self.temperature,
                        'max_output_tokens': self.max_tokens,
                    }
                )
                
                if response and response.text and response.text.strip():
                    self.logger.info("✅ Primary Gemini model responded")
                    return response.text.strip()
                    
            except Exception as e:
                self.logger.warning(f"⚠️ Primary model failed: {e}")
                
                # Try fallback model with different API key
                try:
                    import google.generativeai as genai
                    genai.configure(api_key=self.fallback_api_key)
                    fallback_model = genai.GenerativeModel(self.fallback_model_name)
                    
                    response = await fallback_model.generate_content_async(
                        full_prompt,
                        generation_config={
                            'temperature': self.temperature,
                            'max_output_tokens': self.max_tokens,
                        }
                    )
                    
                    if response and response.text and response.text.strip():
                        self.logger.info("✅ Fallback Gemini model responded")
                        return response.text.strip()
                        
                except Exception as e2:
                    self.logger.error(f"❌ Fallback model also failed: {e2}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Gemini API call failed: {e}")
            return None

    def _parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse JSON response from Gemini AI
        
        Args:
            response_text: Raw response from Gemini
            
        Returns:
            Parsed JSON dict or None if failed
        """
        try:
            # Clean response text
            text = response_text.strip()
            
            # Remove markdown formatting if present
            if '```json' in text:
                start = text.find('```json') + 7
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            elif '```' in text:
                start = text.find('```') + 3
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            
            # Find JSON object boundaries
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_text = text[start_idx:end_idx]
                result = json.loads(json_text)
                self.logger.info("✅ JSON response parsed successfully")
                return result
            else:
                self.logger.warning("⚠️ No valid JSON found in response")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON parsing error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Response parsing error: {e}")
            return None

    def _validate_structure(self, structure: Dict[str, Any]) -> bool:
        """
        Validate structure design format
        
        Args:
            structure: Structure design to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required top-level fields
            required_fields = ["milestone_structure", "dependency_graph", "critical_path_analysis", "optimization_score"]
            
            for field in required_fields:
                if field not in structure:
                    self.logger.error(f"Missing required field: {field}")
                    return False
            
            # Validate milestones
            milestones = structure.get("milestone_structure", [])
            if not isinstance(milestones, list) or len(milestones) < 1:
                self.logger.error("Invalid milestone structure")
                return False
            
            # Validate first milestone format
            first_milestone = milestones[0]
            milestone_required = ["milestone_id", "name", "tasks"]
            
            for field in milestone_required:
                if field not in first_milestone:
                    self.logger.error(f"Missing milestone field: {field}")
                    return False
            
            # Validate tasks
            tasks = first_milestone.get("tasks", [])
            if not isinstance(tasks, list) or len(tasks) < 1:
                self.logger.error("Invalid tasks structure")
                return False
            
            # Validate first task format
            first_task = tasks[0]
            task_required = ["task_id", "name"]
            
            for field in task_required:
                if field not in first_task:
                    self.logger.error(f"Missing task field: {field}")
                    return False
            
            self.logger.info("✅ Structure validation passed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Structure validation error: {e}")
            return False

    def _create_minimal_fallback_structure(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create minimal fallback structure when Gemini fails
        
        Args:
            domain_analysis: Domain analysis from Agent 1
            
        Returns:
            Basic structure design
        """
        domain = domain_analysis.get("primary_domain", "general_project")
        complexity = domain_analysis.get("complexity_level", "intermediate")
        
        # Create domain-specific milestones
        if domain == "machine_learning":
            milestones = [
                {
                    "milestone_id": "M1",
                    "name": "Foundation: Math and Programming",
                    "description": "Build mathematical foundation and programming skills",
                    "position": 1,
                    "estimated_duration": "4-6 weeks",
                    "tasks": [
                        {"task_id": "T1.1", "name": "Linear Algebra Basics", "description": "Learn vectors, matrices, eigenvalues", "estimated_duration": "1 week", "complexity": "medium"},
                        {"task_id": "T1.2", "name": "Statistics Fundamentals", "description": "Probability, distributions, hypothesis testing", "estimated_duration": "1 week", "complexity": "medium"},
                        {"task_id": "T1.3", "name": "Python for Data Science", "description": "NumPy, Pandas, Matplotlib basics", "estimated_duration": "2 weeks", "complexity": "easy"}
                    ]
                },
                {
                    "milestone_id": "M2",
                    "name": "Machine Learning Algorithms",
                    "description": "Learn core ML algorithms and concepts",
                    "position": 2,
                    "estimated_duration": "6-8 weeks",
                    "tasks": [
                        {"task_id": "T2.1", "name": "Supervised Learning", "description": "Linear regression, classification algorithms", "estimated_duration": "2 weeks", "complexity": "medium"},
                        {"task_id": "T2.2", "name": "Unsupervised Learning", "description": "Clustering, dimensionality reduction", "estimated_duration": "2 weeks", "complexity": "medium"},
                        {"task_id": "T2.3", "name": "Model Evaluation", "description": "Cross-validation, metrics, overfitting", "estimated_duration": "1 week", "complexity": "medium"}
                    ]
                },
                {
                    "milestone_id": "M3",
                    "name": "Deep Learning and Neural Networks",
                    "description": "Advanced ML with neural networks",
                    "position": 3,
                    "estimated_duration": "8-10 weeks",
                    "tasks": [
                        {"task_id": "T3.1", "name": "Neural Network Basics", "description": "Perceptrons, backpropagation, activation functions", "estimated_duration": "2 weeks", "complexity": "hard"},
                        {"task_id": "T3.2", "name": "Deep Learning Frameworks", "description": "TensorFlow/PyTorch implementation", "estimated_duration": "3 weeks", "complexity": "hard"},
                        {"task_id": "T3.3", "name": "CNN and RNN", "description": "Computer vision and sequence models", "estimated_duration": "3 weeks", "complexity": "hard"}
                    ]
                },
                {
                    "milestone_id": "M4",
                    "name": "Real-world Projects",
                    "description": "Apply ML to practical projects",
                    "position": 4,
                    "estimated_duration": "6-8 weeks",
                    "tasks": [
                        {"task_id": "T4.1", "name": "End-to-end ML Project", "description": "Complete ML pipeline from data to deployment", "estimated_duration": "4 weeks", "complexity": "hard"},
                        {"task_id": "T4.2", "name": "Model Deployment", "description": "Deploy models using Flask/FastAPI", "estimated_duration": "2 weeks", "complexity": "medium"}
                    ]
                }
            ]
        elif domain == "web_development":
            milestones = [
                {
                    "milestone_id": "M1",
                    "name": "Frontend Fundamentals",
                    "description": "HTML, CSS, JavaScript basics",
                    "position": 1,
                    "estimated_duration": "4-6 weeks",
                    "tasks": [
                        {"task_id": "T1.1", "name": "HTML Structure", "description": "Semantic HTML, forms, accessibility", "estimated_duration": "1 week", "complexity": "easy"},
                        {"task_id": "T1.2", "name": "CSS Styling", "description": "Flexbox, Grid, responsive design", "estimated_duration": "2 weeks", "complexity": "medium"},
                        {"task_id": "T1.3", "name": "JavaScript Basics", "description": "DOM manipulation, events, ES6+", "estimated_duration": "2 weeks", "complexity": "medium"}
                    ]
                },
                {
                    "milestone_id": "M2",
                    "name": "React Development",
                    "description": "Modern frontend with React",
                    "position": 2,
                    "estimated_duration": "6-8 weeks",
                    "tasks": [
                        {"task_id": "T2.1", "name": "React Fundamentals", "description": "Components, props, state, hooks", "estimated_duration": "3 weeks", "complexity": "medium"},
                        {"task_id": "T2.2", "name": "State Management", "description": "Context API, Redux basics", "estimated_duration": "2 weeks", "complexity": "medium"},
                        {"task_id": "T2.3", "name": "React Router", "description": "Navigation and routing", "estimated_duration": "1 week", "complexity": "easy"}
                    ]
                },
                {
                    "milestone_id": "M3",
                    "name": "Backend with Node.js",
                    "description": "Server-side development",
                    "position": 3,
                    "estimated_duration": "6-8 weeks",
                    "tasks": [
                        {"task_id": "T3.1", "name": "Node.js Basics", "description": "Server setup, Express.js, middleware", "estimated_duration": "2 weeks", "complexity": "medium"},
                        {"task_id": "T3.2", "name": "Database Integration", "description": "MongoDB/PostgreSQL, ORMs", "estimated_duration": "2 weeks", "complexity": "medium"},
                        {"task_id": "T3.3", "name": "API Development", "description": "RESTful APIs, authentication", "estimated_duration": "2 weeks", "complexity": "medium"}
                    ]
                },
                {
                    "milestone_id": "M4",
                    "name": "Full-stack Projects",
                    "description": "Complete web applications",
                    "position": 4,
                    "estimated_duration": "8-10 weeks",
                    "tasks": [
                        {"task_id": "T4.1", "name": "Portfolio Website", "description": "Personal portfolio with React", "estimated_duration": "3 weeks", "complexity": "medium"},
                        {"task_id": "T4.2", "name": "Full-stack App", "description": "Complete CRUD application", "estimated_duration": "4 weeks", "complexity": "hard"},
                        {"task_id": "T4.3", "name": "Deployment", "description": "Deploy to cloud platforms", "estimated_duration": "1 week", "complexity": "medium"}
                    ]
                }
            ]
        else:
            # Generic fallback structure
            milestones = [
                {
                    "milestone_id": "M1",
                    "name": "Foundation and Setup",
                    "description": f"Basic setup and fundamentals for {domain.replace('_', ' ')}",
                    "position": 1,
                    "estimated_duration": "3-4 weeks",
                    "tasks": [
                        {"task_id": "T1.1", "name": "Environment Setup", "description": "Set up development environment", "estimated_duration": "1 week", "complexity": "easy"},
                        {"task_id": "T1.2", "name": "Basic Concepts", "description": "Learn fundamental concepts", "estimated_duration": "2 weeks", "complexity": "medium"}
                    ]
                },
                {
                    "milestone_id": "M2",
                    "name": "Skill Development",
                    "description": f"Develop core skills in {domain.replace('_', ' ')}",
                    "position": 2,
                    "estimated_duration": "6-8 weeks",
                    "tasks": [
                        {"task_id": "T2.1", "name": "Core Skills", "description": "Master essential skills", "estimated_duration": "4 weeks", "complexity": "medium"},
                        {"task_id": "T2.2", "name": "Practice Projects", "description": "Apply skills in small projects", "estimated_duration": "3 weeks", "complexity": "medium"}
                    ]
                },
                {
                    "milestone_id": "M3",
                    "name": "Advanced Topics",
                    "description": f"Advanced concepts and techniques in {domain.replace('_', ' ')}",
                    "position": 3,
                    "estimated_duration": "6-8 weeks",
                    "tasks": [
                        {"task_id": "T3.1", "name": "Advanced Techniques", "description": "Learn advanced methods", "estimated_duration": "4 weeks", "complexity": "hard"},
                        {"task_id": "T3.2", "name": "Optimization", "description": "Optimize and improve skills", "estimated_duration": "2 weeks", "complexity": "medium"}
                    ]
                },
                {
                    "milestone_id": "M4",
                    "name": "Final Project",
                    "description": f"Complete project demonstrating {domain.replace('_', ' ')} mastery",
                    "position": 4,
                    "estimated_duration": "4-6 weeks",
                    "tasks": [
                        {"task_id": "T4.1", "name": "Project Planning", "description": "Plan and design final project", "estimated_duration": "1 week", "complexity": "medium"},
                        {"task_id": "T4.2", "name": "Implementation", "description": "Build the final project", "estimated_duration": "4 weeks", "complexity": "hard"}
                    ]
                }
            ]
        
        return {
            "milestone_structure": milestones,
            "dependency_graph": {
                "dependencies": [
                    {"from": "M1", "to": "M2", "type": "sequential"},
                    {"from": "M2", "to": "M3", "type": "sequential"},
                    {"from": "M3", "to": "M4", "type": "sequential"}
                ]
            },
            "critical_path_analysis": {
                "critical_milestones": [m["milestone_id"] for m in milestones],
                "total_duration": "20-32 weeks"
            },
            "optimization_score": 0.7,
            "generation_method": "minimal_fallback",
            "domain": domain,
            "complexity": complexity
        }

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """
        Validate agent output format
        
        Args:
            output: Agent output to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check success field
            if "success" not in output:
                return False
            
            # If successful, validate structure_design
            if output["success"]:
                structure_design = output.get("structure_design", {})
                return self._validate_structure(structure_design)
            else:
                # If failed, should have error field
                return "error" in output
                
        except Exception as e:
            self.logger.error(f"❌ Output validation error: {e}")
            return False
