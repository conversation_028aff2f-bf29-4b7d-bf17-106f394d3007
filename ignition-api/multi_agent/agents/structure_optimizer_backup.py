"""
Structure Optimization Agent

This agent creates optimal project structure with milestones, tasks,
and dependency management based on domain analysis.
"""

import math
import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState
from .gemini_structure_generator import GeminiStructureGenerator


class StructureOptimizationAgent(BaseIgnitionAgent):
    """
    Agent responsible for creating optimal project structure.

    Capabilities:
    - Generate optimal milestone structure (5 milestones)
    - Create balanced task distribution (25 tasks total)
    - Analyze dependencies and critical path
    - Calculate optimization scores
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Structure Optimization Agent with Gemini-powered generation."""
        super().__init__("structure_optimizer", config)

        # Initialize Gemini Structure Generator
        self.gemini_generator = GeminiStructureGenerator(config)

        # Keep minimal fallback configuration
        self.fallback_milestones = 5
        self.fallback_tasks_per_milestone = 4

        # Optimization weights for scoring
        self.optimization_weights = {
            "time_efficiency": 0.3,
            "resource_balance": 0.25,
            "risk_mitigation": 0.2,
            "dependency_optimization": 0.25
        }

        self.logger.info("✅ Structure Optimization Agent initialized with Gemini AI support")

    def _load_milestone_templates(self) -> Dict[str, Any]:
        """Load milestone templates for different domains"""
        return {
            "mobile_app_development": {
                "standard_flow": [
                    {
                        "name": "Research and Planning",
                        "description": "Market research, requirements analysis, technical planning",
                        "typical_duration_weeks": 2,
                        "complexity_weight": 0.7,
                        "critical_path": True,
                        "typical_tasks": [
                            "Market research and competitor analysis",
                            "User persona development and user journey mapping",
                            "Technical architecture design and technology stack selection",
                            "UI/UX wireframing and design system creation",
                            "Project setup and development environment configuration"
                        ]
                    },
                    {
                        "name": "Core Development Setup",
                        "description": "Foundation development, basic app structure, core features",
                        "typical_duration_weeks": 3,
                        "complexity_weight": 1.0,
                        "critical_path": True,
                        "typical_tasks": [
                            "Mobile app project initialization and basic structure setup",
                            "User authentication system implementation",
                            "Database design and backend API development",
                            "Core navigation and app flow implementation",
                            "Basic UI components and design system implementation"
                        ]
                    },
                    {
                        "name": "Feature Development",
                        "description": "Main features implementation, business logic development",
                        "typical_duration_weeks": 4,
                        "complexity_weight": 1.2,
                        "critical_path": True,
                        "typical_tasks": [
                            "Product catalog and search functionality implementation",
                            "Shopping cart and checkout process development",
                            "Payment gateway integration and testing",
                            "User profile and account management features",
                            "Push notifications and communication features"
                        ]
                    },
                    {
                        "name": "Testing and Refinement",
                        "description": "Quality assurance, performance optimization, bug fixes",
                        "typical_duration_weeks": 2,
                        "complexity_weight": 0.8,
                        "critical_path": True,
                        "typical_tasks": [
                            "Comprehensive testing suite development and execution",
                            "Performance optimization and memory management",
                            "Security testing and vulnerability assessment",
                            "User acceptance testing and feedback integration",
                            "Bug fixes and stability improvements"
                        ]
                    },
                    {
                        "name": "Deployment and Launch",
                        "description": "App store submission, production deployment, launch preparation",
                        "typical_duration_weeks": 1,
                        "complexity_weight": 0.6,
                        "critical_path": True,
                        "typical_tasks": [
                            "App store optimization and submission preparation",
                            "Production environment setup and deployment",
                            "Launch marketing materials and documentation",
                            "Monitoring and analytics implementation",
                            "Post-launch support and maintenance planning"
                        ]
                    }
                ]
            },
            "web_development": {
                "standard_flow": [
                    {
                        "name": "Planning and Design",
                        "description": "Requirements gathering, design, architecture planning",
                        "typical_duration_weeks": 2,
                        "complexity_weight": 0.7,
                        "critical_path": True,
                        "typical_tasks": [
                            "Requirements analysis and stakeholder interviews",
                            "User experience design and wireframing",
                            "Technical architecture and database design",
                            "Design system and visual identity creation",
                            "Development environment and tooling setup"
                        ]
                    },
                    {
                        "name": "Frontend Development",
                        "description": "User interface development, responsive design",
                        "typical_duration_weeks": 3,
                        "complexity_weight": 1.0,
                        "critical_path": True,
                        "typical_tasks": [
                            "Frontend framework setup and configuration",
                            "Responsive layout and component development",
                            "User interface implementation and styling",
                            "Client-side functionality and interactions",
                            "Frontend testing and optimization"
                        ]
                    },
                    {
                        "name": "Backend Development",
                        "description": "Server-side logic, database, API development",
                        "typical_duration_weeks": 4,
                        "complexity_weight": 1.1,
                        "critical_path": True,
                        "typical_tasks": [
                            "Backend framework and API structure setup",
                            "Database implementation and data modeling",
                            "Business logic and service layer development",
                            "Authentication and authorization systems",
                            "API endpoints and integration development"
                        ]
                    },
                    {
                        "name": "Integration and Testing",
                        "description": "System integration, testing, optimization",
                        "typical_duration_weeks": 2,
                        "complexity_weight": 0.9,
                        "critical_path": True,
                        "typical_tasks": [
                            "Frontend and backend integration",
                            "End-to-end testing implementation",
                            "Performance testing and optimization",
                            "Security testing and vulnerability assessment",
                            "User acceptance testing and bug fixes"
                        ]
                    },
                    {
                        "name": "Deployment and Launch",
                        "description": "Production deployment, launch, monitoring",
                        "typical_duration_weeks": 1,
                        "complexity_weight": 0.6,
                        "critical_path": True,
                        "typical_tasks": [
                            "Production environment setup and configuration",
                            "Deployment pipeline and CI/CD implementation",
                            "Domain setup and SSL configuration",
                            "Monitoring and analytics implementation",
                            "Launch preparation and documentation"
                        ]
                    }
                ]
            }
        }

    def _select_optimal_template(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Select best template based on domain analysis"""
        primary_domain = domain_analysis["primary_domain"]
        complexity_level = domain_analysis["complexity_level"]

        # Get base template
        base_template = self.milestone_templates.get(primary_domain,
                                                    self.milestone_templates["mobile_app_development"])

        # Adjust based on complexity
        complexity_multipliers = {
            "beginner": 0.8,
            "intermediate": 1.0,
            "advanced": 1.3,
            "expert": 1.6
        }

        multiplier = complexity_multipliers.get(complexity_level, 1.0)

        # Apply complexity adjustments
        adjusted_template = []
        for milestone in base_template["standard_flow"]:
            adjusted_milestone = milestone.copy()
            adjusted_milestone["typical_duration_weeks"] = math.ceil(
                milestone["typical_duration_weeks"] * multiplier
            )
            adjusted_milestone["complexity_weight"] *= multiplier
            adjusted_template.append(adjusted_milestone)

        return {"standard_flow": adjusted_template}

    def _generate_milestones(self, domain_analysis: Dict[str, Any], template: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate optimized milestones based on template and requirements"""

        milestones = []
        requirements = domain_analysis["extracted_requirements"]
        constraints = domain_analysis["constraints"]

        for i, milestone_template in enumerate(template["standard_flow"]):
            milestone = {
                "milestone_id": f"M{i+1}",
                "name": milestone_template["name"],
                "description": milestone_template["description"],
                "position": i + 1,
                "estimated_duration": f"{milestone_template['typical_duration_weeks']}_weeks",
                "dependencies": self._calculate_dependencies(i, template["standard_flow"]),
                "critical_path": milestone_template["critical_path"],
                "task_count": 5,  # Standard 5 tasks per milestone
                "complexity_weight": milestone_template["complexity_weight"],
                "success_criteria": self._generate_success_criteria(milestone_template, requirements)
            }

            # Customize based on specific requirements
            milestone = self._customize_milestone_for_requirements(milestone, requirements)

            milestones.append(milestone)

        return milestones

    def _calculate_dependencies(self, milestone_index: int, template_flow: List[Dict[str, Any]]) -> List[str]:
        """Calculate milestone dependencies"""
        if milestone_index == 0:
            return []
        elif milestone_index == 1:
            return ["M1"]
        elif milestone_index <= 2:
            return [f"M{milestone_index}"]
        else:
            # Later milestones may depend on multiple previous ones
            return [f"M{milestone_index}"]

    def _generate_success_criteria(self, milestone_template: Dict[str, Any], requirements: Dict[str, Any]) -> str:
        """Generate success criteria for milestone"""
        base_criteria = {
            "Research and Planning": "Requirements documented, architecture designed, team aligned",
            "Core Development Setup": "Basic functionality working, foundation established",
            "Feature Development": "Main features implemented and tested",
            "Testing and Refinement": "Quality standards met, performance optimized",
            "Deployment and Launch": "Successfully deployed, monitoring active",
            "Planning and Design": "Design approved, architecture documented",
            "Frontend Development": "User interface complete and responsive",
            "Backend Development": "APIs functional, database operational",
            "Integration and Testing": "System integrated, tests passing"
        }

        return base_criteria.get(milestone_template["name"], "Milestone objectives achieved")

    def _customize_milestone_for_requirements(self, milestone: Dict[str, Any], requirements: Dict[str, Any]) -> Dict[str, Any]:
        """Customize milestone based on specific requirements"""

        # Add e-commerce specific customizations
        if "payment_processing" in requirements.get("functional", []):
            if "Feature Development" in milestone["name"]:
                milestone["name"] = "Feature Development and Payment Integration"
                milestone["description"] += " with secure payment processing"

        # Add security customizations
        if "security" in requirements.get("non_functional", []):
            if "Testing" in milestone["name"]:
                milestone["description"] += " including security audits"

        return milestone

    def _generate_tasks_for_milestone(self, milestone: Dict[str, Any], milestone_template: Dict[str, Any],
                                     domain_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate 5 tasks for each milestone"""

        base_tasks = milestone_template.get("typical_tasks", [])
        requirements = domain_analysis["extracted_requirements"]

        tasks = []

        # Ensure we have exactly 5 tasks
        for i in range(5):
            if i < len(base_tasks):
                task_name = base_tasks[i]
            else:
                # Generate additional tasks if template has less than 5
                task_name = self._generate_additional_task(milestone, i, requirements)

            task = {
                "task_id": f"{milestone['milestone_id']}_T{i+1}",
                "name": task_name,
                "estimated_duration": self._estimate_task_duration(task_name, milestone),
                "complexity": self._assess_task_complexity(task_name, requirements),
                "required_skills": self._identify_required_skills(task_name),
                "dependencies": self._calculate_task_dependencies(i, milestone["milestone_id"]),
                "deliverables": self._define_task_deliverables(task_name),
                "acceptance_criteria": self._generate_task_acceptance_criteria(task_name)
            }

            tasks.append(task)

        return tasks

    def _generate_additional_task(self, milestone: Dict[str, Any], task_index: int, requirements: Dict[str, Any]) -> str:
        """Generate additional task if template has less than 5 tasks"""
        milestone_name = milestone["name"].lower()

        additional_tasks = {
            "research": [
                "Stakeholder interviews and requirements validation",
                "Risk assessment and mitigation planning"
            ],
            "development": [
                "Code review and quality assurance",
                "Documentation and knowledge transfer"
            ],
            "testing": [
                "Performance benchmarking and optimization",
                "User feedback collection and analysis"
            ],
            "deployment": [
                "Backup and recovery procedures setup",
                "Team training and handover documentation"
            ]
        }

        # Find appropriate category
        for category, tasks in additional_tasks.items():
            if category in milestone_name:
                return tasks[task_index % len(tasks)]

        return f"Additional task {task_index + 1} for {milestone['name']}"

    def _estimate_task_duration(self, task_name: str, milestone: Dict[str, Any]) -> str:
        """Estimate task duration based on complexity and milestone duration"""

        # Parse milestone duration
        milestone_weeks = int(milestone["estimated_duration"].split("_")[0])

        # Task complexity indicators
        complexity_indicators = {
            "research": 0.8,
            "design": 1.0,
            "development": 1.2,
            "implementation": 1.2,
            "testing": 0.9,
            "integration": 1.1,
            "deployment": 0.7,
            "documentation": 0.6
        }

        # Determine base complexity
        base_complexity = 1.0
        for indicator, weight in complexity_indicators.items():
            if indicator in task_name.lower():
                base_complexity = weight
                break

        # Calculate task duration (assuming 5 tasks per milestone)
        task_days = math.ceil((milestone_weeks * 5 * base_complexity) / 5)

        if task_days <= 3:
            return f"{task_days}_days"
        else:
            task_weeks = math.ceil(task_days / 5)
            return f"{task_weeks}_weeks"

    def _assess_task_complexity(self, task_name: str, requirements: Dict[str, Any]) -> str:
        """Assess task complexity level"""

        high_complexity_indicators = [
            "integration", "security", "payment", "real-time", "scalability",
            "performance", "architecture", "complex", "advanced"
        ]

        medium_complexity_indicators = [
            "development", "implementation", "design", "testing", "api"
        ]

        task_lower = task_name.lower()

        if any(indicator in task_lower for indicator in high_complexity_indicators):
            return "high"
        elif any(indicator in task_lower for indicator in medium_complexity_indicators):
            return "medium"
        else:
            return "low"

    def _identify_required_skills(self, task_name: str) -> List[str]:
        """Identify required skills for task"""

        skill_mapping = {
            "research": ["business_analysis", "market_research"],
            "design": ["ui_design", "ux_design", "graphic_design"],
            "frontend": ["frontend_development", "javascript", "react"],
            "backend": ["backend_development", "api_development", "database"],
            "mobile": ["mobile_development", "ios", "android"],
            "testing": ["qa_testing", "automation_testing"],
            "deployment": ["devops", "cloud_deployment"],
            "security": ["security_engineering", "penetration_testing"]
        }

        identified_skills = []
        task_lower = task_name.lower()

        for keyword, skills in skill_mapping.items():
            if keyword in task_lower:
                identified_skills.extend(skills)

        return list(set(identified_skills)) if identified_skills else ["general_development"]

    def _calculate_task_dependencies(self, task_index: int, milestone_id: str) -> List[str]:
        """Calculate task dependencies within milestone"""
        if task_index == 0:
            return []
        else:
            # Each task depends on the previous task in the same milestone
            return [f"{milestone_id}_T{task_index}"]

    def _define_task_deliverables(self, task_name: str) -> List[str]:
        """Define deliverables for task"""
        task_lower = task_name.lower()

        if "research" in task_lower:
            return ["Research report", "Analysis document", "Recommendations"]
        elif "design" in task_lower:
            return ["Design mockups", "Wireframes", "Design system"]
        elif "development" in task_lower or "implementation" in task_lower:
            return ["Working code", "Unit tests", "Documentation"]
        elif "testing" in task_lower:
            return ["Test results", "Bug reports", "Quality metrics"]
        elif "deployment" in task_lower:
            return ["Deployed system", "Deployment guide", "Monitoring setup"]
        else:
            return ["Completed deliverable", "Documentation", "Review report"]

    def _generate_task_acceptance_criteria(self, task_name: str) -> str:
        """Generate acceptance criteria for task"""
        task_lower = task_name.lower()

        if "research" in task_lower:
            return "Research completed, findings documented, stakeholders informed"
        elif "design" in task_lower:
            return "Design approved by stakeholders, meets requirements, ready for development"
        elif "development" in task_lower or "implementation" in task_lower:
            return "Code implemented, tested, reviewed, and meets specifications"
        elif "testing" in task_lower:
            return "All tests pass, quality standards met, issues resolved"
        elif "deployment" in task_lower:
            return "Successfully deployed, monitoring active, team trained"
        else:
            return "Task completed according to specifications and quality standards"

    def _analyze_dependencies(self, milestones: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze dependencies and create dependency graph"""

        dependency_graph = {}

        # Milestone-level dependencies
        for milestone in milestones:
            milestone_id = milestone["milestone_id"]
            dependency_graph[milestone_id] = milestone["dependencies"]

        # Task-level dependencies (within and across milestones)
        task_dependencies = {}

        for milestone in milestones:
            for task in milestone.get("tasks", []):
                task_id = task["task_id"]
                task_dependencies[task_id] = self._calculate_detailed_task_dependencies(
                    task, milestone, milestones
                )

        return {
            "milestone_dependencies": dependency_graph,
            "task_dependencies": task_dependencies,
            "critical_path": self._identify_critical_path(milestones),
            "parallel_opportunities": self._identify_parallel_opportunities(milestones)
        }

    def _calculate_detailed_task_dependencies(self, task: Dict[str, Any], milestone: Dict[str, Any],
                                            milestones: List[Dict[str, Any]]) -> List[str]:
        """Calculate detailed task dependencies across milestones"""
        dependencies = task.get("dependencies", [])

        # Add cross-milestone dependencies for certain task types
        task_name_lower = task["name"].lower()
        milestone_position = milestone["position"]

        # If this is a development task and not in first milestone, depend on design completion
        if "development" in task_name_lower and milestone_position > 1:
            # Find design tasks in previous milestones
            for prev_milestone in milestones:
                if prev_milestone["position"] < milestone_position:
                    for prev_task in prev_milestone.get("tasks", []):
                        if "design" in prev_task["name"].lower():
                            dependencies.append(prev_task["task_id"])

        return dependencies

    def _identify_critical_path(self, milestones: List[Dict[str, Any]]) -> List[str]:
        """Identify critical path through milestones"""

        critical_milestones = []

        for milestone in milestones:
            if milestone["critical_path"]:
                critical_milestones.append(milestone["milestone_id"])

        return critical_milestones

    def _identify_parallel_opportunities(self, milestones: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify tasks that can be executed in parallel"""

        parallel_opportunities = []

        # Look for tasks within same milestone that can run parallel
        for milestone in milestones:
            tasks = milestone.get("tasks", [])

            # Group tasks by type for parallel execution
            design_tasks = []
            development_tasks = []

            for task in tasks:
                task_name_lower = task["name"].lower()
                if "design" in task_name_lower or "wireframe" in task_name_lower:
                    design_tasks.append(task["task_id"])
                elif "development" in task_name_lower or "implementation" in task_name_lower:
                    development_tasks.append(task["task_id"])

            # If multiple tasks of same type, they can potentially run parallel
            if len(design_tasks) > 1:
                parallel_opportunities.append({
                    "tasks": design_tasks,
                    "type": "design_parallel",
                    "milestone": milestone["milestone_id"],
                    "estimated_time_saved": "3-5_days"
                })

            if len(development_tasks) > 1:
                parallel_opportunities.append({
                    "tasks": development_tasks,
                    "type": "development_parallel",
                    "milestone": milestone["milestone_id"],
                    "estimated_time_saved": "1-2_weeks"
                })

        return parallel_opportunities

    def _calculate_optimization_score(self, structure: Dict[str, Any], domain_analysis: Dict[str, Any]) -> float:
        """Calculate overall optimization score for the structure"""

        scores = {
            "time_efficiency": self._score_time_efficiency(structure),
            "resource_balance": self._score_resource_balance(structure),
            "risk_mitigation": self._score_risk_mitigation(structure, domain_analysis),
            "dependency_optimization": self._score_dependency_optimization(structure)
        }

        # Weighted average
        total_score = 0
        for metric, weight in self.optimization_weights.items():
            total_score += scores[metric] * weight

        return round(total_score, 2)

    def _score_time_efficiency(self, structure: Dict[str, Any]) -> float:
        """Score time efficiency of the structure"""

        milestones = structure["milestone_structure"]
        parallel_ops = structure.get("parallel_opportunities", [])

        # Base score from parallel opportunities
        parallel_score = min(len(parallel_ops) * 0.15, 0.6)

        # Score from balanced milestone durations
        durations = []
        for milestone in milestones:
            weeks = int(milestone["estimated_duration"].split("_")[0])
            durations.append(weeks)

        # Penalize if durations are too unbalanced
        duration_variance = max(durations) - min(durations)
        balance_score = max(0.4 - (duration_variance * 0.1), 0)

        return min(parallel_score + balance_score, 1.0)

    def _score_resource_balance(self, structure: Dict[str, Any]) -> float:
        """Score resource balance across milestones"""

        milestones = structure["milestone_structure"]

        # Analyze skill distribution
        skill_distribution = {}
        total_tasks = 0

        for milestone in milestones:
            for task in milestone.get("tasks", []):
                total_tasks += 1
                for skill in task.get("required_skills", []):
                    skill_distribution[skill] = skill_distribution.get(skill, 0) + 1

        # Calculate balance score (avoid over-concentration of skills)
        if not skill_distribution:
            return 0.5

        max_skill_usage = max(skill_distribution.values())
        balance_ratio = max_skill_usage / total_tasks

        # Better balance = higher score
        balance_score = max(1.0 - balance_ratio, 0.3)

        return balance_score

    def _score_risk_mitigation(self, structure: Dict[str, Any], domain_analysis: Dict[str, Any]) -> float:
        """Score risk mitigation effectiveness"""

        complexity_level = domain_analysis["complexity_level"]
        milestones = structure["milestone_structure"]

        # Higher complexity projects need better risk mitigation
        complexity_weights = {
            "beginner": 0.7,
            "intermediate": 0.8,
            "advanced": 0.9,
            "expert": 1.0
        }

        base_weight = complexity_weights.get(complexity_level, 0.8)

        # Check for risk mitigation patterns
        risk_mitigation_score = 0.5  # Base score

        # Bonus for having testing milestone
        if any("testing" in m["name"].lower() for m in milestones):
            risk_mitigation_score += 0.2

        # Bonus for gradual complexity increase
        complexity_weights_list = [m["complexity_weight"] for m in milestones]
        if complexity_weights_list == sorted(complexity_weights_list):
            risk_mitigation_score += 0.2

        return min(risk_mitigation_score * base_weight, 1.0)

    def _score_dependency_optimization(self, structure: Dict[str, Any]) -> float:
        """Score dependency optimization"""

        dependency_graph = structure.get("dependency_graph", {})
        parallel_ops = structure.get("parallel_opportunities", [])

        # Score based on dependency complexity
        total_dependencies = sum(len(deps) for deps in dependency_graph.values())
        total_milestones = len(dependency_graph)

        if total_milestones == 0:
            return 0.5

        # Lower dependency ratio is better (but not too low)
        dependency_ratio = total_dependencies / total_milestones
        dependency_score = max(0.3, 1.0 - (dependency_ratio * 0.2))

        # Bonus for parallel opportunities
        parallel_bonus = min(len(parallel_ops) * 0.1, 0.3)

        return min(dependency_score + parallel_bonus, 1.0)

    def _calculate_total_duration(self, milestones: List[Dict[str, Any]]) -> str:
        """Calculate total project duration"""
        total_weeks = 0
        for milestone in milestones:
            weeks = int(milestone["estimated_duration"].split("_")[0])
            total_weeks += weeks

        return f"{total_weeks}_weeks"

    def _generate_optimization_rationale(self, structure: Dict[str, Any], domain_analysis: Dict[str, Any]) -> str:
        """Generate rationale for optimization decisions"""

        optimization_score = structure["optimization_score"]
        complexity_level = domain_analysis["complexity_level"]
        primary_domain = domain_analysis["primary_domain"]

        rationale_parts = [
            f"Structure optimized for {primary_domain} with {complexity_level} complexity level.",
            f"Achieved optimization score of {optimization_score}.",
        ]

        # Add specific rationale based on features
        parallel_ops = structure.get("parallel_opportunities", [])
        if parallel_ops:
            rationale_parts.append(f"Identified {len(parallel_ops)} parallel execution opportunities to reduce timeline.")

        critical_path = structure.get("critical_path_analysis", {}).get("critical_milestones", [])
        if critical_path:
            rationale_parts.append(f"Critical path includes {len(critical_path)} milestones requiring sequential execution.")

        return " ".join(rationale_parts)

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Flexible validation that adapts to context and complexity"""
        
        self.logger.info("🔍 Starting flexible validation...")
        
        structure = output.get("structure_design", {})
        
        # Required fields (these are still mandatory)
        required_fields = [
            "milestone_structure", "dependency_graph", "critical_path_analysis",
            "optimization_score"
        ]
        
        if not all(field in structure for field in required_fields):
            missing_fields = [field for field in required_fields if field not in structure]
            self.logger.error(f"Missing required fields: {missing_fields}")
            return False
        
        # Flexible milestone validation
        milestones = structure.get("milestone_structure", [])
        milestone_count = len(milestones)
        
        # Adaptive milestone count based on complexity and duration
        if milestone_count < 2:
            self.logger.error(f"Too few milestones: {milestone_count} (minimum: 2)")
            return False
        elif milestone_count > 10:
            self.logger.error(f"Too many milestones: {milestone_count} (maximum: 10)")
            return False
        
        self.logger.info(f"✅ Milestone count: {milestone_count} (acceptable range: 2-10)")
        
        # Flexible task validation per milestone
        for i, milestone in enumerate(milestones):
            if not isinstance(milestone, dict):
                self.logger.error(f"Milestone {i+1} is not a dictionary")
                return False
            
            # Required milestone fields
            milestone_required = ["milestone_id", "name", "tasks"]
            missing_milestone_fields = [field for field in milestone_required if field not in milestone]
            if missing_milestone_fields:
                self.logger.error(f"Milestone {i+1} missing fields: {missing_milestone_fields}")
                return False
            
            tasks = milestone.get("tasks", [])
            task_count = len(tasks)
            
            # Adaptive task count based on milestone complexity
            if task_count < 1:
                self.logger.error(f"Milestone {milestone['milestone_id']}: Too few tasks: {task_count}")
                return False
            elif task_count > 15:
                self.logger.error(f"Milestone {milestone['milestone_id']}: Too many tasks: {task_count}")
                return False
            
            self.logger.info(f"✅ Milestone {milestone['milestone_id']}: {task_count} tasks (acceptable)")
            
            # Validate task structure
            for j, task in enumerate(tasks):
                if not isinstance(task, dict):
                    self.logger.error(f"Task {j+1} in milestone {milestone['milestone_id']} is not a dictionary")
                    return False
                
                # Required task fields
                task_required = ["task_id", "name"]
                missing_task_fields = [field for field in task_required if field not in task]
                if missing_task_fields:
                    self.logger.error(f"Task {j+1} in milestone {milestone['milestone_id']} missing fields: {missing_task_fields}")
                    return False
        
        # Validate optimization score
        opt_score = structure.get("optimization_score", 0)
        if not isinstance(opt_score, (int, float)) or not (0 <= opt_score <= 1):
            self.logger.error(f"Invalid optimization score: {opt_score} (must be 0-1)")
            return False
        
        # Validate dependency graph structure
        dep_graph = structure.get("dependency_graph", {})
        if not isinstance(dep_graph, dict):
            self.logger.error("Dependency graph must be a dictionary")
            return False
        
        # Validate critical path analysis
        critical_path = structure.get("critical_path_analysis", {})
        if not isinstance(critical_path, dict):
            self.logger.error("Critical path analysis must be a dictionary")
            return False
        
        self.logger.info("✅ Flexible validation passed!")
        return True

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Main structure optimization process using Gemini AI

        Args:
            state: Current plan generation state with domain_analysis

        Returns:
            Dict containing structure design
        """

        domain_analysis = state["domain_analysis"]

        try:
            self.logger.info("🚀 AGENT 2 STARTING: Gemini-powered structure optimization...")
            self.logger.info(f"📥 AGENT 2 INPUT: Received domain_analysis from Agent 1")
            self.logger.info(f"🎯 PRIMARY DOMAIN: {domain_analysis.get('primary_domain', 'unknown')}")
            self.logger.info(f"📊 CONFIDENCE SCORE: {domain_analysis.get('confidence_score', 0):.2f}")
            self.logger.info(f"🔧 COMPLEXITY LEVEL: {domain_analysis.get('complexity_level', 'unknown')}")

            # Primary approach: Use Gemini AI for structure generation
            self.logger.info("🤖 GEMINI AI CALL: Requesting structure generation...")
            structure = await self.gemini_generator.generate_optimal_structure(domain_analysis)

            if structure and self._validate_gemini_structure(structure):
                self.logger.info("✅ GEMINI SUCCESS: Structure generation completed")
                milestones_count = len(structure.get("milestones", []))
                total_tasks = sum(len(m.get("tasks", [])) for m in structure.get("milestones", []))
                self.logger.info(f"📊 GEMINI OUTPUT: Generated {milestones_count} milestones, {total_tasks} tasks")

                # Convert Gemini structure to our expected format
                self.logger.info("🔄 AGENT 2 PROCESSING: Converting Gemini structure to legacy format...")
                converted_structure = self._convert_gemini_to_legacy_format(structure)

                # Add optimization analysis
                self.logger.info("📈 AGENT 2 PROCESSING: Calculating optimization scores...")
                converted_structure["optimization_score"] = self._calculate_optimization_score(converted_structure)
                converted_structure["generation_method"] = "gemini_ai"
                converted_structure["generation_timestamp"] = datetime.now().isoformat()

                self.logger.info("🎉 AGENT 2 SUCCESS: Structure optimization completed with Gemini AI")
                return converted_structure

            else:
                self.logger.warning("⚠️ GEMINI FAILED: Structure generation failed, using fallback...")
                self.logger.info("🔄 AGENT 2 FALLBACK: Switching to simplified structure generation...")
                return await self._fallback_structure_generation(domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ AGENT 2 ERROR: Structure optimization failed: {e}")
            self.logger.info("🔄 AGENT 2 FALLBACK: Using emergency fallback structure generation...")
            return await self._fallback_structure_generation(domain_analysis)

    async def _fallback_structure_generation(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fallback structure generation that creates valid structure for flexible validation
        """
        try:
            self.logger.info("🔄 Using fallback structure generation...")
            self.logger.info("🎯 Creating structure compatible with flexible validation...")

            # Determine adaptive structure based on domain analysis
            complexity = domain_analysis.get("complexity_level", "intermediate")
            domain = domain_analysis.get("primary_domain", "general")
            
            # Determine milestone count based on complexity
            if complexity == "beginner":
                milestone_count = 3
            elif complexity == "intermediate":
                milestone_count = 4
            elif complexity == "advanced":
                milestone_count = 5
            else:
                milestone_count = 4  # default
            
            self.logger.info(f"📊 Creating {milestone_count} milestones for {complexity} complexity")
            
            # Create adaptive milestones
            milestones = self._create_adaptive_fallback_milestones(domain_analysis, milestone_count)
            
            # Create dependency graph
            dependency_graph = {}
            for i, milestone in enumerate(milestones):
                milestone_id = milestone["milestone_id"]
                if i == 0:
                    dependency_graph[milestone_id] = []
                else:
                    dependency_graph[milestone_id] = [milestones[i-1]["milestone_id"]]
            
            # Create critical path analysis
            critical_path_analysis = {
                "critical_path": [m["milestone_id"] for m in milestones],
                "total_duration": f"{milestone_count * 2 + 2}_weeks",
                "bottlenecks": [milestones[1]["milestone_id"]] if len(milestones) > 1 else [],
                "parallel_opportunities": max(1, milestone_count // 3),
                "risk_factors": ["technical_learning_curve", "time_constraints"]
            }
            
            # Calculate optimization score
            optimization_score = 0.7 + (0.05 * min(3, milestone_count - 2))
            optimization_score = min(1.0, optimization_score)
            
            # Create structure in correct format for flexible validation
            structure_design = {
                "milestone_structure": milestones,
                "dependency_graph": dependency_graph,
                "critical_path_analysis": critical_path_analysis,
                "optimization_score": optimization_score,
                "generation_method": "adaptive_fallback",
                "generation_timestamp": datetime.now().isoformat(),
                "adaptation_info": {
                    "complexity": complexity,
                    "domain": domain,
                    "milestone_count": milestone_count,
                    "fallback_reason": "Gemini generation failed, using adaptive fallback"
                }
            }

            # Return in the correct wrapper format
            result = {"structure_design": structure_design}
            
            self.logger.info(f"✅ Fallback structure created: {milestone_count} milestones, {sum(len(m['tasks']) for m in milestones)} tasks")
            
            return result

        except Exception as e:
            self.logger.error(f"❌ Fallback structure generation failed: {e}")
            return self._create_emergency_fallback_structure(domain_analysis)

    def _create_adaptive_fallback_milestones(self, domain_analysis: Dict[str, Any], milestone_count: int) -> list:
        """Create adaptive milestones for fallback based on domain and complexity"""
        
        domain = domain_analysis.get("primary_domain", "general")
        complexity = domain_analysis.get("complexity_level", "intermediate")
        
        # Photography-specific milestone templates
        if domain == "art_design":
            milestone_templates = [
                {
                    "milestone_id": "M1",
                    "name": "🎯 Nền tảng cơ bản và làm quen với máy ảnh",
                    "description": "Học các khái niệm cơ bản về nhiếp ảnh và làm quen với thiết bị",
                    "base_tasks": [
                        "Tìm hiểu cấu tạo máy ảnh",
                        "Học tam giác phơi sáng",
                        "Thực hành các chế độ chụp",
                        "Học cách cầm máy và tư thế"
                    ]
                },
                {
                    "milestone_id": "M2",
                    "name": "📸 Kỹ thuật chụp ảnh chuyên nghiệp",
                    "description": "Thực hành các kỹ thuật chụp ảnh chuyên nghiệp",
                    "base_tasks": [
                        "Thực hành chụp ảnh chân dung",
                        "Thực hành chụp ảnh phong cảnh",
                        "Thực hành street photography",
                        "Học bố cục và composition",
                        "Thực hành với ánh sáng"
                    ]
                },
                {
                    "milestone_id": "M3",
                    "name": "🎨 Chỉnh sửa ảnh và hậu kỳ",
                    "description": "Học cách chỉnh sửa và tối ưu hóa ảnh",
                    "base_tasks": [
                        "Học Adobe Lightroom",
                        "Học Adobe Photoshop",
                        "Thực hành workflow chỉnh sửa",
                        "Tối ưu hóa và export ảnh"
                    ]
                },
                {
                    "milestone_id": "M4",
                    "name": "💼 Portfolio và chuẩn bị freelance",
                    "description": "Tạo portfolio và chuẩn bị cho freelance",
                    "base_tasks": [
                        "Chọn lọc ảnh tốt nhất",
                        "Tạo portfolio website",
                        "Chuẩn bị pricing và packages",
                        "Marketing và networking"
                    ]
                },
                {
                    "milestone_id": "M5",
                    "name": "🚀 Thực hành và hoàn thiện",
                    "description": "Thực hành tổng hợp và hoàn thiện kỹ năng",
                    "base_tasks": [
                        "Review và đánh giá tiến độ",
                        "Thực hành tổng hợp",
                        "Hoàn thiện portfolio",
                        "Lập kế hoạch phát triển"
                    ]
                }
            ]
        else:
            # Generic milestone templates for other domains
            milestone_templates = [
                {
                    "milestone_id": "M1",
                    "name": "🎯 Nền tảng và chuẩn bị",
                    "description": "Học các khái niệm cơ bản và chuẩn bị",
                    "base_tasks": ["Tìm hiểu cơ bản", "Chuẩn bị công cụ", "Lập kế hoạch", "Thiết lập môi trường"]
                },
                {
                    "milestone_id": "M2",
                    "name": "📚 Học tập và thực hành",
                    "description": "Học tập và thực hành các kỹ năng cốt lõi",
                    "base_tasks": ["Học lý thuyết", "Thực hành cơ bản", "Áp dụng kiến thức", "Đánh giá tiến độ"]
                },
                {
                    "milestone_id": "M3",
                    "name": "🔧 Nâng cao và tối ưu",
                    "description": "Nâng cao kỹ năng và tối ưu hóa",
                    "base_tasks": ["Học nâng cao", "Tối ưu hóa", "Giải quyết vấn đề", "Cải tiến"]
                },
                {
                    "milestone_id": "M4",
                    "name": "💼 Ứng dụng thực tế",
                    "description": "Ứng dụng vào thực tế và dự án",
                    "base_tasks": ["Dự án thực tế", "Kiểm tra chất lượng", "Hoàn thiện", "Triển khai"]
                },
                {
                    "milestone_id": "M5",
                    "name": "🚀 Hoàn thiện và phát triển",
                    "description": "Hoàn thiện và lập kế hoạch phát triển",
                    "base_tasks": ["Đánh giá tổng quan", "Hoàn thiện", "Lập kế hoạch tiếp theo", "Chia sẻ kinh nghiệm"]
                }
            ]
        
        # Select milestones based on count
        selected_templates = milestone_templates[:milestone_count]
        
        # Create milestones with adaptive task counts
        milestones = []
        for i, template in enumerate(selected_templates):
            # Determine task count based on position and complexity
            base_task_count = len(template["base_tasks"])
            
            if complexity == "beginner":
                task_count = max(3, base_task_count - 1)
            elif complexity == "advanced":
                task_count = min(8, base_task_count + 2)
            else:  # intermediate
                task_count = base_task_count
            
            # Create tasks
            tasks = []
            base_tasks = template["base_tasks"]
            
            for j in range(task_count):
                if j < len(base_tasks):
                    task_name = base_tasks[j]
                else:
                    task_name = f"Bài tập bổ sung {j - len(base_tasks) + 1}"
                
                task = {
                    "task_id": f"{template['milestone_id']}_T{j+1}",
                    "name": task_name,
                    "description": f"Chi tiết cho: {task_name}",
                    "estimated_duration": "2-5 days",
                    "complexity": "medium",
                    "dependencies": [],
                    "deliverables": [f"Hoàn thành {task_name.lower()}"]
                }
                tasks.append(task)
            
            # Create milestone
            milestone = {
                "milestone_id": template["milestone_id"],
                "name": template["name"],
                "description": template["description"],
                "position": i + 1,
                "estimated_duration": f"{2 + i}_weeks",
                "dependencies": [f"M{i}"] if i > 0 else [],
                "critical_path": True,
                "tasks": tasks,
                "task_count": len(tasks),
                "complexity_weight": 0.6 + (0.1 * i)
            }
            
            milestones.append(milestone)
        
        return milestones

    def _validate_gemini_structure(self, structure: Dict[str, Any]) -> bool:
        """Validate Gemini-generated structure"""
        try:
            # Check required keys
            required_keys = ["project_overview", "milestones", "optimization_analysis"]
            if not all(key in structure for key in required_keys):
                return False

            # Check milestones
            milestones = structure.get("milestones", [])
            if not isinstance(milestones, list) or len(milestones) == 0:
                return False

            # Check each milestone has tasks
            for milestone in milestones:
                if not isinstance(milestone.get("tasks", []), list):
                    return False
                if len(milestone.get("tasks", [])) == 0:
                    return False

            return True

        except Exception as e:
            self.logger.warning(f"Structure validation error: {e}")
            return False

    def _convert_gemini_to_legacy_format(self, gemini_structure: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Gemini structure format to legacy Agent 2 format"""
        try:
            milestones = []

            for milestone in gemini_structure.get("milestones", []):
                converted_milestone = {
                    "milestone_id": f"milestone_{milestone.get('milestone_number', 1)}",
                    "name": milestone.get("name", "Unnamed Milestone"),
                    "description": milestone.get("description", ""),
                    "typical_duration_weeks": milestone.get("typical_duration_weeks", 2),
                    "complexity_weight": milestone.get("complexity_weight", 1.0),
                    "critical_path": milestone.get("critical_path", True),
                    "learning_objectives": milestone.get("learning_objectives", []),
                    "success_criteria": milestone.get("success_criteria", []),
                    "tasks": []
                }

                # Convert tasks
                for task in milestone.get("tasks", []):
                    converted_task = {
                        "task_id": f"task_{milestone.get('milestone_number', 1)}_{task.get('task_number', 1)}",
                        "name": task.get("name", "Unnamed Task"),
                        "description": task.get("description", ""),
                        "estimated_hours": task.get("estimated_hours", 2),
                        "difficulty_level": task.get("difficulty_level", "intermediate"),
                        "task_type": task.get("task_type", "general"),
                        "dependencies": task.get("dependencies", []),
                        "success_metrics": task.get("success_metrics", []),
                        "resources_needed": task.get("resources_needed", [])
                    }
                    converted_milestone["tasks"].append(converted_task)

                milestones.append(converted_milestone)

            # Create legacy structure format
            return {
                "milestone_structure": milestones,
                "dependency_graph": {},
                "task_dependencies": {},
                "critical_path_analysis": {
                    "critical_milestones": gemini_structure.get("optimization_analysis", {}).get("critical_path", []),
                    "total_critical_duration": gemini_structure.get("project_overview", {}).get("total_duration_weeks", 12),
                    "buffer_time": "1_week"
                },
                "parallel_opportunities": gemini_structure.get("optimization_analysis", {}).get("parallel_opportunities", []),
                "gemini_metadata": gemini_structure.get("generation_metadata", {}),
                "adaptation_notes": gemini_structure.get("adaptation_notes", {})
            }

        except Exception as e:
            self.logger.error(f"❌ Structure conversion failed: {e}")
            return self._create_emergency_fallback_structure({})

    def _create_basic_milestones(self, domain_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create basic milestone structure for fallback"""
        domain = domain_analysis.get("primary_domain", "general")

        return [
            {
                "milestone_id": "milestone_1",
                "name": "Getting Started",
                "description": "Initial setup and goal setting",
                "typical_duration_weeks": 2,
                "complexity_weight": 0.6,
                "critical_path": True,
                "learning_objectives": ["Set up learning environment", "Define clear goals"],
                "success_criteria": ["Environment ready", "Goals documented"],
                "tasks": [
                    {
                        "task_id": "task_1_1",
                        "name": "Initial Assessment",
                        "description": "Complete initial knowledge assessment",
                        "estimated_hours": 2,
                        "difficulty_level": "beginner",
                        "task_type": "assessment",
                        "dependencies": [],
                        "success_metrics": ["Assessment completed"],
                        "resources_needed": ["Assessment materials"]
                    },
                    {
                        "task_id": "task_1_2",
                        "name": "Goal Setting",
                        "description": "Define specific learning objectives",
                        "estimated_hours": 1,
                        "difficulty_level": "beginner",
                        "task_type": "planning",
                        "dependencies": ["task_1_1"],
                        "success_metrics": ["Goals documented"],
                        "resources_needed": ["Planning templates"]
                    }
                ]
            },
            {
                "milestone_id": "milestone_2",
                "name": "Foundation Building",
                "description": "Build core knowledge and skills",
                "typical_duration_weeks": 4,
                "complexity_weight": 0.8,
                "critical_path": True,
                "learning_objectives": ["Master fundamental concepts", "Develop basic skills"],
                "success_criteria": ["Concepts understood", "Skills demonstrated"],
                "tasks": [
                    {
                        "task_id": "task_2_1",
                        "name": "Core Learning",
                        "description": "Study fundamental concepts",
                        "estimated_hours": 6,
                        "difficulty_level": "intermediate",
                        "task_type": "study",
                        "dependencies": [],
                        "success_metrics": ["Concepts mastered"],
                        "resources_needed": ["Learning materials"]
                    },
                    {
                        "task_id": "task_2_2",
                        "name": "Practice Activities",
                        "description": "Apply knowledge through practice",
                        "estimated_hours": 4,
                        "difficulty_level": "intermediate",
                        "task_type": "practice",
                        "dependencies": ["task_2_1"],
                        "success_metrics": ["Practice completed"],
                        "resources_needed": ["Practice materials"]
                    }
                ]
            }
        ]

    def _create_emergency_fallback_structure(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create minimal emergency fallback structure"""
        return {
            "milestone_structure": [
                {
                    "milestone_id": "emergency_milestone",
                    "name": "Learning Project",
                    "description": "Complete learning objectives",
                    "typical_duration_weeks": 8,
                    "complexity_weight": 1.0,
                    "critical_path": True,
                    "learning_objectives": ["Complete learning goals"],
                    "success_criteria": ["Goals achieved"],
                    "tasks": [
                        {
                            "task_id": "emergency_task",
                            "name": "Learning Task",
                            "description": "Complete assigned learning activities",
                            "estimated_hours": 10,
                            "difficulty_level": "intermediate",
                            "task_type": "general",
                            "dependencies": [],
                            "success_metrics": ["Task completed"],
                            "resources_needed": ["Basic materials"]
                        }
                    ]
                }
            ],
            "dependency_graph": {},
            "task_dependencies": {},
            "critical_path_analysis": {
                "critical_milestones": ["emergency_milestone"],
                "total_critical_duration": 8,
                "buffer_time": "1_week"
            },
            "parallel_opportunities": [],
            "optimization_score": {
                "time_efficiency": 0.5,
                "resource_balance": 0.5,
                "risk_mitigation": 0.5,
                "dependency_optimization": 0.5,
                "overall_score": 0.5
            },
            "generation_method": "emergency_fallback",
            "generation_timestamp": datetime.now().isoformat()
        }
