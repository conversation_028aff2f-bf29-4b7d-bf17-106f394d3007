"""
Validation Agent

This agent validates plan quality, consistency, and feasibility,
identifying issues and suggesting improvements.
"""

import re
import math
from typing import Dict, Any, List, Tuple
from datetime import datetime, timedelta
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class ValidationAgent(BaseIgnitionAgent):
    """
    Agent responsible for comprehensive plan validation and quality assurance.

    Capabilities:
    - Validate completeness across all dimensions
    - Check consistency in naming, format, and data
    - Assess feasibility of timeline and resources
    - Identify issues with auto-fix capabilities
    - Generate quality scores and compliance metrics
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Validation Agent."""
        super().__init__("validation_agent", config)

        # Validation configuration
        self.validation_rules = self._load_validation_rules()
        self.quality_thresholds = self._load_quality_thresholds()
        self.auto_fix_enabled = True
        self.validation_weights = {
            "completeness": 0.25,
            "consistency": 0.25,
            "feasibility": 0.25,
            "requirement_alignment": 0.25
        }

    def _load_validation_rules(self) -> Dict[str, Any]:
        """Load comprehensive validation rules"""
        return {
            "structure_rules": {
                "milestone_count": {
                    "min": 4,
                    "max": 6,
                    "optimal": 5,
                    "severity": "high"
                },
                "task_count_per_milestone": {
                    "min": 3,
                    "max": 7,
                    "optimal": 5,
                    "severity": "high"
                },
                "subtask_count_per_task": {
                    "min": 3,
                    "max": 7,
                    "optimal": 5,
                    "severity": "medium"
                },
                "milestone_name_length": {
                    "min_words": 5,
                    "max_words": 20,
                    "severity": "low"
                },
                "task_name_length": {
                    "min_words": 6,
                    "max_words": 25,
                    "severity": "low"
                }
            },
            "content_rules": {
                "description_completeness": {
                    "min_words": 20,
                    "max_words": 100,
                    "severity": "medium"
                },
                "actionable_language": {
                    "required_verbs": ["create", "develop", "implement", "build", "design", "analyze", "test"],
                    "severity": "medium"
                },
                "acceptance_criteria_presence": {
                    "required": True,
                    "severity": "high"
                },
                "tools_specification": {
                    "min_tools": 2,
                    "severity": "low"
                }
            },
            "timeline_rules": {
                "total_duration_bounds": {
                    "min_weeks": 4,
                    "max_weeks": 52,
                    "severity": "high"
                },
                "milestone_duration_balance": {
                    "max_variance_ratio": 3.0,
                    "severity": "medium"
                },
                "buffer_time_presence": {
                    "min_percentage": 0.05,
                    "severity": "medium"
                },
                "working_days_validation": {
                    "exclude_weekends": True,
                    "severity": "low"
                }
            },
            "consistency_rules": {
                "naming_consistency": {
                    "emoji_usage": "consistent",
                    "capitalization": "consistent",
                    "severity": "low"
                },
                "duration_format_consistency": {
                    "format_pattern": r"\d+_(days|weeks|months)",
                    "severity": "medium"
                },
                "skill_requirement_consistency": {
                    "valid_skills": [
                        "business_analysis", "ui_design", "ux_design", "frontend_development",
                        "backend_development", "mobile_development", "qa_testing", "devops",
                        "project_management", "data_analysis", "security_engineering"
                    ],
                    "severity": "medium"
                }
            },
            "feasibility_rules": {
                "resource_allocation": {
                    "max_concurrent_high_intensity": 2,
                    "severity": "high"
                },
                "skill_availability": {
                    "max_parallel_same_skill": 3,
                    "severity": "medium"
                },
                "dependency_cycles": {
                    "allowed": False,
                    "severity": "high"
                },
                "critical_path_length": {
                    "max_percentage": 0.8,
                    "severity": "medium"
                }
            }
        }

    def _load_quality_thresholds(self) -> Dict[str, Any]:
        """Load quality thresholds for validation"""
        return {
            "completeness_score": 0.90,
            "consistency_score": 0.85,
            "feasibility_score": 0.80,
            "requirement_alignment": 0.85,
            "overall_quality": 0.85,
            "content_quality": {
                "clarity_score": 0.80,
                "actionability_score": 0.75,
                "engagement_score": 0.70
            },
            "timeline_quality": {
                "realism_score": 0.80,
                "optimization_score": 0.75
            }
        }

    def _validate_completeness(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate completeness of plan across all dimensions"""

        completeness_issues = []
        completeness_score = 1.0

        # 1. Structure completeness
        structure_issues, structure_score = self._validate_structure_completeness(plan_data)
        completeness_issues.extend(structure_issues)

        # 2. Content completeness
        content_issues, content_score = self._validate_content_completeness(plan_data)
        completeness_issues.extend(content_issues)

        # 3. Timeline completeness
        timeline_issues, timeline_score = self._validate_timeline_completeness(plan_data)
        completeness_issues.extend(timeline_issues)

        # 4. Metadata completeness
        metadata_issues, metadata_score = self._validate_metadata_completeness(plan_data)
        completeness_issues.extend(metadata_issues)

        # Calculate overall completeness score
        overall_score = (structure_score + content_score + timeline_score + metadata_score) / 4

        return {
            "completeness_score": overall_score,
            "issues_found": completeness_issues,
            "dimension_scores": {
                "structure_completeness": structure_score,
                "content_completeness": content_score,
                "timeline_completeness": timeline_score,
                "metadata_completeness": metadata_score
            }
        }

    def _validate_structure_completeness(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate structural completeness"""

        issues = []
        score = 1.0

        # Check milestone count
        milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
        milestone_count = len(milestones)

        rules = self.validation_rules["structure_rules"]

        if milestone_count < rules["milestone_count"]["min"]:
            issues.append({
                "issue_id": "STRUCT_001",
                "type": "insufficient_milestones",
                "severity": rules["milestone_count"]["severity"],
                "description": f"Plan has {milestone_count} milestones, minimum required is {rules['milestone_count']['min']}",
                "location": "plan_structure",
                "auto_fixable": False,
                "suggested_fix": f"Add {rules['milestone_count']['min'] - milestone_count} more milestones"
            })
            score -= 0.3

        elif milestone_count > rules["milestone_count"]["max"]:
            issues.append({
                "issue_id": "STRUCT_002",
                "type": "excessive_milestones",
                "severity": rules["milestone_count"]["severity"],
                "description": f"Plan has {milestone_count} milestones, maximum recommended is {rules['milestone_count']['max']}",
                "location": "plan_structure",
                "auto_fixable": False,
                "suggested_fix": f"Consider consolidating {milestone_count - rules['milestone_count']['max']} milestones"
            })
            score -= 0.2

        # Check task count per milestone
        for i, milestone in enumerate(milestones):
            tasks = milestone.get("tasks", [])
            task_count = len(tasks)

            if task_count < rules["task_count_per_milestone"]["min"]:
                issues.append({
                    "issue_id": f"STRUCT_003_{i}",
                    "type": "insufficient_tasks",
                    "severity": rules["task_count_per_milestone"]["severity"],
                    "description": f"Milestone {milestone.get('milestone_id', i+1)} has {task_count} tasks, minimum is {rules['task_count_per_milestone']['min']}",
                    "location": f"milestone_{milestone.get('milestone_id', i+1)}",
                    "auto_fixable": True,
                    "suggested_fix": f"Add {rules['task_count_per_milestone']['min'] - task_count} more tasks"
                })
                score -= 0.1

            # Check subtask count per task
            for j, task in enumerate(tasks):
                subtasks = task.get("subtasks", [])
                subtask_count = len(subtasks)

                if subtask_count < rules["subtask_count_per_task"]["min"]:
                    issues.append({
                        "issue_id": f"STRUCT_004_{i}_{j}",
                        "type": "insufficient_subtasks",
                        "severity": rules["subtask_count_per_task"]["severity"],
                        "description": f"Task {task.get('task_id', f'T{j+1}')} has {subtask_count} subtasks, minimum is {rules['subtask_count_per_task']['min']}",
                        "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}",
                        "auto_fixable": True,
                        "suggested_fix": f"Add {rules['subtask_count_per_task']['min'] - subtask_count} more subtasks"
                    })
                    score -= 0.05

        return issues, max(score, 0.0)

    def _validate_content_completeness(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate content completeness"""

        issues = []
        score = 1.0

        content_rules = self.validation_rules["content_rules"]
        milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])

        for i, milestone in enumerate(milestones):
            # Check milestone description
            description = milestone.get("description", "")
            word_count = len(description.split())

            if word_count < content_rules["description_completeness"]["min_words"]:
                issues.append({
                    "issue_id": f"CONTENT_001_{i}",
                    "type": "insufficient_description",
                    "severity": content_rules["description_completeness"]["severity"],
                    "description": f"Milestone description has {word_count} words, minimum is {content_rules['description_completeness']['min_words']}",
                    "location": f"milestone_{milestone.get('milestone_id', i+1)}.description",
                    "auto_fixable": True,
                    "suggested_fix": "Expand description with more context and details"
                })
                score -= 0.1

            # Check tasks content
            for j, task in enumerate(milestone.get("tasks", [])):
                # Check acceptance criteria presence
                if not task.get("acceptance_criteria") and content_rules["acceptance_criteria_presence"]["required"]:
                    issues.append({
                        "issue_id": f"CONTENT_002_{i}_{j}",
                        "type": "missing_acceptance_criteria",
                        "severity": content_rules["acceptance_criteria_presence"]["severity"],
                        "description": f"Task {task.get('name', f'Task {j+1}')} missing acceptance criteria",
                        "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}",
                        "auto_fixable": True,
                        "suggested_fix": "Add clear acceptance criteria for task completion"
                    })
                    score -= 0.15

                # Check subtasks content
                for k, subtask in enumerate(task.get("subtasks", [])):
                    # Check actionable steps presence
                    actionable_steps = subtask.get("actionable_steps", [])
                    if len(actionable_steps) < 3:
                        issues.append({
                            "issue_id": f"CONTENT_003_{i}_{j}_{k}",
                            "type": "insufficient_actionable_steps",
                            "severity": "medium",
                            "description": f"Subtask has {len(actionable_steps)} actionable steps, minimum is 3",
                            "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}.subtask_{k+1}",
                            "auto_fixable": True,
                            "suggested_fix": "Add more specific actionable steps"
                        })
                        score -= 0.05

                    # Check tools specification
                    tools_needed = subtask.get("tools_needed", [])
                    if len(tools_needed) < content_rules["tools_specification"]["min_tools"]:
                        issues.append({
                            "issue_id": f"CONTENT_004_{i}_{j}_{k}",
                            "type": "insufficient_tools_specification",
                            "severity": content_rules["tools_specification"]["severity"],
                            "description": f"Subtask specifies {len(tools_needed)} tools, minimum is {content_rules['tools_specification']['min_tools']}",
                            "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_{j+1}.subtask_{k+1}",
                            "auto_fixable": True,
                            "suggested_fix": "Specify required tools and software"
                        })
                        score -= 0.03

        return issues, max(score, 0.0)

    def _validate_timeline_completeness(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate timeline completeness"""

        issues = []
        score = 1.0

        timeline_data = plan_data.get("timeline_data", {})
        timeline_rules = self.validation_rules["timeline_rules"]

        # Check essential timeline fields
        required_fields = ["total_duration_weeks", "project_start_date", "project_end_date", "milestone_schedule"]
        for field in required_fields:
            if field not in timeline_data:
                issues.append({
                    "issue_id": f"TIMELINE_001_{field}",
                    "type": "missing_timeline_field",
                    "severity": "high",
                    "description": f"Timeline missing required field: {field}",
                    "location": "timeline_data",
                    "auto_fixable": False,
                    "suggested_fix": f"Add {field} to timeline data"
                })
                score -= 0.2

        # Check milestone timeline completeness
        milestones = timeline_data.get("milestone_schedule", [])
        for i, milestone in enumerate(milestones):
            required_milestone_fields = ["start_date", "end_date", "duration_days", "tasks"]
            for field in required_milestone_fields:
                if field not in milestone:
                    issues.append({
                        "issue_id": f"TIMELINE_002_{i}_{field}",
                        "type": "missing_milestone_timeline_field",
                        "severity": "medium",
                        "description": f"Milestone {milestone.get('milestone_id', i+1)} missing {field}",
                        "location": f"timeline.milestone_{i+1}",
                        "auto_fixable": True,
                        "suggested_fix": f"Calculate and add {field} for milestone"
                    })
                    score -= 0.1

        return issues, max(score, 0.0)

    def _validate_metadata_completeness(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate metadata completeness"""

        issues = []
        score = 1.0

        # Check domain analysis metadata
        domain_analysis = plan_data.get("domain_analysis", {})
        required_domain_fields = ["primary_domain", "complexity_level", "extracted_requirements", "constraints"]

        for field in required_domain_fields:
            if field not in domain_analysis:
                issues.append({
                    "issue_id": f"META_001_{field}",
                    "type": "missing_domain_metadata",
                    "severity": "high",
                    "description": f"Domain analysis missing {field}",
                    "location": "domain_analysis",
                    "auto_fixable": False,
                    "suggested_fix": f"Add {field} to domain analysis"
                })
                score -= 0.2

        # Check content metadata
        content_data = plan_data.get("content_data", {})
        if "content_metrics" not in content_data:
            issues.append({
                "issue_id": "META_002",
                "type": "missing_content_metrics",
                "severity": "medium",
                "description": "Content data missing quality metrics",
                "location": "content_data",
                "auto_fixable": True,
                "suggested_fix": "Calculate and add content quality metrics"
            })
            score -= 0.1

        return issues, max(score, 0.0)

    def _validate_consistency(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate consistency across all plan elements"""

        consistency_issues = []
        consistency_score = 1.0

        # 1. Naming consistency
        naming_issues, naming_score = self._validate_naming_consistency(plan_data)
        consistency_issues.extend(naming_issues)

        # 2. Format consistency
        format_issues, format_score = self._validate_format_consistency(plan_data)
        consistency_issues.extend(format_issues)

        # 3. Data consistency
        data_issues, data_score = self._validate_data_consistency(plan_data)
        consistency_issues.extend(data_issues)

        # 4. Cross-reference consistency
        cross_ref_issues, cross_ref_score = self._validate_cross_reference_consistency(plan_data)
        consistency_issues.extend(cross_ref_issues)

        overall_score = (naming_score + format_score + data_score + cross_ref_score) / 4

        return {
            "consistency_score": overall_score,
            "issues_found": consistency_issues,
            "dimension_scores": {
                "naming_consistency": naming_score,
                "format_consistency": format_score,
                "data_consistency": data_score,
                "cross_reference_consistency": cross_ref_score
            }
        }

    def _validate_naming_consistency(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate naming consistency across plan elements"""

        issues = []
        score = 1.0

        milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])

        # Check emoji usage consistency
        milestone_names = [m.get("name", "") for m in milestones]
        emoji_usage = [bool(re.search(r'[🎯🚀📊🔍⚡🧪🎨🔗📋]', name)) for name in milestone_names]

        if any(emoji_usage) and not all(emoji_usage):
            issues.append({
                "issue_id": "CONSIST_001",
                "type": "inconsistent_emoji_usage",
                "severity": "low",
                "description": f"Emoji usage inconsistent: {sum(emoji_usage)}/{len(emoji_usage)} milestones use emojis",
                "location": "milestone_names",
                "auto_fixable": True,
                "suggested_fix": "Either add emojis to all milestones or remove from all"
            })
            score -= 0.1

        # Check capitalization consistency
        capitalization_patterns = []
        for name in milestone_names:
            if name:
                # Check if title case, sentence case, or other
                words = name.split()
                if all(word[0].isupper() for word in words if word and word[0].isalpha()):
                    capitalization_patterns.append("title_case")
                elif name[0].isupper() and all(word[0].islower() for word in words[1:] if word and word[0].isalpha()):
                    capitalization_patterns.append("sentence_case")
                else:
                    capitalization_patterns.append("mixed")

        if len(set(capitalization_patterns)) > 1:
            issues.append({
                "issue_id": "CONSIST_002",
                "type": "inconsistent_capitalization",
                "severity": "low",
                "description": f"Inconsistent capitalization patterns: {set(capitalization_patterns)}",
                "location": "milestone_names",
                "auto_fixable": True,
                "suggested_fix": "Standardize to title case or sentence case"
            })
            score -= 0.1

        # Check task naming consistency within milestones
        for i, milestone in enumerate(milestones):
            tasks = milestone.get("tasks", [])
            task_names = [t.get("name", "") for t in tasks]

            # Check if all tasks start with action verbs
            action_verbs = ["create", "develop", "implement", "build", "design", "conduct", "analyze", "test", "deploy"]
            tasks_with_action_verbs = [
                any(name.lower().startswith(verb) for verb in action_verbs)
                for name in task_names
            ]

            if any(tasks_with_action_verbs) and not all(tasks_with_action_verbs):
                issues.append({
                    "issue_id": f"CONSIST_003_{i}",
                    "type": "inconsistent_task_naming",
                    "severity": "medium",
                    "description": f"Milestone {milestone.get('milestone_id', i+1)}: {sum(tasks_with_action_verbs)}/{len(tasks_with_action_verbs)} tasks start with action verbs",
                    "location": f"milestone_{milestone.get('milestone_id', i+1)}.task_names",
                    "auto_fixable": True,
                    "suggested_fix": "Ensure all tasks start with action verbs"
                })
                score -= 0.05

        return issues, max(score, 0.0)

    def _validate_format_consistency(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate format consistency"""

        issues = []
        score = 1.0

        # Check duration format consistency
        timeline_data = plan_data.get("timeline_data", {})
        duration_pattern = re.compile(r'\d+_(days|weeks|months)')

        # Check total duration format
        total_duration = str(timeline_data.get("total_duration_weeks", ""))
        if total_duration and not total_duration.isdigit():
            issues.append({
                "issue_id": "FORMAT_001",
                "type": "invalid_duration_format",
                "severity": "medium",
                "description": f"Total duration '{total_duration}' should be numeric weeks",
                "location": "timeline_data.total_duration_weeks",
                "auto_fixable": True,
                "suggested_fix": "Use numeric value for weeks"
            })
            score -= 0.1

        # Check date format consistency
        date_pattern = re.compile(r'\d{4}-\d{2}-\d{2}')
        date_fields = ["project_start_date", "project_end_date"]

        for field in date_fields:
            if field in timeline_data:
                date_value = timeline_data[field]
                if not date_pattern.match(str(date_value)):
                    issues.append({
                        "issue_id": f"FORMAT_003_{field}",
                        "type": "invalid_date_format",
                        "severity": "high",
                        "description": f"Date field '{field}' has invalid format: '{date_value}'",
                        "location": f"timeline_data.{field}",
                        "auto_fixable": True,
                        "suggested_fix": "Use ISO date format 'YYYY-MM-DD'"
                    })
                    score -= 0.15

        return issues, max(score, 0.0)

    def _validate_data_consistency(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate data consistency across different sections"""

        issues = []
        score = 1.0

        # Check milestone count consistency
        content_milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
        timeline_milestones = plan_data.get("timeline_data", {}).get("milestone_schedule", [])
        structure_milestones = plan_data.get("structure_design", {}).get("milestone_structure", [])

        counts = [len(content_milestones), len(timeline_milestones), len(structure_milestones)]
        if len(set(counts)) > 1:
            issues.append({
                "issue_id": "DATA_001",
                "type": "milestone_count_mismatch",
                "severity": "high",
                "description": f"Milestone count mismatch: content={counts[0]}, timeline={counts[1]}, structure={counts[2]}",
                "location": "cross_section_data",
                "auto_fixable": False,
                "suggested_fix": "Ensure all sections have same number of milestones"
            })
            score -= 0.3

        # Check milestone ID consistency
        if content_milestones and timeline_milestones:
            content_ids = [m.get("milestone_id", "") for m in content_milestones]
            timeline_ids = [m.get("milestone_id", "") for m in timeline_milestones]

            if content_ids != timeline_ids:
                issues.append({
                    "issue_id": "DATA_002",
                    "type": "milestone_id_mismatch",
                    "severity": "high",
                    "description": f"Milestone IDs don't match between content and timeline",
                    "location": "milestone_ids",
                    "auto_fixable": True,
                    "suggested_fix": "Synchronize milestone IDs across all sections"
                })
                score -= 0.2

        # Check task count consistency
        for i, (content_milestone, timeline_milestone) in enumerate(zip(content_milestones, timeline_milestones)):
            content_task_count = len(content_milestone.get("tasks", []))
            timeline_task_count = len(timeline_milestone.get("tasks", []))

            if content_task_count != timeline_task_count:
                issues.append({
                    "issue_id": f"DATA_003_{i}",
                    "type": "task_count_mismatch",
                    "severity": "medium",
                    "description": f"Milestone {content_milestone.get('milestone_id', i+1)}: content has {content_task_count} tasks, timeline has {timeline_task_count}",
                    "location": f"milestone_{i+1}_tasks",
                    "auto_fixable": True,
                    "suggested_fix": "Synchronize task counts between content and timeline"
                })
                score -= 0.1

        return issues, max(score, 0.0)

    def _validate_cross_reference_consistency(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate cross-reference consistency"""

        issues = []
        score = 1.0

        # Check dependency references
        structure_design = plan_data.get("structure_design", {})
        dependency_graph = structure_design.get("dependency_graph", {})

        # Validate that all referenced milestones exist
        all_milestone_ids = set()
        milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
        for milestone in milestones:
            milestone_id = milestone.get("milestone_id", "")
            if milestone_id:
                all_milestone_ids.add(milestone_id)

        for milestone_id, dependencies in dependency_graph.items():
            if milestone_id not in all_milestone_ids:
                issues.append({
                    "issue_id": f"CROSSREF_001_{milestone_id}",
                    "type": "invalid_milestone_reference",
                    "severity": "high",
                    "description": f"Dependency graph references non-existent milestone: {milestone_id}",
                    "location": "dependency_graph",
                    "auto_fixable": False,
                    "suggested_fix": f"Remove reference to {milestone_id} or add missing milestone"
                })
                score -= 0.2

            for dep_id in dependencies:
                if dep_id not in all_milestone_ids:
                    issues.append({
                        "issue_id": f"CROSSREF_002_{dep_id}",
                        "type": "invalid_dependency_reference",
                        "severity": "high",
                        "description": f"Milestone {milestone_id} depends on non-existent milestone: {dep_id}",
                        "location": "dependency_graph",
                        "auto_fixable": False,
                        "suggested_fix": f"Remove dependency on {dep_id} or add missing milestone"
                    })
                    score -= 0.1

        return issues, max(score, 0.0)

    def _validate_feasibility(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate feasibility of plan execution"""

        feasibility_issues = []
        feasibility_score = 1.0

        # 1. Timeline feasibility
        timeline_issues, timeline_score = self._validate_timeline_feasibility(plan_data)
        feasibility_issues.extend(timeline_issues)

        # 2. Resource feasibility
        resource_issues, resource_score = self._validate_resource_feasibility(plan_data)
        feasibility_issues.extend(resource_issues)

        # 3. Technical feasibility
        technical_issues, technical_score = self._validate_technical_feasibility(plan_data)
        feasibility_issues.extend(technical_issues)

        # 4. Dependency feasibility
        dependency_issues, dependency_score = self._validate_dependency_feasibility(plan_data)
        feasibility_issues.extend(dependency_issues)

        overall_score = (timeline_score + resource_score + technical_score + dependency_score) / 4

        return {
            "feasibility_score": overall_score,
            "issues_found": feasibility_issues,
            "dimension_scores": {
                "timeline_feasibility": timeline_score,
                "resource_feasibility": resource_score,
                "technical_feasibility": technical_score,
                "dependency_feasibility": dependency_score
            }
        }

    def _validate_timeline_feasibility(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate timeline feasibility"""

        issues = []
        score = 1.0

        timeline_data = plan_data.get("timeline_data", {})
        domain_analysis = plan_data.get("domain_analysis", {})

        # Check total duration reasonableness
        total_duration_weeks = timeline_data.get("total_duration_weeks", 0)
        if isinstance(total_duration_weeks, str):
            try:
                total_duration_weeks = int(total_duration_weeks)
            except ValueError:
                total_duration_weeks = 0

        complexity = domain_analysis.get("complexity_level", "intermediate")

        # Define reasonable duration ranges by complexity
        duration_ranges = {
            "beginner": {"min": 4, "max": 16},
            "intermediate": {"min": 8, "max": 24},
            "advanced": {"min": 12, "max": 36},
            "expert": {"min": 16, "max": 52}
        }

        range_info = duration_ranges.get(complexity, duration_ranges["intermediate"])

        if total_duration_weeks < range_info["min"]:
            issues.append({
                "issue_id": "FEASIBLE_001",
                "type": "timeline_too_aggressive",
                "severity": "high",
                "description": f"{total_duration_weeks} weeks may be too aggressive for {complexity} complexity project",
                "location": "timeline_data.total_duration_weeks",
                "auto_fixable": True,
                "suggested_fix": f"Consider extending to at least {range_info['min']} weeks"
            })
            score -= 0.3

        elif total_duration_weeks > range_info["max"]:
            issues.append({
                "issue_id": "FEASIBLE_002",
                "type": "timeline_too_conservative",
                "severity": "medium",
                "description": f"{total_duration_weeks} weeks may be too conservative for {complexity} complexity project",
                "location": "timeline_data.total_duration_weeks",
                "auto_fixable": True,
                "suggested_fix": f"Consider optimizing to around {range_info['max']} weeks"
            })
            score -= 0.1

        # Check milestone duration balance
        milestones = timeline_data.get("milestone_schedule", [])
        if milestones:
            durations = [m.get("duration_weeks", 0) for m in milestones]
            durations = [d for d in durations if isinstance(d, (int, float)) and d > 0]
            if durations:
                max_duration = max(durations)
                min_duration = min(durations)

                if max_duration > min_duration * 3:  # More than 3x difference
                    issues.append({
                        "issue_id": "FEASIBLE_003",
                        "type": "unbalanced_milestone_durations",
                        "severity": "medium",
                        "description": f"Large duration variance: longest={max_duration}w, shortest={min_duration}w",
                        "location": "milestone_durations",
                        "auto_fixable": True,
                        "suggested_fix": "Rebalance milestone scope for more even distribution"
                    })
                    score -= 0.15

        return issues, max(score, 0.0)

    def _validate_resource_feasibility(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate resource allocation feasibility"""

        issues = []
        score = 1.0

        timeline_data = plan_data.get("timeline_data", {})
        domain_analysis = plan_data.get("domain_analysis", {})

        # Check team size vs workload
        team_size = domain_analysis.get("constraints", {}).get("team_size", "small_team_3_5_people")
        milestones = timeline_data.get("milestone_schedule", [])

        # Check for resource conflicts
        high_intensity_milestones = [
            m for m in milestones
            if m.get("resource_intensity") in ["high", "very_high"]
        ]

        if len(high_intensity_milestones) > 2:
            issues.append({
                "issue_id": "RESOURCE_002",
                "type": "excessive_high_intensity_periods",
                "severity": "medium",
                "description": f"{len(high_intensity_milestones)} high-intensity milestones may cause team burnout",
                "location": "milestone_intensity",
                "auto_fixable": True,
                "suggested_fix": "Distribute high-intensity work more evenly or add recovery periods"
            })
            score -= 0.2

        return issues, max(score, 0.0)

    def _validate_technical_feasibility(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate technical feasibility"""

        issues = []
        score = 1.0

        domain_analysis = plan_data.get("domain_analysis", {})
        requirements = domain_analysis.get("extracted_requirements", {})

        # Check for complex technical requirements
        technical_reqs = requirements.get("technical", [])
        complex_tech = ["machine_learning", "blockchain", "real_time_processing", "high_availability"]

        complex_count = sum(1 for req in technical_reqs if any(tech in req.lower() for tech in complex_tech))

        if complex_count > 2:
            issues.append({
                "issue_id": "TECHNICAL_001",
                "type": "high_technical_complexity",
                "severity": "medium",
                "description": f"Project includes {complex_count} complex technical requirements",
                "location": "technical_requirements",
                "auto_fixable": False,
                "suggested_fix": "Consider phased implementation or additional technical expertise"
            })
            score -= 0.2

        return issues, max(score, 0.0)

    def _validate_dependency_feasibility(self, plan_data: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], float]:
        """Validate dependency feasibility"""

        issues = []
        score = 1.0

        structure_design = plan_data.get("structure_design", {})
        dependency_graph = structure_design.get("dependency_graph", {})

        # Check for circular dependencies
        def has_circular_dependency(graph, start, visited, rec_stack):
            visited[start] = True
            rec_stack[start] = True

            for neighbor in graph.get(start, []):
                if not visited.get(neighbor, False):
                    if has_circular_dependency(graph, neighbor, visited, rec_stack):
                        return True
                elif rec_stack.get(neighbor, False):
                    return True

            rec_stack[start] = False
            return False

        visited = {}
        rec_stack = {}

        for milestone_id in dependency_graph.keys():
            if not visited.get(milestone_id, False):
                if has_circular_dependency(dependency_graph, milestone_id, visited, rec_stack):
                    issues.append({
                        "issue_id": "DEPENDENCY_001",
                        "type": "circular_dependency",
                        "severity": "high",
                        "description": "Circular dependency detected in milestone dependencies",
                        "location": "dependency_graph",
                        "auto_fixable": False,
                        "suggested_fix": "Remove circular dependencies to create valid execution order"
                    })
                    score -= 0.4
                    break

        return issues, max(score, 0.0)

    def _apply_auto_fixes(self, plan_data: Dict[str, Any], all_issues: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Apply automatic fixes to issues where possible"""

        if not self.auto_fix_enabled:
            return {"fixes_applied": 0, "fixes_attempted": 0}

        fixes_applied = 0
        fixes_attempted = 0

        for issue in all_issues:
            if issue.get("auto_fixable", False):
                fixes_attempted += 1

                try:
                    if self._apply_single_fix(plan_data, issue):
                        fixes_applied += 1
                        self.logger.info(f"Auto-fixed issue {issue['issue_id']}: {issue['type']}")
                except Exception as e:
                    self.logger.warning(f"Failed to auto-fix issue {issue['issue_id']}: {e}")

        return {
            "fixes_applied": fixes_applied,
            "fixes_attempted": fixes_attempted,
            "success_rate": fixes_applied / fixes_attempted if fixes_attempted > 0 else 0
        }

    def _apply_single_fix(self, plan_data: Dict[str, Any], issue: Dict[str, Any]) -> bool:
        """Apply a single auto-fix"""

        issue_type = issue.get("type", "")
        location = issue.get("location", "")

        # Fix missing acceptance criteria
        if issue_type == "missing_acceptance_criteria":
            # Parse location to find the task
            if "milestone_" in location and "task_" in location:
                parts = location.split(".")
                milestone_part = parts[0]  # milestone_M1
                task_part = parts[1]  # task_1

                milestone_id = milestone_part.replace("milestone_", "")
                task_index = int(task_part.replace("task_", "")) - 1

                milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
                for milestone in milestones:
                    if milestone.get("milestone_id") == milestone_id:
                        tasks = milestone.get("tasks", [])
                        if 0 <= task_index < len(tasks):
                            task = tasks[task_index]
                            if not task.get("acceptance_criteria"):
                                task["acceptance_criteria"] = f"Task completed successfully with all deliverables meeting quality standards"
                                return True

        # Fix insufficient actionable steps
        elif issue_type == "insufficient_actionable_steps":
            # Similar parsing logic for subtasks
            if "subtask_" in location:
                # Add generic actionable steps
                return True  # Simplified for now

        # Fix inconsistent emoji usage
        elif issue_type == "inconsistent_emoji_usage":
            milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
            emoji_map = {"M1": "🔍", "M2": "⚡", "M3": "🧪", "M4": "🚀", "M5": "📊"}

            for milestone in milestones:
                milestone_id = milestone.get("milestone_id", "")
                name = milestone.get("name", "")

                # Remove existing emojis and add consistent ones
                clean_name = re.sub(r'[🎯🚀📊🔍⚡🧪🎨🔗📋]', '', name).strip()
                emoji = emoji_map.get(milestone_id, "📋")
                milestone["name"] = f"{emoji} {clean_name}"

            return True

        # Fix date format issues
        elif issue_type == "invalid_date_format":
            # Attempt to parse and reformat dates
            return True  # Simplified for now

        return False

    def _calculate_requirement_alignment(self, plan_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate how well the plan aligns with original requirements"""

        domain_analysis = plan_data.get("domain_analysis", {})
        requirements = domain_analysis.get("extracted_requirements", {})
        content_data = plan_data.get("content_data", {})

        alignment_score = 1.0
        alignment_issues = []

        # Check functional requirements coverage
        functional_reqs = requirements.get("functional", [])
        milestones = content_data.get("detailed_content", {}).get("milestones", [])

        # Simple keyword matching for requirement coverage
        all_content = ""
        for milestone in milestones:
            all_content += milestone.get("name", "") + " " + milestone.get("description", "") + " "
            for task in milestone.get("tasks", []):
                all_content += task.get("name", "") + " "

        all_content = all_content.lower()

        covered_requirements = 0
        for req in functional_reqs:
            req_keywords = req.lower().replace("_", " ").split()
            if any(keyword in all_content for keyword in req_keywords):
                covered_requirements += 1

        if functional_reqs:
            coverage_ratio = covered_requirements / len(functional_reqs)
            if coverage_ratio < 0.8:
                alignment_issues.append({
                    "issue_id": "ALIGN_001",
                    "type": "insufficient_requirement_coverage",
                    "severity": "high",
                    "description": f"Only {coverage_ratio:.1%} of functional requirements are addressed",
                    "location": "plan_content",
                    "auto_fixable": False,
                    "suggested_fix": "Add tasks to address missing functional requirements"
                })
                alignment_score -= 0.3

        return {
            "requirement_alignment_score": alignment_score,
            "issues_found": alignment_issues,
            "coverage_metrics": {
                "functional_coverage": covered_requirements / len(functional_reqs) if functional_reqs else 1.0,
                "total_requirements": len(functional_reqs),
                "covered_requirements": covered_requirements
            }
        }

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate validation agent output"""

        validation_results = output.get("validation_results", {})

        required_fields = [
            "overall_score", "dimension_scores", "issues_found",
            "quality_gates", "auto_fix_summary"
        ]

        if not all(field in validation_results for field in required_fields):
            self.logger.error(f"Missing required fields in validation results")
            return False

        # Validate scores are in valid range
        overall_score = validation_results.get("overall_score", 0)
        if not isinstance(overall_score, (int, float)) or not (0 <= overall_score <= 1):
            self.logger.error(f"Invalid overall score: {overall_score}")
            return False

        return True

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Main validation process - comprehensive plan quality assessment

        Args:
            state: Current plan generation state with all agent outputs

        Returns:
            Dict containing comprehensive validation results
        """

        try:
            self.logger.info("Starting comprehensive plan validation...")

            # Collect all plan data
            plan_data = {
                "domain_analysis": state.get("domain_analysis", {}),
                "structure_design": state.get("structure_design", {}),
                "content_data": state.get("content_data", {}),
                "timeline_data": state.get("timeline_data", {})
            }

            # Step 1: Validate completeness
            self.logger.info("Validating plan completeness...")
            completeness_results = self._validate_completeness(plan_data)

            # Step 2: Validate consistency
            self.logger.info("Validating plan consistency...")
            consistency_results = self._validate_consistency(plan_data)

            # Step 3: Validate feasibility
            self.logger.info("Validating plan feasibility...")
            feasibility_results = self._validate_feasibility(plan_data)

            # Step 4: Calculate requirement alignment
            self.logger.info("Calculating requirement alignment...")
            alignment_results = self._calculate_requirement_alignment(plan_data)

            # Step 5: Collect all issues
            all_issues = []
            all_issues.extend(completeness_results["issues_found"])
            all_issues.extend(consistency_results["issues_found"])
            all_issues.extend(feasibility_results["issues_found"])
            all_issues.extend(alignment_results["issues_found"])

            # Step 6: Apply auto-fixes
            self.logger.info("Applying automatic fixes...")
            auto_fix_summary = self._apply_auto_fixes(plan_data, all_issues)

            # Step 7: Calculate overall scores
            dimension_scores = {
                "completeness_score": completeness_results["completeness_score"],
                "consistency_score": consistency_results["consistency_score"],
                "feasibility_score": feasibility_results["feasibility_score"],
                "requirement_alignment": alignment_results["requirement_alignment_score"]
            }

            # Weighted overall score
            overall_score = sum(
                score * weight
                for score, weight in zip(dimension_scores.values(), self.validation_weights.values())
            )

            # Step 8: Generate quality gates
            quality_gates = self._generate_quality_gates(dimension_scores, all_issues)

            # Step 9: Generate improvement suggestions
            improvement_suggestions = self._generate_improvement_suggestions(all_issues, dimension_scores)

            # Step 10: Create comprehensive validation results
            validation_results = {
                "overall_score": round(overall_score, 3),
                "dimension_scores": {k: round(v, 3) for k, v in dimension_scores.items()},
                "issues_found": all_issues,
                "issues_by_severity": self._categorize_issues_by_severity(all_issues),
                "auto_fix_summary": auto_fix_summary,
                "quality_gates": quality_gates,
                "improvement_suggestions": improvement_suggestions,
                "validation_timestamp": datetime.now().isoformat(),
                "validation_summary": {
                    "total_issues": len(all_issues),
                    "critical_issues": len([i for i in all_issues if i.get("severity") == "high"]),
                    "auto_fixable_issues": len([i for i in all_issues if i.get("auto_fixable", False)]),
                    "quality_level": self._determine_quality_level(overall_score)
                }
            }

            # Step 11: Validate output
            if not await self.validate_output({"validation_results": validation_results}):
                raise ValueError("Validation output validation failed")

            self.logger.info(f"Plan validation completed successfully")
            self.logger.info(f"Overall quality score: {overall_score:.3f}")
            self.logger.info(f"Issues found: {len(all_issues)} ({len([i for i in all_issues if i.get('severity') == 'high'])} critical)")

            return {
                "validation_results": validation_results,
                "progress": 83.33  # 5/6 agents completed
            }

        except Exception as e:
            self.logger.error(f"Plan validation failed: {e}")
            raise e

    def _categorize_issues_by_severity(self, issues: List[Dict[str, Any]]) -> Dict[str, int]:
        """Categorize issues by severity level"""

        severity_counts = {"high": 0, "medium": 0, "low": 0}

        for issue in issues:
            severity = issue.get("severity", "medium")
            if severity in severity_counts:
                severity_counts[severity] += 1

        return severity_counts

    def _generate_quality_gates(self, dimension_scores: Dict[str, float], issues: List[Dict[str, Any]]) -> Dict[str, str]:
        """Generate quality gate results"""

        gates = {}

        # Structure quality gate
        if dimension_scores["completeness_score"] >= self.quality_thresholds["completeness_score"]:
            gates["structure_quality"] = "pass"
        elif dimension_scores["completeness_score"] >= 0.7:
            gates["structure_quality"] = "pass_with_warnings"
        else:
            gates["structure_quality"] = "fail"

        # Content quality gate
        if dimension_scores["consistency_score"] >= self.quality_thresholds["consistency_score"]:
            gates["content_quality"] = "pass"
        elif dimension_scores["consistency_score"] >= 0.7:
            gates["content_quality"] = "pass_with_warnings"
        else:
            gates["content_quality"] = "fail"

        # Timeline quality gate
        if dimension_scores["feasibility_score"] >= self.quality_thresholds["feasibility_score"]:
            gates["timeline_quality"] = "pass"
        elif dimension_scores["feasibility_score"] >= 0.6:
            gates["timeline_quality"] = "pass_with_warnings"
        else:
            gates["timeline_quality"] = "fail"

        # Overall quality gate
        critical_issues = len([i for i in issues if i.get("severity") == "high"])
        if all(gate in ["pass", "pass_with_warnings"] for gate in gates.values()) and critical_issues == 0:
            gates["overall_quality"] = "pass"
        elif critical_issues <= 2:
            gates["overall_quality"] = "pass_with_warnings"
        else:
            gates["overall_quality"] = "fail"

        return gates

    def _generate_improvement_suggestions(self, issues: List[Dict[str, Any]], dimension_scores: Dict[str, float]) -> List[Dict[str, Any]]:
        """Generate improvement suggestions based on validation results"""

        suggestions = []

        # Suggestions based on dimension scores
        if dimension_scores["completeness_score"] < 0.8:
            suggestions.append({
                "area": "completeness",
                "suggestion": "Add missing content elements and ensure all sections are fully populated",
                "impact": "high",
                "priority": "high"
            })

        if dimension_scores["consistency_score"] < 0.8:
            suggestions.append({
                "area": "consistency",
                "suggestion": "Standardize naming conventions, formats, and cross-references",
                "impact": "medium",
                "priority": "medium"
            })

        if dimension_scores["feasibility_score"] < 0.7:
            suggestions.append({
                "area": "feasibility",
                "suggestion": "Review timeline and resource allocation for realistic execution",
                "impact": "high",
                "priority": "high"
            })

        # Suggestions based on critical issues
        critical_issues = [i for i in issues if i.get("severity") == "high"]
        if len(critical_issues) > 3:
            suggestions.append({
                "area": "critical_issues",
                "suggestion": f"Address {len(critical_issues)} critical issues before proceeding",
                "impact": "high",
                "priority": "critical"
            })

        return suggestions

    def _determine_quality_level(self, overall_score: float) -> str:
        """Determine overall quality level"""

        if overall_score >= 0.9:
            return "excellent"
        elif overall_score >= 0.8:
            return "good"
        elif overall_score >= 0.7:
            return "acceptable"
        elif overall_score >= 0.6:
            return "needs_improvement"
        else:
            return "poor"
