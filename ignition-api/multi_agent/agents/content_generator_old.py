"""
Content Generation Agent - Gemini AI Powered

This agent uses Gemini AI to generate detailed, contextual content for all plan elements
including enhanced names, descriptions, and 125 detailed subtasks with actionable steps.
"""

import re
import json
import os
from typing import Dict, Any, List
from datetime import datetime
import google.generativeai as genai
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class ContentGenerationAgent(BaseIgnitionAgent):
    """
    Gemini AI-powered agent responsible for generating detailed content.

    Capabilities:
    - Generate contextual names (7-15 words) with domain-specific emojis using Gemini AI
    - Create engaging descriptions (30-60 words) with motivational context via Gemini AI
    - Generate 125 detailed subtasks (5×5×5) with actionable steps using Gemini AI
    - Add tools needed, time estimates, and expected outcomes via AI analysis
    - Apply intelligent style guidelines and quality assessment through Gemini AI
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Gemini AI-powered Content Generation Agent."""
        super().__init__("content_generator", config)

        # Initialize Gemini AI client
        self._init_gemini_client()

        # Quality thresholds for AI-generated content
        self.quality_thresholds = {
            "clarity_score": 0.85,
            "actionability_score": 0.80,
            "engagement_score": 0.75,
            "consistency_score": 0.90
        }

        # Agent-specific configuration
        self.subtasks_per_task = 5

    def _init_gemini_client(self):
        """Initialize Gemini AI client for content generation"""
        try:
            # Configure Gemini AI
            api_key = self.config.get("google_ai_api_key") or os.getenv("GOOGLE_AI_API_KEY")
            if not api_key:
                raise ValueError("Google AI API key not found in config or environment")

            genai.configure(api_key=api_key)

            # Initialize models
            self.primary_model = genai.GenerativeModel('gemini-2.0-flash-exp')
            self.fallback_model = genai.GenerativeModel('gemini-1.5-pro')

            self.logger.info("✅ Gemini AI client initialized successfully")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini AI client: {e}")
            raise e

    async def _call_gemini_ai(self, prompt: str, operation_type: str) -> Dict[str, Any]:
        """Call Gemini AI with error handling and fallback"""
        try:
            self.logger.info(f"🤖 GEMINI API CALL: {operation_type}")

            # Try primary model first
            try:
                response = await self.primary_model.generate_content_async(prompt)
                result = self._parse_gemini_response(response.text)

                if result:
                    self.logger.info(f"✅ GEMINI PRIMARY SUCCESS: {operation_type}")
                    return result

            except Exception as e:
                self.logger.warning(f"⚠️ GEMINI PRIMARY FAILED: {e}, trying fallback...")

                # Try fallback model
                response = await self.fallback_model.generate_content_async(prompt)
                result = self._parse_gemini_response(response.text)

                if result:
                    self.logger.info(f"✅ GEMINI FALLBACK SUCCESS: {operation_type}")
                    return result

            raise Exception("Both primary and fallback models failed")

        except Exception as e:
            self.logger.error(f"❌ GEMINI COMPLETE FAILURE: {operation_type} - {e}")
            return None

    def _parse_gemini_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Gemini AI response and extract JSON"""
        try:
            # Try to find JSON in response
            import re
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                return json.loads(json_str)
            else:
                # If no JSON found, try to parse entire response
                return json.loads(response_text)

        except Exception as e:
            self.logger.error(f"❌ Failed to parse Gemini response: {e}")
            return None

    async def _generate_milestone_content_with_ai(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced milestone content using Gemini AI"""

        prompt = f"""
You are an expert project manager and content creator. Generate enhanced milestone content for a {domain_analysis['primary_domain']} project.

INPUT MILESTONE:
- Name: {milestone['name']}
- Position: {milestone.get('position', 1)}
- Duration: {milestone.get('estimated_duration', 'unknown')}

DOMAIN CONTEXT:
- Primary Domain: {domain_analysis['primary_domain']}
- Sub-domains: {domain_analysis.get('sub_domains', [])}
- Complexity Level: {domain_analysis['complexity_level']}
- Requirements: {domain_analysis.get('extracted_requirements', {})}

REQUIREMENTS:
1. Enhanced Name: 7-15 words, include relevant emoji, action-oriented, domain-specific
2. Description: 30-60 words, engaging, contextual, explains milestone importance
3. Motivation Message: Encouraging message to inspire progress
4. Success Message: Celebratory message for milestone completion

OUTPUT FORMAT (JSON):
{{
    "enhanced_name": "🔍 Market Research and Technical Foundation Setup for Mobile App Development",
    "description": "Comprehensive analysis of mobile app market landscape, competitor research, and technical architecture establishment to create a solid foundation for successful app development with clear user requirements and technology stack selection.",
    "motivation_message": "🚀 Every successful app starts with understanding the market! This foundation phase sets you up for long-term success.",
    "success_message": "🎉 Foundation Complete! You now have clear market insights and technical direction for your mobile app!"
}}

Generate enhanced milestone content:
"""

        try:
            self.logger.info(f"🤖 GEMINI AI: Generating milestone content for '{milestone['name']}'")

            response = await self._call_gemini_ai(prompt, "milestone_content_generation")

            if response and self._validate_milestone_content(response):
                self.logger.info(f"✅ GEMINI SUCCESS: Enhanced milestone content generated")
                return response
            else:
                self.logger.warning(f"⚠️ GEMINI FAILED: Invalid milestone content, using fallback")
                return self._create_fallback_milestone_content(milestone, domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ GEMINI ERROR: Milestone content generation failed: {e}")
            return self._create_fallback_milestone_content(milestone, domain_analysis)

    async def _generate_task_content_with_ai(self, task: Dict[str, Any], milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced task content using Gemini AI"""

        prompt = f"""
You are an expert project manager and content creator. Generate enhanced task content for a {domain_analysis['primary_domain']} project.

INPUT TASK:
- Name: {task['name']}
- Task ID: {task.get('task_id', 'unknown')}
- Estimated Duration: {task.get('estimated_duration', 'unknown')}
- Complexity: {task.get('complexity', 'medium')}

MILESTONE CONTEXT:
- Milestone Name: {milestone['name']}
- Milestone Position: {milestone.get('position', 1)}

DOMAIN CONTEXT:
- Primary Domain: {domain_analysis['primary_domain']}
- Sub-domains: {domain_analysis.get('sub_domains', [])}
- Complexity Level: {domain_analysis['complexity_level']}
- Requirements: {domain_analysis.get('extracted_requirements', {})}

REQUIREMENTS:
1. Enhanced Name: 8-20 words, start with action verb, include domain-specific terms
2. Description: 40-80 words, engaging, explains what to do and why it's important
3. Acceptance Criteria: Clear deliverables and success criteria
4. Why Important: Explanation of task importance for project success
5. Success Tips: 3-4 practical tips for completing the task successfully

OUTPUT FORMAT (JSON):
{{
    "enhanced_name": "🎯 Conduct comprehensive market analysis for fashion e-commerce mobile applications",
    "description": "Dive deep into the fashion e-commerce mobile app landscape! Research your target audience, analyze successful competitors, and identify unique opportunities that will make your solution stand out in the competitive marketplace.",
    "acceptance_criteria": "Comprehensive market research report with competitor analysis, user personas, and strategic recommendations for product differentiation and market positioning.",
    "why_important": "Market research is the foundation of any successful project. Understanding your competition and target audience ensures you build something people actually want and need.",
    "success_tips": [
        "Focus on user pain points and unmet needs in the market",
        "Look for gaps in competitor offerings that you can exploit",
        "Interview real potential users to validate your assumptions",
        "Document everything systematically for future reference"
    ]
}}

Generate enhanced task content:
"""

        try:
            self.logger.info(f"🤖 GEMINI AI: Generating task content for '{task['name']}'")

            response = await self._call_gemini_ai(prompt, "task_content_generation")

            if response and self._validate_task_content(response):
                self.logger.info(f"✅ GEMINI SUCCESS: Enhanced task content generated")
                return response
            else:
                self.logger.warning(f"⚠️ GEMINI FAILED: Invalid task content, using fallback")
                return self._create_fallback_task_content(task, domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ GEMINI ERROR: Task content generation failed: {e}")
            return self._create_fallback_task_content(task, domain_analysis)

    async def _generate_subtasks_with_ai(self, task: Dict[str, Any], domain_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate 5 detailed subtasks using Gemini AI - CORE FEATURE"""

        prompt = f"""
You are an expert project manager and task breakdown specialist. Generate exactly 5 detailed subtasks for a {domain_analysis['primary_domain']} project task.

INPUT TASK:
- Name: {task['name']}
- Task ID: {task.get('task_id', 'unknown')}
- Estimated Duration: {task.get('estimated_duration', 'unknown')}

DOMAIN CONTEXT:
- Primary Domain: {domain_analysis['primary_domain']}
- Sub-domains: {domain_analysis.get('sub_domains', [])}
- Complexity Level: {domain_analysis['complexity_level']}
- Requirements: {domain_analysis.get('extracted_requirements', {})}

REQUIREMENTS FOR EACH SUBTASK:
1. Name: 6-15 words, specific and measurable
2. Description: 20-40 words, clear explanation of what to do
3. Actionable Steps: 3-5 specific, concrete steps to complete the subtask
4. Tools Needed: List of specific software, tools, or resources required
5. Time Estimate: Realistic hours needed (format: "X hours")
6. Expected Outcome: Clear deliverable or result
7. Difficulty: "easy", "medium", or "hard"

OUTPUT FORMAT (JSON):
{{
    "subtasks": [
        {{
            "name": "Survey and analyze top 15 fashion e-commerce mobile applications",
            "description": "Download, test, and document features, user flows, and design patterns of leading fashion e-commerce apps to understand market standards.",
            "actionable_steps": [
                "Identify top 15 fashion e-commerce apps through app store rankings and industry reports",
                "Download and thoroughly test each application on both iOS and Android platforms",
                "Document key features, user flows, and unique design patterns for each app",
                "Create detailed comparison matrix highlighting strengths and weaknesses",
                "Identify market gaps and opportunities for differentiation"
            ],
            "tools_needed": ["Smartphone (iOS/Android)", "Screen recording software", "Spreadsheet application", "Note-taking app"],
            "time_estimate": "16 hours",
            "expected_outcome": "Comprehensive competitor analysis spreadsheet with feature matrix and strategic insights",
            "difficulty": "medium"
        }},
        {{
            "name": "Conduct 8-12 user interviews and create detailed personas",
            "description": "Interview potential users to understand needs, pain points, and preferences for developing accurate user personas.",
            "actionable_steps": [
                "Recruit 8-12 diverse participants from target demographic",
                "Prepare structured interview questions focusing on shopping behaviors",
                "Conduct 30-45 minute interviews via video calls or in-person",
                "Analyze responses to identify common patterns and pain points",
                "Create 3-5 detailed user personas with demographics and motivations"
            ],
            "tools_needed": ["Video conferencing software", "Interview script template", "Recording software", "Persona template"],
            "time_estimate": "20 hours",
            "expected_outcome": "3-5 detailed user personas with validated insights from real user interviews",
            "difficulty": "medium"
        }},
        {{
            "name": "Research fashion e-commerce industry best practices and trends",
            "description": "Study current industry standards, emerging technologies, and best practices specific to fashion e-commerce mobile applications.",
            "actionable_steps": [
                "Research latest fashion e-commerce industry reports and trend analyses",
                "Study mobile commerce best practices and UX design guidelines",
                "Analyze successful case studies of fashion app launches and growth",
                "Identify emerging technologies relevant to fashion e-commerce",
                "Document key findings and recommendations for implementation"
            ],
            "tools_needed": ["Industry research databases", "Design pattern libraries", "Case study resources", "Documentation tools"],
            "time_estimate": "12 hours",
            "expected_outcome": "Industry best practices report with technology recommendations and implementation guidelines",
            "difficulty": "easy"
        }},
        {{
            "name": "Create comprehensive competitive analysis with SWOT assessment",
            "description": "Develop detailed competitive landscape analysis with strengths, weaknesses, opportunities, and threats for strategic positioning.",
            "actionable_steps": [
                "Identify direct and indirect competitors in fashion e-commerce space",
                "Analyze each competitor's strengths, weaknesses, market position",
                "Identify market opportunities and potential threats",
                "Create SWOT matrix comparing your solution against competitors",
                "Develop competitive positioning strategy and differentiation plan"
            ],
            "tools_needed": ["SWOT analysis template", "Competitive intelligence tools", "Market research data", "Strategy frameworks"],
            "time_estimate": "14 hours",
            "expected_outcome": "SWOT analysis matrix with competitive positioning strategy and differentiation recommendations",
            "difficulty": "medium"
        }},
        {{
            "name": "Develop technical requirements specification with acceptance criteria",
            "description": "Create detailed technical requirements document with clear acceptance criteria and measurable success metrics.",
            "actionable_steps": [
                "Gather functional and non-functional requirements from stakeholders",
                "Define detailed technical specifications for each feature",
                "Create clear acceptance criteria for each requirement",
                "Establish measurable success metrics and testing criteria",
                "Review and validate requirements with development team and stakeholders"
            ],
            "tools_needed": ["Requirements documentation template", "Stakeholder input sessions", "Technical specification tools"],
            "time_estimate": "18 hours",
            "expected_outcome": "Technical requirements document with detailed acceptance criteria and validation metrics",
            "difficulty": "hard"
        }}
    ]
}}

Generate exactly 5 detailed subtasks:
"""

        try:
            self.logger.info(f"🤖 GEMINI AI: Generating 5 subtasks for '{task['name']}'")

            response = await self._call_gemini_ai(prompt, "subtask_generation")

            if response and self._validate_subtasks_content(response):
                subtasks = response.get("subtasks", [])
                if len(subtasks) == 5:
                    self.logger.info(f"✅ GEMINI SUCCESS: Generated 5 detailed subtasks")
                    return subtasks
                else:
                    self.logger.warning(f"⚠️ GEMINI FAILED: Generated {len(subtasks)} subtasks instead of 5, using fallback")
                    return self._create_fallback_subtasks(task, domain_analysis)
            else:
                self.logger.warning(f"⚠️ GEMINI FAILED: Invalid subtasks content, using fallback")
                return self._create_fallback_subtasks(task, domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ GEMINI ERROR: Subtask generation failed: {e}")
            return self._create_fallback_subtasks(task, domain_analysis)

    def _validate_milestone_content(self, content: Dict[str, Any]) -> bool:
        """Validate Gemini-generated milestone content"""
        required_fields = ["enhanced_name", "description", "motivation_message", "success_message"]
        return all(field in content and content[field] for field in required_fields)

    def _validate_task_content(self, content: Dict[str, Any]) -> bool:
        """Validate Gemini-generated task content"""
        required_fields = ["enhanced_name", "description", "acceptance_criteria", "why_important", "success_tips"]
        return all(field in content and content[field] for field in required_fields)

    def _validate_subtasks_content(self, content: Dict[str, Any]) -> bool:
        """Validate Gemini-generated subtasks content"""
        if "subtasks" not in content:
            return False

        subtasks = content["subtasks"]
        if not isinstance(subtasks, list) or len(subtasks) != 5:
            return False

        required_fields = ["name", "description", "actionable_steps", "tools_needed", "time_estimate", "expected_outcome", "difficulty"]

        for subtask in subtasks:
            if not all(field in subtask and subtask[field] for field in required_fields):
                return False

            # Validate actionable_steps is a list with 3-5 items
            if not isinstance(subtask["actionable_steps"], list) or not (3 <= len(subtask["actionable_steps"]) <= 5):
                return False

        return True

    def _create_fallback_milestone_content(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback milestone content when Gemini fails"""
        domain = domain_analysis["primary_domain"]

        return {
            "enhanced_name": f"🎯 {milestone['name']} for {domain.replace('_', ' ').title()}",
            "description": f"Important milestone for {domain.replace('_', ' ')} project development with focus on quality deliverables and stakeholder requirements.",
            "motivation_message": "🚀 Great progress! This milestone brings you closer to your project goals.",
            "success_message": "🎉 Milestone completed successfully! Excellent work on achieving this important goal."
        }

    def _create_fallback_task_content(self, task: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback task content when Gemini fails"""
        domain = domain_analysis["primary_domain"]

        return {
            "enhanced_name": f"🔧 {task['name']} for {domain.replace('_', ' ').title()}",
            "description": f"Complete {task['name'].lower()} with attention to detail and quality standards for {domain.replace('_', ' ')} project.",
            "acceptance_criteria": "Task completed according to specifications with all deliverables meeting quality standards.",
            "why_important": "This task is essential for project success and contributes to overall quality and functionality.",
            "success_tips": [
                "Plan your approach carefully before starting",
                "Focus on quality over speed",
                "Test your work thoroughly",
                "Document your process and results"
            ]
        }

    def _create_fallback_subtasks(self, task: Dict[str, Any], domain_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create fallback subtasks when Gemini fails"""
        domain = domain_analysis["primary_domain"]
        task_name = task["name"]

        return [
            {
                "name": f"Plan and prepare for {task_name.lower()} implementation",
                "description": "Analyze requirements, gather resources, and create detailed implementation plan",
                "actionable_steps": [
                    "Review task requirements and acceptance criteria",
                    "Gather necessary tools and resources",
                    "Create step-by-step implementation plan",
                    "Set up workspace and development environment"
                ],
                "tools_needed": ["Planning tools", "Documentation", "Development environment"],
                "time_estimate": "6 hours",
                "expected_outcome": "Comprehensive implementation plan with resource allocation",
                "difficulty": "easy"
            },
            {
                "name": f"Execute core {task_name.lower()} development work",
                "description": "Implement main functionality following best practices and quality standards",
                "actionable_steps": [
                    "Follow implementation plan step by step",
                    "Apply best practices and coding standards",
                    "Implement core functionality with error handling",
                    "Conduct initial testing and validation"
                ],
                "tools_needed": ["Development tools", "Testing frameworks", "Code editors"],
                "time_estimate": "16 hours",
                "expected_outcome": "Core functionality implemented and working",
                "difficulty": "medium"
            },
            {
                "name": f"Test and validate {task_name.lower()} implementation",
                "description": "Comprehensive testing to ensure quality and functionality meets requirements",
                "actionable_steps": [
                    "Create comprehensive test cases",
                    "Execute manual and automated tests",
                    "Validate against acceptance criteria",
                    "Fix any identified issues or bugs"
                ],
                "tools_needed": ["Testing tools", "Bug tracking", "Validation frameworks"],
                "time_estimate": "8 hours",
                "expected_outcome": "Fully tested and validated implementation",
                "difficulty": "medium"
            },
            {
                "name": f"Document and review {task_name.lower()} results",
                "description": "Create comprehensive documentation and conduct thorough review process",
                "actionable_steps": [
                    "Document implementation details and decisions",
                    "Create user guides and technical documentation",
                    "Conduct peer review and feedback collection",
                    "Update project documentation and knowledge base"
                ],
                "tools_needed": ["Documentation tools", "Review platforms", "Knowledge management"],
                "time_estimate": "6 hours",
                "expected_outcome": "Complete documentation and review reports",
                "difficulty": "easy"
            },
            {
                "name": f"Finalize and deliver {task_name.lower()} outcomes",
                "description": "Complete final preparations and deliver all task outcomes to stakeholders",
                "actionable_steps": [
                    "Conduct final quality assurance checks",
                    "Prepare delivery package with all deliverables",
                    "Present results to stakeholders",
                    "Gather feedback and plan next steps"
                ],
                "tools_needed": ["Presentation tools", "Delivery platforms", "Feedback collection"],
                "time_estimate": "4 hours",
                "expected_outcome": "Delivered task outcomes with stakeholder approval",
                "difficulty": "easy"
            }
        ]

    # OLD TEMPLATE METHODS REMOVED - NOW USING GEMINI AI
    # All milestone content generation is now powered by Gemini AI

    # OLD TEMPLATE METHODS REMOVED - ALL CONTENT NOW GENERATED BY GEMINI AI

    def _generate_enhanced_description(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> str:
        """Generate detailed, contextual milestone description"""

        base_description = milestone.get("description", "")
        domain = domain_analysis["primary_domain"]
        requirements = domain_analysis["extracted_requirements"]

        # Add domain-specific context
        domain_context = {
            "mobile_app_development": "mobile application with intuitive user experience and robust performance",
            "web_development": "web platform with responsive design and optimal performance across all devices",
            "e_commerce": "e-commerce solution with secure payment processing and comprehensive inventory management"
        }

        context = domain_context.get(domain, "technical solution with modern architecture")

        # Add requirement-specific details
        requirement_details = []
        if "payment_processing" in requirements.get("functional", []):
            requirement_details.append("secure payment gateway integration")
        if "user_management" in requirements.get("functional", []):
            requirement_details.append("comprehensive user authentication system")
        if "security" in requirements.get("non_functional", []):
            requirement_details.append("robust security measures and data protection")

        # Combine into enhanced description
        enhanced_desc = f"{base_description} This critical phase focuses on building a {context}"

        if requirement_details:
            enhanced_desc += f" with special emphasis on {', '.join(requirement_details[:2])}"

        enhanced_desc += ". The milestone ensures a solid foundation for subsequent development phases and establishes clear success criteria for seamless project progression."

        return enhanced_desc

    def _generate_success_message(self, milestone_name: str) -> str:
        """Generate celebratory success message"""

        success_messages = {
            "research": "🎉 Foundation Complete! Market insights gathered and technical roadmap established!",
            "development": "🚀 Core Features Live! Your application is taking shape beautifully!",
            "feature": "✨ Advanced Features Ready! Your solution now offers exceptional value!",
            "testing": "✅ Quality Assured! Your solution is robust, secure, and ready for users!",
            "deployment": "🌟 Successfully Launched! Your project is live and making an impact!",
            "setup": "⚙️ Setup Complete! Development environment ready for amazing things!",
            "integration": "🔗 Integration Success! All systems working together seamlessly!"
        }

        for keyword, message in success_messages.items():
            if keyword in milestone_name.lower():
                return message

        return "🎯 Milestone Achieved! Excellent progress toward your project goals!"

    def _generate_motivation_message(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> str:
        """Generate motivational message for milestone"""

        position = milestone.get("position", 1)

        motivation_templates = {
            1: "🚀 Every successful project starts with solid research and planning. You're building the blueprint for success!",
            2: "⚡ Now the real magic begins! This is where your vision starts becoming reality.",
            3: "🔥 You're in the heart of development! This is where your unique value proposition comes to life.",
            4: "🧪 Quality is what separates good projects from great ones. You're ensuring excellence!",
            5: "🌟 The finish line is in sight! You're about to share your creation with the world!"
        }

        return motivation_templates.get(position, "💪 Keep pushing forward! You're making excellent progress!")

    def _enhance_task_content(self, task: Dict[str, Any], milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance task with detailed, actionable content"""

        enhanced_task = task.copy()

        # Enhance task name
        enhanced_task["name"] = self._generate_enhanced_task_name(
            task["name"], domain_analysis
        )

        # Generate detailed description
        enhanced_task["description"] = self._generate_task_description(
            task, milestone, domain_analysis
        )

        # Add acceptance criteria
        enhanced_task["acceptance_criteria"] = self._generate_acceptance_criteria(
            task["name"]
        )

        # Add why it's important
        enhanced_task["why_important"] = self._explain_task_importance(
            task["name"], domain_analysis
        )

        # Add success tips
        enhanced_task["success_tips"] = self._generate_success_tips(task["name"])

        # Add effort estimation
        enhanced_task["estimated_effort"] = self._generate_effort_description(
            task.get("estimated_duration", "1_week")
        )

        # Add difficulty indicator
        enhanced_task["difficulty_level"] = self._generate_difficulty_indicator(
            task.get("complexity", "medium")
        )

        return enhanced_task

    def _generate_enhanced_task_name(self, base_name: str, domain_analysis: Dict[str, Any]) -> str:
        """Generate enhanced task name with action verbs and specificity"""

        domain = domain_analysis["primary_domain"]

        # Add emoji based on task type
        task_emojis = {
            "research": "🎯",
            "analysis": "📊",
            "design": "🎨",
            "development": "⚡",
            "implementation": "🔧",
            "testing": "🧪",
            "integration": "🔗",
            "deployment": "🚀",
            "setup": "⚙️",
            "optimization": "🔧",
            "security": "🔒"
        }

        emoji = "📌"  # Default
        for keyword, emoji_char in task_emojis.items():
            if keyword in base_name.lower():
                emoji = emoji_char
                break

        # Add domain context if not already present
        domain_terms = {
            "mobile_app_development": "mobile applications",
            "web_development": "web platforms",
            "e_commerce": "e-commerce systems"
        }

        domain_term = domain_terms.get(domain, "applications")

        # Enhance with specificity
        if not any(term in base_name.lower() for term in domain_terms.values()):
            enhanced_name = f"{emoji} {base_name} for {domain_term}"
        else:
            enhanced_name = f"{emoji} {base_name}"

        return enhanced_name

    def _generate_task_description(self, task: Dict[str, Any], milestone: Dict[str, Any],
                                  domain_analysis: Dict[str, Any]) -> str:
        """Generate detailed, actionable task description"""

        base_name = task["name"]
        domain = domain_analysis["primary_domain"]
        requirements = domain_analysis["extracted_requirements"]

        # Base description templates
        description_templates = {
            "research": "Dive deep into the {domain} landscape! Research your target audience, analyze successful competitors, and identify unique opportunities that will make your solution stand out in the marketplace.",
            "design": "Create intuitive and visually appealing {domain} interface with smooth navigation, responsive design, and engaging user interactions that delight users and drive engagement.",
            "development": "Build robust and scalable {domain} functionality with clean code architecture, comprehensive error handling, and optimal performance characteristics for exceptional user experience.",
            "testing": "Ensure your {domain} solution meets the highest quality standards through comprehensive testing, performance optimization, and security validation to deliver a flawless user experience.",
            "integration": "Seamlessly connect all {domain} components and third-party services to create a cohesive, well-functioning system that provides smooth user workflows.",
            "deployment": "Successfully launch your {domain} solution to production with proper monitoring, backup systems, and post-launch support procedures for reliable operation.",
            "setup": "Establish a solid foundation for your {domain} project with proper development environment, tools, and processes that enable efficient and effective development.",
            "optimization": "Fine-tune your {domain} solution for optimal performance, user experience, and scalability to ensure long-term success and user satisfaction."
        }

        # Determine template based on task type
        template = "Build high-quality {domain} functionality with attention to detail and exceptional user experience."
        for keyword, desc_template in description_templates.items():
            if keyword in base_name.lower():
                template = desc_template
                break

        # Fill in domain context
        domain_context = {
            "mobile_app_development": "mobile app",
            "web_development": "web application",
            "e_commerce": "e-commerce platform"
        }

        context = domain_context.get(domain, "application")
        description = template.format(domain=context)

        # Add requirement-specific details
        if "payment_processing" in requirements.get("functional", []):
            if "integration" in base_name.lower() or "payment" in base_name.lower():
                description += " Focus on secure payment processing with multiple payment methods, fraud protection, and seamless checkout experience."

        if "security" in requirements.get("non_functional", []):
            if "security" in base_name.lower() or "testing" in base_name.lower():
                description += " Implement robust security measures including data encryption, secure authentication, and vulnerability protection."

        return description

    def _generate_acceptance_criteria(self, task_name: str) -> str:
        """Generate clear acceptance criteria for task"""

        criteria_templates = {
            "research": "Comprehensive research report with competitor analysis, market insights, user personas, and actionable recommendations for project direction",
            "design": "Complete design mockups with user flow diagrams, responsive layouts, design system documentation, and stakeholder approval",
            "development": "Fully functional feature with comprehensive unit tests, error handling, performance optimization, and code review approval",
            "testing": "Test results documentation with bug reports, performance metrics, security assessment, and quality assurance sign-off",
            "integration": "Successfully integrated system with all components working together, comprehensive testing completed, and performance validated",
            "deployment": "Live system running in production with monitoring active, backup procedures tested, and post-deployment checklist completed",
            "setup": "Development environment fully configured, all tools installed and tested, team onboarded, and development workflow established",
            "optimization": "Performance improvements documented, metrics showing improvement, user experience enhanced, and scalability validated"
        }

        for keyword, criteria in criteria_templates.items():
            if keyword in task_name.lower():
                return criteria

        return "Task completed according to specifications with all deliverables meeting quality standards and stakeholder requirements"

    def _explain_task_importance(self, task_name: str, domain_analysis: Dict[str, Any]) -> str:
        """Explain why this task is important for project success"""

        importance_explanations = {
            "research": "Market research is the foundation of any successful project. Understanding your competition and target audience ensures you build something people actually want and need.",
            "design": "Great design is what separates good products from exceptional ones. Users judge your entire solution within seconds of their first interaction.",
            "development": "This is where your vision becomes reality. Quality development practices ensure your solution is reliable, scalable, and maintainable.",
            "testing": "Testing prevents costly mistakes and ensures user satisfaction. A bug-free launch builds trust and credibility with your audience.",
            "integration": "Seamless integration creates a cohesive user experience. When all parts work together smoothly, users can focus on getting value from your solution.",
            "deployment": "A successful launch is the culmination of all your hard work. Proper deployment ensures your solution reaches users reliably and performs well.",
            "setup": "A solid foundation enables everything that follows. Proper setup prevents technical debt and enables efficient development.",
            "optimization": "Optimization ensures your solution can grow with your success. Performance and scalability are key to long-term viability."
        }

        for keyword, explanation in importance_explanations.items():
            if keyword in task_name.lower():
                return explanation

        return "This task is a critical building block that contributes to the overall success and quality of your project."

    def _generate_success_tips(self, task_name: str) -> List[str]:
        """Generate practical success tips for the task"""

        tips_database = {
            "research": [
                "Start with your target users - interview at least 5-10 potential customers",
                "Use tools like SimilarWeb or App Annie to analyze competitor traffic and downloads",
                "Create a feature comparison matrix to identify gaps in the market",
                "Document everything - insights you think you'll remember often get forgotten"
            ],
            "design": [
                "Follow the 3-click rule - users should reach any feature within 3 clicks",
                "Test your designs with real users before finalizing",
                "Maintain consistency in colors, fonts, and spacing throughout",
                "Design for mobile first, then scale up to larger screens"
            ],
            "development": [
                "Write tests before writing code - it saves time in the long run",
                "Use version control for everything, commit early and often",
                "Follow coding standards and document complex logic",
                "Optimize for readability - code is read more often than it's written"
            ],
            "testing": [
                "Test on real devices, not just simulators or browsers",
                "Create test cases that cover both happy paths and edge cases",
                "Automate repetitive tests to save time and ensure consistency",
                "Get feedback from users outside your development team"
            ],
            "integration": [
                "Test integrations in a staging environment first",
                "Have rollback plans ready for each integration",
                "Monitor system performance during and after integration",
                "Document all API endpoints and data flows"
            ],
            "deployment": [
                "Deploy during low-traffic hours to minimize user impact",
                "Have monitoring and alerting set up before going live",
                "Prepare a communication plan for users about new features",
                "Keep the previous version ready for quick rollback if needed"
            ]
        }

        for keyword, tips in tips_database.items():
            if keyword in task_name.lower():
                return tips[:3]  # Return top 3 tips

        return [
            "Break the task into smaller, manageable pieces",
            "Set clear milestones and celebrate small wins",
            "Don't hesitate to ask for help when you need it"
        ]

    def _generate_effort_description(self, estimated_duration: str) -> str:
        """Generate human-readable effort description"""

        effort_descriptions = {
            "1_day": "Quick task ⚡ - Can be completed in a focused work session",
            "2_days": "Short sprint 🏃 - Perfect for a couple of productive days",
            "3_days": "Mini project 📋 - Requires planning but very manageable",
            "1_week": "Standard task 📅 - Typical development work, plan accordingly",
            "2_weeks": "Major effort 💪 - Significant work requiring sustained focus",
            "3_weeks": "Big project 🎯 - Major milestone requiring careful planning",
            "1_month": "Epic task 🚀 - Large scope requiring team coordination"
        }

        return effort_descriptions.get(estimated_duration, "Moderate effort 📊 - Plan your time accordingly")

    def _generate_difficulty_indicator(self, complexity: str) -> Dict[str, Any]:
        """Generate difficulty indicator with helpful context"""

        difficulty_mapping = {
            "low": {
                "level": "Beginner Friendly 🟢",
                "description": "Great for getting started or building confidence",
                "skills_needed": "Basic technical knowledge",
                "support_level": "Minimal guidance needed"
            },
            "medium": {
                "level": "Intermediate Challenge 🟡",
                "description": "Requires some experience but very achievable",
                "skills_needed": "Solid technical foundation",
                "support_level": "Some guidance may be helpful"
            },
            "high": {
                "level": "Advanced Task 🟠",
                "description": "Complex work requiring expertise and careful planning",
                "skills_needed": "Strong technical skills and experience",
                "support_level": "Expert guidance recommended"
            }
        }

        return difficulty_mapping.get(complexity, difficulty_mapping["medium"])

    def _generate_subtasks(self, task: Dict[str, Any], domain_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate 5 detailed subtasks for each task"""

        task_name = task["name"]
        task_id = task["task_id"]
        domain = domain_analysis["primary_domain"]

        # Get subtask templates based on task type
        subtask_templates = self._get_subtask_templates(task_name, domain)

        subtasks = []
        for i in range(5):
            subtask_template = subtask_templates[i] if i < len(subtask_templates) else self._generate_generic_subtask(task_name, i+1)

            subtask = {
                "subtask_id": f"{task_id}_ST{i+1}",
                "name": subtask_template["name"],
                "description": subtask_template["description"],
                "expected_outcome": subtask_template["expected_outcome"],
                "tools_needed": subtask_template.get("tools_needed", ["Standard development tools"]),
                "estimated_hours": subtask_template.get("estimated_hours", 8),
                "difficulty": subtask_template.get("difficulty", "medium"),
                "actionable_steps": subtask_template.get("actionable_steps", [
                    "Plan the approach and gather requirements",
                    "Execute the main work with attention to detail",
                    "Review and validate the results",
                    "Document the process and outcomes"
                ]),
                "success_criteria": subtask_template.get("success_criteria", "Deliverable meets specifications and quality standards")
            }

            subtasks.append(subtask)

        return subtasks

    def _get_subtask_templates(self, task_name: str, domain: str) -> List[Dict[str, Any]]:
        """Get appropriate subtask templates based on task type"""

        # Research task subtasks
        if "research" in task_name.lower() or "analysis" in task_name.lower():
            return [
                {
                    "name": f"Survey and analyze top 10-15 {domain} applications in the market",
                    "description": "Download, test, and document features, user flows, and design patterns of leading competitors",
                    "expected_outcome": "Comprehensive competitor analysis spreadsheet with feature matrix",
                    "tools_needed": ["App stores", "Screen recording tools", "Spreadsheet software"],
                    "estimated_hours": 16,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Identify top competitors through app store rankings and market research",
                        "Download and thoroughly test each application",
                        "Document key features, user flows, and design patterns",
                        "Create comparison matrix highlighting strengths and weaknesses",
                        "Identify opportunities for differentiation"
                    ],
                    "success_criteria": "Complete analysis of 10+ competitors with actionable insights"
                },
                {
                    "name": "Conduct 8-12 user interviews and create detailed personas",
                    "description": "Interview potential users to understand needs, pain points, and preferences for persona development",
                    "expected_outcome": "3-5 detailed user personas with demographics, goals, and pain points",
                    "tools_needed": ["Video conferencing", "Interview scripts", "Persona templates"],
                    "estimated_hours": 20,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Develop interview script with open-ended questions",
                        "Recruit diverse participants from target demographic",
                        "Conduct interviews and record key insights",
                        "Analyze patterns and create persona profiles",
                        "Validate personas with additional stakeholder input"
                    ],
                    "success_criteria": "Rich personas based on real user insights and validated by team"
                },
                {
                    "name": f"Research {domain} industry best practices and emerging trends",
                    "description": "Study industry standards, emerging technologies, and best practices relevant to the project",
                    "expected_outcome": "Industry best practices report with technology recommendations",
                    "tools_needed": ["Industry reports", "Technical documentation", "Expert interviews"],
                    "estimated_hours": 12,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Identify key industry publications and thought leaders",
                        "Review recent industry reports and trend analyses",
                        "Research emerging technologies and their adoption rates",
                        "Document best practices and implementation guidelines",
                        "Create recommendations for project application"
                    ],
                    "success_criteria": "Comprehensive industry analysis with actionable recommendations"
                },
                {
                    "name": "Create comprehensive competitive analysis with SWOT assessment",
                    "description": "Develop detailed competitive landscape analysis with strengths, weaknesses, opportunities, and threats",
                    "expected_outcome": "SWOT analysis matrix with competitive positioning strategy",
                    "tools_needed": ["Analysis frameworks", "Market data", "Competitive intelligence tools"],
                    "estimated_hours": 14,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Map competitive landscape and identify key players",
                        "Analyze competitor strengths and weaknesses",
                        "Identify market opportunities and threats",
                        "Develop competitive positioning strategy",
                        "Create actionable recommendations for differentiation"
                    ],
                    "success_criteria": "Clear competitive strategy with differentiation opportunities identified"
                },
                {
                    "name": "Develop technical requirements specification with acceptance criteria",
                    "description": "Create detailed technical requirements document with clear acceptance criteria and success metrics",
                    "expected_outcome": "Technical requirements document with measurable acceptance criteria",
                    "tools_needed": ["Requirements templates", "Stakeholder input", "Technical documentation"],
                    "estimated_hours": 18,
                    "difficulty": "high",
                    "actionable_steps": [
                        "Gather requirements from all stakeholders",
                        "Define functional and non-functional requirements",
                        "Create detailed acceptance criteria for each requirement",
                        "Establish success metrics and testing criteria",
                        "Review and validate requirements with team"
                    ],
                    "success_criteria": "Complete requirements document approved by all stakeholders"
                }
            ]

        # Development task subtasks
        elif "development" in task_name.lower() or "implementation" in task_name.lower():
            return [
                {
                    "name": "Set up development environment and project structure with best practices",
                    "description": "Configure development tools, establish project architecture, and implement coding standards",
                    "expected_outcome": "Fully configured development environment with project scaffolding",
                    "tools_needed": ["IDE", "Version control", "Package managers", "Build tools"],
                    "estimated_hours": 12,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Install and configure development tools and dependencies",
                        "Set up version control repository with branching strategy",
                        "Create project structure following best practices",
                        "Configure build tools and automation scripts",
                        "Document setup process for team members"
                    ],
                    "success_criteria": "Development environment ready with all team members onboarded"
                },
                {
                    "name": "Implement core functionality with comprehensive unit testing coverage",
                    "description": "Develop main features with robust error handling and comprehensive test coverage",
                    "expected_outcome": "Working core features with 90%+ test coverage",
                    "tools_needed": ["Development framework", "Testing libraries", "Code coverage tools"],
                    "estimated_hours": 24,
                    "difficulty": "high",
                    "actionable_steps": [
                        "Design and implement core business logic",
                        "Add comprehensive error handling and validation",
                        "Write unit tests for all functions and methods",
                        "Achieve high test coverage and validate edge cases",
                        "Refactor code for maintainability and performance"
                    ],
                    "success_criteria": "Core functionality working with comprehensive test coverage"
                },
                {
                    "name": "Create database schema design with optimization and indexing strategies",
                    "description": "Design efficient database structure with proper relationships, indexes, and performance optimization",
                    "expected_outcome": "Optimized database schema with migration scripts",
                    "tools_needed": ["Database management system", "Schema design tools", "Migration tools"],
                    "estimated_hours": 16,
                    "difficulty": "high",
                    "actionable_steps": [
                        "Analyze data requirements and relationships",
                        "Design normalized database schema",
                        "Create indexes for query optimization",
                        "Implement database migration scripts",
                        "Test schema with sample data and queries"
                    ],
                    "success_criteria": "Efficient database schema with optimal performance characteristics"
                },
                {
                    "name": "Develop API endpoints with authentication and comprehensive validation",
                    "description": "Create secure, well-documented API endpoints with proper authentication and data validation",
                    "expected_outcome": "Secure API with comprehensive documentation",
                    "tools_needed": ["API framework", "Authentication libraries", "Documentation tools"],
                    "estimated_hours": 20,
                    "difficulty": "high",
                    "actionable_steps": [
                        "Design RESTful API structure and endpoints",
                        "Implement authentication and authorization",
                        "Add comprehensive input validation and sanitization",
                        "Create API documentation with examples",
                        "Test all endpoints with various scenarios"
                    ],
                    "success_criteria": "Secure, well-documented API ready for frontend integration"
                },
                {
                    "name": "Integrate frontend components with backend services and error handling",
                    "description": "Connect user interface with backend APIs, implementing proper error handling and user feedback",
                    "expected_outcome": "Fully integrated application with seamless user experience",
                    "tools_needed": ["Frontend framework", "HTTP clients", "State management"],
                    "estimated_hours": 18,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Connect frontend components to backend APIs",
                        "Implement proper error handling and user feedback",
                        "Add loading states and progress indicators",
                        "Test integration with various network conditions",
                        "Optimize performance and user experience"
                    ],
                    "success_criteria": "Seamless integration with excellent user experience"
                }
            ]

        # Testing task subtasks
        elif "testing" in task_name.lower() or "quality" in task_name.lower():
            return [
                {
                    "name": "Execute comprehensive automated and manual testing suites",
                    "description": "Run complete testing protocols including unit, integration, and end-to-end tests",
                    "expected_outcome": "Complete test results with bug reports and quality metrics",
                    "tools_needed": ["Testing frameworks", "Automation tools", "Bug tracking"],
                    "estimated_hours": 20,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Execute all automated test suites",
                        "Perform manual testing of critical user flows",
                        "Document bugs and issues with reproduction steps",
                        "Verify fixes and retest affected areas",
                        "Generate comprehensive test reports"
                    ],
                    "success_criteria": "All critical bugs resolved and quality standards met"
                },
                {
                    "name": "Perform security vulnerability assessment and penetration testing",
                    "description": "Conduct thorough security testing to identify and address potential vulnerabilities",
                    "expected_outcome": "Security assessment report with vulnerability fixes",
                    "tools_needed": ["Security testing tools", "Vulnerability scanners"],
                    "estimated_hours": 16,
                    "difficulty": "high",
                    "actionable_steps": [
                        "Run automated security scans",
                        "Perform manual penetration testing",
                        "Test authentication and authorization systems",
                        "Validate data encryption and protection",
                        "Document and fix security vulnerabilities"
                    ],
                    "success_criteria": "No critical security vulnerabilities remaining"
                },
                {
                    "name": "Conduct performance optimization and load testing analysis",
                    "description": "Test application performance under various load conditions and optimize bottlenecks",
                    "expected_outcome": "Performance optimization report with improvements implemented",
                    "tools_needed": ["Load testing tools", "Performance monitoring", "Profiling tools"],
                    "estimated_hours": 18,
                    "difficulty": "high",
                    "actionable_steps": [
                        "Set up load testing scenarios",
                        "Execute performance tests under various conditions",
                        "Identify performance bottlenecks and issues",
                        "Implement optimizations and improvements",
                        "Validate performance improvements with retesting"
                    ],
                    "success_criteria": "Application meets performance requirements under expected load"
                },
                {
                    "name": "Implement user acceptance testing with real user feedback collection",
                    "description": "Conduct UAT with actual users to validate functionality and gather improvement feedback",
                    "expected_outcome": "UAT results with user feedback and improvement recommendations",
                    "tools_needed": ["UAT protocols", "Feedback collection tools", "User recruitment"],
                    "estimated_hours": 14,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Recruit representative users for testing",
                        "Create UAT scenarios and test scripts",
                        "Facilitate user testing sessions",
                        "Collect and analyze user feedback",
                        "Prioritize and implement critical improvements"
                    ],
                    "success_criteria": "Positive user acceptance with critical feedback addressed"
                },
                {
                    "name": "Complete final quality assurance and release preparation",
                    "description": "Final QA review, documentation updates, and release readiness verification",
                    "expected_outcome": "Release-ready application with complete documentation",
                    "tools_needed": ["QA checklists", "Documentation tools", "Release management"],
                    "estimated_hours": 12,
                    "difficulty": "medium",
                    "actionable_steps": [
                        "Complete final QA checklist review",
                        "Update all documentation and user guides",
                        "Verify release readiness criteria",
                        "Prepare rollback procedures",
                        "Get final stakeholder approval for release"
                    ],
                    "success_criteria": "Application ready for production release with stakeholder approval"
                }
            ]

        # Default generic subtasks
        else:
            return self._get_generic_subtasks(task_name)

    def _get_generic_subtasks(self, task_name: str) -> List[Dict[str, Any]]:
        """Generate generic subtasks for any task type"""
        return [
            {
                "name": f"Plan and prepare for {task_name.lower()} implementation",
                "description": "Analyze requirements, gather resources, and create detailed implementation plan",
                "expected_outcome": "Comprehensive implementation plan with resource allocation",
                "tools_needed": ["Planning tools", "Documentation"],
                "estimated_hours": 6,
                "difficulty": "low"
            },
            {
                "name": f"Execute core {task_name.lower()} development work",
                "description": "Implement main functionality following best practices and quality standards",
                "expected_outcome": "Core functionality implemented and working",
                "tools_needed": ["Development tools", "Testing frameworks"],
                "estimated_hours": 16,
                "difficulty": "medium"
            },
            {
                "name": f"Test and validate {task_name.lower()} implementation",
                "description": "Comprehensive testing to ensure quality and functionality requirements are met",
                "expected_outcome": "Validated implementation meeting all requirements",
                "tools_needed": ["Testing tools", "Validation frameworks"],
                "estimated_hours": 8,
                "difficulty": "medium"
            },
            {
                "name": f"Optimize and refine {task_name.lower()} performance",
                "description": "Performance tuning and optimization for best user experience",
                "expected_outcome": "Optimized implementation with improved performance",
                "tools_needed": ["Performance tools", "Monitoring systems"],
                "estimated_hours": 6,
                "difficulty": "medium"
            },
            {
                "name": f"Document and finalize {task_name.lower()} deliverables",
                "description": "Complete documentation and prepare deliverables for handoff",
                "expected_outcome": "Complete documentation and finalized deliverables",
                "tools_needed": ["Documentation tools", "Review processes"],
                "estimated_hours": 4,
                "difficulty": "low"
            }
        ]

    def _generate_generic_subtask(self, task_name: str, subtask_number: int) -> Dict[str, Any]:
        """Generate a generic subtask when templates are insufficient"""
        return {
            "name": f"Complete subtask {subtask_number} for {task_name.lower()}",
            "description": f"Execute specific work required for subtask {subtask_number} of {task_name.lower()}",
            "expected_outcome": f"Subtask {subtask_number} deliverables completed",
            "tools_needed": ["Standard development tools"],
            "estimated_hours": 8,
            "difficulty": "medium"
        }

    def _assess_content_quality(self, content_data: Dict[str, Any]) -> Dict[str, float]:
        """Assess quality of generated content across multiple dimensions"""

        milestones = content_data.get("milestones", [])

        # Calculate clarity score
        clarity_score = self._calculate_clarity_score(milestones)

        # Calculate actionability score
        actionability_score = self._calculate_actionability_score(milestones)

        # Calculate engagement score
        engagement_score = self._calculate_engagement_score(milestones)

        # Calculate consistency score
        consistency_score = self._calculate_consistency_score(milestones)

        return {
            "clarity_score": clarity_score,
            "actionability_score": actionability_score,
            "engagement_score": engagement_score,
            "consistency_score": consistency_score,
            "overall_score": (clarity_score + actionability_score + engagement_score + consistency_score) / 4
        }

    def _calculate_clarity_score(self, milestones: List[Dict[str, Any]]) -> float:
        """Calculate clarity score based on description quality"""

        total_score = 0
        total_items = 0

        for milestone in milestones:
            # Check milestone description clarity
            desc = milestone.get("description", "")
            if len(desc.split()) >= 25:  # Minimum word count
                total_score += 1
            total_items += 1

            # Check task descriptions
            for task in milestone.get("tasks", []):
                task_desc = task.get("description", "")
                if len(task_desc.split()) >= 20:
                    total_score += 1
                total_items += 1

                # Check subtask descriptions
                for subtask in task.get("subtasks", []):
                    subtask_desc = subtask.get("description", "")
                    if len(subtask_desc.split()) >= 10:
                        total_score += 1
                    total_items += 1

        return total_score / total_items if total_items > 0 else 0.0

    def _calculate_actionability_score(self, milestones: List[Dict[str, Any]]) -> float:
        """Calculate actionability score based on specific, measurable content"""

        total_score = 0
        total_items = 0

        action_indicators = ["implement", "create", "develop", "test", "analyze", "design", "build", "execute"]

        for milestone in milestones:
            for task in milestone.get("tasks", []):
                # Check if task has actionable steps
                if "actionable_steps" in task:
                    total_score += 1
                total_items += 1

                # Check task name for action verbs
                task_name = task.get("name", "").lower()
                if any(verb in task_name for verb in action_indicators):
                    total_score += 0.5

                for subtask in task.get("subtasks", []):
                    # Check if subtask has specific steps
                    if "actionable_steps" in subtask and len(subtask["actionable_steps"]) >= 3:
                        total_score += 1
                    total_items += 1

        return min(total_score / total_items, 1.0) if total_items > 0 else 0.0

    def _calculate_engagement_score(self, milestones: List[Dict[str, Any]]) -> float:
        """Calculate engagement score based on motivational elements"""

        total_score = 0
        total_items = 0

        engagement_indicators = ["🎯", "🚀", "✨", "💪", "🎉", "⚡", "🔥"]

        for milestone in milestones:
            # Check for emojis and motivational language
            milestone_name = milestone.get("name", "")
            if any(emoji in milestone_name for emoji in engagement_indicators):
                total_score += 1
            total_items += 1

            # Check for success messages
            if "success_message" in milestone:
                total_score += 1

            # Check for motivation messages
            if "motivation_message" in milestone:
                total_score += 1

            for task in milestone.get("tasks", []):
                task_name = task.get("name", "")
                if any(emoji in task_name for emoji in engagement_indicators):
                    total_score += 0.5
                total_items += 1

                # Check for success tips
                if "success_tips" in task and len(task["success_tips"]) >= 2:
                    total_score += 1

        return min(total_score / total_items, 1.0) if total_items > 0 else 0.0

    def _calculate_consistency_score(self, milestones: List[Dict[str, Any]]) -> float:
        """Calculate consistency score based on format and structure"""

        total_score = 0
        total_checks = 0

        # Check milestone structure consistency
        required_milestone_fields = ["name", "description", "tasks"]
        for milestone in milestones:
            for field in required_milestone_fields:
                if field in milestone:
                    total_score += 1
                total_checks += 1

        # Check task structure consistency
        required_task_fields = ["name", "description", "subtasks"]
        for milestone in milestones:
            for task in milestone.get("tasks", []):
                for field in required_task_fields:
                    if field in task:
                        total_score += 1
                    total_checks += 1

                # Check subtask count consistency (should be 5)
                subtasks = task.get("subtasks", [])
                if len(subtasks) == 5:
                    total_score += 1
                total_checks += 1

        return total_score / total_checks if total_checks > 0 else 0.0

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate content generation output"""

        content_data = output.get("content_data", {})
        detailed_content = content_data.get("detailed_content", {})

        # Check required structure
        if "milestones" not in detailed_content:
            self.logger.error("Missing milestones in content data")
            return False

        milestones = detailed_content["milestones"]

        # Validate milestone count
        if len(milestones) != 5:
            self.logger.error(f"Expected 5 milestones, got {len(milestones)}")
            return False

        # Validate each milestone
        for milestone in milestones:
            # Check required fields
            required_fields = ["milestone_id", "name", "description", "tasks"]
            if not all(field in milestone for field in required_fields):
                self.logger.error(f"Missing required fields in milestone {milestone.get('milestone_id', 'unknown')}")
                return False

            # Check task count
            tasks = milestone.get("tasks", [])
            if len(tasks) != 5:
                self.logger.error(f"Expected 5 tasks per milestone, got {len(tasks)} in {milestone['milestone_id']}")
                return False

            # Validate each task
            for task in tasks:
                # Check required fields
                task_required_fields = ["task_id", "name", "description", "subtasks"]
                if not all(field in task for field in task_required_fields):
                    self.logger.error(f"Missing required fields in task {task.get('task_id', 'unknown')}")
                    return False

                # Check subtask count
                subtasks = task.get("subtasks", [])
                if len(subtasks) != 5:
                    self.logger.error(f"Expected 5 subtasks per task, got {len(subtasks)} in {task['task_id']}")
                    return False

                # Validate each subtask
                for subtask in subtasks:
                    subtask_required_fields = ["subtask_id", "name", "description", "expected_outcome"]
                    if not all(field in subtask for field in subtask_required_fields):
                        self.logger.error(f"Missing required fields in subtask {subtask.get('subtask_id', 'unknown')}")
                        return False

        # Check quality metrics
        content_metrics = content_data.get("content_metrics", {})
        for metric, threshold in self.quality_thresholds.items():
            if metric in content_metrics and content_metrics[metric] < threshold:
                self.logger.warning(f"Quality metric {metric} below threshold: {content_metrics[metric]} < {threshold}")

        return True

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Main content generation process

        Args:
            state: Current plan generation state with structure_design

        Returns:
            Dict containing detailed content data
        """

        structure_design = state["structure_design"]
        domain_analysis = state["domain_analysis"]

        try:
            self.logger.info("🚀 AGENT 3 STARTING: Gemini AI-powered content generation...")
            self.logger.info(f"📥 INPUT: Processing {len(structure_design['milestone_structure'])} milestones")

            # Step 1: Generate enhanced content using Gemini AI
            enhanced_milestones = []
            milestones = structure_design["milestone_structure"]

            for milestone in milestones:
                self.logger.info(f"🎯 PROCESSING: {milestone['milestone_id']} - {milestone['name']}")

                # Generate enhanced milestone content with Gemini AI
                milestone_content = await self._generate_milestone_content_with_ai(milestone, domain_analysis)

                # Merge with original milestone data
                enhanced_milestone = milestone.copy()
                enhanced_milestone.update({
                    "name": milestone_content["enhanced_name"],
                    "description": milestone_content["description"],
                    "motivation_message": milestone_content["motivation_message"],
                    "success_message": milestone_content["success_message"]
                })

                # Generate enhanced tasks with Gemini AI
                enhanced_tasks = []
                for task in milestone.get("tasks", []):
                    self.logger.info(f"🔧 PROCESSING TASK: {task.get('task_id', 'unknown')} - {task['name']}")

                    # Generate enhanced task content with Gemini AI
                    task_content = await self._generate_task_content_with_ai(task, milestone, domain_analysis)

                    # Merge with original task data
                    enhanced_task = task.copy()
                    enhanced_task.update({
                        "name": task_content["enhanced_name"],
                        "description": task_content["description"],
                        "acceptance_criteria": task_content["acceptance_criteria"],
                        "why_important": task_content["why_important"],
                        "success_tips": task_content["success_tips"]
                    })

                    # Generate 5 detailed subtasks with Gemini AI (CORE FEATURE)
                    self.logger.info(f"🎯 GENERATING SUBTASKS: Creating 5 detailed subtasks with Gemini AI...")
                    enhanced_task["subtasks"] = await self._generate_subtasks_with_ai(task, domain_analysis)

                    subtask_count = len(enhanced_task["subtasks"])
                    self.logger.info(f"✅ SUBTASKS GENERATED: {subtask_count} subtasks created")

                    enhanced_tasks.append(enhanced_task)

                enhanced_milestone["tasks"] = enhanced_tasks
                enhanced_milestones.append(enhanced_milestone)

                milestone_tasks = len(enhanced_tasks)
                milestone_subtasks = sum(len(t["subtasks"]) for t in enhanced_tasks)
                self.logger.info(f"✅ MILESTONE COMPLETE: {milestone_tasks} tasks, {milestone_subtasks} subtasks")

            # Step 2: Create detailed content structure
            total_milestones = len(enhanced_milestones)
            total_tasks = sum(len(m["tasks"]) for m in enhanced_milestones)
            total_subtasks = sum(
                len(t["subtasks"])
                for m in enhanced_milestones
                for t in m["tasks"]
            )

            detailed_content = {
                "milestones": enhanced_milestones,
                "total_milestones": total_milestones,
                "total_tasks": total_tasks,
                "total_subtasks": total_subtasks,
                "content_generation_timestamp": datetime.now().isoformat(),
                "domain_context": domain_analysis["primary_domain"],
                "complexity_level": domain_analysis["complexity_level"],
                "generation_method": "gemini_ai"
            }

            self.logger.info(f"📊 CONTENT STRUCTURE: {total_milestones} milestones, {total_tasks} tasks, {total_subtasks} subtasks")

            # Step 3: Assess content quality using AI-enhanced metrics
            self.logger.info("📈 QUALITY ASSESSMENT: Analyzing Gemini-generated content...")
            content_metrics = self._assess_content_quality(detailed_content)

            self.logger.info(f"✅ QUALITY SCORES: Overall={content_metrics.get('overall_score', 0):.2f}")
            self.logger.info(f"   Clarity: {content_metrics.get('clarity_score', 0):.2f}")
            self.logger.info(f"   Actionability: {content_metrics.get('actionability_score', 0):.2f}")
            self.logger.info(f"   Engagement: {content_metrics.get('engagement_score', 0):.2f}")
            self.logger.info(f"   Consistency: {content_metrics.get('consistency_score', 0):.2f}")

            # Step 4: Create final content data
            content_data = {
                "detailed_content": detailed_content,
                "content_metrics": content_metrics,
                "gemini_ai_generated": True,
                "style_guidelines_applied": True,
                "engagement_elements_included": True,
                "actionability_verified": True
            }

            # Step 5: Validate output
            if not await self.validate_output({"content_data": content_data}):
                raise ValueError("Gemini AI content generation validation failed")

            self.logger.info("🎉 AGENT 3 SUCCESS: Gemini AI content generation completed!")
            self.logger.info(f"📊 FINAL OUTPUT: {total_subtasks} detailed subtasks across {total_tasks} tasks")
            self.logger.info(f"🤖 GENERATION METHOD: 100% Gemini AI-powered content creation")

            return {
                "content_data": content_data,
                "progress": 50.0  # 3/6 agents completed
            }

        except Exception as e:
            self.logger.error(f"Content generation failed: {e}")
            raise e
