"""
Content Generation Agent - Simplified Gemini AI

This agent uses Gemini AI to generate detailed content for project milestones and tasks.
Simplified approach with better error handling and cleaner prompts.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, List
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class ContentGenerationAgent(BaseIgnitionAgent):
    """
    Simplified Content Generation Agent - Clean Gemini AI approach
    
    Purpose: Generate detailed content for milestones and tasks
    Input: domain_analysis and structure_design from Agents 1-2
    Output: enhanced_content with detailed subtasks and resources
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Content Generation Agent with simplified Gemini AI."""
        super().__init__("content_generator", config)
        
        # Gemini AI configuration
        self.temperature = 0.6
        self.max_tokens = 3000
        self.timeout = 45
        
        # Initialize Gemini client
        self._init_gemini_client()
        
        self.logger.info("📝 Content Generation Agent initialized - Simplified Gemini AI")

    def _init_gemini_client(self):
        """Initialize Gemini AI client with proper API keys"""
        try:
            import google.generativeai as genai
            
            # Agent 3 uses API keys 5 and 6
            api_key_5 = os.getenv('GOOGLE_AI_API_KEY_5')
            api_key_6 = os.getenv('GOOGLE_AI_API_KEY_6')
            
            if not api_key_5:
                # Fallback to other keys if 5-6 not available
                api_key_5 = os.getenv('GOOGLE_AI_API_KEY_1')
                api_key_6 = os.getenv('GOOGLE_AI_API_KEY_2')
            
            if not api_key_5:
                raise ValueError("No Gemini API keys available for Agent 3")
            
            # Configure primary client
            genai.configure(api_key=api_key_5)
            self.primary_model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Store fallback info
            self.fallback_api_key = api_key_6 if api_key_6 else api_key_5
            self.fallback_model_name = 'gemini-2.5-flash'
            
            self.logger.info("✅ Gemini AI client initialized successfully for Agent 3")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini client: {e}")
            raise e

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Process structure design to generate detailed content using Gemini AI
        
        Args:
            state: PlanGenerationState with domain_analysis and structure_design
            
        Returns:
            Dict with success status and enhanced_content
        """
        domain_analysis = state.get("domain_analysis", {})
        structure_design = state.get("structure_design", {})
        
        if not domain_analysis or not structure_design:
            error_msg = "Missing domain_analysis or structure_design in state"
            self.logger.error(f"❌ {error_msg}")
            return {"success": False, "error": error_msg}
        
        self.logger.info("🚀 AGENT 3 STARTING: Content generation with Gemini AI")
        
        milestones = structure_design.get("milestone_structure", [])
        domain = domain_analysis.get("primary_domain", "unknown")
        
        self.logger.info(f"🎯 Domain: {domain}")
        self.logger.info(f"📋 Milestones to process: {len(milestones)}")
        
        try:
            # Generate content for all milestones
            enhanced_content = await self._generate_content_with_gemini(
                domain_analysis, structure_design
            )
            
            if enhanced_content and self._validate_content(enhanced_content):
                total_content_items = sum(
                    len(milestone.get("content_items", []))
                    for milestone in enhanced_content.get("enhanced_milestones", [])
                )
                
                self.logger.info(f"✅ SUCCESS: Generated {total_content_items} content items")
                
                return {
                    "success": True,
                    "enhanced_content": enhanced_content,
                    "metadata": {
                        "agent": "ContentGenerationAgent",
                        "method": "gemini_ai_simplified",
                        "timestamp": datetime.now().isoformat(),
                        "content_items_count": total_content_items
                    }
                }
            else:
                # Create minimal fallback content
                self.logger.warning("⚠️ Gemini failed, creating minimal fallback content")
                fallback_content = self._create_minimal_fallback_content(
                    domain_analysis, structure_design
                )
                
                return {
                    "success": True,
                    "enhanced_content": fallback_content,
                    "metadata": {
                        "agent": "ContentGenerationAgent",
                        "method": "minimal_fallback",
                        "timestamp": datetime.now().isoformat()
                    }
                }
                
        except Exception as e:
            error_msg = f"Content generation error: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            # Return fallback content even on error
            fallback_content = self._create_minimal_fallback_content(
                domain_analysis, structure_design
            )
            
            return {
                "success": True,  # Still return success with fallback
                "enhanced_content": fallback_content,
                "metadata": {
                    "agent": "ContentGenerationAgent",
                    "method": "error_fallback",
                    "timestamp": datetime.now().isoformat(),
                    "error": error_msg
                }
            }

    async def _generate_content_with_gemini(self, domain_analysis: Dict[str, Any], structure_design: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate detailed content using Gemini AI with simplified approach
        
        Args:
            domain_analysis: Domain analysis from Agent 1
            structure_design: Structure design from Agent 2
            
        Returns:
            Dict with enhanced content or None if failed
        """
        try:
            domain = domain_analysis.get("primary_domain", "general_project")
            complexity = domain_analysis.get("complexity_level", "intermediate")
            milestones = structure_design.get("milestone_structure", [])
            
            # Process milestones in smaller batches to avoid timeouts
            enhanced_milestones = []
            batch_size = 2  # Process 2 milestones at a time
            
            for i in range(0, len(milestones), batch_size):
                batch = milestones[i:i + batch_size]
                self.logger.info(f"📝 Processing milestone batch {i//batch_size + 1}/{(len(milestones) + batch_size - 1)//batch_size}")
                
                batch_result = await self._process_milestone_batch(batch, domain, complexity)
                
                if batch_result:
                    enhanced_milestones.extend(batch_result)
                else:
                    # Add fallback content for failed batch
                    for milestone in batch:
                        enhanced_milestones.append(self._create_fallback_milestone_content(milestone, domain))
            
            if enhanced_milestones:
                return {
                    "enhanced_milestones": enhanced_milestones,
                    "generation_metadata": {
                        "total_milestones": len(enhanced_milestones),
                        "domain": domain,
                        "complexity": complexity,
                        "generation_method": "gemini_batch_processing"
                    }
                }
            else:
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Gemini content generation error: {e}")
            return None

    async def _process_milestone_batch(self, milestones: List[Dict], domain: str, complexity: str) -> List[Dict]:
        """
        Process a batch of milestones with Gemini AI
        
        Args:
            milestones: List of milestone dicts to process
            domain: Project domain
            complexity: Complexity level
            
        Returns:
            List of enhanced milestones or None if failed
        """
        try:
            # Create simplified prompt for batch processing
            system_prompt = f"""You are an expert content creator for {domain.replace('_', ' ')} learning projects.

Create detailed content for each milestone including:
- Learning resources (tutorials, courses, books)
- Practical exercises and projects
- Key concepts to master
- Assessment criteria

Return ONLY valid JSON in this format:
{{
    "milestones": [
        {{
            "milestone_id": "M1",
            "name": "Milestone name",
            "content_items": [
                {{
                    "item_id": "C1.1",
                    "type": "tutorial|exercise|project|resource",
                    "title": "Content title",
                    "description": "Detailed description",
                    "estimated_time": "2-3 hours",
                    "difficulty": "easy|medium|hard",
                    "resources": ["link1", "link2"]
                }}
            ]
        }}
    ]
}}"""

            # Create milestone summaries for the prompt
            milestone_summaries = []
            for milestone in milestones:
                tasks = milestone.get("tasks", [])
                task_names = [task.get("name", "Unknown task") for task in tasks[:3]]  # First 3 tasks
                
                milestone_summaries.append({
                    "id": milestone.get("milestone_id", "M?"),
                    "name": milestone.get("name", "Unknown milestone"),
                    "description": milestone.get("description", ""),
                    "tasks": task_names
                })

            user_prompt = f"""Create detailed learning content for these {domain.replace('_', ' ')} milestones:

{json.dumps(milestone_summaries, indent=2)}

Complexity level: {complexity}

For each milestone, generate 3-5 content items including tutorials, exercises, and projects appropriate for {domain} learning."""

            # Call Gemini AI
            response = await self._call_gemini_api(system_prompt, user_prompt)
            
            if response:
                # Parse and validate response
                content = self._parse_json_response(response)
                
                if content and "milestones" in content:
                    result_milestones = content["milestones"]
                    
                    # Validate and merge with original milestone data
                    enhanced_milestones = []
                    for i, original_milestone in enumerate(milestones):
                        if i < len(result_milestones):
                            enhanced_milestone = original_milestone.copy()
                            enhanced_milestone["content_items"] = result_milestones[i].get("content_items", [])
                            enhanced_milestones.append(enhanced_milestone)
                        else:
                            # Fallback for missing milestones
                            enhanced_milestones.append(
                                self._create_fallback_milestone_content(original_milestone, domain)
                            )
                    
                    self.logger.info(f"✅ Batch processed successfully: {len(enhanced_milestones)} milestones")
                    return enhanced_milestones
                else:
                    self.logger.warning("⚠️ Invalid Gemini content format")
                    return None
            else:
                self.logger.warning("⚠️ No response from Gemini AI")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Milestone batch processing error: {e}")
            return None

    async def _call_gemini_api(self, system_prompt: str, user_prompt: str) -> str:
        """
        Call Gemini AI API with error handling and fallback
        
        Args:
            system_prompt: System instructions
            user_prompt: User query
            
        Returns:
            Response text or None if failed
        """
        try:
            # Combine prompts
            full_prompt = f"{system_prompt}\n\n{user_prompt}"
            
            # Try primary model first
            try:
                response = await self.primary_model.generate_content_async(
                    full_prompt,
                    generation_config={
                        'temperature': self.temperature,
                        'max_output_tokens': self.max_tokens,
                    }
                )
                
                if response and response.text and response.text.strip():
                    self.logger.info("✅ Primary Gemini model responded")
                    return response.text.strip()
                    
            except Exception as e:
                self.logger.warning(f"⚠️ Primary model failed: {e}")
                
                # Try fallback model with different API key
                try:
                    import google.generativeai as genai
                    genai.configure(api_key=self.fallback_api_key)
                    fallback_model = genai.GenerativeModel(self.fallback_model_name)
                    
                    response = await fallback_model.generate_content_async(
                        full_prompt,
                        generation_config={
                            'temperature': self.temperature,
                            'max_output_tokens': self.max_tokens,
                        }
                    )
                    
                    if response and response.text and response.text.strip():
                        self.logger.info("✅ Fallback Gemini model responded")
                        return response.text.strip()
                        
                except Exception as e2:
                    self.logger.error(f"❌ Fallback model also failed: {e2}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Gemini API call failed: {e}")
            return None

    def _parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse JSON response from Gemini AI
        
        Args:
            response_text: Raw response from Gemini
            
        Returns:
            Parsed JSON dict or None if failed
        """
        try:
            # Clean response text
            text = response_text.strip()
            
            # Remove markdown formatting if present
            if '```json' in text:
                start = text.find('```json') + 7
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            elif '```' in text:
                start = text.find('```') + 3
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            
            # Find JSON object boundaries
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_text = text[start_idx:end_idx]
                result = json.loads(json_text)
                self.logger.info("✅ JSON response parsed successfully")
                return result
            else:
                self.logger.warning("⚠️ No valid JSON found in response")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON parsing error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Response parsing error: {e}")
            return None

    def _validate_content(self, content: Dict[str, Any]) -> bool:
        """
        Validate enhanced content format
        
        Args:
            content: Enhanced content to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required fields
            if "enhanced_milestones" not in content:
                self.logger.error("Missing enhanced_milestones field")
                return False
            
            milestones = content["enhanced_milestones"]
            if not isinstance(milestones, list) or len(milestones) < 1:
                self.logger.error("Invalid enhanced_milestones structure")
                return False
            
            # Validate first milestone
            first_milestone = milestones[0]
            if "milestone_id" not in first_milestone or "content_items" not in first_milestone:
                self.logger.error("Invalid milestone structure")
                return False
            
            # Check if content items exist (flexible validation)
            content_items = first_milestone.get("content_items", [])
            if not isinstance(content_items, list):
                self.logger.error("Invalid content_items structure")
                return False
            
            self.logger.info("✅ Content validation passed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Content validation error: {e}")
            return False

    def _create_fallback_milestone_content(self, milestone: Dict[str, Any], domain: str) -> Dict[str, Any]:
        """
        Create fallback content for a single milestone
        
        Args:
            milestone: Original milestone dict
            domain: Project domain
            
        Returns:
            Enhanced milestone with fallback content
        """
        milestone_id = milestone.get("milestone_id", "M?")
        milestone_name = milestone.get("name", "Unknown Milestone")
        tasks = milestone.get("tasks", [])
        
        # Create domain-specific content items
        content_items = []
        
        for i, task in enumerate(tasks[:3]):  # Max 3 content items per milestone
            task_name = task.get("name", f"Task {i+1}")
            
            content_items.append({
                "item_id": f"C{milestone_id[1:]}.{i+1}",
                "type": "tutorial",
                "title": f"Learn: {task_name}",
                "description": f"Complete tutorial and exercises for {task_name}",
                "estimated_time": "2-4 hours",
                "difficulty": task.get("complexity", "medium"),
                "resources": [
                    f"Online tutorial for {task_name.lower()}",
                    f"Practice exercises for {domain.replace('_', ' ')}"
                ]
            })
        
        # Add a project item
        content_items.append({
            "item_id": f"C{milestone_id[1:]}.P",
            "type": "project",
            "title": f"{milestone_name} - Hands-on Project",
            "description": f"Apply concepts from {milestone_name} in a practical project",
            "estimated_time": "1-2 weeks",
            "difficulty": "medium",
            "resources": [
                f"Project template for {domain.replace('_', ' ')}",
                "Best practices guide"
            ]
        })
        
        enhanced_milestone = milestone.copy()
        enhanced_milestone["content_items"] = content_items
        
        return enhanced_milestone

    def _create_minimal_fallback_content(self, domain_analysis: Dict[str, Any], structure_design: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create minimal fallback content when Gemini fails
        
        Args:
            domain_analysis: Domain analysis from Agent 1
            structure_design: Structure design from Agent 2
            
        Returns:
            Basic enhanced content
        """
        domain = domain_analysis.get("primary_domain", "general_project")
        milestones = structure_design.get("milestone_structure", [])
        
        enhanced_milestones = []
        
        for milestone in milestones:
            enhanced_milestone = self._create_fallback_milestone_content(milestone, domain)
            enhanced_milestones.append(enhanced_milestone)
        
        return {
            "enhanced_milestones": enhanced_milestones,
            "generation_metadata": {
                "total_milestones": len(enhanced_milestones),
                "domain": domain,
                "generation_method": "minimal_fallback"
            }
        }

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """
        Validate agent output format
        
        Args:
            output: Agent output to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check success field
            if "success" not in output:
                return False
            
            # If successful, validate enhanced_content
            if output["success"]:
                enhanced_content = output.get("enhanced_content", {})
                return self._validate_content(enhanced_content)
            else:
                # If failed, should have error field
                return "error" in output
                
        except Exception as e:
            self.logger.error(f"❌ Output validation error: {e}")
            return False
