"""
Gemini-Powered Structure Generator for Educational Projects

This module provides AI-driven project structure generation using Google's Gemini AI,
replacing static templates with intelligent, context-aware structure creation.
"""

import json
import asyncio
import hashlib
import time
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import logging


class GeminiStructureGenerator:
    """
    AI-powered structure generator using Gemini for educational project optimization
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(__name__)

        # Initialize Gemini client
        self._init_gemini_client()

        # Optimized generation parameters for better Gemini output
        self.temperature = self.config.get('structure_temperature', 0.4)  # Increased for more creative, domain-specific content
        self.max_tokens = self.config.get('structure_max_tokens', 4000)    # Increased for more detailed output
        self.timeout = self.config.get('structure_timeout', 60)           # Increased timeout for complex domains

        # Rate limiting (10 requests/min)
        self.rate_limit = 10  # requests per minute
        self.max_requests_per_minute = 60  # Gemini API rate limit
        self.request_timestamps = []

        # Caching mechanism
        self.cache = {}
        self.cache_ttl = 3600  # 1 hour cache TTL
        self.max_cache_size = 100
        
    def _init_gemini_client(self):
        """Initialize Gemini client for structure generation with multiple API keys"""
        try:
            import google.generativeai as genai

            # Agent 2 uses API keys 3 and 4
            self.api_keys = [
                os.getenv('GOOGLE_AI_API_KEY_3'),
                os.getenv('GOOGLE_AI_API_KEY_4')
            ]

            # Filter out None values
            self.api_keys = [key for key in self.api_keys if key]

            if not self.api_keys:
                raise ValueError("GOOGLE_AI_API_KEY_3 and GOOGLE_AI_API_KEY_4 environment variables not set")

            # Configure Gemini with first API key
            genai.configure(api_key=self.api_keys[0])

            # Get model names from environment or use defaults
            primary_model_name = os.getenv('GEMINI_PRIMARY_MODEL', 'gemini-2.0-flash')
            fallback_model_name = os.getenv('GEMINI_FALLBACK_MODEL', 'gemini-1.5-flash')

            # Initialize models
            self.primary_model = genai.GenerativeModel(primary_model_name)
            self.fallback_model = genai.GenerativeModel(fallback_model_name)
            self.primary_model_name = primary_model_name
            self.fallback_model_name = fallback_model_name

            self.current_key_index = 0
            self.logger.info(f"✅ AGENT 2 Gemini Structure Generator initialized with {len(self.api_keys)} API keys")
            self.logger.info(f"🔑 Using keys: KEY_3, KEY_4 for load balancing")
            self.logger.info(f"📊 Primary model: {primary_model_name}, Fallback: {fallback_model_name}")

        except ImportError:
            self.logger.error("❌ google-generativeai library not installed")
            raise
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini client: {e}")
            raise

    def _rotate_api_key(self):
        """Rotate API key for load balancing"""
        try:
            import google.generativeai as genai

            # Rotate to next API key
            self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
            current_key = self.api_keys[self.current_key_index]

            # Reconfigure Gemini with new API key
            genai.configure(api_key=current_key)

            # Reinitialize models with new key
            self.primary_model = genai.GenerativeModel(self.primary_model_name)
            self.fallback_model = genai.GenerativeModel(self.fallback_model_name)

            self.logger.info(f"🔄 AGENT 2 API Key rotated to KEY_{self.current_key_index + 3}")

        except Exception as e:
            self.logger.warning(f"⚠️ API key rotation failed: {e}")

    async def generate_optimal_structure(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main method: Generate optimal project structure using Gemini AI - SIMPLIFIED APPROACH

        Args:
            domain_analysis: Complete analysis from Agent 1

        Returns:
            Dict containing optimized project structure
        """
        try:
            # Check cache first
            cache_key = self._generate_cache_key(domain_analysis)
            cached_result = self._get_from_cache(cache_key)

            if cached_result:
                self.logger.info(f"🎯 CACHE HIT: Found cached structure for domain: {domain_analysis.get('primary_domain')}")
                self.logger.info(f"⚡ GEMINI SKIPPED: Using cached result (faster processing)")
                return cached_result

            self.logger.info(f"🚀 GEMINI GENERATOR: Starting structure generation for domain: {domain_analysis.get('primary_domain')}")
            self.logger.info(f"📊 INPUT ANALYSIS: Complexity={domain_analysis.get('complexity_level')}, Confidence={domain_analysis.get('confidence_score', 0):.2f}")

            # SIMPLIFIED APPROACH: Single API call instead of 3 steps
            self.logger.info("🏗️ GEMINI SINGLE CALL: Generating complete structure in one call...")
            self.logger.info("🤖 GEMINI API CALL: Requesting domain-specific structure...")

            optimal_structure = await self._generate_complete_structure_simplified(domain_analysis)

            if optimal_structure:
                self.logger.info("✅ GEMINI SUCCESS: Structure generated successfully")

                # Add metadata
                optimal_structure["generation_metadata"] = {
                    "generated_by": "gemini_ai_simplified",
                    "generation_timestamp": datetime.now().isoformat(),
                    "domain": domain_analysis.get("primary_domain"),
                    "complexity": domain_analysis.get("complexity_level"),
                    "confidence": domain_analysis.get("confidence_score", 0)
                }

                # Cache the result
                self._store_in_cache(cache_key, optimal_structure)
                self.logger.info("💾 CACHE SAVED: Structure cached for future use")

                return optimal_structure
            else:
                self.logger.warning("⚠️ GEMINI FAILED: Falling back to default structure")
                raise Exception("Gemini structure generation failed")

        except Exception as e:
            self.logger.error(f"❌ GEMINI GENERATOR FAILED: {e}")
            self.logger.info("🔄 FALLBACK MODE: Creating domain-specific fallback structure...")
            # Return domain-specific fallback structure
            return self._create_domain_specific_fallback_structure(domain_analysis)

    async def _generate_complete_structure_simplified(self, domain_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Generate complete project structure with simplified, domain-specific prompt
        """
        try:
            domain = domain_analysis.get("primary_domain", "unknown")
            complexity = domain_analysis.get("complexity_level", "intermediate")
            requirements = domain_analysis.get("extracted_requirements", {})
            functional_reqs = requirements.get("functional", [])

            # Create adaptive, domain-specific prompt with flexible structure
            duration = domain_analysis.get('constraints', {}).get('time', '6 months')
            
            # Determine optimal milestone count based on duration and complexity
            if duration in ['1_month', '2_months']:
                milestone_count = "3-4"
                tasks_per_milestone = "3-5"
            elif duration in ['3_months', '4_months']:
                milestone_count = "4-6"
                tasks_per_milestone = "4-6"
            elif duration in ['6_months', '1_year']:
                milestone_count = "5-8"
                tasks_per_milestone = "5-8"
            else:
                milestone_count = "4-6"
                tasks_per_milestone = "4-6"
            
            # Domain-specific context and examples
            if domain == "cooking_culinary":
                domain_context = "cooking skills, recipes, culinary techniques, kitchen management, food safety"
                milestone_examples = "Kitchen Basics → Fundamental Techniques → Recipe Mastery → Advanced Skills → Specialization"
                domain_focus = "practical cooking skills with hands-on practice"
            elif domain == "language_learning":
                domain_context = "language learning, grammar, vocabulary, conversation, reading, writing, cultural understanding"
                milestone_examples = "Alphabet/Basics → Grammar Foundation → Vocabulary Building → Conversation Practice → Advanced Fluency"
                domain_focus = "progressive language acquisition with speaking practice"
            elif domain in ["machine_learning", "data_science", "artificial_intelligence"]:
                domain_context = "machine learning, data analysis, algorithms, programming, statistics, model development"
                milestone_examples = "Math/Stats Foundation → Programming Skills → ML Algorithms → Project Implementation → Advanced Topics"
                domain_focus = "theoretical understanding combined with practical implementation"
            elif domain in ["mobile_app_development", "web_development", "software_development"]:
                domain_context = "programming, software design, user interface, testing, deployment, project management"
                milestone_examples = "Programming Basics → Framework Learning → Project Development → Advanced Features → Production Deployment"
                domain_focus = "hands-on coding with real project development"
            else:
                domain_context = f"{domain.replace('_', ' ')} skills, knowledge, and practical application"
                milestone_examples = "Foundation → Skill Development → Practice → Advanced Topics → Mastery"
                domain_focus = "balanced theoretical and practical learning"

            system_prompt = f"""You are an expert educational planner specializing in {domain.replace('_', ' ')}.

Create a comprehensive learning plan with {milestone_count} milestones, each containing {tasks_per_milestone} tasks.

Domain Focus: {domain_context}
Learning Approach: {domain_focus}
Progression Path: {milestone_examples}

IMPORTANT REQUIREMENTS:
1. Each milestone must have a unique milestone_id (M1, M2, M3, etc.)
2. Each task must have a unique task_id (T1.1, T1.2, T2.1, etc.)
3. Milestone names should be specific to {domain.replace('_', ' ')}
4. Task descriptions should be actionable and measurable
5. Estimated durations should be realistic for {complexity} level

Required JSON format (respond with ONLY valid JSON):
{{
    "milestones": [
        {{
            "milestone_id": "M1",
            "name": "Specific milestone name for {domain.replace('_', ' ')}",
            "description": "Detailed description of what will be achieved",
            "position": 1,
            "estimated_duration": "2-4 weeks",
            "tasks": [
                {{
                    "task_id": "T1.1",
                    "name": "Specific task name",
                    "description": "Actionable task description with clear outcomes",
                    "estimated_duration": "3-5 days",
                    "complexity": "easy|medium|hard"
                }}
            ]
        }}
    ]
}}"""

            user_prompt = f"""Create a {complexity} level {domain.replace('_', ' ')} learning plan.

Specific Requirements:
{chr(10).join(f'- {req}' for req in functional_reqs[:5]) if functional_reqs else '- Comprehensive skill development'}

Time Constraint: {duration.replace('_', ' ')}
Complexity Level: {complexity}

Generate {milestone_count} progressive milestones with domain-specific, actionable content."""

            # Call Gemini with simplified prompt
            result = await self._call_gemini_with_retry(system_prompt, user_prompt)

            if result and self._validate_structure_format(result):
                self.logger.info(f"✅ SIMPLIFIED GENERATION SUCCESS: {domain} structure created")
                return result
            else:
                self.logger.warning(f"⚠️ SIMPLIFIED GENERATION FAILED: Invalid structure format")
                return None

        except Exception as e:
            self.logger.error(f"❌ SIMPLIFIED GENERATION ERROR: {e}")
            return None

    def _validate_structure_format(self, structure: Dict[str, Any]) -> bool:
        """Validate that structure has required format"""
        try:
            if not isinstance(structure, dict):
                return False

            # Check required top-level keys
            if "milestones" not in structure:
                return False

            milestones = structure["milestones"]
            if not isinstance(milestones, list) or len(milestones) == 0:
                return False

            # Check first milestone format
            first_milestone = milestones[0]
            required_milestone_fields = ["milestone_id", "name", "tasks"]

            if not all(field in first_milestone for field in required_milestone_fields):
                return False

            # Check tasks format
            tasks = first_milestone.get("tasks", [])
            if not isinstance(tasks, list) or len(tasks) == 0:
                return False

            # Check first task format
            first_task = tasks[0]
            required_task_fields = ["task_id", "name"]

            if not all(field in first_task for field in required_task_fields):
                return False

            return True

        except Exception as e:
            self.logger.error(f"❌ VALIDATION ERROR: {e}")
            return False

    def _create_domain_specific_fallback_structure(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create domain-specific fallback structure when Gemini fails"""
        domain = domain_analysis.get("primary_domain", "general_project")
        constraints = domain_analysis.get("constraints", {})
        duration = constraints.get("time", "6_months")

        self.logger.info(f"🎯 DOMAIN-SPECIFIC FALLBACK: Creating structure for {domain}")

        if domain == "cooking_culinary":
            return self._create_cooking_fallback_structure(domain_analysis)
        elif domain == "language_learning":
            return self._create_language_learning_fallback_structure(domain_analysis)
        elif domain in ["mobile_app_development", "web_development"]:
            return self._create_development_fallback_structure(domain_analysis)
        else:
            # Generic fallback for unknown domains
            return self._create_default_fallback_structure(domain_analysis)

    def _create_cooking_fallback_structure(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create cooking-specific fallback structure"""
        milestones = [
            {
                "milestone_id": "M1",
                "name": "🔪 Kitchen Fundamentals and Basic Skills",
                "description": "Master essential kitchen skills, knife techniques, and basic cooking methods",
                "position": 1,
                "estimated_duration": "4-6 weeks",
                "tasks": [
                    {"task_id": "T1.1", "name": "Kitchen Setup and Equipment Familiarization", "description": "Learn about essential kitchen tools and equipment", "estimated_duration": "1 week", "complexity": "easy"},
                    {"task_id": "T1.2", "name": "Knife Skills and Cutting Techniques", "description": "Master basic knife cuts: julienne, dice, chop, mince", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T1.3", "name": "Food Safety and Kitchen Hygiene", "description": "Learn proper food handling, storage, and sanitation", "estimated_duration": "1 week", "complexity": "easy"},
                    {"task_id": "T1.4", "name": "Basic Cooking Methods", "description": "Practice boiling, steaming, sautéing, and pan-frying", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T1.5", "name": "Ingredient Selection and Preparation", "description": "Learn to select fresh ingredients and basic prep techniques", "estimated_duration": "1 week", "complexity": "easy"}
                ]
            },
            {
                "milestone_id": "M2",
                "name": "🍳 Cooking Techniques and Flavor Development",
                "description": "Develop intermediate cooking skills and understanding of flavors",
                "position": 2,
                "estimated_duration": "6-8 weeks",
                "tasks": [
                    {"task_id": "T2.1", "name": "Advanced Cooking Methods", "description": "Master roasting, braising, grilling, and deep-frying", "estimated_duration": "2 weeks", "complexity": "medium"},
                    {"task_id": "T2.2", "name": "Spices and Seasoning Mastery", "description": "Learn spice combinations and flavor balancing", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T2.3", "name": "Sauce Making Fundamentals", "description": "Create basic sauces: roux, reduction, emulsion", "estimated_duration": "1 week", "complexity": "hard"},
                    {"task_id": "T2.4", "name": "Stock and Broth Preparation", "description": "Make chicken, beef, and vegetable stocks", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T2.5", "name": "Cooking Timing and Multi-tasking", "description": "Learn to coordinate multiple dishes and timing", "estimated_duration": "1 week", "complexity": "hard"}
                ]
            },
            {
                "milestone_id": "M3",
                "name": "🍜 Vietnamese Cuisine Mastery",
                "description": "Master traditional Vietnamese dishes and techniques",
                "position": 3,
                "estimated_duration": "6-8 weeks",
                "tasks": [
                    {"task_id": "T3.1", "name": "Pho and Noodle Soups", "description": "Master pho bo, pho ga, bun bo hue", "estimated_duration": "2 weeks", "complexity": "hard"},
                    {"task_id": "T3.2", "name": "Rice Dishes and Com Tam", "description": "Perfect broken rice dishes and accompaniments", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T3.3", "name": "Banh Mi and Vietnamese Sandwiches", "description": "Create authentic banh mi with proper bread and fillings", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T3.4", "name": "Fresh Spring Rolls and Appetizers", "description": "Make goi cuon, nem nuong, and other appetizers", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T3.5", "name": "Vietnamese Desserts and Drinks", "description": "Prepare che, Vietnamese coffee, and sweet treats", "estimated_duration": "1 week", "complexity": "easy"}
                ]
            },
            {
                "milestone_id": "M4",
                "name": "🌍 International Cuisine Exploration",
                "description": "Expand skills with Western and Asian cuisines",
                "position": 4,
                "estimated_duration": "8-10 weeks",
                "tasks": [
                    {"task_id": "T4.1", "name": "Italian Pasta and Risotto", "description": "Master fresh pasta making and risotto techniques", "estimated_duration": "2 weeks", "complexity": "medium"},
                    {"task_id": "T4.2", "name": "Western Meat Preparation", "description": "Perfect steak cooking, roasts, and meat handling", "estimated_duration": "2 weeks", "complexity": "hard"},
                    {"task_id": "T4.3", "name": "Japanese Sushi and Sashimi", "description": "Learn sushi rice, knife skills, and presentation", "estimated_duration": "2 weeks", "complexity": "hard"},
                    {"task_id": "T4.4", "name": "Thai Curry and Stir-fry", "description": "Master Thai curry pastes and wok cooking", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T4.5", "name": "French Cooking Fundamentals", "description": "Learn classic French techniques and mother sauces", "estimated_duration": "1 week", "complexity": "hard"}
                ]
            },
            {
                "milestone_id": "M5",
                "name": "👨‍🍳 Professional Skills and Restaurant Operations",
                "description": "Develop professional-level skills and business knowledge",
                "position": 5,
                "estimated_duration": "6-8 weeks",
                "tasks": [
                    {"task_id": "T5.1", "name": "Plating and Food Presentation", "description": "Master professional plating techniques and garnishing", "estimated_duration": "2 weeks", "complexity": "medium"},
                    {"task_id": "T5.2", "name": "Menu Planning and Cost Control", "description": "Design menus and calculate food costs", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T5.3", "name": "Kitchen Workflow and Organization", "description": "Learn mise en place and kitchen efficiency", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T5.4", "name": "Large Batch Cooking", "description": "Scale recipes and cook for multiple servings", "estimated_duration": "1 week", "complexity": "hard"},
                    {"task_id": "T5.5", "name": "Final Cooking Assessment", "description": "Prepare a complete multi-course meal", "estimated_duration": "1 week", "complexity": "hard"}
                ]
            }
        ]

        return {
            "project_overview": {
                "total_milestones": 5,
                "total_tasks": 25,
                "estimated_total_duration": "6 months",
                "development_progression": "gradual"
            },
            "milestones": milestones,
            "generation_metadata": {
                "generated_by": "domain_specific_fallback_cooking",
                "generation_timestamp": datetime.now().isoformat(),
                "domain": "cooking_culinary",
                "complexity": domain_analysis.get("complexity_level", "intermediate"),
                "confidence": 0.85
            }
        }

    def _create_language_learning_fallback_structure(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create language learning-specific fallback structure"""
        milestones = [
            {
                "milestone_id": "M1",
                "name": "🔤 Foundation and Writing Systems",
                "description": "Master basic writing systems and pronunciation fundamentals",
                "position": 1,
                "estimated_duration": "6-8 weeks",
                "tasks": [
                    {"task_id": "T1.1", "name": "Hiragana Mastery", "description": "Learn and memorize all 46 hiragana characters", "estimated_duration": "2 weeks", "complexity": "easy"},
                    {"task_id": "T1.2", "name": "Katakana Mastery", "description": "Learn and memorize all 46 katakana characters", "estimated_duration": "2 weeks", "complexity": "easy"},
                    {"task_id": "T1.3", "name": "Basic Pronunciation and Phonetics", "description": "Master Japanese pronunciation rules and sounds", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T1.4", "name": "Essential Kanji Introduction", "description": "Learn first 50 most common kanji characters", "estimated_duration": "2 weeks", "complexity": "medium"},
                    {"task_id": "T1.5", "name": "Basic Greetings and Phrases", "description": "Master everyday greetings and polite expressions", "estimated_duration": "1 week", "complexity": "easy"}
                ]
            },
            {
                "milestone_id": "M2",
                "name": "📚 Grammar Foundation and Basic Communication",
                "description": "Build solid grammar foundation and basic conversation skills",
                "position": 2,
                "estimated_duration": "8-10 weeks",
                "tasks": [
                    {"task_id": "T2.1", "name": "Sentence Structure and Particles", "description": "Master wa, ga, wo, ni, de particles and basic sentence patterns", "estimated_duration": "3 weeks", "complexity": "medium"},
                    {"task_id": "T2.2", "name": "Verb Conjugations", "description": "Learn present, past, negative forms of verbs", "estimated_duration": "2 weeks", "complexity": "hard"},
                    {"task_id": "T2.3", "name": "Adjectives and Descriptions", "description": "Master i-adjectives and na-adjectives usage", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T2.4", "name": "Numbers, Time, and Dates", "description": "Learn counting systems and time expressions", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T2.5", "name": "Basic Conversation Practice", "description": "Practice simple dialogues and self-introduction", "estimated_duration": "1 week", "complexity": "medium"}
                ]
            },
            {
                "milestone_id": "M3",
                "name": "📖 Vocabulary Building and Intermediate Grammar",
                "description": "Expand vocabulary and tackle intermediate grammar concepts",
                "position": 3,
                "estimated_duration": "10-12 weeks",
                "tasks": [
                    {"task_id": "T3.1", "name": "Core Vocabulary Expansion", "description": "Learn 1500+ essential vocabulary words", "estimated_duration": "4 weeks", "complexity": "medium"},
                    {"task_id": "T3.2", "name": "Kanji Progression", "description": "Master 200+ kanji with readings and meanings", "estimated_duration": "4 weeks", "complexity": "hard"},
                    {"task_id": "T3.3", "name": "Intermediate Grammar Patterns", "description": "Learn conditional, potential, and causative forms", "estimated_duration": "2 weeks", "complexity": "hard"},
                    {"task_id": "T3.4", "name": "Keigo (Polite Language)", "description": "Introduction to honorific and humble language", "estimated_duration": "1 week", "complexity": "hard"},
                    {"task_id": "T3.5", "name": "Reading Comprehension", "description": "Practice reading simple texts and stories", "estimated_duration": "1 week", "complexity": "medium"}
                ]
            },
            {
                "milestone_id": "M4",
                "name": "🎧 Listening and Speaking Development",
                "description": "Develop practical listening and speaking abilities",
                "position": 4,
                "estimated_duration": "10-12 weeks",
                "tasks": [
                    {"task_id": "T4.1", "name": "Anime and Drama Listening", "description": "Practice listening with Japanese media content", "estimated_duration": "4 weeks", "complexity": "medium"},
                    {"task_id": "T4.2", "name": "Conversation with Native Speakers", "description": "Regular speaking practice with language partners", "estimated_duration": "4 weeks", "complexity": "hard"},
                    {"task_id": "T4.3", "name": "Podcast and News Listening", "description": "Listen to Japanese podcasts and news", "estimated_duration": "2 weeks", "complexity": "hard"},
                    {"task_id": "T4.4", "name": "Pronunciation Refinement", "description": "Work on accent and natural speech patterns", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T4.5", "name": "Presentation Skills", "description": "Practice giving short presentations in Japanese", "estimated_duration": "1 week", "complexity": "hard"}
                ]
            },
            {
                "milestone_id": "M5",
                "name": "🎯 JLPT N2 Preparation and Advanced Skills",
                "description": "Prepare for JLPT N2 exam and achieve intermediate fluency",
                "position": 5,
                "estimated_duration": "8-10 weeks",
                "tasks": [
                    {"task_id": "T5.1", "name": "JLPT N2 Grammar Mastery", "description": "Master all N2 level grammar points", "estimated_duration": "3 weeks", "complexity": "hard"},
                    {"task_id": "T5.2", "name": "Advanced Kanji and Vocabulary", "description": "Learn remaining N2 kanji and vocabulary", "estimated_duration": "3 weeks", "complexity": "hard"},
                    {"task_id": "T5.3", "name": "Reading Complex Texts", "description": "Practice reading manga, light novels, and articles", "estimated_duration": "1 week", "complexity": "hard"},
                    {"task_id": "T5.4", "name": "Mock Exams and Test Strategy", "description": "Take practice tests and develop exam strategies", "estimated_duration": "1 week", "complexity": "medium"},
                    {"task_id": "T5.5", "name": "Final Assessment and JLPT N2", "description": "Take official JLPT N2 exam", "estimated_duration": "1 week", "complexity": "hard"}
                ]
            }
        ]

        return {
            "project_overview": {
                "total_milestones": 5,
                "total_tasks": 25,
                "estimated_total_duration": "12 months",
                "development_progression": "gradual"
            },
            "milestones": milestones,
            "generation_metadata": {
                "generated_by": "domain_specific_fallback_language",
                "generation_timestamp": datetime.now().isoformat(),
                "domain": "language_learning",
                "complexity": domain_analysis.get("complexity_level", "intermediate"),
                "confidence": 0.85
            }
        }

    async def _call_gemini_with_retry(self, system_prompt: str, user_prompt: str, use_fallback: bool = False) -> Optional[Dict[str, Any]]:
        """
        Call Gemini API with retry logic and API key rotation
        """
        try:
            # Rotate API key for load balancing
            self._rotate_api_key()

            # Select model
            model = self.fallback_model if use_fallback else self.primary_model
            model_name = "gemini-2.5-flash" if use_fallback else "gemini-2.0-flash"
            
            # Combine prompts
            combined_prompt = f"{system_prompt}\n\nUser Request:\n{user_prompt}"
            
            # Configure generation
            generation_config = {
                'temperature': self.temperature,
                'max_output_tokens': self.max_tokens,
            }
            
            # Make API call
            response = await model.generate_content_async(
                combined_prompt,
                generation_config=generation_config
            )
            
            if response and response.text and response.text.strip():
                response_text = response.text.strip()
                self.logger.info(f"📝 {model_name} response length: {len(response_text)} chars")

                # Parse JSON response
                try:
                    result = json.loads(response_text)
                    self.logger.info(f"✅ {model_name} API call successful")
                    return result
                except json.JSONDecodeError as e:
                    self.logger.warning(f"⚠️ JSON parsing failed for {model_name}: {e}")
                    # Try to extract JSON from response
                    extracted = self._extract_json_from_response(response_text)
                    if extracted:
                        self.logger.info(f"✅ {model_name} JSON extraction successful")
                        return extracted
                    else:
                        self.logger.warning(f"❌ {model_name} JSON extraction failed")
                        return None
            else:
                self.logger.warning(f"❌ {model_name} returned empty response")
                return None
            
        except Exception as e:
            self.logger.warning(f"❌ {model_name} API call failed: {e}")
            
            # Try fallback model if primary failed
            if not use_fallback:
                self.logger.info("🔄 Trying fallback model...")
                return await self._call_gemini_with_retry(system_prompt, user_prompt, use_fallback=True)
            
            return None
    
    def _extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """
        Extract JSON from response text that might contain markdown or extra text
        Enhanced version with multiple parsing strategies
        """
        try:
            # Strategy 1: Clean response text
            text = response_text.strip()

            # Remove markdown code blocks
            if '```json' in text:
                start = text.find('```json') + 7
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            elif '```' in text:
                start = text.find('```') + 3
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()

            # Strategy 2: Try to parse cleaned text directly
            try:
                return json.loads(text)
            except json.JSONDecodeError:
                pass

            # Strategy 3: Find JSON object boundaries
            start = text.find('{')
            end = text.rfind('}') + 1

            if start >= 0 and end > start:
                json_text = text[start:end]

                # Strategy 4: Fix common JSON issues
                json_text = self._fix_json_issues(json_text)

                try:
                    return json.loads(json_text)
                except json.JSONDecodeError:
                    pass

            # Strategy 5: Try to find multiple JSON objects and take the largest
            import re
            json_objects = re.findall(r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}', text)

            for json_obj in sorted(json_objects, key=len, reverse=True):
                try:
                    fixed_json = self._fix_json_issues(json_obj)
                    return json.loads(fixed_json)
                except json.JSONDecodeError:
                    continue

            return None

        except Exception as e:
            self.logger.warning(f"JSON extraction failed: {e}")
            return None

    def _fix_json_issues(self, json_str: str) -> str:
        """Fix common JSON formatting issues"""
        import re

        # Remove trailing commas before closing brackets/braces
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

        # Fix unescaped quotes in strings (basic fix)
        # This is a simple approach - in production you might need more sophisticated handling
        json_str = json_str.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')

        return json_str
    
    async def _analyze_domain_expertise(self, domain_analysis: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Step 1: Analyze domain expertise and learning characteristics
        """
        system_prompt = """
        You are a Universal Project Structure Expert with deep knowledge across ALL domains and industries.

        Your expertise includes:
        - Project management methodologies across all sectors (technology, business, creative, scientific, manufacturing, service, etc.)
        - Domain-specific development patterns and best practices
        - Milestone progression patterns for any type of project
        - Resource optimization and constraint management
        - Success metrics and progress tracking for diverse projects

        You can handle ANY domain and must adapt your approach based on the specific project type.
        You must respond with valid JSON only.
        """
        
        user_prompt = f"""
        Analyze this project domain and provide expert insights for optimal structure design:

        DOMAIN ANALYSIS:
        {json.dumps(domain_analysis, indent=2)}

        Provide detailed analysis in JSON format:
        {{
            "domain_characteristics": {{
                "development_progression": "sequential|parallel|mixed",
                "typical_milestone_count": "3-8 range with reasoning",
                "complexity_factors": ["list of key complexity drivers for this domain"],
                "prerequisite_dependencies": ["critical prerequisites and dependencies"],
                "success_measurement_patterns": ["how progress should be measured in this domain"]
            }},
            "project_scope_indicators": {{
                "detected_scope": "small|medium|large|enterprise",
                "confidence": 0.85,
                "scope_specific_considerations": ["adaptations needed for this project scope"]
            }},
            "optimal_structure_recommendations": {{
                "recommended_milestones": 5,
                "recommended_tasks_per_milestone": 5,
                "total_duration_estimate": "based on constraints and complexity",
                "rationale": "detailed explanation of why these numbers are optimal for this specific domain and scope"
            }}
        }}
        """
        
        return await self._call_gemini_with_retry(system_prompt, user_prompt)
    
    async def _assess_project_complexity(self, domain_analysis: Dict[str, Any], domain_expertise: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Step 2: Assess project complexity across multiple dimensions
        """
        system_prompt = """
        You are a Universal Project Complexity Assessment Specialist.

        Evaluate project complexity across multiple dimensions for ANY domain:
        - Technical complexity and implementation difficulty
        - Time requirements and scheduling constraints
        - Resource needs and dependencies
        - Team capability and skill requirements
        - Risk factors and mitigation strategies

        You must respond with valid JSON only.
        """
        
        user_prompt = f"""
        Assess the complexity of this project across all relevant dimensions:

        DOMAIN: {domain_analysis.get('primary_domain')}
        PROJECT DESCRIPTION: {domain_analysis.get('user_input', '')}
        CONSTRAINTS: {json.dumps(domain_analysis.get('constraints', {}))}
        REQUIREMENTS: {json.dumps(domain_analysis.get('extracted_requirements', {}))}
        DOMAIN EXPERTISE: {json.dumps(domain_expertise)}

        Return comprehensive complexity assessment:
        {{
            "overall_complexity": "low|medium|high|expert",
            "complexity_score": 0.75,
            "complexity_breakdown": {{
                "technical_complexity": 0.8,
                "time_complexity": 0.7,
                "resource_complexity": 0.6,
                "implementation_difficulty": 0.5
            }},
            "scaling_recommendations": {{
                "milestone_adjustment": "recommended milestone count adjustment",
                "task_density_adjustment": "recommended tasks per milestone",
                "duration_adjustment": "recommended timeline adjustment",
                "reasoning": "detailed explanation of why these adjustments are needed for this specific domain and project type"
            }}
        }}
        """
        
        return await self._call_gemini_with_retry(system_prompt, user_prompt)

    async def _generate_structure_with_gemini(self, domain_analysis: Dict[str, Any],
                                           domain_expertise: Dict[str, Any],
                                           complexity_assessment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Step 3: Generate complete project structure using Gemini AI
        """
        # Check rate limit before making API call
        await self._check_rate_limit()

        system_prompt = """
        You are a Master Project Designer with 20+ years of experience across ALL domains and industries.

        Create optimal project structures that:
        - Follow industry best practices and proven methodologies
        - Adapt to project scope, complexity, and constraints
        - Optimize for efficiency, quality, and successful delivery
        - Include measurable milestones and clear deliverables
        - Balance ambition with realistic achievability
        - Incorporate modern project management principles
        - Scale appropriately for the specific domain and requirements

        You can handle ANY type of project: technology, business, creative, scientific, manufacturing, service, etc.
        You must respond with valid JSON only.
        """

        user_prompt = f"""
        Design an optimal project structure based on this comprehensive analysis:

        DOMAIN ANALYSIS:
        {json.dumps(domain_analysis, indent=2)}

        DOMAIN EXPERTISE:
        {json.dumps(domain_expertise, indent=2)}

        COMPLEXITY ASSESSMENT:
        {json.dumps(complexity_assessment, indent=2)}

        Generate complete project structure in this exact JSON format:
        {{
            "project_overview": {{
                "total_milestones": 5,
                "total_tasks": 25,
                "estimated_total_duration": "based on constraints and complexity",
                "development_progression": "gradual|steep|mixed",
                "execution_approach": "sequential|parallel|blended"
            }},
            "milestones": [
                {{
                    "milestone_id": "M1",
                    "name": "Clear, actionable milestone name",
                    "description": "Detailed description of milestone purpose and deliverables",
                    "position": 1,
                    "estimated_duration": "2 weeks",
                    "complexity_weight": 0.8,
                    "critical_path": true,
                    "objectives": [
                        "Specific, measurable objective 1",
                        "Specific, measurable objective 2"
                    ],
                    "success_criteria": [
                        "Clear success metric 1",
                        "Clear success metric 2"
                    ],
                    "tasks": [
                        {{
                            "task_id": "T1.1",
                            "name": "Specific, actionable task name",
                            "description": "Detailed task description with clear deliverables and instructions",
                            "estimated_duration": "2 days",
                            "complexity": "easy|medium|hard",
                            "task_type": "research|development|testing|design|planning|implementation",
                            "dependencies": ["prerequisite tasks if any"],
                            "deliverables": ["specific outputs expected"],
                            "resources_needed": ["tools, technologies, or skills required"]
                        }}
                    ]
                }}
            ],
            "optimization_analysis": {{
                "critical_path": ["milestone sequence that determines minimum duration"],
                "parallel_opportunities": ["tasks/milestones that can run concurrently"],
                "bottlenecks": ["potential blocking points"],
                "optimization_score": {{
                    "time_efficiency": 0.85,
                    "resource_balance": 0.90,
                    "risk_mitigation": 0.80,
                    "delivery_effectiveness": 0.95
                }}
            }},
            "adaptation_notes": {{
                "scope_adaptations": ["how structure adapts to detected project scope"],
                "complexity_adjustments": ["adjustments made based on complexity assessment"],
                "constraint_accommodations": ["how time/resource constraints were addressed"],
                "customization_opportunities": ["areas where structure can be further customized"]
            }},
            "generation_metadata": {{
                "generated_by": "gemini_ai",
                "generation_timestamp": "{datetime.now().isoformat()}",
                "domain_optimized_for": "{domain_analysis.get('primary_domain', 'unknown')}"
            }}
        }}

        IMPORTANT: Generate exactly 5 milestones with exactly 5 tasks each, totaling 25 tasks.
        Ensure all tasks are domain-appropriate, actionable, and realistic for the project scope and constraints.
        """

        return await self._call_gemini_with_retry(system_prompt, user_prompt)

    async def _validate_and_optimize(self, structure: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        Step 4: Validate and optimize the generated structure
        """
        try:
            # Basic validation
            if not self._validate_structure_format(structure):
                self.logger.warning("⚠️ Structure format validation failed, applying fixes...")
                structure = self._fix_structure_format(structure)

            # Add metadata
            structure["generation_metadata"] = {
                "generated_by": "gemini_ai",
                "generation_timestamp": datetime.now().isoformat(),
                "source_domain": domain_analysis.get("primary_domain"),
                "confidence_score": self._calculate_confidence_score(structure),
                "validation_status": "validated_and_optimized"
            }

            # Log success
            total_milestones = structure.get("project_overview", {}).get("total_milestones", 0)
            total_tasks = structure.get("project_overview", {}).get("total_tasks", 0)

            self.logger.info(f"✅ Structure validated: {total_milestones} milestones, {total_tasks} tasks")

            return structure

        except Exception as e:
            self.logger.error(f"❌ Structure validation failed: {e}")
            # Return basic fallback structure
            return self._create_default_fallback_structure(domain_analysis)

    def _generate_cache_key(self, domain_analysis: Dict[str, Any]) -> str:
        """Generate cache key from domain analysis"""
        # Create a simplified version for caching
        cache_data = {
            "primary_domain": domain_analysis.get("primary_domain"),
            "user_input": domain_analysis.get("user_input", "")[:200],  # Truncate long inputs
            "constraints": domain_analysis.get("constraints", {}),
            "complexity_factors": sorted(domain_analysis.get("complexity_factors", []))
        }

        # Create hash
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()

    def _get_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """Get structure from cache if valid"""
        if cache_key in self.cache:
            cached_item = self.cache[cache_key]

            # Check if cache is still valid
            if time.time() - cached_item["timestamp"] < self.cache_ttl:
                return cached_item["data"]
            else:
                # Remove expired cache
                del self.cache[cache_key]

        return None

    def _store_in_cache(self, cache_key: str, data: Dict[str, Any]):
        """Store structure in cache"""
        # Clean old cache if too large
        if len(self.cache) >= self.max_cache_size:
            self._clean_cache()

        self.cache[cache_key] = {
            "data": data,
            "timestamp": time.time()
        }

        self.logger.info(f"💾 Structure cached with key: {cache_key[:8]}...")

    def _clean_cache(self):
        """Remove oldest cache entries"""
        # Sort by timestamp and remove oldest half
        sorted_items = sorted(self.cache.items(), key=lambda x: x[1]["timestamp"])
        items_to_remove = len(sorted_items) // 2

        for i in range(items_to_remove):
            del self.cache[sorted_items[i][0]]

        self.logger.info(f"🧹 Cache cleaned, removed {items_to_remove} old entries")

    async def _check_rate_limit(self):
        """Check and enforce rate limiting"""
        current_time = time.time()

        # Remove timestamps older than 1 minute
        self.request_timestamps = [
            ts for ts in self.request_timestamps
            if current_time - ts < 60
        ]

        # Check if we're at the limit
        if len(self.request_timestamps) >= self.rate_limit:
            # Calculate wait time
            oldest_request = min(self.request_timestamps)
            wait_time = 60 - (current_time - oldest_request)

            if wait_time > 0:
                self.logger.warning(f"⏱️ Rate limit reached, waiting {wait_time:.1f}s...")
                await asyncio.sleep(wait_time)

        # Add current request timestamp
        self.request_timestamps.append(current_time)

    def _validate_structure_format(self, structure: Dict[str, Any]) -> bool:
        """Validate that the generated structure has required format"""
        try:
            # Check required top-level keys
            required_keys = ["project_overview", "milestones", "optimization_analysis", "adaptation_notes"]
            if not all(key in structure for key in required_keys):
                return False

            # Check project overview
            overview = structure["project_overview"]
            if not all(key in overview for key in ["total_milestones", "total_tasks", "total_duration_weeks"]):
                return False

            # Check milestones
            milestones = structure["milestones"]
            if not isinstance(milestones, list) or len(milestones) == 0:
                return False

            # Check each milestone has required structure
            for milestone in milestones:
                if not all(key in milestone for key in ["name", "description", "tasks"]):
                    return False

                # Check tasks
                if not isinstance(milestone["tasks"], list) or len(milestone["tasks"]) == 0:
                    return False

            return True

        except Exception as e:
            self.logger.warning(f"Structure validation error: {e}")
            return False

    def _fix_structure_format(self, structure: Dict[str, Any]) -> Dict[str, Any]:
        """Fix common structure format issues"""
        # Add missing keys with defaults
        if "project_overview" not in structure:
            structure["project_overview"] = {
                "total_milestones": 5,
                "total_tasks": 20,
                "total_duration_weeks": 12,
                "difficulty_progression": "gradual"
            }

        if "milestones" not in structure or not structure["milestones"]:
            structure["milestones"] = self._create_default_milestones()

        if "optimization_analysis" not in structure:
            structure["optimization_analysis"] = {
                "critical_path": ["All milestones are sequential"],
                "parallel_opportunities": ["Individual tasks within milestones"],
                "bottlenecks": ["Assessment and feedback points"],
                "optimization_score": {
                    "time_efficiency": 0.8,
                    "resource_balance": 0.8,
                    "risk_mitigation": 0.8,
                    "learning_effectiveness": 0.8
                }
            }

        if "adaptation_notes" not in structure:
            structure["adaptation_notes"] = {
                "student_level_adaptations": ["Structure adapted for general student level"],
                "complexity_adjustments": ["Standard complexity applied"],
                "constraint_accommodations": ["Standard timeline and resources assumed"]
            }

        return structure

    def _calculate_confidence_score(self, structure: Dict[str, Any]) -> float:
        """Calculate confidence score for generated structure"""
        score = 0.8  # Base score

        # Check completeness
        if len(structure.get("milestones", [])) >= 4:
            score += 0.1

        # Check task distribution
        total_tasks = sum(len(m.get("tasks", [])) for m in structure.get("milestones", []))
        if 15 <= total_tasks <= 45:
            score += 0.1

        return min(1.0, score)

    def _create_default_fallback_structure(self, domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create a default fallback structure when Gemini fails - GENERIC FOR ALL DOMAINS"""
        domain = domain_analysis.get("primary_domain", "general_project")
        constraints = domain_analysis.get("constraints", {})
        duration = constraints.get("time", "12_weeks")
        domain_title = domain.replace('_', ' ').title()

        # Create 5 generic milestones with 5 tasks each
        milestones = []

        milestone_templates = [
            {
                "name": f"Project Planning and Requirements Analysis",
                "description": f"Establish project foundation, analyze requirements, and create detailed planning",
                "tasks": [
                    "Requirements gathering and analysis",
                    "Stakeholder identification and engagement",
                    "Project scope definition and documentation",
                    "Risk assessment and mitigation planning",
                    "Project timeline and resource planning"
                ]
            },
            {
                "name": f"Design and Architecture Phase",
                "description": f"Create detailed design specifications and technical architecture",
                "tasks": [
                    "System architecture design and documentation",
                    "User interface and experience design",
                    "Technical specifications development",
                    "Design review and stakeholder approval",
                    "Implementation strategy planning"
                ]
            },
            {
                "name": f"Development and Implementation",
                "description": f"Execute the main development and implementation work",
                "tasks": [
                    "Core functionality development",
                    "Feature implementation and integration",
                    "Quality assurance and testing setup",
                    "Performance optimization and tuning",
                    "Documentation and code review"
                ]
            },
            {
                "name": f"Testing and Quality Assurance",
                "description": f"Comprehensive testing and quality validation",
                "tasks": [
                    "Unit testing and component validation",
                    "Integration testing and system verification",
                    "User acceptance testing coordination",
                    "Performance and security testing",
                    "Bug fixing and quality improvements"
                ]
            },
            {
                "name": f"Deployment and Launch",
                "description": f"Final deployment, launch, and project completion",
                "tasks": [
                    "Production environment setup and configuration",
                    "Deployment execution and monitoring",
                    "User training and documentation delivery",
                    "Go-live support and issue resolution",
                    "Project closure and success evaluation"
                ]
            }
        ]

        for i, template in enumerate(milestone_templates, 1):
            tasks = []
            for j, task_name in enumerate(template["tasks"], 1):
                tasks.append({
                    "task_id": f"T{i}.{j}",
                    "name": f"{task_name} for {domain_title}",
                    "description": f"Complete {task_name.lower()} with focus on {domain_title.lower()} requirements and best practices",
                    "estimated_duration": "2-3 days",
                    "complexity": "medium",
                    "task_type": "implementation",
                    "dependencies": [],
                    "deliverables": [f"{task_name} deliverable"],
                    "resources_needed": ["Standard project tools", "Domain expertise"]
                })

            milestones.append({
                "milestone_id": f"M{i}",
                "name": template["name"],
                "description": template["description"],
                "position": i,
                "estimated_duration": "2-3 weeks",
                "complexity_weight": 0.6 + (i * 0.1),
                "critical_path": True,
                "objectives": [f"Complete {template['name'].lower()}"],
                "success_criteria": [f"{template['name']} completed successfully"],
                "tasks": tasks
            })

        return {
            "project_overview": {
                "total_milestones": 5,
                "total_tasks": 25,
                "estimated_total_duration": duration,
                "development_progression": "gradual",
                "execution_approach": "sequential"
            },
            "milestones": milestones,
            "generation_metadata": {
                "generated_by": "default_fallback",
                "generation_timestamp": datetime.now().isoformat(),
                "domain_optimized_for": domain
            }
        }



    def _validate_structure_format(self, structure: Dict[str, Any]) -> bool:
        """Validate that the generated structure has required format"""
        try:
            # Check required top-level keys
            required_keys = ["project_overview", "milestones"]
            if not all(key in structure for key in required_keys):
                return False

            # Check milestones format
            milestones = structure.get("milestones", [])
            if not isinstance(milestones, list) or len(milestones) != 5:
                return False

            # Check each milestone has required fields
            for milestone in milestones:
                required_milestone_keys = ["milestone_id", "name", "tasks"]
                if not all(key in milestone for key in required_milestone_keys):
                    return False

                # Check tasks format
                tasks = milestone.get("tasks", [])
                if not isinstance(tasks, list) or len(tasks) != 5:
                    return False

                # Check each task has required fields
                for task in tasks:
                    required_task_keys = ["task_id", "name"]
                    if not all(key in task for key in required_task_keys):
                        return False

            return True

        except Exception:
            return False
