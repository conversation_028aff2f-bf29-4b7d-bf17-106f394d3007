"""
Domain Classification Agent - Gemini AI Only

This agent <PERSON><PERSON><PERSON> uses Gemini AI to classify domains from user input.
No fallback, no pattern matching, no keyword detection - pure Gemini AI.
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, Any
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class DomainClassificationAgent(BaseIgnitionAgent):
    """
    Simplified Domain Classification Agent - Gemini AI Only
    
    Purpose: Classify user input into specific domains using only Gemini AI
    Input: user_input (string)
    Output: domain_analysis with primary_domain and basic metadata
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Domain Classification Agent with Gemini AI only."""
        super().__init__("domain_classifier", config)
        
        # Gemini AI configuration
        self.temperature = 0.3
        self.max_tokens = 1000
        self.timeout = 30
        
        # Initialize Gemini client
        self._init_gemini_client()
        
        self.logger.info("🤖 Domain Classification Agent initialized - Gemini AI Only")

    def _init_gemini_client(self):
        """Initialize Gemini AI client"""
        try:
            import google.generativeai as genai
            
            # Get API keys
            api_key_1 = os.getenv('GOOGLE_AI_API_KEY_1')
            api_key_2 = os.getenv('GOOGLE_AI_API_KEY_2')
            
            if not api_key_1 or not api_key_2:
                raise ValueError("GOOGLE_AI_API_KEY_1 and GOOGLE_AI_API_KEY_2 environment variables not set")
            
            # Configure primary client
            genai.configure(api_key=api_key_1)
            self.primary_model = genai.GenerativeModel('gemini-2.0-flash-exp')
            
            # Configure fallback client (different API key)
            self.fallback_client = genai
            self.fallback_api_key = api_key_2
            self.fallback_model_name = 'gemini-2.5-flash'
            
            self.logger.info("✅ Gemini AI client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini client: {e}")
            raise e

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Process user input to classify domain using Gemini AI only
        
        Args:
            state: PlanGenerationState with user_input
            
        Returns:
            Dict with success status and domain_analysis
        """
        user_input = state["user_input"]
        
        self.logger.info("🚀 AGENT 1 STARTING: Gemini AI domain classification")
        self.logger.info(f"📝 Input length: {len(user_input)} characters")
        
        try:
            # Call Gemini AI for domain classification
            domain_analysis = await self._classify_with_gemini(user_input)
            
            if domain_analysis:
                self.logger.info(f"✅ SUCCESS: Domain '{domain_analysis['primary_domain']}' (confidence: {domain_analysis['confidence_score']:.2f})")
                
                return {
                    "success": True,
                    "domain_analysis": domain_analysis,
                    "metadata": {
                        "agent": "DomainClassificationAgent",
                        "method": "gemini_ai_only",
                        "timestamp": datetime.now().isoformat()
                    }
                }
            else:
                # If Gemini fails, return error (no fallback)
                error_msg = "Gemini AI domain classification failed"
                self.logger.error(f"❌ {error_msg}")
                
                return {
                    "success": False,
                    "error": error_msg,
                    "metadata": {
                        "agent": "DomainClassificationAgent",
                        "method": "gemini_ai_failed",
                        "timestamp": datetime.now().isoformat()
                    }
                }
                
        except Exception as e:
            error_msg = f"Domain classification error: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "metadata": {
                    "agent": "DomainClassificationAgent",
                    "method": "exception",
                    "timestamp": datetime.now().isoformat()
                }
            }

    async def _classify_with_gemini(self, user_input: str) -> Dict[str, Any]:
        """
        Classify domain using Gemini AI - ONLY METHOD
        
        Args:
            user_input: User's project description
            
        Returns:
            Dict with domain analysis or None if failed
        """
        try:
            # Create domain classification prompt
            system_prompt = """You are an expert domain classifier. Analyze the user's request and classify it into the MOST SPECIFIC domain.

AVAILABLE DOMAINS (choose the most specific one):
- machine_learning (for ML, data science, AI projects, neural networks, deep learning)
- web_development (for websites, web applications, React, Node.js, HTML, CSS, JavaScript)
- mobile_app_development (for mobile apps, iOS, Android, Flutter, React Native)
- data_science (for data analysis, statistics, visualization, data processing)
- artificial_intelligence (for AI systems, chatbots, computer vision, NLP)
- blockchain_development (for blockchain, cryptocurrency, smart contracts, DeFi)
- game_development (for games, Unity, game engines, gaming platforms)
- e_commerce (for online stores, marketplaces, shopping platforms)
- fintech (for financial technology, payments, banking, trading)
- language_learning (for learning languages, language apps, translation)
- cooking_culinary (for cooking, recipes, culinary skills, chef training)
- fitness_health (for fitness, health, wellness, medical applications)
- photography (for photography, photo editing, visual arts)
- music_production (for music, audio, sound production, instruments)
- video_production (for video editing, filmmaking, streaming)
- education_learning (for educational platforms, online courses, tutoring)
- business_management (for business tools, CRM, project management)
- social_media (for social platforms, community building, networking)
- content_creation (for blogging, writing, content management)
- general_project (for unclear or mixed domains)

RETURN ONLY VALID JSON:
{
    "primary_domain": "domain_name",
    "confidence_score": 0.0-1.0,
    "complexity_level": "beginner|intermediate|advanced",
    "reasoning": "brief explanation of classification",
    "extracted_requirements": {
        "functional": ["requirement1", "requirement2"],
        "non_functional": ["performance", "scalability"]
    },
    "constraints": {
        "time": "estimated_duration"
    }
}"""

            user_prompt = f"""Classify this user request into the most specific domain:

"{user_input}"

Provide domain classification with confidence score and basic analysis."""

            # Call Gemini AI
            response = await self._call_gemini_api(system_prompt, user_prompt)
            
            if response:
                # Parse JSON response
                domain_analysis = self._parse_json_response(response)
                
                if domain_analysis and self._validate_response(domain_analysis):
                    # Add metadata
                    domain_analysis["processing_method"] = "gemini_ai_only"
                    domain_analysis["model_used"] = "gemini_ai"
                    domain_analysis["timestamp"] = datetime.now().isoformat()
                    
                    return domain_analysis
                else:
                    self.logger.warning("⚠️ Invalid Gemini response format")
                    return None
            else:
                self.logger.warning("⚠️ No response from Gemini AI")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Gemini classification error: {e}")
            return None

    async def _call_gemini_api(self, system_prompt: str, user_prompt: str) -> str:
        """
        Call Gemini AI API with error handling
        
        Args:
            system_prompt: System instructions
            user_prompt: User query
            
        Returns:
            Response text or None if failed
        """
        try:
            # Combine prompts
            full_prompt = f"{system_prompt}\n\n{user_prompt}"
            
            # Try primary model first
            try:
                response = await self.primary_model.generate_content_async(
                    full_prompt,
                    generation_config={
                        'temperature': self.temperature,
                        'max_output_tokens': self.max_tokens,
                    }
                )
                
                if response and response.text:
                    self.logger.info("✅ Primary Gemini model responded")
                    return response.text.strip()
                    
            except Exception as e:
                self.logger.warning(f"⚠️ Primary model failed: {e}")
                
                # Try fallback model with different API key
                try:
                    import google.generativeai as genai
                    genai.configure(api_key=self.fallback_api_key)
                    fallback_model = genai.GenerativeModel(self.fallback_model_name)
                    
                    response = await fallback_model.generate_content_async(
                        full_prompt,
                        generation_config={
                            'temperature': self.temperature,
                            'max_output_tokens': self.max_tokens,
                        }
                    )
                    
                    if response and response.text:
                        self.logger.info("✅ Fallback Gemini model responded")
                        return response.text.strip()
                        
                except Exception as e2:
                    self.logger.error(f"❌ Fallback model also failed: {e2}")
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Gemini API call failed: {e}")
            return None

    def _parse_json_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse JSON response from Gemini AI
        
        Args:
            response_text: Raw response from Gemini
            
        Returns:
            Parsed JSON dict or None if failed
        """
        try:
            # Clean response text
            text = response_text.strip()
            
            # Remove markdown formatting if present
            if '```json' in text:
                start = text.find('```json') + 7
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            elif '```' in text:
                start = text.find('```') + 3
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            
            # Find JSON object boundaries
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            
            if start_idx >= 0 and end_idx > start_idx:
                json_text = text[start_idx:end_idx]
                result = json.loads(json_text)
                self.logger.info("✅ JSON response parsed successfully")
                return result
            else:
                self.logger.warning("⚠️ No valid JSON found in response")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"❌ JSON parsing error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Response parsing error: {e}")
            return None

    def _validate_response(self, domain_analysis: Dict[str, Any]) -> bool:
        """
        Validate domain analysis response
        
        Args:
            domain_analysis: Parsed response from Gemini
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required fields
            required_fields = ["primary_domain", "confidence_score", "complexity_level"]
            
            for field in required_fields:
                if field not in domain_analysis:
                    self.logger.error(f"Missing required field: {field}")
                    return False
            
            # Validate confidence score
            confidence = domain_analysis.get("confidence_score", 0)
            if not isinstance(confidence, (int, float)) or not (0 <= confidence <= 1):
                self.logger.error(f"Invalid confidence score: {confidence}")
                return False
            
            # Validate complexity level
            complexity = domain_analysis.get("complexity_level", "")
            valid_complexity = ["beginner", "intermediate", "advanced"]
            if complexity not in valid_complexity:
                self.logger.error(f"Invalid complexity level: {complexity}")
                return False
            
            # Validate domain name (not empty)
            domain = domain_analysis.get("primary_domain", "")
            if not domain or not isinstance(domain, str):
                self.logger.error(f"Invalid domain name: {domain}")
                return False
            
            self.logger.info("✅ Response validation passed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Validation error: {e}")
            return False

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """
        Validate agent output format
        
        Args:
            output: Agent output to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            # Check success field
            if "success" not in output:
                return False
            
            # If successful, validate domain_analysis
            if output["success"]:
                domain_analysis = output.get("domain_analysis", {})
                return self._validate_response(domain_analysis)
            else:
                # If failed, should have error field
                return "error" in output
                
        except Exception as e:
            self.logger.error(f"❌ Output validation error: {e}")
            return False
