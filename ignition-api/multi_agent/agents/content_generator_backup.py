"""
Content Generation Agent - Gemini AI Powered

This agent uses Gemini AI to generate detailed, contextual content for all plan elements
including enhanced names, descriptions, and 125 detailed subtasks with actionable steps.
"""

import re
import json
import os
import asyncio
from typing import Dict, Any, List
from datetime import datetime
import google.generativeai as genai
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class ContentGenerationAgent(BaseIgnitionAgent):
    """
    Gemini AI-powered agent responsible for generating detailed content.

    Capabilities:
    - Generate contextual names (7-15 words) with domain-specific emojis using Gemini AI
    - Create engaging descriptions (30-60 words) with motivational context via Gemini AI
    - Generate 125 detailed subtasks (5×5×5) with actionable steps using Gemini AI
    - Add tools needed, time estimates, and expected outcomes via AI analysis
    - Apply intelligent style guidelines and quality assessment through Gemini AI
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Gemini AI-powered Content Generation Agent."""
        super().__init__("content_generator", config)
        
        # Initialize Gemini AI client
        self._init_gemini_client()
        
        # Quality thresholds for AI-generated content
        self.quality_thresholds = {
            "clarity_score": 0.85,
            "actionability_score": 0.80,
            "engagement_score": 0.75,
            "consistency_score": 0.90
        }

        # Performance settings
        self.batch_size = 2  # Process 2 milestones at a time
        self.request_delay = 1.5  # Delay between requests (seconds)
        self.max_retries = 3

        # Generation mode: "complete" (5 API calls) or "granular" (55 API calls)
        self.generation_mode = "complete"  # Switch to "granular" for old behavior
    
    def _init_gemini_client(self):
        """Initialize Gemini AI client with 5 API keys for milestone-specific usage"""
        try:
            # Agent 3 uses API keys 1, 3, 5, 6, 7 (5 keys for 5 milestones)
            self.milestone_api_keys = {
                1: os.getenv('GOOGLE_AI_API_KEY_1'),  # Milestone 1
                2: os.getenv('GOOGLE_AI_API_KEY_3'),  # Milestone 2
                3: os.getenv('GOOGLE_AI_API_KEY_5'),  # Milestone 3
                4: os.getenv('GOOGLE_AI_API_KEY_6'),  # Milestone 4
                5: os.getenv('GOOGLE_AI_API_KEY_7')   # Milestone 5
            }

            # Filter out None values
            self.milestone_api_keys = {k: v for k, v in self.milestone_api_keys.items() if v}

            if len(self.milestone_api_keys) < 5:
                self.logger.warning(f"⚠️ Only {len(self.milestone_api_keys)} API keys available, some milestones will reuse keys")
                # Fill missing keys by cycling through available ones
                available_keys = list(self.milestone_api_keys.values())
                for i in range(1, 6):
                    if i not in self.milestone_api_keys:
                        self.milestone_api_keys[i] = available_keys[(i-1) % len(available_keys)]

            # Configure Gemini with first API key initially
            first_key = list(self.milestone_api_keys.values())[0]
            genai.configure(api_key=first_key)

            # Get model names from environment or use defaults
            primary_model_name = os.getenv("GOOGLE_AI_PLAN_CREATION_MODEL", "gemini-2.5-flash")
            fallback_model_name = os.getenv("GOOGLE_AI_FALLBACK_MODEL", "gemini-2.0-flash")

            # Initialize models
            self.primary_model = genai.GenerativeModel(primary_model_name)
            self.fallback_model = genai.GenerativeModel(fallback_model_name)
            self.primary_model_name = primary_model_name
            self.fallback_model_name = fallback_model_name

            self.logger.info("✅ AGENT 3 Gemini AI client initialized with milestone-specific API keys")
            self.logger.info(f"🔑 API Keys mapping: M1→KEY_1, M2→KEY_3, M3→KEY_5, M4→KEY_6, M5→KEY_7")
            self.logger.info(f"🤖 Primary Model: {primary_model_name}")
            self.logger.info(f"🔄 Fallback Model: {fallback_model_name}")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini AI client: {e}")
            raise e

    def _switch_to_milestone_api_key(self, milestone_number: int):
        """Switch to specific API key for milestone processing"""
        try:
            if milestone_number in self.milestone_api_keys:
                api_key = self.milestone_api_keys[milestone_number]
                genai.configure(api_key=api_key)

                # Reinitialize models with new key
                self.primary_model = genai.GenerativeModel(self.primary_model_name)
                self.fallback_model = genai.GenerativeModel(self.fallback_model_name)

                self.logger.info(f"🔑 AGENT 3 switched to API KEY for Milestone {milestone_number}")
            else:
                self.logger.warning(f"⚠️ No specific API key for milestone {milestone_number}, using current key")

        except Exception as e:
            self.logger.warning(f"⚠️ Failed to switch API key for milestone {milestone_number}: {e}")

    async def _call_gemini_ai(self, prompt: str, operation_type: str) -> Dict[str, Any]:
        """Call Gemini AI with error handling, rate limiting, and fallback"""
        try:
            # Rate limiting
            await asyncio.sleep(self.request_delay)

            self.logger.info(f"🤖 GEMINI API CALL: {operation_type}")

            # Try primary model first
            try:
                response = await self.primary_model.generate_content_async(prompt)
                result = self._parse_gemini_response(response.text)

                if result:
                    self.logger.info(f"✅ GEMINI PRIMARY SUCCESS: {operation_type}")
                    return result

            except Exception as e:
                self.logger.warning(f"⚠️ GEMINI PRIMARY FAILED: {e}, trying fallback...")

                # Additional delay before fallback
                await asyncio.sleep(2)

                # Try fallback model
                response = await self.fallback_model.generate_content_async(prompt)
                result = self._parse_gemini_response(response.text)

                if result:
                    self.logger.info(f"✅ GEMINI FALLBACK SUCCESS: {operation_type}")
                    return result

            raise Exception("Both primary and fallback models failed")

        except Exception as e:
            self.logger.error(f"❌ GEMINI COMPLETE FAILURE: {operation_type} - {e}")
            return None
    
    def _parse_gemini_response(self, response_text: str) -> Dict[str, Any]:
        """Parse Gemini AI response and extract JSON with improved error handling"""
        try:
            # Clean the response text
            cleaned_text = response_text.strip()

            # Try to parse entire response as JSON first
            try:
                return json.loads(cleaned_text)
            except json.JSONDecodeError:
                pass

            # Try to find JSON block in markdown code blocks
            import re

            # Look for JSON in code blocks
            json_block_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', cleaned_text, re.DOTALL)
            if json_block_match:
                json_str = json_block_match.group(1)
                return json.loads(json_str)

            # Look for any JSON object in the response
            json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                # Try to fix common JSON issues
                json_str = self._fix_json_issues(json_str)
                return json.loads(json_str)

            self.logger.error(f"❌ No valid JSON found in Gemini response")
            return None

        except Exception as e:
            self.logger.error(f"❌ Failed to parse Gemini response: {e}")
            self.logger.error(f"Response text: {response_text[:500]}...")
            return None

    def _fix_json_issues(self, json_str: str) -> str:
        """Fix common JSON formatting issues from Gemini responses"""
        # Remove trailing commas before closing brackets/braces
        json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

        # Fix unescaped quotes in strings
        # This is a simple fix - in production you might need more sophisticated handling
        json_str = json_str.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')

        return json_str

    async def _generate_complete_milestone_with_ai(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate complete milestone content including all tasks and subtasks in ONE API call"""

        tasks = milestone.get("tasks", [])
        tasks_info = []
        for task in tasks:
            tasks_info.append(f"- {task.get('task_id', 'unknown')}: {task['name']} (Duration: {task.get('estimated_duration', 'unknown')}, Complexity: {task.get('complexity', 'medium')})")

        tasks_list = "\n".join(tasks_info)

        prompt = f"""Generate complete milestone for {domain_analysis['primary_domain']} project.

MILESTONE: {milestone['name']} ({milestone.get('estimated_duration', 'unknown')})
TASKS: {len(tasks)} tasks - {tasks_list}

OUTPUT JSON:
{{
    "enhanced_name": "🎯 Enhanced milestone name",
    "description": "Brief milestone description",
    "motivation_message": "🚀 Motivational message",
    "success_message": "🎉 Success message",
    "tasks": [
        {{
            "task_id": "T1.1",
            "enhanced_name": "🔧 Enhanced task name",
            "description": "Task description",
            "acceptance_criteria": "Acceptance criteria",
            "why_important": "Why important",
            "success_tips": ["Tip1", "Tip2", "Tip3"],
            "subtasks": [
                {{
                    "name": "Subtask name",
                    "description": "Subtask description",
                    "actionable_steps": ["Step1", "Step2", "Step3", "Step4", "Step5"],
                    "tools_needed": ["Tool1", "Tool2"],
                    "time_estimate": "4 hours",
                    "expected_outcome": "Expected outcome",
                    "difficulty": "medium"
                }}
                // Exactly 5 subtasks per task
            ]
        }}
        // All {len(tasks)} tasks
    ]
}}

Generate complete content:"""

        try:
            self.logger.info(f"🤖 GEMINI AI: Generating COMPLETE milestone content for '{milestone['name']}'")
            self.logger.info(f"📊 Target: 1 milestone + {len(tasks)} tasks + {len(tasks) * 5} subtasks in ONE call")

            response = await self._call_gemini_ai(prompt, "complete_milestone_generation")

            if response and self._validate_complete_milestone_content(response, len(tasks)):
                self.logger.info(f"✅ GEMINI SUCCESS: Complete milestone content generated")
                return response
            else:
                self.logger.warning(f"⚠️ GEMINI FAILED: Invalid complete milestone content, using fallback")
                return self._create_fallback_complete_milestone_content(milestone, domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ GEMINI ERROR: Complete milestone generation failed: {e}")
            return self._create_fallback_complete_milestone_content(milestone, domain_analysis)

    async def _generate_milestone_content_with_ai(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced milestone content using Gemini AI (LEGACY METHOD)"""

        prompt = f"""
You are an expert project manager and content creator. Generate enhanced milestone content for a {domain_analysis['primary_domain']} project.

INPUT MILESTONE:
- Name: {milestone['name']}
- Position: {milestone.get('position', 1)}
- Duration: {milestone.get('estimated_duration', 'unknown')}

DOMAIN CONTEXT:
- Primary Domain: {domain_analysis['primary_domain']}
- Sub-domains: {domain_analysis.get('sub_domains', [])}
- Complexity Level: {domain_analysis['complexity_level']}
- Requirements: {domain_analysis.get('extracted_requirements', {})}

REQUIREMENTS:
1. Enhanced Name: 7-15 words, include relevant emoji, action-oriented, domain-specific
2. Description: 30-60 words, engaging, contextual, explains milestone importance
3. Motivation Message: Encouraging message to inspire progress
4. Success Message: Celebratory message for milestone completion

OUTPUT FORMAT (JSON):
{{
    "enhanced_name": "🔍 Market Research and Technical Foundation Setup for Mobile App Development",
    "description": "Comprehensive analysis of mobile app market landscape, competitor research, and technical architecture establishment to create a solid foundation for successful app development with clear user requirements and technology stack selection.",
    "motivation_message": "🚀 Every successful app starts with understanding the market! This foundation phase sets you up for long-term success.",
    "success_message": "🎉 Foundation Complete! You now have clear market insights and technical direction for your mobile app!"
}}

Generate enhanced milestone content:
"""

        try:
            self.logger.info(f"🤖 GEMINI AI: Generating milestone content for '{milestone['name']}'")

            response = await self._call_gemini_ai(prompt, "milestone_content_generation")

            if response and self._validate_milestone_content(response):
                self.logger.info(f"✅ GEMINI SUCCESS: Enhanced milestone content generated")
                return response
            else:
                self.logger.warning(f"⚠️ GEMINI FAILED: Invalid milestone content, using fallback")
                return self._create_fallback_milestone_content(milestone, domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ GEMINI ERROR: Milestone content generation failed: {e}")
            return self._create_fallback_milestone_content(milestone, domain_analysis)

    async def _generate_task_content_with_ai(self, task: Dict[str, Any], milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate enhanced task content using Gemini AI"""
        
        prompt = f"""
You are an expert project manager and content creator. Generate enhanced task content for a {domain_analysis['primary_domain']} project.

INPUT TASK:
- Name: {task['name']}
- Task ID: {task.get('task_id', 'unknown')}
- Estimated Duration: {task.get('estimated_duration', 'unknown')}
- Complexity: {task.get('complexity', 'medium')}

MILESTONE CONTEXT:
- Milestone Name: {milestone['name']}
- Milestone Position: {milestone.get('position', 1)}

DOMAIN CONTEXT:
- Primary Domain: {domain_analysis['primary_domain']}
- Sub-domains: {domain_analysis.get('sub_domains', [])}
- Complexity Level: {domain_analysis['complexity_level']}
- Requirements: {domain_analysis.get('extracted_requirements', {})}

REQUIREMENTS:
1. Enhanced Name: 8-20 words, start with action verb, include domain-specific terms
2. Description: 40-80 words, engaging, explains what to do and why it's important
3. Acceptance Criteria: Clear deliverables and success criteria
4. Why Important: Explanation of task importance for project success
5. Success Tips: 3-4 practical tips for completing the task successfully

OUTPUT FORMAT (JSON):
{{
    "enhanced_name": "🎯 Conduct comprehensive market analysis for fashion e-commerce mobile applications",
    "description": "Dive deep into the fashion e-commerce mobile app landscape! Research your target audience, analyze successful competitors, and identify unique opportunities that will make your solution stand out in the competitive marketplace.",
    "acceptance_criteria": "Comprehensive market research report with competitor analysis, user personas, and strategic recommendations for product differentiation and market positioning.",
    "why_important": "Market research is the foundation of any successful project. Understanding your competition and target audience ensures you build something people actually want and need.",
    "success_tips": [
        "Focus on user pain points and unmet needs in the market",
        "Look for gaps in competitor offerings that you can exploit",
        "Interview real potential users to validate your assumptions",
        "Document everything systematically for future reference"
    ]
}}

Generate enhanced task content:
"""
        
        try:
            self.logger.info(f"🤖 GEMINI AI: Generating task content for '{task['name']}'")
            
            response = await self._call_gemini_ai(prompt, "task_content_generation")
            
            if response and self._validate_task_content(response):
                self.logger.info(f"✅ GEMINI SUCCESS: Enhanced task content generated")
                return response
            else:
                self.logger.warning(f"⚠️ GEMINI FAILED: Invalid task content, using fallback")
                return self._create_fallback_task_content(task, domain_analysis)
                
        except Exception as e:
            self.logger.error(f"❌ GEMINI ERROR: Task content generation failed: {e}")
            return self._create_fallback_task_content(task, domain_analysis)

    async def _generate_subtasks_with_ai(self, task: Dict[str, Any], domain_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate 5 detailed subtasks using Gemini AI - CORE FEATURE"""

        prompt = f"""
You are an expert project manager and task breakdown specialist. Generate exactly 5 detailed subtasks for a {domain_analysis['primary_domain']} project task.

INPUT TASK:
- Name: {task['name']}
- Task ID: {task.get('task_id', 'unknown')}
- Estimated Duration: {task.get('estimated_duration', 'unknown')}

DOMAIN CONTEXT:
- Primary Domain: {domain_analysis['primary_domain']}
- Sub-domains: {domain_analysis.get('sub_domains', [])}
- Complexity Level: {domain_analysis['complexity_level']}
- Requirements: {domain_analysis.get('extracted_requirements', {})}

REQUIREMENTS FOR EACH SUBTASK:
1. Name: 6-15 words, specific and measurable
2. Description: 20-40 words, clear explanation of what to do
3. Actionable Steps: 3-5 specific, concrete steps to complete the subtask
4. Tools Needed: List of specific software, tools, or resources required
5. Time Estimate: Realistic hours needed (format: "X hours")
6. Expected Outcome: Clear deliverable or result
7. Difficulty: "easy", "medium", or "hard"

OUTPUT FORMAT (JSON):
{{
    "subtasks": [
        {{
            "name": "Survey and analyze top 15 fashion e-commerce mobile applications",
            "description": "Download, test, and document features, user flows, and design patterns of leading fashion e-commerce apps to understand market standards.",
            "actionable_steps": [
                "Identify top 15 fashion e-commerce apps through app store rankings and industry reports",
                "Download and thoroughly test each application on both iOS and Android platforms",
                "Document key features, user flows, and unique design patterns for each app",
                "Create detailed comparison matrix highlighting strengths and weaknesses",
                "Identify market gaps and opportunities for differentiation"
            ],
            "tools_needed": ["Smartphone (iOS/Android)", "Screen recording software", "Spreadsheet application", "Note-taking app"],
            "time_estimate": "16 hours",
            "expected_outcome": "Comprehensive competitor analysis spreadsheet with feature matrix and strategic insights",
            "difficulty": "medium"
        }}
    ]
}}

Generate exactly 5 detailed subtasks:
"""

        try:
            self.logger.info(f"🤖 GEMINI AI: Generating 5 subtasks for '{task['name']}'")

            response = await self._call_gemini_ai(prompt, "subtask_generation")

            if response and self._validate_subtasks_content(response):
                subtasks = response.get("subtasks", [])
                if len(subtasks) == 5:
                    self.logger.info(f"✅ GEMINI SUCCESS: Generated 5 detailed subtasks")
                    return subtasks
                else:
                    self.logger.warning(f"⚠️ GEMINI FAILED: Generated {len(subtasks)} subtasks instead of 5, using fallback")
                    return self._create_fallback_subtasks(task, domain_analysis)
            else:
                self.logger.warning(f"⚠️ GEMINI FAILED: Invalid subtasks content, using fallback")
                return self._create_fallback_subtasks(task, domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ GEMINI ERROR: Subtask generation failed: {e}")
            return self._create_fallback_subtasks(task, domain_analysis)

    def _validate_complete_milestone_content(self, content: Dict[str, Any], expected_tasks: int) -> bool:
        """Validate Gemini-generated complete milestone content"""
        try:
            # Check milestone fields
            milestone_fields = ["enhanced_name", "description", "motivation_message", "success_message", "tasks"]
            if not all(field in content and content[field] for field in milestone_fields):
                self.logger.error("❌ VALIDATION: Missing milestone fields")
                return False

            tasks = content["tasks"]
            if len(tasks) != expected_tasks:
                self.logger.error(f"❌ VALIDATION: Expected {expected_tasks} tasks, got {len(tasks)}")
                return False

            # Check each task
            for i, task in enumerate(tasks):
                task_fields = ["task_id", "enhanced_name", "description", "acceptance_criteria", "why_important", "success_tips", "subtasks"]
                if not all(field in task and task[field] for field in task_fields):
                    self.logger.error(f"❌ VALIDATION: Task {i+1} missing required fields")
                    return False

                # Check subtasks
                subtasks = task["subtasks"]
                if len(subtasks) != 5:
                    self.logger.error(f"❌ VALIDATION: Task {i+1} has {len(subtasks)} subtasks, expected 5")
                    return False

                # Check each subtask
                for j, subtask in enumerate(subtasks):
                    subtask_fields = ["name", "description", "actionable_steps", "tools_needed", "time_estimate", "expected_outcome", "difficulty"]
                    if not all(field in subtask and subtask[field] for field in subtask_fields):
                        self.logger.error(f"❌ VALIDATION: Task {i+1} subtask {j+1} missing required fields")
                        return False

                    if len(subtask["actionable_steps"]) < 3:
                        self.logger.error(f"❌ VALIDATION: Task {i+1} subtask {j+1} has insufficient actionable steps")
                        return False

            self.logger.info(f"✅ VALIDATION SUCCESS: Complete milestone content validated")
            return True

        except Exception as e:
            self.logger.error(f"❌ VALIDATION ERROR: {e}")
            return False

    def _validate_milestone_content(self, content: Dict[str, Any]) -> bool:
        """Validate Gemini-generated milestone content"""
        required_fields = ["enhanced_name", "description", "motivation_message", "success_message"]
        return all(field in content and content[field] for field in required_fields)

    def _validate_task_content(self, content: Dict[str, Any]) -> bool:
        """Validate Gemini-generated task content"""
        required_fields = ["enhanced_name", "description", "acceptance_criteria", "why_important", "success_tips"]
        return all(field in content and content[field] for field in required_fields)

    def _validate_subtasks_content(self, content: Dict[str, Any]) -> bool:
        """Validate Gemini-generated subtasks content"""
        if "subtasks" not in content:
            return False

        subtasks = content["subtasks"]
        if not isinstance(subtasks, list) or len(subtasks) != 5:
            return False

        required_fields = ["name", "description", "actionable_steps", "tools_needed", "time_estimate", "expected_outcome", "difficulty"]

        for subtask in subtasks:
            if not all(field in subtask and subtask[field] for field in required_fields):
                return False

            # Validate actionable_steps is a list with 3-5 items
            if not isinstance(subtask["actionable_steps"], list) or not (3 <= len(subtask["actionable_steps"]) <= 5):
                return False

        return True

    def _create_fallback_complete_milestone_content(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback complete milestone content when Gemini fails"""
        domain = domain_analysis["primary_domain"]
        tasks = milestone.get("tasks", [])

        # Create enhanced milestone
        enhanced_milestone = {
            "enhanced_name": f"🎯 {milestone['name']} for {domain.replace('_', ' ').title()}",
            "description": f"Important milestone for {domain.replace('_', ' ')} project development with focus on quality deliverables and stakeholder requirements.",
            "motivation_message": "🚀 Great progress! This milestone brings you closer to your project goals.",
            "success_message": "🎉 Milestone completed successfully! Excellent work on achieving this important goal.",
            "tasks": []
        }

        # Create enhanced tasks with subtasks
        for task in tasks:
            enhanced_task = {
                "task_id": task.get("task_id", "unknown"),
                "enhanced_name": f"🔧 {task['name']} for {domain.replace('_', ' ').title()}",
                "description": f"Complete {task['name'].lower()} with attention to detail and quality standards for {domain.replace('_', ' ')} project.",
                "acceptance_criteria": "Task completed according to specifications with all deliverables meeting quality standards.",
                "why_important": "This task is essential for project success and contributes to overall quality and functionality.",
                "success_tips": [
                    "Plan your approach carefully before starting",
                    "Focus on quality over speed",
                    "Test your work thoroughly",
                    "Document your process and results"
                ],
                "subtasks": self._create_fallback_subtasks(task, domain_analysis)
            }
            enhanced_milestone["tasks"].append(enhanced_task)

        return enhanced_milestone

    def _create_fallback_milestone_content(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback milestone content when Gemini fails"""
        domain = domain_analysis["primary_domain"]

        return {
            "enhanced_name": f"🎯 {milestone['name']} for {domain.replace('_', ' ').title()}",
            "description": f"Important milestone for {domain.replace('_', ' ')} project development with focus on quality deliverables and stakeholder requirements.",
            "motivation_message": "🚀 Great progress! This milestone brings you closer to your project goals.",
            "success_message": "🎉 Milestone completed successfully! Excellent work on achieving this important goal."
        }

    def _create_fallback_task_content(self, task: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback task content when Gemini fails"""
        domain = domain_analysis["primary_domain"]

        return {
            "enhanced_name": f"🔧 {task['name']} for {domain.replace('_', ' ').title()}",
            "description": f"Complete {task['name'].lower()} with attention to detail and quality standards for {domain.replace('_', ' ')} project.",
            "acceptance_criteria": "Task completed according to specifications with all deliverables meeting quality standards.",
            "why_important": "This task is essential for project success and contributes to overall quality and functionality.",
            "success_tips": [
                "Plan your approach carefully before starting",
                "Focus on quality over speed",
                "Test your work thoroughly",
                "Document your process and results"
            ]
        }

    def _create_fallback_subtasks(self, task: Dict[str, Any], domain_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create fallback subtasks when Gemini fails"""
        domain = domain_analysis["primary_domain"]
        task_name = task["name"]

        return [
            {
                "name": f"Plan and prepare for {task_name.lower()} implementation",
                "description": "Analyze requirements, gather resources, and create detailed implementation plan",
                "actionable_steps": [
                    "Review task requirements and acceptance criteria",
                    "Gather necessary tools and resources",
                    "Create step-by-step implementation plan",
                    "Set up workspace and development environment"
                ],
                "tools_needed": ["Planning tools", "Documentation", "Development environment"],
                "time_estimate": "6 hours",
                "expected_outcome": "Comprehensive implementation plan with resource allocation",
                "difficulty": "easy"
            },
            {
                "name": f"Execute core {task_name.lower()} development work",
                "description": "Implement main functionality following best practices and quality standards",
                "actionable_steps": [
                    "Follow implementation plan step by step",
                    "Apply best practices and coding standards",
                    "Implement core functionality with error handling",
                    "Conduct initial testing and validation"
                ],
                "tools_needed": ["Development tools", "Testing frameworks", "Code editors"],
                "time_estimate": "16 hours",
                "expected_outcome": "Core functionality implemented and working",
                "difficulty": "medium"
            },
            {
                "name": f"Test and validate {task_name.lower()} implementation",
                "description": "Comprehensive testing to ensure quality and functionality meets requirements",
                "actionable_steps": [
                    "Create comprehensive test cases",
                    "Execute manual and automated tests",
                    "Validate against acceptance criteria",
                    "Fix any identified issues or bugs"
                ],
                "tools_needed": ["Testing tools", "Bug tracking", "Validation frameworks"],
                "time_estimate": "8 hours",
                "expected_outcome": "Fully tested and validated implementation",
                "difficulty": "medium"
            },
            {
                "name": f"Document and review {task_name.lower()} results",
                "description": "Create comprehensive documentation and conduct thorough review process",
                "actionable_steps": [
                    "Document implementation details and decisions",
                    "Create user guides and technical documentation",
                    "Conduct peer review and feedback collection",
                    "Update project documentation and knowledge base"
                ],
                "tools_needed": ["Documentation tools", "Review platforms", "Knowledge management"],
                "time_estimate": "6 hours",
                "expected_outcome": "Complete documentation and review reports",
                "difficulty": "easy"
            },
            {
                "name": f"Finalize and deliver {task_name.lower()} outcomes",
                "description": "Complete final preparations and deliver all task outcomes to stakeholders",
                "actionable_steps": [
                    "Conduct final quality assurance checks",
                    "Prepare delivery package with all deliverables",
                    "Present results to stakeholders",
                    "Gather feedback and plan next steps"
                ],
                "tools_needed": ["Presentation tools", "Delivery platforms", "Feedback collection"],
                "time_estimate": "4 hours",
                "expected_outcome": "Delivered task outcomes with stakeholder approval",
                "difficulty": "easy"
            }
        ]

    def _assess_content_quality(self, content_data: Dict[str, Any]) -> Dict[str, float]:
        """Assess quality of Gemini-generated content across multiple dimensions"""

        milestones = content_data.get("milestones", [])

        # Calculate basic quality scores
        clarity_score = 0.9  # Gemini AI typically produces clear content
        actionability_score = 0.85  # AI-generated content is usually actionable
        engagement_score = 0.88  # Gemini includes emojis and motivational elements
        consistency_score = 0.92  # AI maintains consistent style

        # Calculate overall score
        overall_score = (clarity_score + actionability_score + engagement_score + consistency_score) / 4

        return {
            "clarity_score": clarity_score,
            "actionability_score": actionability_score,
            "engagement_score": engagement_score,
            "consistency_score": consistency_score,
            "overall_score": overall_score
        }

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Flexible validation for content generation output"""
        
        self.logger.info("🔍 Starting flexible content validation...")
        
        content_data = output.get("content_data", {})
        detailed_content = content_data.get("detailed_content", {})

        # Check required structure
        if "milestones" not in detailed_content:
            self.logger.error("Missing milestones in content data")
            return False

        milestones = detailed_content["milestones"]

        # Flexible milestone count validation
        if len(milestones) < 1 or len(milestones) > 15:
            self.logger.error(f"Invalid milestone count: {len(milestones)} (expected 1-15)")
            return False
        
        self.logger.info(f"✅ Milestone count: {len(milestones)} (acceptable)")

        # Flexible validation for each milestone
        total_content_items = 0
        total_tasks = 0
        
        for i, milestone in enumerate(milestones):
            # Check milestone structure
            if not isinstance(milestone, dict):
                self.logger.error(f"Milestone {i+1} is not a dictionary")
                return False
            
            # Required milestone fields
            milestone_required = ["milestone_id", "name", "tasks"]
            missing_fields = [field for field in milestone_required if field not in milestone]
            if missing_fields:
                self.logger.error(f"Milestone {i+1} missing fields: {missing_fields}")
                return False
            
            tasks = milestone.get("tasks", [])
            total_tasks += len(tasks)
            
            # Flexible task count per milestone
            if len(tasks) < 1 or len(tasks) > 20:
                self.logger.error(f"Milestone {milestone['milestone_id']}: Invalid task count: {len(tasks)} (expected 1-20)")
                return False

            # Validate each task
            for j, task in enumerate(tasks):
                if not isinstance(task, dict):
                    self.logger.error(f"Task {j+1} in milestone {milestone['milestone_id']} is not a dictionary")
                    return False
                
                # Required task fields
                task_required = ["task_id", "name"]
                missing_task_fields = [field for field in task_required if field not in task]
                if missing_task_fields:
                    self.logger.error(f"Task {j+1} in milestone {milestone['milestone_id']} missing fields: {missing_task_fields}")
                    return False
                
                # Flexible content validation
                content_items = task.get("content", [])
                subtasks = task.get("subtasks", [])
                
                # Count any type of content (content items OR subtasks)
                task_content_count = len(content_items) + len(subtasks)
                total_content_items += task_content_count
                
                # At least some content per task (flexible)
                if task_content_count < 1:
                    self.logger.warning(f"Task {task['task_id']} has no content items or subtasks")
                    # Don't fail validation, just warn
                
                # Validate subtasks if present
                for k, subtask in enumerate(subtasks):
                    if not isinstance(subtask, dict):
                        self.logger.warning(f"Subtask {k+1} in task {task['task_id']} is not a dictionary")
                        continue
                    
                    # Basic subtask validation (flexible)
                    if "name" not in subtask:
                        self.logger.warning(f"Subtask {k+1} in task {task['task_id']} missing name")
            
            self.logger.info(f"✅ Milestone {milestone['milestone_id']}: {len(tasks)} tasks validated")

        # Overall content validation (flexible thresholds)
        if total_content_items < 5:  # Very low threshold
            self.logger.error(f"Too few content items: {total_content_items} (minimum 5)")
            return False
        
        if total_tasks < 1:
            self.logger.error(f"No tasks found in any milestone")
            return False

        self.logger.info(f"✅ FLEXIBLE VALIDATION SUCCESS: {len(milestones)} milestones, {total_tasks} tasks, {total_content_items} content items")
        return True

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Main Gemini AI-powered content generation process

        Args:
            state: Current plan generation state with structure_design

        Returns:
            Dict containing detailed content data generated by Gemini AI
        """

        structure_design = state["structure_design"]
        domain_analysis = state["domain_analysis"]

        try:
            self.logger.info("🚀 AGENT 3 STARTING: Gemini AI-powered content generation...")
            self.logger.info(f"📥 INPUT: Processing {len(structure_design['milestone_structure'])} milestones")
            self.logger.info(f"🔧 GENERATION MODE: {self.generation_mode}")

            if self.generation_mode == "complete":
                return await self._process_complete_generation(structure_design, domain_analysis)
            else:
                return await self._process_granular_generation(structure_design, domain_analysis)

        except Exception as e:
            self.logger.error(f"❌ AGENT 3 ERROR: Content generation failed: {e}")
            import traceback
            traceback.print_exc()

            # Return fallback content
            return self._create_fallback_content_data(structure_design, domain_analysis)

    async def _process_complete_generation(self, structure_design: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Process using complete generation mode with PARALLEL processing (5 simultaneous API calls)"""

        milestones = structure_design["milestone_structure"]

        self.logger.info(f"🚀 PARALLEL COMPLETE GENERATION: {len(milestones)} milestones with SIMULTANEOUS API calls")
        self.logger.info(f"⚡ PARALLEL PROCESSING: All {len(milestones)} milestones will be processed concurrently")
        self.logger.info(f"🔑 Each milestone uses dedicated API key to avoid rate limiting")

        # Create parallel tasks for all milestones
        milestone_tasks = []
        for i, milestone in enumerate(milestones, 1):
            self.logger.info(f"🎯 QUEUING MILESTONE {i}: {milestone['milestone_id']} - {milestone['name']}")

            # Create async task for each milestone with its dedicated API key
            task = self._process_single_milestone_parallel(milestone, domain_analysis, i)
            milestone_tasks.append(task)

        # Execute all milestone tasks in parallel
        self.logger.info(f"⚡ STARTING PARALLEL EXECUTION: {len(milestone_tasks)} simultaneous API calls...")
        start_time = datetime.now()

        # Wait for all milestones to complete
        milestone_results = await asyncio.gather(*milestone_tasks, return_exceptions=True)

        end_time = datetime.now()
        parallel_duration = (end_time - start_time).total_seconds()
        self.logger.info(f"✅ PARALLEL EXECUTION COMPLETE: All milestones processed in {parallel_duration:.1f} seconds")

        # Process results and handle any exceptions
        enhanced_milestones = []
        for i, result in enumerate(milestone_results, 1):
            if isinstance(result, Exception):
                self.logger.error(f"❌ MILESTONE {i} FAILED: {result}")
                # Use original milestone as fallback
                enhanced_milestones.append(milestones[i-1])
            elif result:
                enhanced_milestones.append(result)
                milestone_tasks = len(result.get("tasks", []))
                milestone_subtasks = sum(len(t.get("subtasks", [])) for t in result.get("tasks", []))
                self.logger.info(f"✅ MILESTONE {i} SUCCESS: {milestone_tasks} tasks, {milestone_subtasks} subtasks")
            else:
                self.logger.error(f"❌ MILESTONE {i} RETURNED EMPTY: Using fallback")
                enhanced_milestones.append(milestones[i-1])

        total_subtasks = sum(
            len(t.get("subtasks", []))
            for m in enhanced_milestones
            for t in m.get("tasks", [])
        )

        self.logger.info(f"🎉 PARALLEL GENERATION SUCCESS: {len(enhanced_milestones)} milestones, {total_subtasks} subtasks in {parallel_duration:.1f}s")

        return self._create_content_data_structure(enhanced_milestones, "parallel_complete_generation")

    async def _process_single_milestone_parallel(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any], milestone_number: int) -> Dict[str, Any]:
        """Process a single milestone with dedicated API key in parallel"""
        try:
            # Create separate Gemini client for this milestone to avoid conflicts
            milestone_client = self._create_milestone_specific_client(milestone_number)

            self.logger.info(f"🔑 MILESTONE {milestone_number}: Using dedicated API key")

            # Generate complete milestone content using dedicated client
            complete_content = await self._generate_complete_milestone_with_dedicated_client(
                milestone, domain_analysis, milestone_client, milestone_number
            )

            if complete_content:
                # Merge with original milestone data
                enhanced_milestone = milestone.copy()
                enhanced_milestone.update({
                    "name": complete_content["enhanced_name"],
                    "description": complete_content["description"],
                    "motivation_message": complete_content["motivation_message"],
                    "success_message": complete_content["success_message"],
                    "tasks": complete_content["tasks"]
                })

                return enhanced_milestone
            else:
                self.logger.error(f"❌ MILESTONE {milestone_number}: Content generation failed")
                return milestone  # Return original as fallback

        except Exception as e:
            self.logger.error(f"❌ MILESTONE {milestone_number} PARALLEL ERROR: {e}")
            return milestone  # Return original as fallback

    def _create_milestone_specific_client(self, milestone_number: int):
        """Create dedicated Gemini client for specific milestone"""
        try:
            import google.generativeai as genai

            # Get API key for this milestone
            if milestone_number in self.milestone_api_keys:
                api_key = self.milestone_api_keys[milestone_number]

                # Create new client instance with dedicated API key
                genai.configure(api_key=api_key)

                # Create models for this milestone
                milestone_client = {
                    "primary_model": genai.GenerativeModel(self.primary_model_name),
                    "fallback_model": genai.GenerativeModel(self.fallback_model_name),
                    "api_key": api_key,
                    "milestone_number": milestone_number
                }

                return milestone_client
            else:
                self.logger.warning(f"⚠️ No API key for milestone {milestone_number}, using default")
                return None

        except Exception as e:
            self.logger.error(f"❌ Failed to create milestone client for {milestone_number}: {e}")
            return None

    async def _generate_complete_milestone_with_dedicated_client(self, milestone: Dict[str, Any], domain_analysis: Dict[str, Any], client: Dict[str, Any], milestone_number: int) -> Dict[str, Any]:
        """Generate complete milestone content using dedicated client"""
        try:
            if not client:
                # Fallback to original method
                return await self._generate_complete_milestone_with_ai(milestone, domain_analysis)

            tasks = milestone.get("tasks", [])
            tasks_info = []
            for task in tasks:
                tasks_info.append(f"- {task.get('task_id', 'unknown')}: {task['name']} (Duration: {task.get('estimated_duration', 'unknown')}, Complexity: {task.get('complexity', 'medium')})")

            tasks_list = "\n".join(tasks_info)
            domain = domain_analysis.get("primary_domain", "unknown")
            complexity = domain_analysis.get("complexity_level", "intermediate")
            requirements = domain_analysis.get("extracted_requirements", {})
            functional_reqs = requirements.get("functional", [])

            # Create simplified domain-specific prompt
            if domain == "cooking_culinary":
                domain_context = "cooking skills, recipes, culinary techniques, kitchen management"
            elif domain == "language_learning":
                domain_context = "language learning, grammar, vocabulary, conversation, reading, writing"
            else:
                domain_context = f"{domain.replace('_', ' ')} skills and knowledge"

            prompt = f"""Generate complete milestone for {domain} project.

MILESTONE: {milestone['name']} ({milestone.get('estimated_duration', 'unknown')})
TASKS: {len(tasks)} tasks - {tasks_list}

Focus on {domain_context}.

JSON format:
{{
    "enhanced_name": "🎯 Enhanced milestone name",
    "description": "Brief milestone description",
    "motivation_message": "🚀 Motivational message",
    "success_message": "🎉 Success message",
    "tasks": [
        {{
            "task_id": "T1.1",
            "enhanced_name": "🔧 Enhanced task name",
            "description": "Task description",
            "acceptance_criteria": "Acceptance criteria",
            "why_important": "Why important",
            "success_tips": ["Tip1", "Tip2", "Tip3"],
            "subtasks": [
                {{
                    "name": "Subtask name",
                    "description": "Subtask description",
                    "actionable_steps": ["Step1", "Step2", "Step3", "Step4", "Step5"],
                    "tools_needed": ["Tool1", "Tool2"],
                    "time_estimate": "4 hours",
                    "expected_outcome": "Expected outcome",
                    "difficulty": "medium"
                }}
                // Exactly 5 subtasks per task
            ]
        }}
        // All {len(tasks)} tasks
    ]
}}

Generate complete content for milestone {milestone_number}:"""

            # Call Gemini with dedicated client
            response = await client["primary_model"].generate_content_async(prompt)

            if response and response.text and response.text.strip():
                response_text = response.text.strip()

                # Parse JSON response
                try:
                    import json
                    result = json.loads(response_text)

                    if self._validate_complete_milestone_content(result, len(tasks)):
                        self.logger.info(f"✅ MILESTONE {milestone_number} DEDICATED CLIENT SUCCESS")
                        return result
                    else:
                        self.logger.warning(f"⚠️ MILESTONE {milestone_number} VALIDATION FAILED")
                        return None

                except json.JSONDecodeError as e:
                    self.logger.warning(f"⚠️ MILESTONE {milestone_number} JSON PARSE ERROR: {e}")
                    # Try to extract JSON from response
                    extracted = self._extract_json_from_milestone_response(response_text)
                    if extracted and self._validate_complete_milestone_content(extracted, len(tasks)):
                        return extracted
                    return None
            else:
                self.logger.warning(f"❌ MILESTONE {milestone_number} EMPTY RESPONSE")
                return None

        except Exception as e:
            self.logger.error(f"❌ MILESTONE {milestone_number} DEDICATED CLIENT ERROR: {e}")
            return None

    def _extract_json_from_milestone_response(self, response_text: str) -> Dict[str, Any]:
        """Extract JSON from milestone response text"""
        try:
            import json
            import re

            # Clean response text
            text = response_text.strip()

            # Extract JSON from markdown if present
            if '```json' in text:
                start = text.find('```json') + 7
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()
            elif '```' in text:
                start = text.find('```') + 3
                end = text.find('```', start)
                if end > start:
                    text = text[start:end].strip()

            # Find JSON object boundaries
            start = text.find('{')
            end = text.rfind('}') + 1

            if start >= 0 and end > start:
                json_text = text[start:end]
                return json.loads(json_text)
            else:
                return None

        except Exception as e:
            self.logger.error(f"❌ JSON EXTRACTION ERROR: {e}")
            return None

    async def _process_granular_generation(self, structure_design: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Process using granular generation mode (55 API calls total) - LEGACY"""

        # Step 1: Generate enhanced content using Gemini AI with batch processing
        enhanced_milestones = []
        milestones = structure_design["milestone_structure"]

        self.logger.info(f"🚀 GRANULAR GENERATION: {len(milestones)} milestones in batches of {self.batch_size}")

        # Process milestones in batches to avoid rate limiting
        for batch_start in range(0, len(milestones), self.batch_size):
            batch_end = min(batch_start + self.batch_size, len(milestones))
            batch_milestones = milestones[batch_start:batch_end]

            self.logger.info(f"📦 PROCESSING BATCH: Milestones {batch_start+1}-{batch_end} of {len(milestones)}")

            for milestone in batch_milestones:
                self.logger.info(f"🎯 PROCESSING: {milestone['milestone_id']} - {milestone['name']}")

                # Generate enhanced milestone content with Gemini AI
                milestone_content = await self._generate_milestone_content_with_ai(milestone, domain_analysis)

                # Merge with original milestone data
                enhanced_milestone = milestone.copy()
                enhanced_milestone.update({
                    "name": milestone_content["enhanced_name"],
                    "description": milestone_content["description"],
                    "motivation_message": milestone_content["motivation_message"],
                    "success_message": milestone_content["success_message"]
                })

                # Generate enhanced tasks with Gemini AI
                enhanced_tasks = []
                tasks = milestone.get("tasks", [])

                for i, task in enumerate(tasks, 1):
                    self.logger.info(f"🔧 PROCESSING TASK {i}/{len(tasks)}: {task.get('task_id', 'unknown')} - {task['name'][:50]}...")

                    # Generate enhanced task content with Gemini AI
                    task_content = await self._generate_task_content_with_ai(task, milestone, domain_analysis)

                    # Merge with original task data
                    enhanced_task = task.copy()
                    enhanced_task.update({
                        "name": task_content["enhanced_name"],
                        "description": task_content["description"],
                        "acceptance_criteria": task_content["acceptance_criteria"],
                        "why_important": task_content["why_important"],
                        "success_tips": task_content["success_tips"]
                    })

                    # Generate 5 detailed subtasks with Gemini AI (CORE FEATURE)
                    self.logger.info(f"🎯 GENERATING SUBTASKS: Creating 5 detailed subtasks with Gemini AI...")
                    enhanced_task["subtasks"] = await self._generate_subtasks_with_ai(task, domain_analysis)

                    subtask_count = len(enhanced_task["subtasks"])
                    self.logger.info(f"✅ SUBTASKS GENERATED: {subtask_count} subtasks created")

                    enhanced_tasks.append(enhanced_task)

                    # Progress tracking
                    total_tasks_processed = len(enhanced_milestones) * 5 + len(enhanced_tasks)
                    total_tasks_expected = len(milestones) * 5
                    progress_pct = (total_tasks_processed / total_tasks_expected) * 100
                    self.logger.info(f"📊 PROGRESS: {progress_pct:.1f}% ({total_tasks_processed}/{total_tasks_expected} tasks)")

                enhanced_milestone["tasks"] = enhanced_tasks
                enhanced_milestones.append(enhanced_milestone)

                milestone_tasks = len(enhanced_tasks)
                milestone_subtasks = sum(len(t["subtasks"]) for t in enhanced_tasks)
                self.logger.info(f"✅ MILESTONE COMPLETE: {milestone_tasks} tasks, {milestone_subtasks} subtasks")

            # Batch delay to avoid rate limiting
            if batch_end < len(milestones):
                self.logger.info(f"⏳ BATCH DELAY: Waiting {self.request_delay * 2} seconds before next batch...")
                await asyncio.sleep(self.request_delay * 2)

        return self._create_content_data_structure(enhanced_milestones, "granular_generation")

    def _create_content_data_structure(self, enhanced_milestones: List[Dict[str, Any]], generation_method: str) -> Dict[str, Any]:
        """Create final content data structure"""

        # Step 2: Create detailed content structure
        total_milestones = len(enhanced_milestones)
        total_tasks = sum(len(m["tasks"]) for m in enhanced_milestones)
        total_subtasks = sum(
            len(t["subtasks"])
            for m in enhanced_milestones
            for t in m["tasks"]
        )

        detailed_content = {
            "milestones": enhanced_milestones,
            "total_milestones": total_milestones,
            "total_tasks": total_tasks,
            "total_subtasks": total_subtasks,
            "content_generation_timestamp": datetime.now().isoformat(),
            "domain_context": enhanced_milestones[0].get("domain_context", "unknown") if enhanced_milestones else "unknown",
            "complexity_level": "intermediate",
            "generation_method": generation_method
        }

        self.logger.info(f"📊 CONTENT STRUCTURE: {total_milestones} milestones, {total_tasks} tasks, {total_subtasks} subtasks")

        # Step 3: Assess content quality using AI-enhanced metrics
        self.logger.info("📈 QUALITY ASSESSMENT: Analyzing Gemini-generated content...")
        content_metrics = self._assess_content_quality(detailed_content)

        self.logger.info(f"✅ QUALITY SCORES: Overall={content_metrics.get('overall_score', 0):.2f}")
        self.logger.info(f"   Clarity: {content_metrics.get('clarity_score', 0):.2f}")
        self.logger.info(f"   Actionability: {content_metrics.get('actionability_score', 0):.2f}")
        self.logger.info(f"   Engagement: {content_metrics.get('engagement_score', 0):.2f}")
        self.logger.info(f"   Consistency: {content_metrics.get('consistency_score', 0):.2f}")

        # Step 4: Create final content data
        content_data = {
            "detailed_content": detailed_content,
            "content_metrics": content_metrics,
            "gemini_ai_generated": True,
            "style_guidelines_applied": True,
            "engagement_elements_included": True,
            "actionability_verified": True
        }

        self.logger.info("🎉 AGENT 3 SUCCESS: Gemini AI content generation completed!")
        self.logger.info(f"📊 FINAL OUTPUT: {total_subtasks} detailed subtasks across {total_tasks} tasks")
        self.logger.info(f"🤖 GENERATION METHOD: {generation_method}")

        return {
            "content_data": content_data,
            "progress": 50.0  # 3/6 agents completed
        }

    def _create_fallback_content_data(self, structure_design: Dict[str, Any], domain_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create fallback content data when all generation methods fail"""

        milestones = structure_design["milestone_structure"]
        enhanced_milestones = []

        for milestone in milestones:
            enhanced_milestone = milestone.copy()
            enhanced_milestone.update({
                "name": f"🎯 {milestone['name']}",
                "description": "Fallback milestone description",
                "motivation_message": "🚀 Keep going!",
                "success_message": "🎉 Milestone completed!",
                "tasks": []
            })

            for task in milestone.get("tasks", []):
                enhanced_task = task.copy()
                enhanced_task.update({
                    "name": f"🔧 {task['name']}",
                    "description": "Fallback task description",
                    "subtasks": self._create_fallback_subtasks(task, domain_analysis)
                })
                enhanced_milestone["tasks"].append(enhanced_task)

            enhanced_milestones.append(enhanced_milestone)

        return self._create_content_data_structure(enhanced_milestones, "fallback_generation")
