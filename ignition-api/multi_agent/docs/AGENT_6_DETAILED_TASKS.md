# 🌟 **AGENT 6: QUALITY ENHANCEMENT AGENT - CHI TIẾT TASKS**

## 🎯 **TỔNG QUAN AGENT 6**

**<PERSON>ai trò**: Master <PERSON><PERSON> ch<PERSON><PERSON>n enhance và finalize plan quality
**Vị trí trong pipeline**: Agent <PERSON><PERSON><PERSON>, t<PERSON><PERSON> ra final enhanced plan
**<PERSON><PERSON><PERSON> tiêu chính**: Transform validated plan thành world-class, engaging, actionable final product

---

## 📋 **BREAKDOWN CHI TIẾT TASKS**

### **Task 4.2: Implement Quality Enhancement Agent**
**<PERSON><PERSON><PERSON> tiêu**: Polish và enhance final plan với premium quality elements

#### **Subtask 4.2.1: Create Agent Class Structure**
**📥 INPUT:** BaseIgnitionAgent, enhancement requirements, personalization features

**🔧 THỰC HIỆN:**
```python
# agents/quality_enhancer.py
from multi_agent.base_agent import BaseIgnitionAgent
from multi_agent.utils.state_management import PlanGenerationState
from assistant.ai_providers import create_chat_completion
from datetime import datetime
import json
import re
from typing import Dict, List, Any

class QualityEnhancementAgent(BaseIgnitionAgent):
    def __init__(self):
        super().__init__(
            agent_name="quality_enhancer",
            config={
                "primary_model": "gemini-2.0-flash-exp",
                "fallback_model": "gemini-1.5-pro",
                "temperature": 0.4,  # Higher creativity for enhancement
                "max_tokens": 3500,
                "timeout": 60
            }
        )
        self.enhancement_templates = self._load_enhancement_templates()
        self.personalization_engine = self._load_personalization_engine()
        self.quality_targets = {
            "readability_score": 0.95,
            "engagement_score": 0.90,
            "actionability_score": 0.92,
            "motivation_score": 0.88,
            "completeness_score": 0.96,
            "overall_quality": 0.94
        }
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Main quality enhancement logic"""
        # Implementation sẽ được detail trong subtasks khác
        pass
```

**📤 OUTPUT:** QualityEnhancementAgent class với enhancement templates
**⏱️ Duration:** 4 giờ

---

#### **Subtask 4.2.2: Load Enhancement Templates và Personalization Engine**
**📥 INPUT:** Enhancement patterns, personalization rules, engagement strategies

**🔧 THỰC HIỆN:**
```python
def _load_enhancement_templates(self) -> dict:
    """Load enhancement templates for different content types"""
    return {
        "executive_summary_templates": {
            "mobile_app": "This comprehensive plan outlines the development of a {domain_specific} mobile application targeting {target_audience}. The project follows a structured {duration} approach with {methodology} methodology, ensuring {key_benefits} while maintaining focus on {success_factors}.",
            "web_platform": "This strategic plan details the creation of a {domain_specific} web platform designed for {target_audience}. Through a {duration} development cycle using {methodology} practices, the project will deliver {key_benefits} with emphasis on {success_factors}.",
            "e_commerce": "This detailed roadmap guides the development of a {domain_specific} e-commerce solution tailored for {target_audience}. The {duration} implementation plan leverages {methodology} principles to achieve {key_benefits} while prioritizing {success_factors}."
        },
        "motivation_templates": {
            "project_start": [
                "🚀 Every groundbreaking project starts with a single step. You're about to embark on an exciting journey that will transform your vision into reality!",
                "💡 Innovation begins with courage to start. Your project has the potential to make a real impact - let's make it happen!",
                "🎯 Success is built on solid foundations. This plan will guide you through every step toward achieving your goals!"
            ],
            "milestone_completion": [
                "🎉 Outstanding progress! You've successfully completed another crucial milestone. Your dedication is paying off!",
                "⭐ Excellent work! Each completed milestone brings you closer to your ultimate vision. Keep up the momentum!",
                "🏆 Milestone achieved! Your consistent effort and focus are the keys to this success. Onward to the next challenge!"
            ],
            "project_completion": [
                "🌟 Congratulations! You've successfully brought your vision to life. This achievement represents dedication, skill, and perseverance!",
                "🎊 Project completed! You've transformed an idea into reality through careful planning and execution. Well done!",
                "🚀 Launch successful! Your hard work has culminated in something truly valuable. Time to celebrate and plan the next adventure!"
            ]
        },
        "enhancement_patterns": {
            "clarity_boosters": [
                "specific_examples", "step_by_step_breakdown", "visual_indicators",
                "clear_outcomes", "measurable_criteria", "context_explanation"
            ],
            "engagement_boosters": [
                "personal_relevance", "success_stories", "progress_visualization",
                "achievement_rewards", "community_elements", "gamification"
            ],
            "actionability_boosters": [
                "concrete_steps", "tool_specifications", "time_estimates",
                "resource_lists", "checkpoint_validation", "troubleshooting_guides"
            ]
        }
    }

def _load_personalization_engine(self) -> dict:
    """Load personalization engine for tailored content"""
    return {
        "user_profiles": {
            "entrepreneur_developer": {
                "motivation_style": "achievement_oriented",
                "communication_preference": "direct_actionable",
                "detail_level": "comprehensive",
                "success_metrics": ["business_impact", "technical_excellence", "user_satisfaction"]
            },
            "corporate_team_lead": {
                "motivation_style": "team_focused",
                "communication_preference": "structured_professional",
                "detail_level": "executive_summary_plus_details",
                "success_metrics": ["team_efficiency", "deadline_adherence", "stakeholder_satisfaction"]
            },
            "startup_founder": {
                "motivation_style": "vision_driven",
                "communication_preference": "inspirational_practical",
                "detail_level": "strategic_with_tactical",
                "success_metrics": ["market_impact", "growth_potential", "investor_appeal"]
            }
        },
        "domain_personalizations": {
            "mobile_app_development": {
                "key_concerns": ["user_experience", "app_store_approval", "device_compatibility"],
                "success_indicators": ["download_metrics", "user_ratings", "retention_rates"],
                "common_challenges": ["platform_differences", "performance_optimization", "user_acquisition"]
            },
            "e_commerce": {
                "key_concerns": ["conversion_optimization", "payment_security", "inventory_management"],
                "success_indicators": ["sales_metrics", "customer_satisfaction", "operational_efficiency"],
                "common_challenges": ["competition", "customer_trust", "scalability"]
            }
        },
        "enhancement_rules": {
            "readability": {
                "sentence_length": {"max": 25, "optimal": 15},
                "paragraph_length": {"max": 5, "optimal": 3},
                "technical_jargon": "explain_on_first_use",
                "active_voice_preference": 0.8
            },
            "engagement": {
                "personal_pronouns": "use_you_your",
                "emotional_language": "moderate_positive",
                "storytelling_elements": "include_where_appropriate",
                "visual_elements": "emojis_and_icons"
            }
        }
    }
```

**📤 OUTPUT:** Comprehensive enhancement templates và personalization engine
**⏱️ Duration:** 6 giờ

---

#### **Subtask 4.2.3: Implement Content Polishing Engine**
**📥 INPUT:** Validated plan, enhancement templates, quality targets

**🔧 THỰC HIỆN:**
```python
def _polish_content(self, plan_data: dict, validation_results: dict) -> dict:
    """Polish all content elements for maximum quality"""
    
    polished_plan = plan_data.copy()
    enhancements_applied = []
    
    # 1. Polish executive summary
    executive_summary = self._create_executive_summary(plan_data)
    polished_plan["executive_summary"] = executive_summary
    enhancements_applied.append({
        "type": "executive_summary_creation",
        "description": "Created comprehensive executive summary with key insights"
    })
    
    # 2. Polish milestone content
    milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    polished_milestones = []
    
    for i, milestone in enumerate(milestones):
        polished_milestone = self._polish_milestone_content(milestone, plan_data, i)
        polished_milestones.append(polished_milestone)
        
        enhancements_applied.append({
            "type": "milestone_enhancement",
            "location": f"milestone_{milestone.get('milestone_id', i+1)}",
            "description": "Enhanced clarity, engagement, and actionability"
        })
    
    polished_plan["content_data"]["detailed_content"]["milestones"] = polished_milestones
    
    # 3. Add motivational elements
    motivational_elements = self._add_motivational_elements(polished_plan)
    polished_plan.update(motivational_elements)
    enhancements_applied.append({
        "type": "motivational_enhancement",
        "description": "Added celebration milestones and progress motivation"
    })
    
    # 4. Enhance actionability
    actionability_enhancements = self._enhance_actionability(polished_plan)
    polished_plan.update(actionability_enhancements)
    enhancements_applied.append({
        "type": "actionability_enhancement",
        "description": "Added specific tools, steps, and validation checkpoints"
    })
    
    return {
        "enhanced_plan": polished_plan,
        "enhancements_applied": enhancements_applied
    }

def _create_executive_summary(self, plan_data: dict) -> dict:
    """Create comprehensive executive summary"""
    
    domain_analysis = plan_data.get("domain_analysis", {})
    timeline_data = plan_data.get("timeline_data", {})
    
    domain = domain_analysis.get("primary_domain", "software_development")
    complexity = domain_analysis.get("complexity_level", "intermediate")
    duration = timeline_data.get("total_duration", "12_weeks")
    
    # Select appropriate template
    template_key = "mobile_app" if "mobile" in domain else "web_platform" if "web" in domain else "e_commerce" if "commerce" in domain else "mobile_app"
    template = self.enhancement_templates["executive_summary_templates"][template_key]
    
    # Fill template with project-specific data
    summary_text = template.format(
        domain_specific=self._get_domain_description(domain),
        target_audience=self._extract_target_audience(domain_analysis),
        duration=duration.replace("_", " "),
        methodology="Agile development",
        key_benefits=self._extract_key_benefits(domain_analysis),
        success_factors=self._extract_success_factors(domain_analysis)
    )
    
    # Extract key milestones
    milestones = plan_data.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    key_milestones = [
        f"{m.get('name', '').replace('🔍', '').replace('⚡', '').replace('🧪', '').replace('🚀', '').strip()}"
        for m in milestones[:3]  # First 3 milestones
    ]
    
    # Expected outcomes
    requirements = domain_analysis.get("extracted_requirements", {})
    functional_reqs = requirements.get("functional", [])
    expected_outcomes = [
        f"Fully functional {domain.replace('_', ' ')} solution",
        f"Implementation of {len(functional_reqs)} core features",
        "Comprehensive testing and quality assurance",
        "Production-ready deployment with monitoring"
    ]
    
    return {
        "project_overview": summary_text,
        "key_milestones": key_milestones,
        "success_factors": [
            "User-centric design approach",
            "Agile development methodology with regular feedback",
            "Continuous testing and quality assurance",
            "Stakeholder engagement throughout development"
        ],
        "expected_outcomes": expected_outcomes,
        "project_duration": duration.replace("_", " "),
        "complexity_assessment": f"{complexity.title()} complexity level",
        "team_requirements": self._get_team_requirements(domain_analysis)
    }

def _polish_milestone_content(self, milestone: dict, plan_data: dict, milestone_index: int) -> dict:
    """Polish individual milestone content"""
    
    polished_milestone = milestone.copy()
    
    # Enhance milestone name with better formatting
    original_name = milestone.get("name", "")
    polished_name = self._enhance_milestone_name(original_name, milestone_index)
    polished_milestone["name"] = polished_name
    
    # Enhance description with more context
    original_description = milestone.get("description", "")
    enhanced_description = self._enhance_description(original_description, milestone, plan_data)
    polished_milestone["description"] = enhanced_description
    
    # Add enhanced motivational message
    enhanced_motivation = self._create_enhanced_motivation_message(milestone, milestone_index)
    polished_milestone["motivation_message"] = enhanced_motivation
    
    # Add celebration milestone
    celebration_message = self._create_celebration_message(milestone, milestone_index)
    polished_milestone["celebration_milestone"] = celebration_message
    
    # Add enhanced features
    enhanced_features = {
        "progress_visualization": self._get_progress_icon(milestone_index),
        "completion_reward": self._get_completion_reward(milestone_index),
        "team_motivation": self._get_team_motivation_message(milestone),
        "success_tips": self._generate_milestone_success_tips(milestone),
        "common_pitfalls": self._identify_common_pitfalls(milestone),
        "quality_checkpoints": self._define_quality_checkpoints(milestone)
    }
    polished_milestone["enhanced_features"] = enhanced_features
    
    # Polish tasks within milestone
    polished_tasks = []
    for task in milestone.get("tasks", []):
        polished_task = self._polish_task_content(task, milestone, plan_data)
        polished_tasks.append(polished_task)
    
    polished_milestone["tasks"] = polished_tasks
    
    return polished_milestone

def _enhance_milestone_name(self, original_name: str, milestone_index: int) -> str:
    """Enhance milestone name with better formatting and clarity"""
    
    # Remove existing emojis to avoid duplication
    clean_name = re.sub(r'[🎯🚀📊🔍⚡🧪🎨🔗📋]', '', original_name).strip()
    
    # Add appropriate emoji based on milestone position
    emoji_sequence = ["🔍", "⚡", "🔧", "🧪", "🚀"]
    emoji = emoji_sequence[milestone_index] if milestone_index < len(emoji_sequence) else "📌"
    
    # Enhance with phase indicator
    phase_indicators = [
        "Foundation Phase:",
        "Development Phase:",
        "Implementation Phase:",
        "Quality Assurance Phase:",
        "Launch Phase:"
    ]
    
    phase = phase_indicators[milestone_index] if milestone_index < len(phase_indicators) else "Execution Phase:"
    
    return f"{emoji} {phase} {clean_name}"

def _enhance_description(self, original_description: str, milestone: dict, plan_data: dict) -> str:
    """Enhance milestone description with more context and value proposition"""
    
    domain_analysis = plan_data.get("domain_analysis", {})
    domain = domain_analysis.get("primary_domain", "")
    
    # Add context about why this milestone is important
    importance_context = {
        0: "This foundational phase is crucial for project success, establishing clear direction and preventing costly changes later.",
        1: "This development phase transforms your vision into tangible functionality, creating the core value of your solution.",
        2: "This implementation phase brings together all components into a cohesive, working system that users can interact with.",
        3: "This quality assurance phase ensures your solution meets professional standards and provides excellent user experience.",
        4: "This launch phase makes your solution available to users and establishes the foundation for ongoing success."
    }
    
    milestone_position = milestone.get("position", 1) - 1
    context = importance_context.get(milestone_position, "This phase is essential for achieving your project objectives.")
    
    # Enhance with domain-specific insights
    domain_insights = {
        "mobile_app_development": "Mobile users expect intuitive, fast, and reliable experiences.",
        "web_development": "Web users value accessibility, performance, and cross-browser compatibility.",
        "e_commerce": "E-commerce success depends on trust, security, and seamless user journeys."
    }
    
    insight = domain_insights.get(domain, "Users expect high-quality, reliable solutions.")
    
    # Combine original description with enhancements
    enhanced = f"{original_description} {context} {insight} This milestone includes built-in quality checkpoints and success validation to ensure optimal outcomes."
    
    return enhanced

def _polish_task_content(self, task: dict, milestone: dict, plan_data: dict) -> dict:
    """Polish individual task content"""
    
    polished_task = task.copy()
    
    # Enhance task name
    original_name = task.get("name", "")
    enhanced_name = self._enhance_task_name(original_name)
    polished_task["name"] = enhanced_name
    
    # Enhance description with more actionable language
    original_description = task.get("description", "")
    enhanced_description = self._enhance_task_description(original_description, task)
    polished_task["description"] = enhanced_description
    
    # Add enhanced elements
    polished_task["why_important"] = self._explain_task_importance_enhanced(task, milestone)
    polished_task["success_tips"] = self._generate_enhanced_success_tips(task)
    polished_task["quality_criteria"] = self._define_task_quality_criteria(task)
    polished_task["estimated_effort"] = self._enhance_effort_description(task)
    polished_task["difficulty_level"] = self._enhance_difficulty_indicator(task)
    polished_task["skill_development"] = self._identify_skill_development_opportunities(task)
    
    # Polish subtasks
    polished_subtasks = []
    for subtask in task.get("subtasks", []):
        polished_subtask = self._polish_subtask_content(subtask, task)
        polished_subtasks.append(polished_subtask)
    
    polished_task["subtasks"] = polished_subtasks
    
    return polished_task
```

**📤 OUTPUT:** Content polishing engine với comprehensive enhancement capabilities
**⏱️ Duration:** 12 giờ

---

#### **Subtask 4.2.4: Implement Personalization Features**
**📥 INPUT:** User profile, domain context, personalization rules

**🔧 THỰC HIỆN:**
```python
def _apply_personalization(self, enhanced_plan: dict, plan_data: dict) -> dict:
    """Apply personalization based on user profile and domain"""
    
    # Determine user profile
    user_profile = self._determine_user_profile(plan_data)
    domain = plan_data.get("domain_analysis", {}).get("primary_domain", "")
    
    personalized_plan = enhanced_plan.copy()
    personalization_elements = {}
    
    # 1. Personalize communication style
    communication_style = self._personalize_communication_style(personalized_plan, user_profile)
    personalization_elements["communication_style"] = communication_style
    
    # 2. Personalize motivation approach
    motivation_approach = self._personalize_motivation_approach(personalized_plan, user_profile)
    personalization_elements["motivation_approach"] = motivation_approach
    
    # 3. Personalize detail level
    detail_level = self._adjust_detail_level(personalized_plan, user_profile)
    personalization_elements["detail_level"] = detail_level
    
    # 4. Add domain-specific personalizations
    domain_personalizations = self._apply_domain_personalizations(personalized_plan, domain)
    personalization_elements["domain_specific"] = domain_personalizations
    
    # 5. Personalize success metrics
    success_metrics = self._personalize_success_metrics(personalized_plan, user_profile, domain)
    personalization_elements["success_metrics"] = success_metrics
    
    personalized_plan["personalization_elements"] = personalization_elements
    
    return personalized_plan

def _determine_user_profile(self, plan_data: dict) -> str:
    """Determine user profile based on plan characteristics"""
    
    domain_analysis = plan_data.get("domain_analysis", {})
    constraints = domain_analysis.get("constraints", {})
    
    team_size = constraints.get("team_size", "small_team_3_5_people")
    complexity = domain_analysis.get("complexity_level", "intermediate")
    
    # Simple heuristics for profile determination
    if team_size == "solo" and complexity in ["beginner", "intermediate"]:
        return "entrepreneur_developer"
    elif team_size in ["medium_team_6_10_people", "large_team"] and complexity in ["advanced", "expert"]:
        return "corporate_team_lead"
    elif "startup" in str(plan_data).lower() or complexity == "advanced":
        return "startup_founder"
    else:
        return "entrepreneur_developer"  # Default

def _personalize_communication_style(self, plan: dict, user_profile: str) -> dict:
    """Personalize communication style based on user profile"""
    
    profile_config = self.personalization_engine["user_profiles"].get(user_profile, {})
    communication_pref = profile_config.get("communication_preference", "direct_actionable")
    
    style_adjustments = {}
    
    if communication_pref == "direct_actionable":
        style_adjustments = {
            "tone": "direct_professional",
            "language_complexity": "moderate",
            "action_orientation": "high",
            "example_usage": "frequent"
        }
    elif communication_pref == "structured_professional":
        style_adjustments = {
            "tone": "formal_professional",
            "language_complexity": "business_level",
            "action_orientation": "moderate",
            "example_usage": "selective"
        }
    elif communication_pref == "inspirational_practical":
        style_adjustments = {
            "tone": "inspirational_motivating",
            "language_complexity": "accessible",
            "action_orientation": "high",
            "example_usage": "story_driven"
        }
    
    # Apply style adjustments to plan content
    self._apply_style_adjustments(plan, style_adjustments)
    
    return style_adjustments

def _personalize_motivation_approach(self, plan: dict, user_profile: str) -> dict:
    """Personalize motivation approach"""
    
    profile_config = self.personalization_engine["user_profiles"].get(user_profile, {})
    motivation_style = profile_config.get("motivation_style", "achievement_oriented")
    
    motivation_config = {}
    
    if motivation_style == "achievement_oriented":
        motivation_config = {
            "focus": "personal_accomplishment",
            "celebration_style": "milestone_achievements",
            "progress_tracking": "detailed_metrics",
            "reward_system": "skill_development_badges"
        }
    elif motivation_style == "team_focused":
        motivation_config = {
            "focus": "team_success",
            "celebration_style": "team_achievements",
            "progress_tracking": "team_metrics",
            "reward_system": "team_recognition"
        }
    elif motivation_style == "vision_driven":
        motivation_config = {
            "focus": "impact_and_vision",
            "celebration_style": "vision_milestones",
            "progress_tracking": "impact_metrics",
            "reward_system": "vision_realization"
        }
    
    # Apply motivation approach to plan
    self._apply_motivation_approach(plan, motivation_config)
    
    return motivation_config

def _apply_domain_personalizations(self, plan: dict, domain: str) -> dict:
    """Apply domain-specific personalizations"""
    
    domain_config = self.personalization_engine["domain_personalizations"].get(domain, {})
    
    personalizations = {
        "key_concerns_addressed": domain_config.get("key_concerns", []),
        "success_indicators_highlighted": domain_config.get("success_indicators", []),
        "common_challenges_prepared": domain_config.get("common_challenges", [])
    }
    
    # Add domain-specific tips and warnings
    if domain == "mobile_app_development":
        self._add_mobile_specific_enhancements(plan)
    elif domain == "e_commerce":
        self._add_ecommerce_specific_enhancements(plan)
    elif domain == "web_development":
        self._add_web_specific_enhancements(plan)
    
    return personalizations

def _add_mobile_specific_enhancements(self, plan: dict) -> None:
    """Add mobile app development specific enhancements"""
    
    # Add mobile-specific success tips
    mobile_tips = [
        "🔋 Optimize for battery life and performance from day one",
        "📱 Test on real devices, not just simulators",
        "🏪 Prepare for app store guidelines early in development",
        "🔄 Plan for offline functionality where appropriate",
        "👆 Design for touch interactions and various screen sizes"
    ]
    
    # Add to relevant milestones
    milestones = plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    for milestone in milestones:
        if "development" in milestone.get("name", "").lower():
            enhanced_features = milestone.get("enhanced_features", {})
            enhanced_features["mobile_specific_tips"] = mobile_tips
            milestone["enhanced_features"] = enhanced_features

def _add_ecommerce_specific_enhancements(self, plan: dict) -> None:
    """Add e-commerce specific enhancements"""
    
    ecommerce_tips = [
        "🛡️ Prioritize security and PCI compliance from the start",
        "💳 Test payment flows extensively with real transactions",
        "📊 Implement analytics to track conversion funnels",
        "🚚 Plan for inventory management and shipping integration",
        "⭐ Build trust through reviews, testimonials, and guarantees"
    ]
    
    milestones = plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    for milestone in milestones:
        if any(keyword in milestone.get("name", "").lower() for keyword in ["development", "implementation"]):
            enhanced_features = milestone.get("enhanced_features", {})
            enhanced_features["ecommerce_specific_tips"] = ecommerce_tips
            milestone["enhanced_features"] = enhanced_features
```

**📤 OUTPUT:** Personalization system với user profile-based customization
**⏱️ Duration:** 10 giờ

---

#### **Subtask 4.2.5: Implement Final Quality Scoring**
**📥 INPUT:** Enhanced plan, quality targets, assessment criteria

**🔧 THỰC HIỆN:**
```python
def _calculate_final_quality_metrics(self, enhanced_plan: dict, original_plan_data: dict) -> dict:
    """Calculate comprehensive final quality metrics"""
    
    quality_metrics = {}
    
    # 1. Readability score
    readability_score = self._assess_readability(enhanced_plan)
    quality_metrics["readability_score"] = readability_score
    
    # 2. Engagement score
    engagement_score = self._assess_engagement(enhanced_plan)
    quality_metrics["engagement_score"] = engagement_score
    
    # 3. Actionability score
    actionability_score = self._assess_actionability(enhanced_plan)
    quality_metrics["actionability_score"] = actionability_score
    
    # 4. Motivation score
    motivation_score = self._assess_motivation_elements(enhanced_plan)
    quality_metrics["motivation_score"] = motivation_score
    
    # 5. Completeness score
    completeness_score = self._assess_completeness(enhanced_plan)
    quality_metrics["completeness_score"] = completeness_score
    
    # 6. Personalization score
    personalization_score = self._assess_personalization(enhanced_plan)
    quality_metrics["personalization_score"] = personalization_score
    
    # 7. Overall quality score
    weights = {
        "readability_score": 0.20,
        "engagement_score": 0.18,
        "actionability_score": 0.20,
        "motivation_score": 0.15,
        "completeness_score": 0.17,
        "personalization_score": 0.10
    }
    
    overall_score = sum(score * weights[metric] for metric, score in quality_metrics.items())
    quality_metrics["overall_quality"] = overall_score
    
    # 8. Quality improvement metrics
    improvement_metrics = self._calculate_improvement_metrics(enhanced_plan, original_plan_data)
    quality_metrics["improvement_metrics"] = improvement_metrics
    
    return quality_metrics

def _assess_readability(self, plan: dict) -> float:
    """Assess readability of plan content"""
    
    readability_score = 0.0
    total_assessments = 0
    
    milestones = plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    for milestone in milestones:
        # Assess milestone description readability
        description = milestone.get("description", "")
        milestone_readability = self._calculate_text_readability(description)
        readability_score += milestone_readability
        total_assessments += 1
        
        # Assess task descriptions
        for task in milestone.get("tasks", []):
            task_description = task.get("description", "")
            task_readability = self._calculate_text_readability(task_description)
            readability_score += task_readability
            total_assessments += 1
            
            # Assess subtask descriptions
            for subtask in task.get("subtasks", []):
                subtask_description = subtask.get("description", "")
                subtask_readability = self._calculate_text_readability(subtask_description)
                readability_score += subtask_readability
                total_assessments += 1
    
    return readability_score / total_assessments if total_assessments > 0 else 0.0

def _calculate_text_readability(self, text: str) -> float:
    """Calculate readability score for text"""
    
    if not text:
        return 0.0
    
    sentences = text.split('.')
    words = text.split()
    
    if len(sentences) == 0 or len(words) == 0:
        return 0.0
    
    # Simple readability metrics
    avg_sentence_length = len(words) / len(sentences)
    avg_word_length = sum(len(word) for word in words) / len(words)
    
    # Readability score (simplified Flesch formula approach)
    # Lower sentence length and word length = higher readability
    sentence_score = max(0, 1 - (avg_sentence_length - 15) / 20)  # Optimal ~15 words
    word_score = max(0, 1 - (avg_word_length - 5) / 5)  # Optimal ~5 characters
    
    # Check for active voice indicators
    active_voice_indicators = ["create", "develop", "implement", "build", "design", "analyze"]
    active_voice_count = sum(1 for word in words if word.lower() in active_voice_indicators)
    active_voice_score = min(active_voice_count / len(sentences), 1.0)
    
    # Combined readability score
    readability = (sentence_score * 0.4 + word_score * 0.3 + active_voice_score * 0.3)
    
    return min(readability, 1.0)

def _assess_engagement(self, plan: dict) -> float:
    """Assess engagement level of plan content"""
    
    engagement_score = 0.0
    engagement_factors = 0
    
    milestones = plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    # Check for engagement elements
    for milestone in milestones:
        # Emoji usage
        if any(emoji in milestone.get("name", "") for emoji in "🎯🚀📊🔍⚡🧪🎨"):
            engagement_score += 0.1
        engagement_factors += 1
        
        # Motivational messages
        if milestone.get("motivation_message"):
            engagement_score += 0.15
        engagement_factors += 1
        
        # Success celebrations
        if milestone.get("celebration_milestone"):
            engagement_score += 0.1
        engagement_factors += 1
        
        # Enhanced features
        enhanced_features = milestone.get("enhanced_features", {})
        if enhanced_features:
            engagement_score += 0.1
        engagement_factors += 1
        
        # Task-level engagement
        for task in milestone.get("tasks", []):
            # Why important explanations
            if task.get("why_important"):
                engagement_score += 0.05
            engagement_factors += 1
            
            # Success tips
            if task.get("success_tips"):
                engagement_score += 0.05
            engagement_factors += 1
    
    # Personalization elements
    if plan.get("personalization_elements"):
        engagement_score += 0.2
        engagement_factors += 1
    
    # Executive summary engagement
    executive_summary = plan.get("executive_summary", {})
    if executive_summary.get("success_factors"):
        engagement_score += 0.1
        engagement_factors += 1
    
    return min(engagement_score / engagement_factors if engagement_factors > 0 else 0, 1.0)

def _assess_actionability(self, plan: dict) -> float:
    """Assess actionability of plan content"""
    
    actionability_score = 0.0
    total_items = 0
    
    milestones = plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    for milestone in milestones:
        for task in milestone.get("tasks", []):
            for subtask in task.get("subtasks", []):
                total_items += 1
                
                # Actionable steps
                actionable_steps = subtask.get("actionable_steps", [])
                if len(actionable_steps) >= 3:
                    actionability_score += 0.3
                
                # Tools specification
                tools_needed = subtask.get("tools_needed", [])
                if len(tools_needed) >= 2:
                    actionability_score += 0.2
                
                # Time estimates
                if subtask.get("time_estimate"):
                    actionability_score += 0.2
                
                # Expected outcomes
                if subtask.get("expected_outcome"):
                    actionability_score += 0.3
    
    return actionability_score / total_items if total_items > 0 else 0.0

def _calculate_improvement_metrics(self, enhanced_plan: dict, original_plan: dict) -> dict:
    """Calculate improvement metrics compared to original plan"""
    
    improvements = {
        "content_expansion": 0,
        "engagement_additions": 0,
        "actionability_enhancements": 0,
        "personalization_additions": 0
    }
    
    # Count content expansions
    original_milestones = original_plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    enhanced_milestones = enhanced_plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])
    
    for orig_milestone, enh_milestone in zip(original_milestones, enhanced_milestones):
        # Check for added elements
        if enh_milestone.get("enhanced_features") and not orig_milestone.get("enhanced_features"):
            improvements["engagement_additions"] += 1
        
        if enh_milestone.get("celebration_milestone") and not orig_milestone.get("celebration_milestone"):
            improvements["engagement_additions"] += 1
    
    # Check for executive summary addition
    if enhanced_plan.get("executive_summary") and not original_plan.get("executive_summary"):
        improvements["content_expansion"] += 1
    
    # Check for personalization additions
    if enhanced_plan.get("personalization_elements"):
        improvements["personalization_additions"] += 1
    
    return improvements
```

**📤 OUTPUT:** Final quality scoring system với comprehensive metrics
**⏱️ Duration:** 8 giờ

---

#### **Subtask 4.2.6: Implement Main Process Method**
**📥 INPUT:** Validated plan từ Agent 5, all enhancement logic

**🔧 THỰC HIỆN:**
```python
async def process(self, state: PlanGenerationState) -> dict:
    """Main quality enhancement process"""
    
    # Collect all plan data
    plan_data = {
        "domain_analysis": state.get("domain_analysis", {}),
        "structure_design": state.get("structure_design", {}),
        "content_data": state.get("content_data", {}),
        "timeline_data": state.get("timeline_data", {})
    }
    
    validation_results = state.get("validation_results", {})
    
    try:
        # Step 1: Apply validation improvements
        improved_plan = self._apply_validation_improvements(plan_data, validation_results)
        
        # Step 2: Polish content for maximum quality
        polishing_results = self._polish_content(improved_plan, validation_results)
        enhanced_plan = polishing_results["enhanced_plan"]
        
        # Step 3: Apply personalization
        personalized_plan = self._apply_personalization(enhanced_plan, plan_data)
        
        # Step 4: Add final enhancements
        final_enhancements = self._add_final_enhancements(personalized_plan)
        final_plan = final_enhancements["enhanced_plan"]
        
        # Step 5: Calculate final quality metrics
        final_metrics = self._calculate_final_quality_metrics(final_plan, plan_data)
        
        # Step 6: Create plan metadata
        plan_metadata = self._create_plan_metadata(final_plan, plan_data, final_metrics)
        
        # Step 7: Compile all enhancements applied
        all_enhancements = []
        all_enhancements.extend(polishing_results["enhancements_applied"])
        all_enhancements.extend(final_enhancements["enhancements_applied"])
        
        # Step 8: Create final enhanced plan structure
        complete_enhanced_plan = {
            "plan_metadata": plan_metadata,
            "executive_summary": final_plan.get("executive_summary", {}),
            "milestones": final_plan.get("content_data", {}).get("detailed_content", {}).get("milestones", []),
            "timeline_optimization": plan_data.get("timeline_data", {}),
            "personalization_elements": final_plan.get("personalization_elements", {}),
            "quality_assurance": {
                "validation_results": validation_results,
                "final_quality_metrics": final_metrics,
                "enhancements_applied": all_enhancements
            }
        }
        
        # Step 9: Validate final output
        if not self.validate_output({"final_plan": complete_enhanced_plan}):
            raise ValueError("Final plan validation failed")
        
        return {
            "final_plan": complete_enhanced_plan,
            "enhancements_applied": all_enhancements,
            "final_metrics": final_metrics,
            "progress": 100.0  # All 6 agents completed
        }
        
    except Exception as e:
        self.logger.error(f"Quality enhancement failed: {e}")
        raise e

def _create_plan_metadata(self, final_plan: dict, original_plan_data: dict, 
                         final_metrics: dict) -> dict:
    """Create comprehensive plan metadata"""
    
    domain_analysis = original_plan_data.get("domain_analysis", {})
    timeline_data = original_plan_data.get("timeline_data", {})
    
    return {
        "plan_id": f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "title": self._generate_plan_title(domain_analysis),
        "description": self._generate_plan_description(domain_analysis, timeline_data),
        "created_date": datetime.now().strftime("%Y-%m-%d"),
        "estimated_completion": timeline_data.get("end_date", ""),
        "quality_score": final_metrics.get("overall_quality", 0.0),
        "enhancement_level": "premium",
        "version": "1.0",
        "generated_by": "Ignition Multi-Agent Planning System",
        "total_milestones": len(final_plan.get("content_data", {}).get("detailed_content", {}).get("milestones", [])),
        "total_tasks": self._count_total_tasks(final_plan),
        "total_subtasks": self._count_total_subtasks(final_plan),
        "estimated_effort_hours": self._calculate_total_effort_hours(timeline_data),
        "complexity_level": domain_analysis.get("complexity_level", "intermediate"),
        "target_domain": domain_analysis.get("primary_domain", "software_development")
    }

def _generate_plan_title(self, domain_analysis: dict) -> str:
    """Generate descriptive plan title"""
    
    domain = domain_analysis.get("primary_domain", "software_development")
    complexity = domain_analysis.get("complexity_level", "intermediate")
    
    domain_titles = {
        "mobile_app_development": "Mobile Application Development Plan",
        "web_development": "Web Platform Development Plan",
        "e_commerce": "E-Commerce Solution Development Plan",
        "data_science": "Data Science Project Implementation Plan"
    }
    
    base_title = domain_titles.get(domain, "Software Development Plan")
    
    # Add complexity indicator
    complexity_indicators = {
        "beginner": "Starter",
        "intermediate": "Professional",
        "advanced": "Enterprise",
        "expert": "Expert-Level"
    }
    
    complexity_indicator = complexity_indicators.get(complexity, "Professional")
    
    return f"{complexity_indicator} {base_title}"

def validate_output(self, output: dict) -> bool:
    """Validate final enhanced plan output"""
    final_plan = output.get("final_plan", {})
    
    required_fields = [
        "plan_metadata", "executive_summary", "milestones", 
        "timeline_optimization", "quality_assurance"
    ]
    
    if not all(field in final_plan for field in required_fields):
        return False
    
    # Validate milestone structure
    milestones = final_plan.get("milestones", [])
    if len(milestones) != 5:
        return False
    
    # Validate quality metrics
    quality_metrics = final_plan.get("quality_assurance", {}).get("final_quality_metrics", {})
    overall_quality = quality_metrics.get("overall_quality", 0)
    
    if overall_quality < 0.8:  # Minimum quality threshold
        self.logger.warning(f"Final plan quality below threshold: {overall_quality}")
        # Don't fail validation, but log warning
    
    return True
```

**📤 OUTPUT:** Complete quality enhancement process với final plan generation
**⏱️ Duration:** 6 giờ

---

## 📊 **TỔNG KẾT AGENT 6**

### **Tổng thời gian ước tính:** 46 giờ (6-7 ngày làm việc)

### **Deliverables chính:**
1. **QualityEnhancementAgent class** - Complete implementation
2. **Enhancement templates** - Domain-specific enhancement patterns
3. **Content polishing engine** - Comprehensive content improvement
4. **Personalization features** - User profile-based customization
5. **Motivational elements** - Engagement và celebration features
6. **Final quality scoring** - Multi-dimensional quality assessment
7. **Complete enhanced plan** - World-class final product

### **Input → Output Transformation:**
```
Validated Plan (Agent 5 output)
    ↓
{
    "final_plan": {
        "plan_metadata": {
            "title": "Professional Mobile Application Development Plan",
            "quality_score": 0.96,
            "enhancement_level": "premium",
            "total_subtasks": 125
        },
        "executive_summary": {
            "project_overview": "Comprehensive 12-week development plan for creating a modern fashion e-commerce mobile application...",
            "key_milestones": ["Market Research and Foundation", "Core Development", "Quality Assurance"],
            "success_factors": ["User-centric design", "Agile methodology", "Continuous testing"],
            "expected_outcomes": ["Fully functional mobile app", "App store ready", "User base of 1000+ beta testers"]
        },
        "milestones": [
            {
                "name": "🔍 Foundation Phase: Market Research and Technical Foundation Setup",
                "description": "Comprehensive analysis of fashion e-commerce market... This foundational phase is crucial for project success...",
                "motivation_message": "🚀 Every groundbreaking project starts with a single step. You're about to embark on an exciting journey!",
                "celebration_milestone": "🎉 Foundation Complete! Market insights gathered and technical roadmap established!",
                "enhanced_features": {
                    "progress_visualization": "foundation_building_icon",
                    "completion_reward": "unlock_development_phase",
                    "mobile_specific_tips": ["🔋 Optimize for battery life", "📱 Test on real devices"],
                    "success_tips": ["Focus on user pain points", "Validate with real users"],
                    "quality_checkpoints": ["Requirements review", "Architecture validation"]
                },
                "tasks": [
                    {
                        "name": "🎯 Conduct comprehensive market analysis for fashion e-commerce mobile applications",
                        "description": "Dive deep into the fashion e-commerce landscape! Research your target audience, analyze successful competitors...",
                        "why_important": "Understanding your market is crucial for creating an app that users actually want and need.",
                        "success_tips": ["Focus on user pain points, not just features", "Look for gaps in competitor offerings"],
                        "quality_criteria": ["Actionable insights", "Data-driven conclusions", "Clear recommendations"],
                        "skill_development": ["Market research", "Competitive analysis", "User research"],
                        "subtasks": [
                            {
                                "name": "📱 Survey and analyze top 15 fashion e-commerce apps",
                                "description": "Download, test, and document features, UX patterns, and user flows...",
                                "actionable_steps": [
                                    "Download apps: Zara, H&M, ASOS, Shein, Uniqlo, etc.",
                                    "Test complete user journey from onboarding to purchase",
                                    "Document unique features, UX patterns, and pain points",
                                    "Create detailed comparison matrix with screenshots",
                                    "Identify market gaps and opportunities for differentiation"
                                ],
                                "tools_needed": ["Smartphone", "Spreadsheet software", "Screen recording app", "Note-taking app"],
                                "time_estimate": "16 hours",
                                "expected_outcome": "Comprehensive competitor analysis report with actionable insights"
                            }
                            // ... 4 more enhanced subtasks
                        ]
                    }
                    // ... 4 more enhanced tasks
                ]
            }
            // ... 4 more enhanced milestones
        ],
        "personalization_elements": {
            "user_type": "entrepreneur_developer",
            "communication_style": "direct_actionable",
            "motivation_approach": "achievement_oriented",
            "domain_specific": "mobile_app_development"
        },
        "quality_assurance": {
            "final_quality_metrics": {
                "readability_score": 0.96,
                "engagement_score": 0.94,
                "actionability_score": 0.92,
                "motivation_score": 0.89,
                "completeness_score": 0.98,
                "overall_quality": 0.96
            }
        }
    }
}
```

**Agent 6 transforms validated plan into world-class, personalized, engaging final product với 125 actionable subtasks!** 🌟

---

## 🎯 **TỔNG KẾT TOÀN BỘ 6 AGENTS**

### **📊 Tổng thời gian implementation:**
- **Agent 1**: 42 giờ (Domain Classification)
- **Agent 2**: 45 giờ (Structure Optimization)  
- **Agent 3**: 50 giờ (Content Generation)
- **Agent 4**: 50 giờ (Timeline Optimization)
- **Agent 5**: 50 giờ (Validation)
- **Agent 6**: 46 giờ (Quality Enhancement)

**TỔNG CỘNG: 283 giờ (35-40 ngày làm việc với 1-2 developers)**

### **🚀 Complete Transformation Journey:**
```
"Tôi muốn tạo app e-commerce bán quần áo"
    ↓ Agent 1: Domain Analysis
Structured requirements và domain classification
    ↓ Agent 2: Structure Design  
5 milestones với 25 tasks framework
    ↓ Agent 3 + 4: Content + Timeline (Parallel)
Detailed content với 125 subtasks + Realistic timeline
    ↓ Agent 5: Validation
Quality-checked plan với issue detection
    ↓ Agent 6: Quality Enhancement
World-class, personalized, actionable final plan
```

**Kết quả: Từ 1 câu đơn giản → 125 subtasks chi tiết với world-class quality!** 🎯
