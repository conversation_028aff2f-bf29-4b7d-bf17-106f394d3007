# 📝 **AGENT 3: CONTENT GENERATION AGENT - CHI TIẾT TASKS**

## 🎯 **TỔNG QUAN AGENT 3**

**Vai trò**: Content Creator chuyên tạo detailed, actionable content cho plan elements
**Vị trí trong pipeline**: Agent thứ 3, ch<PERSON>y song song với Agent 4 (Timeline)
**<PERSON><PERSON><PERSON> tiêu chính**: Transform abstract structure thành detailed content với 125 subtasks

---

## 📋 **BREAKDOWN CHI TIẾT TASKS**

### **Task 3.1: Implement Content Generation Agent**
**<PERSON><PERSON><PERSON> tiêu**: Generate detailed content cho tất cả plan elements với high quality

#### **Subtask 3.1.1: Create Agent Class Structure**
**📥 INPUT:** BaseIgnitionAgent, content generation requirements, style guidelines

**🔧 THỰC HIỆN:**
```python
# agents/content_generator.py
from multi_agent.base_agent import BaseIgnitionAgent
from multi_agent.utils.state_management import PlanGenerationState
from assistant.ai_providers import create_chat_completion
import json
import re
from typing import Dict, List, Any

class ContentGenerationAgent(BaseIgnitionAgent):
    def __init__(self):
        super().__init__(
            agent_name="content_generator",
            config={
                "primary_model": "gemini-2.0-flash-exp",
                "fallback_model": "gemini-1.5-pro",
                "temperature": 0.3,
                "max_tokens": 3000,
                "timeout": 60
            }
        )
        self.content_templates = self._load_content_templates()
        self.style_guidelines = self._load_style_guidelines()
        self.quality_thresholds = {
            "clarity_score": 0.85,
            "actionability_score": 0.80,
            "engagement_score": 0.75,
            "consistency_score": 0.90
        }
    
    async def process(self, state: PlanGenerationState) -> dict:
        """Main content generation logic"""
        # Implementation sẽ được detail trong subtasks khác
        pass
```

**📤 OUTPUT:** ContentGenerationAgent class với templates và quality thresholds
**⏱️ Duration:** 4 giờ

---

#### **Subtask 3.1.2: Load Content Templates và Style Guidelines**
**📥 INPUT:** Domain-specific content patterns, writing best practices

**🔧 THỰC HIỆN:**
```python
def _load_content_templates(self) -> dict:
    """Load content generation templates for different domains"""
    return {
        "milestone_name_patterns": {
            "research": [
                "{domain} Market Research and Competitive Analysis",
                "Comprehensive {domain} Requirements Gathering and Planning",
                "Strategic {domain} Foundation and Technical Architecture Setup"
            ],
            "development": [
                "Core {feature} Development and Implementation",
                "{feature} System Architecture and Backend Development",
                "Advanced {feature} Features and Integration Development"
            ],
            "testing": [
                "Comprehensive Quality Assurance and {domain} Testing",
                "Performance Optimization and Security Testing for {domain}",
                "User Acceptance Testing and {domain} Refinement"
            ],
            "deployment": [
                "Production Deployment and {domain} Launch Preparation",
                "{domain} Go-Live Strategy and Post-Launch Monitoring",
                "Final {domain} Deployment and Success Metrics Implementation"
            ]
        },
        "task_name_patterns": {
            "research_tasks": [
                "Conduct comprehensive {domain} market analysis and competitor research",
                "Develop detailed user personas and customer journey mapping for {domain}",
                "Create technical architecture blueprint and technology stack selection",
                "Design user experience wireframes and interface mockups",
                "Establish project requirements documentation and success metrics"
            ],
            "development_tasks": [
                "Implement core {feature} functionality with robust error handling",
                "Develop secure user authentication and authorization system",
                "Create responsive user interface with modern design principles",
                "Build scalable backend API with comprehensive data validation",
                "Integrate third-party services and payment processing systems"
            ],
            "testing_tasks": [
                "Execute comprehensive automated and manual testing suites",
                "Perform security vulnerability assessment and penetration testing",
                "Conduct performance optimization and load testing analysis",
                "Implement user acceptance testing with real user feedback",
                "Complete final quality assurance and bug resolution process"
            ]
        },
        "subtask_patterns": {
            "research_subtasks": [
                "Survey and analyze top {number} {domain} applications in the market",
                "Conduct {number}+ user interviews and create detailed personas",
                "Research and document {domain} industry best practices and standards",
                "Create comprehensive competitive analysis with feature comparison matrix",
                "Develop technical requirements specification with detailed acceptance criteria"
            ],
            "development_subtasks": [
                "Set up development environment and project structure with best practices",
                "Implement {specific_feature} with comprehensive unit testing coverage",
                "Create database schema design with optimization and indexing strategies",
                "Develop API endpoints with proper authentication and rate limiting",
                "Integrate frontend components with backend services and error handling"
            ]
        }
    }

def _load_style_guidelines(self) -> dict:
    """Load content style guidelines"""
    return {
        "tone": "professional_yet_approachable",
        "language_level": "intermediate_technical",
        "format_preferences": {
            "milestone_names": {
                "min_words": 7,
                "max_words": 15,
                "include_emojis": True,
                "action_oriented": True
            },
            "task_names": {
                "min_words": 8,
                "max_words": 20,
                "start_with_action_verb": True,
                "include_specific_outcomes": True
            },
            "descriptions": {
                "min_words": 25,
                "max_words": 60,
                "include_why_important": True,
                "actionable_language": True
            },
            "subtask_names": {
                "min_words": 6,
                "max_words": 15,
                "specific_and_measurable": True,
                "include_deliverables": True
            }
        },
        "engagement_elements": {
            "use_emojis": True,
            "include_motivational_language": True,
            "add_success_celebrations": True,
            "provide_context_and_rationale": True
        }
    }
```

**📤 OUTPUT:** Comprehensive content templates và style guidelines
**⏱️ Duration:** 5 giờ

---

#### **Subtask 3.1.3: Implement Milestone Content Enhancement**
**📥 INPUT:** Basic milestone structure từ Agent 2, domain context

**🔧 THỰC HIỆN:**
```python
def _enhance_milestone_content(self, milestone: dict, domain_analysis: dict) -> dict:
    """Enhance milestone with detailed, engaging content"""
    
    domain = domain_analysis["primary_domain"]
    requirements = domain_analysis["extracted_requirements"]
    
    enhanced_milestone = milestone.copy()
    
    # Enhance milestone name
    enhanced_milestone["name"] = self._generate_enhanced_milestone_name(
        milestone["name"], domain, requirements
    )
    
    # Enhance description
    enhanced_milestone["description"] = self._generate_enhanced_description(
        milestone, domain_analysis
    )
    
    # Add success message
    enhanced_milestone["success_message"] = self._generate_success_message(
        milestone["name"]
    )
    
    # Add motivational elements
    enhanced_milestone["motivation_message"] = self._generate_motivation_message(
        milestone, domain_analysis
    )
    
    return enhanced_milestone

def _generate_enhanced_milestone_name(self, base_name: str, domain: str, 
                                    requirements: dict) -> str:
    """Generate enhanced milestone name với emojis và specificity"""
    
    # Emoji mapping for different milestone types
    emoji_mapping = {
        "research": "🔍",
        "planning": "📋", 
        "development": "⚡",
        "testing": "🧪",
        "deployment": "🚀",
        "design": "🎨",
        "integration": "🔗"
    }
    
    # Determine appropriate emoji
    emoji = "📌"  # Default
    for keyword, emoji_char in emoji_mapping.items():
        if keyword.lower() in base_name.lower():
            emoji = emoji_char
            break
    
    # Add domain-specific context
    domain_context = {
        "mobile_app_development": "Mobile App",
        "web_development": "Web Platform",
        "e_commerce": "E-Commerce",
        "data_science": "Data Analytics"
    }
    
    context = domain_context.get(domain, "Project")
    
    # Generate enhanced name
    if "research" in base_name.lower():
        return f"{emoji} {context} Market Research and Technical Foundation Setup"
    elif "development" in base_name.lower():
        return f"{emoji} Core {context} Development and Feature Implementation"
    elif "testing" in base_name.lower():
        return f"{emoji} Comprehensive Quality Assurance and {context} Testing"
    elif "deployment" in base_name.lower():
        return f"{emoji} Production Deployment and {context} Launch"
    else:
        return f"{emoji} {base_name} and {context} Optimization"

def _generate_enhanced_description(self, milestone: dict, domain_analysis: dict) -> str:
    """Generate detailed, contextual milestone description"""
    
    base_description = milestone.get("description", "")
    domain = domain_analysis["primary_domain"]
    requirements = domain_analysis["extracted_requirements"]
    
    # Add domain-specific context
    domain_context = {
        "mobile_app_development": "mobile application with intuitive user experience",
        "web_development": "web platform with responsive design and optimal performance",
        "e_commerce": "e-commerce solution with secure payment processing and inventory management"
    }
    
    context = domain_context.get(domain, "technical solution")
    
    # Add requirement-specific details
    requirement_details = []
    if "payment_processing" in requirements.get("functional", []):
        requirement_details.append("secure payment gateway integration")
    if "user_management" in requirements.get("functional", []):
        requirement_details.append("comprehensive user authentication system")
    if "security" in requirements.get("non_functional", []):
        requirement_details.append("robust security measures and data protection")
    
    # Combine into enhanced description
    enhanced_desc = f"{base_description} This phase focuses on building a {context}"
    
    if requirement_details:
        enhanced_desc += f" with emphasis on {', '.join(requirement_details[:2])}"
    
    enhanced_desc += ". The milestone ensures solid foundation for subsequent development phases and establishes clear success criteria for project progression."
    
    return enhanced_desc

def _generate_success_message(self, milestone_name: str) -> str:
    """Generate celebratory success message"""
    
    success_messages = {
        "research": "🎉 Foundation Complete! Market insights gathered and technical roadmap established!",
        "development": "🚀 Core Features Live! Your application is taking shape beautifully!",
        "testing": "✅ Quality Assured! Your solution is robust, secure, and ready for users!",
        "deployment": "🌟 Successfully Launched! Your project is live and making an impact!"
    }
    
    for keyword, message in success_messages.items():
        if keyword in milestone_name.lower():
            return message
    
    return "🎯 Milestone Achieved! Excellent progress toward your project goals!"

def _generate_motivation_message(self, milestone: dict, domain_analysis: dict) -> str:
    """Generate motivational message for milestone"""
    
    position = milestone.get("position", 1)
    
    motivation_templates = {
        1: "🚀 Every successful project starts with solid research and planning. You're building the blueprint for success!",
        2: "⚡ Now the real magic begins! This is where your vision starts becoming reality.",
        3: "🔥 You're in the heart of development! This is where your unique value proposition comes to life.",
        4: "🧪 Quality is what separates good projects from great ones. You're ensuring excellence!",
        5: "🌟 The finish line is in sight! You're about to share your creation with the world!"
    }
    
    return motivation_templates.get(position, "💪 Keep pushing forward! You're making excellent progress!")
```

**📤 OUTPUT:** Milestone content enhancement với emojis, context, và motivation
**⏱️ Duration:** 8 giờ

---

#### **Subtask 3.1.4: Implement Task Content Generation**
**📥 INPUT:** Enhanced milestones, task templates, domain requirements

**🔧 THỰC HIỆN:**
```python
def _enhance_task_content(self, task: dict, milestone: dict, domain_analysis: dict) -> dict:
    """Enhance task with detailed, actionable content"""
    
    enhanced_task = task.copy()
    
    # Enhance task name
    enhanced_task["name"] = self._generate_enhanced_task_name(
        task["name"], domain_analysis
    )
    
    # Generate detailed description
    enhanced_task["description"] = self._generate_task_description(
        task, milestone, domain_analysis
    )
    
    # Add acceptance criteria
    enhanced_task["acceptance_criteria"] = self._generate_acceptance_criteria(
        task["name"]
    )
    
    # Add why it's important
    enhanced_task["why_important"] = self._explain_task_importance(
        task["name"], domain_analysis
    )
    
    # Add success tips
    enhanced_task["success_tips"] = self._generate_success_tips(task["name"])
    
    # Add effort estimation
    enhanced_task["estimated_effort"] = self._generate_effort_description(
        task.get("estimated_duration", "1_week")
    )
    
    # Add difficulty indicator
    enhanced_task["difficulty_level"] = self._generate_difficulty_indicator(
        task.get("complexity", "medium")
    )
    
    return enhanced_task

def _generate_enhanced_task_name(self, base_name: str, domain_analysis: dict) -> str:
    """Generate enhanced task name với action verbs và specificity"""
    
    domain = domain_analysis["primary_domain"]
    
    # Add emoji based on task type
    task_emojis = {
        "research": "🎯",
        "analysis": "📊", 
        "design": "🎨",
        "development": "⚡",
        "implementation": "🔧",
        "testing": "🧪",
        "integration": "🔗",
        "deployment": "🚀"
    }
    
    emoji = "📌"  # Default
    for keyword, emoji_char in task_emojis.items():
        if keyword in base_name.lower():
            emoji = emoji_char
            break
    
    # Add domain context if not already present
    domain_terms = {
        "mobile_app_development": "mobile applications",
        "web_development": "web platforms", 
        "e_commerce": "e-commerce systems"
    }
    
    domain_term = domain_terms.get(domain, "applications")
    
    # Enhance with specificity
    if not any(term in base_name.lower() for term in domain_terms.values()):
        enhanced_name = f"{emoji} {base_name} for {domain_term}"
    else:
        enhanced_name = f"{emoji} {base_name}"
    
    return enhanced_name

def _generate_task_description(self, task: dict, milestone: dict, 
                              domain_analysis: dict) -> str:
    """Generate detailed, actionable task description"""
    
    base_name = task["name"]
    domain = domain_analysis["primary_domain"]
    requirements = domain_analysis["extracted_requirements"]
    
    # Base description templates
    description_templates = {
        "research": "Dive deep into {domain} landscape! Research your target audience, analyze successful competitors, and identify unique opportunities that will make your solution stand out in the marketplace.",
        "design": "Create intuitive and visually appealing {domain} interface with smooth navigation, responsive design, and engaging user interactions that delight users.",
        "development": "Build robust and scalable {domain} functionality with clean code architecture, comprehensive error handling, and optimal performance characteristics.",
        "testing": "Ensure your {domain} solution meets the highest quality standards through comprehensive testing, performance optimization, and security validation.",
        "integration": "Seamlessly connect all {domain} components and third-party services to create a cohesive, well-functioning system.",
        "deployment": "Successfully launch your {domain} solution to production with proper monitoring, backup systems, and post-launch support procedures."
    }
    
    # Determine template based on task type
    template = "Build high-quality {domain} functionality with attention to detail and user experience."
    for keyword, desc_template in description_templates.items():
        if keyword in base_name.lower():
            template = desc_template
            break
    
    # Fill in domain context
    domain_context = {
        "mobile_app_development": "mobile app",
        "web_development": "web application",
        "e_commerce": "e-commerce platform"
    }
    
    context = domain_context.get(domain, "application")
    description = template.format(domain=context)
    
    # Add requirement-specific details
    if "payment_processing" in requirements.get("functional", []):
        if "integration" in base_name.lower() or "payment" in base_name.lower():
            description += " Focus on secure payment processing with multiple payment methods and fraud protection."
    
    return description

def _generate_acceptance_criteria(self, task_name: str) -> str:
    """Generate clear acceptance criteria for task"""
    
    criteria_templates = {
        "research": "Comprehensive research report with competitor analysis, market insights, and actionable recommendations",
        "design": "Complete design mockups with user flow diagrams, responsive layouts, and design system documentation",
        "development": "Fully functional feature with unit tests, error handling, and performance optimization",
        "testing": "Test results documentation with bug reports, performance metrics, and quality assurance sign-off",
        "integration": "Successfully integrated system with all components working together and comprehensive testing completed",
        "deployment": "Live system running in production with monitoring active and post-deployment checklist completed"
    }
    
    for keyword, criteria in criteria_templates.items():
        if keyword in task_name.lower():
            return criteria
    
    return "Task completed according to specifications with all deliverables meeting quality standards"
```

**📤 OUTPUT:** Task content enhancement với detailed descriptions và acceptance criteria
**⏱️ Duration:** 10 giờ

---

#### **Subtask 3.1.5: Implement Subtask Generation**
**📥 INPUT:** Enhanced tasks, subtask patterns, actionable step requirements

**🔧 THỰC HIỆN:**
```python
def _generate_subtasks(self, task: dict, domain_analysis: dict) -> list:
    """Generate 5 detailed subtasks for each task"""
    
    task_name = task["name"]
    domain = domain_analysis["primary_domain"]
    
    subtasks = []
    
    # Generate 5 subtasks based on task type
    subtask_generators = {
        "research": self._generate_research_subtasks,
        "design": self._generate_design_subtasks,
        "development": self._generate_development_subtasks,
        "testing": self._generate_testing_subtasks,
        "integration": self._generate_integration_subtasks,
        "deployment": self._generate_deployment_subtasks
    }
    
    # Determine task type
    task_type = "development"  # Default
    for keyword in subtask_generators.keys():
        if keyword in task_name.lower():
            task_type = keyword
            break
    
    # Generate subtasks
    generator = subtask_generators[task_type]
    subtasks = generator(task, domain_analysis)
    
    # Ensure exactly 5 subtasks
    while len(subtasks) < 5:
        subtasks.append(self._generate_generic_subtask(len(subtasks) + 1, task))
    
    return subtasks[:5]

def _generate_research_subtasks(self, task: dict, domain_analysis: dict) -> list:
    """Generate research-specific subtasks"""
    
    domain = domain_analysis["primary_domain"]
    
    return [
        {
            "subtask_id": f"{task['task_id']}_ST1",
            "name": f"Survey and analyze top 15 {domain} applications and platforms",
            "description": "Download, test, and document features, UX patterns, and user flows of leading applications. Create comparison matrix highlighting strengths and weaknesses.",
            "actionable_steps": [
                f"Download and install top 15 {domain} apps/platforms",
                "Test complete user journey from onboarding to core features",
                "Document unique features, UX patterns, and pain points",
                "Create detailed comparison matrix with screenshots",
                "Identify market gaps and opportunities for differentiation"
            ],
            "expected_outcome": "Comprehensive competitor analysis report with actionable insights",
            "time_estimate": "16 hours",
            "tools_needed": ["Smartphone/Computer", "Spreadsheet software", "Screen recording app", "Note-taking app"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST2", 
            "name": "Conduct user interviews and create detailed customer personas",
            "description": "Interview 20+ potential users to understand their needs, pain points, and preferences. Create detailed personas with demographics and behavioral patterns.",
            "actionable_steps": [
                "Prepare interview questions focusing on user needs and pain points",
                "Recruit 20+ participants from target demographic",
                "Conduct structured interviews (30-45 minutes each)",
                "Analyze responses and identify common patterns",
                "Create 3-5 detailed personas with supporting data"
            ],
            "expected_outcome": "3-5 detailed customer personas with supporting research data",
            "time_estimate": "20 hours",
            "tools_needed": ["Video conferencing software", "Interview recording app", "Survey tools", "Data analysis software"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST3",
            "name": "Research industry best practices and technical standards",
            "description": "Study industry standards, best practices, and emerging trends relevant to the project domain.",
            "actionable_steps": [
                "Research industry reports and whitepapers",
                "Study technical standards and compliance requirements", 
                "Analyze emerging trends and future opportunities",
                "Document best practices and implementation guidelines",
                "Create recommendations for technical approach"
            ],
            "expected_outcome": "Industry best practices guide with technical recommendations",
            "time_estimate": "12 hours",
            "tools_needed": ["Research databases", "Industry reports", "Technical documentation"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST4",
            "name": "Analyze target market size and business opportunity",
            "description": "Conduct market sizing analysis and evaluate business opportunity potential.",
            "actionable_steps": [
                "Research total addressable market (TAM) size",
                "Identify serviceable addressable market (SAM)",
                "Calculate serviceable obtainable market (SOM)",
                "Analyze market growth trends and projections",
                "Create business opportunity assessment report"
            ],
            "expected_outcome": "Market opportunity analysis with size estimates and growth projections",
            "time_estimate": "14 hours", 
            "tools_needed": ["Market research tools", "Financial modeling software", "Industry databases"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST5",
            "name": "Create comprehensive requirements specification document",
            "description": "Compile all research findings into detailed requirements specification with clear acceptance criteria.",
            "actionable_steps": [
                "Synthesize all research findings and insights",
                "Define functional and non-functional requirements",
                "Create detailed user stories with acceptance criteria",
                "Prioritize requirements based on user needs and business value",
                "Document technical constraints and assumptions"
            ],
            "expected_outcome": "Complete requirements specification document with prioritized features",
            "time_estimate": "18 hours",
            "tools_needed": ["Documentation software", "Requirements management tools", "Collaboration platforms"]
        }
    ]

def _generate_development_subtasks(self, task: dict, domain_analysis: dict) -> list:
    """Generate development-specific subtasks"""
    
    return [
        {
            "subtask_id": f"{task['task_id']}_ST1",
            "name": "Set up development environment and project structure",
            "description": "Configure development environment with proper tooling, dependencies, and project structure following best practices.",
            "actionable_steps": [
                "Install and configure development tools and IDEs",
                "Set up version control repository with branching strategy",
                "Configure build tools and dependency management",
                "Create project structure following architectural patterns",
                "Set up development, staging, and production environments"
            ],
            "expected_outcome": "Fully configured development environment ready for coding",
            "time_estimate": "8 hours",
            "tools_needed": ["IDE", "Version control system", "Build tools", "Package managers"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST2",
            "name": "Implement core functionality with comprehensive testing",
            "description": "Develop main feature functionality with robust error handling and comprehensive unit test coverage.",
            "actionable_steps": [
                "Design and implement core business logic",
                "Add comprehensive error handling and validation",
                "Write unit tests with >90% code coverage",
                "Implement logging and monitoring hooks",
                "Optimize performance and memory usage"
            ],
            "expected_outcome": "Core functionality implemented with full test coverage",
            "time_estimate": "24 hours",
            "tools_needed": ["Development framework", "Testing libraries", "Code coverage tools"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST3",
            "name": "Create database schema and data access layer",
            "description": "Design optimized database schema and implement efficient data access patterns.",
            "actionable_steps": [
                "Design normalized database schema with proper indexing",
                "Implement data access layer with ORM/query builder",
                "Add database migration scripts and version control",
                "Implement data validation and sanitization",
                "Optimize queries for performance and scalability"
            ],
            "expected_outcome": "Optimized database schema with efficient data access layer",
            "time_estimate": "16 hours",
            "tools_needed": ["Database management system", "ORM framework", "Migration tools"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST4",
            "name": "Develop API endpoints with authentication and validation",
            "description": "Create RESTful API endpoints with proper authentication, authorization, and input validation.",
            "actionable_steps": [
                "Design RESTful API structure and endpoints",
                "Implement authentication and authorization middleware",
                "Add comprehensive input validation and sanitization",
                "Create API documentation with examples",
                "Implement rate limiting and security measures"
            ],
            "expected_outcome": "Secure, well-documented API endpoints with proper validation",
            "time_estimate": "20 hours",
            "tools_needed": ["API framework", "Authentication libraries", "Documentation tools"]
        },
        {
            "subtask_id": f"{task['task_id']}_ST5",
            "name": "Integrate frontend components with backend services",
            "description": "Connect user interface components with backend APIs and implement proper error handling.",
            "actionable_steps": [
                "Implement API client with proper error handling",
                "Connect UI components to backend services",
                "Add loading states and user feedback mechanisms",
                "Implement offline functionality where appropriate",
                "Test integration thoroughly across different scenarios"
            ],
            "expected_outcome": "Fully integrated frontend-backend system with robust error handling",
            "time_estimate": "18 hours",
            "tools_needed": ["Frontend framework", "HTTP client libraries", "State management tools"]
        }
    ]
```

**📤 OUTPUT:** Comprehensive subtask generation với actionable steps và tools needed
**⏱️ Duration:** 12 giờ

---

#### **Subtask 3.1.6: Implement Content Quality Assessment**
**📥 INPUT:** Generated content, quality thresholds, assessment criteria

**🔧 THỰC HIỆN:**
```python
def _assess_content_quality(self, content: dict) -> dict:
    """Assess quality of generated content across multiple dimensions"""
    
    quality_scores = {
        "clarity_score": self._assess_clarity(content),
        "actionability_score": self._assess_actionability(content),
        "engagement_score": self._assess_engagement(content),
        "consistency_score": self._assess_consistency(content)
    }
    
    # Overall quality score
    overall_score = sum(quality_scores.values()) / len(quality_scores)
    quality_scores["overall_score"] = overall_score
    
    return quality_scores

def _assess_clarity(self, content: dict) -> float:
    """Assess content clarity and readability"""
    
    clarity_score = 0.0
    total_checks = 0
    
    # Check milestone names clarity
    for milestone in content.get("milestones", []):
        total_checks += 1
        name = milestone.get("name", "")
        
        # Check word count (7-15 words optimal)
        word_count = len(name.split())
        if 7 <= word_count <= 15:
            clarity_score += 0.3
        
        # Check for action verbs
        action_verbs = ["create", "develop", "implement", "build", "design", "conduct", "analyze"]
        if any(verb in name.lower() for verb in action_verbs):
            clarity_score += 0.2
        
        # Check for specific terms (not generic)
        generic_terms = ["project", "work", "stuff", "things", "items"]
        if not any(term in name.lower() for term in generic_terms):
            clarity_score += 0.2
        
        # Check tasks clarity
        for task in milestone.get("tasks", []):
            total_checks += 1
            task_name = task.get("name", "")
            
            # Similar checks for tasks
            task_word_count = len(task_name.split())
            if 8 <= task_word_count <= 20:
                clarity_score += 0.2
            
            # Check for clear deliverables
            if task.get("expected_outcome") or task.get("acceptance_criteria"):
                clarity_score += 0.1
    
    return min(clarity_score / total_checks if total_checks > 0 else 0, 1.0)

def _assess_actionability(self, content: dict) -> float:
    """Assess how actionable the content is"""
    
    actionability_score = 0.0
    total_items = 0
    
    for milestone in content.get("milestones", []):
        for task in milestone.get("tasks", []):
            for subtask in task.get("subtasks", []):
                total_items += 1
                
                # Check for actionable steps
                actionable_steps = subtask.get("actionable_steps", [])
                if len(actionable_steps) >= 3:
                    actionability_score += 0.3
                
                # Check for specific tools mentioned
                tools_needed = subtask.get("tools_needed", [])
                if len(tools_needed) >= 2:
                    actionability_score += 0.2
                
                # Check for time estimates
                if subtask.get("time_estimate"):
                    actionability_score += 0.2
                
                # Check for expected outcomes
                if subtask.get("expected_outcome"):
                    actionability_score += 0.3
    
    return min(actionability_score / total_items if total_items > 0 else 0, 1.0)

def _assess_engagement(self, content: dict) -> float:
    """Assess engagement level of content"""
    
    engagement_score = 0.0
    total_checks = 0
    
    for milestone in content.get("milestones", []):
        total_checks += 1
        
        # Check for emojis
        if any(char in milestone.get("name", "") for char in "🎯🚀📊🔍⚡🧪🎨"):
            engagement_score += 0.2
        
        # Check for motivational messages
        if milestone.get("motivation_message"):
            engagement_score += 0.3
        
        # Check for success messages
        if milestone.get("success_message"):
            engagement_score += 0.2
        
        # Check for "why important" explanations
        for task in milestone.get("tasks", []):
            if task.get("why_important"):
                engagement_score += 0.1
            
            if task.get("success_tips"):
                engagement_score += 0.1
    
    return min(engagement_score / total_checks if total_checks > 0 else 0, 1.0)

def _assess_consistency(self, content: dict) -> float:
    """Assess consistency across all content"""
    
    consistency_score = 1.0  # Start with perfect score, deduct for inconsistencies
    
    # Check naming consistency
    milestone_names = [m.get("name", "") for m in content.get("milestones", [])]
    
    # Check emoji usage consistency
    emoji_usage = [bool(any(char in name for char in "🎯🚀📊🔍⚡🧪🎨")) for name in milestone_names]
    if not all(emoji_usage) and any(emoji_usage):
        consistency_score -= 0.2
    
    # Check description length consistency
    descriptions = [len(m.get("description", "").split()) for m in content.get("milestones", [])]
    if descriptions:
        desc_variance = max(descriptions) - min(descriptions)
        if desc_variance > 20:  # Too much variance in description lengths
            consistency_score -= 0.1
    
    # Check subtask count consistency (should be 5 per task)
    for milestone in content.get("milestones", []):
        for task in milestone.get("tasks", []):
            subtask_count = len(task.get("subtasks", []))
            if subtask_count != 5:
                consistency_score -= 0.1
    
    return max(consistency_score, 0.0)
```

**📤 OUTPUT:** Content quality assessment system với multiple dimensions
**⏱️ Duration:** 6 giờ

---

#### **Subtask 3.1.7: Implement Main Process Method**
**📥 INPUT:** Structure design từ Agent 2, all content generation logic

**🔧 THỰC HIỆN:**
```python
async def process(self, state: PlanGenerationState) -> dict:
    """Main content generation process"""
    
    structure_design = state["structure_design"]
    domain_analysis = state["domain_analysis"]
    
    try:
        # Step 1: Enhance milestone content
        enhanced_milestones = []
        
        for milestone in structure_design["milestone_structure"]:
            enhanced_milestone = self._enhance_milestone_content(milestone, domain_analysis)
            
            # Step 2: Enhance task content
            enhanced_tasks = []
            for task in milestone.get("tasks", []):
                enhanced_task = self._enhance_task_content(task, milestone, domain_analysis)
                
                # Step 3: Generate subtasks
                enhanced_task["subtasks"] = self._generate_subtasks(enhanced_task, domain_analysis)
                
                enhanced_tasks.append(enhanced_task)
            
            enhanced_milestone["tasks"] = enhanced_tasks
            enhanced_milestones.append(enhanced_milestone)
        
        # Step 4: Create complete content structure
        detailed_content = {
            "milestones": enhanced_milestones
        }
        
        # Step 5: Assess content quality
        content_metrics = self._assess_content_quality({"milestones": enhanced_milestones})
        
        # Step 6: Apply style guidelines
        style_guidelines = self.style_guidelines.copy()
        
        # Step 7: Create final content data
        content_data = {
            "detailed_content": detailed_content,
            "content_metrics": content_metrics,
            "style_guidelines": style_guidelines,
            "generation_metadata": {
                "total_milestones": len(enhanced_milestones),
                "total_tasks": sum(len(m["tasks"]) for m in enhanced_milestones),
                "total_subtasks": sum(len(t["subtasks"]) for m in enhanced_milestones for t in m["tasks"]),
                "generation_timestamp": datetime.now().isoformat()
            }
        }
        
        # Step 8: Validate output
        if not self.validate_output({"content_data": content_data}):
            raise ValueError("Content generation validation failed")
        
        return {
            "content_data": content_data,
            "progress": 50.0  # 3/6 agents completed (parallel with timeline)
        }
        
    except Exception as e:
        self.logger.error(f"Content generation failed: {e}")
        raise e

def validate_output(self, output: dict) -> bool:
    """Validate content generation output"""
    content_data = output.get("content_data", {})
    
    # Check required fields
    required_fields = ["detailed_content", "content_metrics", "style_guidelines"]
    if not all(field in content_data for field in required_fields):
        return False
    
    # Check milestone count
    milestones = content_data.get("detailed_content", {}).get("milestones", [])
    if len(milestones) != 5:
        return False
    
    # Check task and subtask counts
    for milestone in milestones:
        tasks = milestone.get("tasks", [])
        if len(tasks) != 5:
            return False
        
        for task in tasks:
            subtasks = task.get("subtasks", [])
            if len(subtasks) != 5:
                return False
    
    # Check quality thresholds
    metrics = content_data.get("content_metrics", {})
    for metric, threshold in self.quality_thresholds.items():
        if metrics.get(metric, 0) < threshold:
            self.logger.warning(f"Content quality below threshold: {metric} = {metrics.get(metric, 0)}")
            # Don't fail validation, but log warning
    
    return True
```

**📤 OUTPUT:** Complete content generation process với quality validation
**⏱️ Duration:** 5 giờ

---

## 📊 **TỔNG KẾT AGENT 3**

### **Tổng thời gian ước tính:** 50 giờ (6-7 ngày làm việc)

### **Deliverables chính:**
1. **ContentGenerationAgent class** - Complete implementation
2. **Content templates** - Domain-specific content patterns
3. **Milestone enhancement** - Detailed names, descriptions, motivational elements
4. **Task enhancement** - Actionable descriptions, acceptance criteria, success tips
5. **Subtask generation** - 125 detailed subtasks với actionable steps
6. **Quality assessment** - Multi-dimensional content quality scoring
7. **Style consistency** - Consistent tone, format, engagement elements

### **Input → Output Transformation:**
```
Structure Design (Agent 2 output)
    ↓
{
    "detailed_content": {
        "milestones": [
            {
                "name": "🔍 Market Research and Technical Foundation Setup",
                "description": "Comprehensive analysis of fashion e-commerce market...",
                "motivation_message": "🚀 Every successful app starts with understanding...",
                "tasks": [
                    {
                        "name": "🎯 Conduct comprehensive market analysis...",
                        "description": "Dive deep into fashion e-commerce landscape...",
                        "why_important": "Understanding your market is crucial...",
                        "success_tips": ["Focus on user pain points", "Look for gaps"],
                        "subtasks": [
                            {
                                "name": "Survey and analyze top 15 fashion apps",
                                "actionable_steps": ["Download apps", "Test user journey"],
                                "tools_needed": ["Smartphone", "Spreadsheet"],
                                "time_estimate": "16 hours"
                            }
                            // ... 4 more detailed subtasks
                        ]
                    }
                    // ... 4 more enhanced tasks
                ]
            }
            // ... 4 more enhanced milestones
        ]
    },
    "content_metrics": {
        "clarity_score": 0.95,
        "actionability_score": 0.88,
        "engagement_score": 0.92,
        "consistency_score": 0.94
    }
}
```

**Agent 3 transforms abstract structure into detailed, actionable, engaging content với 125 subtasks!** 📝
