# 🎯 Domain Classification Agent - Complete Logic Documentation

## 🏗️ **KIẾN TRÚC TỔNG QUAN**

### **1. <PERSON>ai trò chính:**
- **<PERSON>ân tích input của user** để xác định domain chính (mobile app, web dev, AI/ML, fintech, v.v.)
- **Trích xuất requirements** (functional, non-functional, technical)
- **Đánh giá độ phức tạp** và constraints của project
- **Cung cấp confidence score** cho từng classification

### **2. Cấu trúc xử lý:**
```
User Input → Pattern-Based Analysis → Gemini Detection (if needed) → AI Enhancement → Final Result
```

---

## 🔍 **CHI TIẾT CÁC BƯỚC XỬ LÝ**

### **BƯỚC 1: PATTERN-BASED CLASSIFICATION**

```python
def _classify_primary_domain(self, user_input: str) -> Tuple[str, float, Dict[str, Any]]:
    """Classify primary domain based on keywords and patterns"""
    scores = {}
    user_input_lower = user_input.lower()

    for domain, patterns in self.domain_patterns.items():
        score = 0
        
        # Keyword matching (base score)
        for keyword in patterns["keywords"]:
            if keyword.lower() in user_input_lower:
                score += 2
        
        # Indicator matching (higher weight)
        for indicator in patterns["indicators"]:
            if indicator.lower() in user_input_lower:
                score += 3
        
        # Complexity factor detection
        complexity_score = 0
        for factor in patterns["complexity_factors"]:
            if factor.lower() in user_input_lower:
                complexity_score += 1
        
        scores[domain] = {
            "base_score": score,
            "complexity_score": complexity_score,
            "total_score": score + complexity_score * 0.5
        }
```

**Logic:**
- **50+ domains** được định nghĩa với keywords, indicators, complexity_factors
- **Scoring system**: Keywords = 2 điểm, Indicators = 3 điểm, Complexity factors = 0.5 điểm
- **Confidence mapping**: Score ≥8 → 95%, Score ≥6 → 85%, Score ≥4 → 75%

### **BƯỚC 2: GEMINI DETECTION (Khi cần thiết)**

```python
async def _apply_gemini_detection_if_needed(self, preliminary_analysis: Dict[str, Any], user_input: str):
    """Apply Gemini detection if pattern-based result is not good enough"""
    
    # Criteria for using Gemini detection:
    # 1. Low confidence (< 0.6)
    # 2. Default fallback domain (web_development with confidence 0.3)
    # 3. Very low total score (< 3)
    # 4. Ambiguous cases (multiple domains with similar scores)
```

**Khi nào sử dụng Gemini:**
- **Confidence < 0.6**: Pattern-based không chắc chắn
- **Default fallback**: Rơi vào domain mặc định (web_development)
- **Score thấp < 3**: Không đủ evidence từ keywords
- **Ambiguous**: Nhiều domains có score gần nhau

### **BƯỚC 3: ALTERNATING MODELS STRATEGY**

```python
def _get_alternating_model(self):
    """Get alternating model for load balancing"""
    self.request_counter += 1
    
    # Alternate between primary and fallback models
    if self.request_counter % 2 == 1:
        return self.gemini_model, self.primary_model
    else:
        return self.gemini_fallback_model, self.fallback_model
```

**Load Balancing:**
- **Model 1**: `gemini-2.0-flash` (primary)
- **Model 2**: `gemini-2.5-flash-lite` (fallback)
- **Alternating pattern**: Request 1,3,5... → Model 1, Request 2,4,6... → Model 2
- **Fallback mechanism**: Nếu model hiện tại fail → thử model còn lại

### **BƯỚC 4: AI ENHANCEMENT (3 Approaches)**

#### **4.1 Basic Enhancement:**
- Sử dụng khi confidence ≥ 0.7 và project đơn giản
- 1 lần call Gemini với prompt chuẩn

#### **4.2 Advanced Enhancement:**
- Sử dụng khi confidence < 0.7 hoặc multi-domain project
- **3 approaches song song:**

```python
# Approach 1: Direct classification with specialized prompt
direct_result = await self._gemini_direct_classify(user_input, specialized_prompt)

# Approach 2: Hierarchical classification  
hierarchical_result = await self._gemini_hierarchical_classify(user_input)

# Approach 3: Cross-domain analysis (if multiple domains detected)
cross_domain_result = await self._gemini_cross_domain_analyze(user_input, preliminary_analysis)
```

#### **4.3 Ensemble Results:**
- **Voting system**: Combine kết quả từ multiple approaches
- **Confidence weighting**: Approaches có confidence cao được ưu tiên
- **Domain consensus**: Chọn domain được vote nhiều nhất

---

## 📊 **DOMAIN PATTERNS (50+ Domains)**

### **Technology Domains:**
- `mobile_app_development`, `web_development`, `ai_ml_development`
- `blockchain_development`, `iot_development`, `ar_vr_development`

### **Business Domains:**
- `e_commerce`, `fintech`, `saas_development`, `enterprise_software`

### **Industry Verticals:**
- `healthcare_tech`, `edtech`, `proptech`, `agtech`, `cleantech`

### **Emerging Tech:**
- `cryptocurrency`, `metaverse`, `creator_economy`, `mobility_tech`

**Mỗi domain có:**
- **Keywords**: Từ khóa cơ bản (app, mobile, AI, blockchain...)
- **Indicators**: Chỉ số chuyên biệt (API, real-time, smart contract...)
- **Complexity factors**: Yếu tố phức tạp (security, scalability, compliance...)
- **Sub-domains**: Các lĩnh vực con

---

## 🎯 **REQUIREMENTS EXTRACTION**

### **3 loại requirements:**

```python
"functional": {
    "user_management": ["đăng nhập", "tài khoản", "user", "authentication"],
    "payment_processing": ["thanh toán", "payment", "momo", "visa"],
    "product_catalog": ["sản phẩm", "danh mục", "catalog", "inventory"],
    "search_functionality": ["tìm kiếm", "search", "filter", "sort"],
    "shopping_cart": ["giỏ hàng", "cart", "add to cart", "checkout"],
    "order_management": ["đơn hàng", "order", "tracking", "history"],
    "reviews_ratings": ["đánh giá", "review", "rating", "comment"],
    "notifications": ["thông báo", "notification", "alert", "push"],
    "social_features": ["share", "like", "follow", "comment", "social"],
    "content_management": ["content", "cms", "blog", "article", "post"],
    "reporting_analytics": ["báo cáo", "report", "analytics", "dashboard"],
    "messaging_chat": ["chat", "message", "conversation", "support"]
},
"non_functional": {
    "performance": ["nhanh", "fast", "performance", "tốc độ", "speed"],
    "security": ["bảo mật", "secure", "an toàn", "encryption", "ssl"],
    "scalability": ["mở rộng", "scale", "nhiều user", "traffic", "load"],
    "usability": ["dễ dùng", "user-friendly", "intuitive", "UX"],
    "reliability": ["ổn định", "reliable", "uptime", "available", "stable"],
    "mobile_responsive": ["mobile", "responsive", "điện thoại", "tablet"],
    "accessibility": ["accessibility", "disabled", "screen reader", "wcag"],
    "maintainability": ["maintain", "update", "modify", "extend"],
    "compatibility": ["compatible", "cross-platform", "browser", "device"]
},
"technical": {
    "mobile_app": ["app", "mobile", "ios", "android", "react native"],
    "web_application": ["website", "web app", "browser", "html", "css"],
    "backend_api": ["api", "backend", "server", "rest", "graphql"],
    "database_design": ["database", "data storage", "mysql", "mongodb"],
    "cloud_hosting": ["cloud", "aws", "azure", "gcp", "hosting"],
    "third_party_integration": ["integration", "api", "payment gateway"],
    "real_time_features": ["real-time", "websocket", "live", "instant"],
    "ai_ml_features": ["ai", "machine learning", "recommendation", "nlp"]
}
```

---

## 🧠 **COMPLEXITY ASSESSMENT**

```python
def _assess_complexity(self, requirements: Dict[str, List[str]], constraints: Dict[str, Any]) -> str:
    """Assess project complexity level"""
    complexity_score = 0
    
    # Count requirements
    total_requirements = sum(len(reqs) for reqs in requirements.values())
    if total_requirements > 15: complexity_score += 2
    elif total_requirements > 10: complexity_score += 1
    
    # Check for complex features
    complex_features = [
        "payment_processing", "real_time_features", "ai_ml_features",
        "third_party_integration", "scalability", "security"
    ]
    
    for req_type in requirements.values():
        for req in req_type:
            if req in complex_features:
                complexity_score += 1
    
    # Time constraints impact
    time_constraint = constraints.get("time", "")
    if "week" in time_constraint or "tuần" in time_constraint:
        complexity_score += 1
    
    # Team size impact
    team_size = constraints.get("team_size", "")
    if team_size == "solo":
        complexity_score += 1
    elif team_size == "large_team":
        complexity_score -= 1
    
    # Determine complexity level
    if complexity_score <= 2: return "beginner"
    elif complexity_score <= 5: return "intermediate"
    elif complexity_score <= 8: return "advanced"
    else: return "expert"
```

**Complexity levels:**
- **Beginner** (score ≤ 2): Simple projects
- **Intermediate** (score 3-5): Standard business apps
- **Advanced** (score 6-8): Complex integrations
- **Expert** (score > 8): Enterprise-level systems

---

## 🔄 **PROCESSING FLOW**

### **Main Process:**
1. **Pattern-based classification** → Primary domain + confidence
2. **Requirements extraction** → Functional/Non-functional/Technical
3. **Constraints analysis** → Time, budget, team size, expertise
4. **Sub-domains identification** → Related areas
5. **Complexity assessment** → Beginner to Expert level
6. **Success metrics** → Domain-specific KPIs
7. **Stakeholders identification** → Project participants

### **Enhancement Flow:**
```
Low confidence? → Gemini Detection
Still low? → AI Enhancement (Basic/Advanced)
Multiple approaches → Ensemble results
Final validation → Output
```

---

## 📈 **OUTPUT STRUCTURE**

```python
{
    "domain_analysis": {
        "primary_domain": "mobile_app_development",
        "sub_domains": ["e_commerce", "user_experience", "payment_systems"],
        "complexity_level": "intermediate",
        "confidence_score": 0.85,
        "extracted_requirements": {
            "functional": ["user_management", "product_catalog"],
            "non_functional": ["performance", "security"],
            "technical": ["mobile_app", "backend_api"]
        },
        "constraints": {
            "time": "3_months",
            "budget": "medium",
            "team_size": "small_team_3_5_people",
            "technical_expertise": "intermediate"
        },
        "success_metrics": [
            "app_store_rating",
            "download_count",
            "user_retention_rate",
            "daily_active_users",
            "crash_rate"
        ],
        "stakeholders": [
            "end_users",
            "development_team",
            "project_owner",
            "app_store_reviewers"
        ],
        "processing_method": "pattern_based_with_gemini_detection",
        "model_used": "gemini-2.0-flash",
        "ai_enhancement_confidence": 0.8,
        "enhancement_method": "gemini_basic"
    }
}
```

---

## 🎯 **KEY FEATURES**

### **1. Intelligent Fallback:**
- Pattern-based → Gemini Detection → AI Enhancement
- Multiple models với alternating strategy
- Graceful degradation khi AI fails

### **2. Multi-language Support:**
- Keywords tiếng Việt và tiếng Anh
- Context-aware classification

### **3. Domain-specific Enhancement:**
- Specialized prompts cho từng domain
- Cross-domain analysis cho complex projects

### **4. Confidence Tracking:**
- Pattern confidence + AI confidence
- Self-assessment mechanism
- Transparent reasoning

### **5. Comprehensive Analysis:**
- 50+ domains coverage
- Requirements extraction
- Complexity assessment
- Success metrics identification

---

## 🚀 **PERFORMANCE & RELIABILITY**

### **Rate Limiting:**
- 5 giây delay giữa các Gemini requests
- Tuân thủ API limits (12 requests/minute)

### **Error Handling:**
- Fallback models khi primary fails
- Graceful degradation về pattern-based
- Comprehensive logging

### **Quality Assurance:**
- Output validation
- Required fields checking
- Data type validation

---

## 🔧 **SPECIALIZED PROMPTS**

### **Domain-specific Prompts:**
- **Fintech**: Focus on regulatory compliance (PCI DSS, KYC, AML)
- **Healthcare**: HIPAA compliance, medical standards
- **Blockchain**: Smart contracts, consensus mechanisms, security audits
- **AI/ML**: Model development, MLOps, deployment challenges
- **IoT**: Edge computing, device management, connectivity

### **Hierarchical Classification:**
1. **Major Category**: TECHNOLOGY / BUSINESS / INDUSTRY
2. **Specific Domain**: Based on category-specific domain list
3. **Sub-domains**: 2-4 related areas
4. **Requirements**: Top 5 most important
5. **Success Metrics**: 3-5 appropriate KPIs

---

## 📊 **SUCCESS METRICS BY DOMAIN**

### **Mobile App Development:**
- App store rating
- Download count
- User retention rate
- Daily active users
- Crash rate

### **E-commerce:**
- Conversion rate
- Average order value
- Customer acquisition cost
- Customer lifetime value
- Cart abandonment rate

### **Web Development:**
- Page load speed
- Bounce rate
- User engagement time
- SEO ranking
- Conversion rate

### **Data Science:**
- Model accuracy
- Data quality score
- Processing time
- Prediction accuracy
- User adoption rate

---

## 🎭 **STAKEHOLDERS BY DOMAIN**

### **Base Stakeholders:**
- End users
- Development team
- Project owner

### **Domain-specific Stakeholders:**

**E-commerce:**
- Customers
- Vendors
- Payment providers

**Mobile App:**
- App store reviewers
- Device manufacturers

**Enterprise Software:**
- System administrators
- Compliance officers

**Data Science:**
- Data scientists
- Business analysts
- Data owners

---

## 🔍 **VALIDATION & QUALITY CONTROL**

### **Output Validation:**
```python
def _validate_output(self, output: Dict[str, Any]) -> bool:
    """Validate domain analysis output"""
    analysis = output.get("domain_analysis", {})

    required_fields = [
        "primary_domain", "sub_domains", "complexity_level",
        "confidence_score", "extracted_requirements",
        "constraints", "success_metrics", "stakeholders"
    ]

    # Check required fields
    for field in required_fields:
        if field not in analysis:
            return False

    # Validate data types and ranges
    if not (0.0 <= analysis["confidence_score"] <= 1.0):
        return False

    # Validate requirements structure
    req_types = ["functional", "non_functional", "technical"]
    for req_type in req_types:
        if req_type not in analysis["extracted_requirements"]:
            return False

    return True
```

---

## 🎉 **CONCLUSION**

Domain Classification Agent là một hệ thống sophisticated kết hợp:

- **Pattern matching truyền thống** với **AI enhancement hiện đại**
- **Multi-model strategy** với **intelligent fallback**
- **Comprehensive domain coverage** với **detailed requirements extraction**
- **Confidence tracking** với **transparent reasoning**

Đảm bảo **accuracy cao** và **reliability tốt** cho việc phân tích project requirements trong hệ thống Ignition multi-agent! 🚀
