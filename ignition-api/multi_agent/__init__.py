"""
Multi-Agent System for Ignition Plan Generation

This module implements a sophisticated multi-agent system using LangGraph
to transform simple user ideas into comprehensive, world-class development plans.

Architecture:
- 6 specialized agents working in sequence and parallel
- State-based communication between agents
- Comprehensive error handling and retry logic
- Real-time progress tracking and quality validation

Agents:
1. Domain Classification Agent: Analyzes user input and extracts requirements
2. Structure Optimization Agent: Creates optimal project structure with milestones  
3. Content Generation Agent: Generates detailed content for tasks and subtasks
4. Timeline Optimization Agent: Calculates realistic timelines and resource allocation
5. Validation Agent: Validates plan quality and consistency
6. Quality Enhancement Agent: Polishes and enhances final plan

Usage:
    from multi_agent.orchestrator import IgnitionPlanOrchestrator
    
    orchestrator = IgnitionPlanOrchestrator()
    result = await orchestrator.generate_plan("Tôi muốn tạo app e-commerce bán quần áo")
"""

__version__ = "1.0.0"
__author__ = "Ignition AI Team"

# Import main components for easy access
try:
    from .orchestrator import IgnitionPlanOrchestrator
    _orchestrator_available = True
except ImportError:
    _orchestrator_available = False

from .base_agent import BaseIgnitionAgent

if _orchestrator_available:
    __all__ = [
        'IgnitionPlanOrchestrator',
        'BaseIgnitionAgent',
    ]
else:
    __all__ = [
        'BaseIgnitionAgent',
    ]
