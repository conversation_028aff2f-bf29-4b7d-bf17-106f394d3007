"""
Test cases for Content Generation Agent

This module contains comprehensive tests for the ContentGenerationAgent
to ensure it properly generates detailed content for all plan elements.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from ..agents.content_generator import ContentGenerationAgent
from ..services.shared_memory import PlanGenerationState, create_initial_state


class TestContentGenerationAgent:
    """Test cases for Content Generation Agent"""
    
    @pytest.fixture
    def agent(self):
        """Create a Content Generation Agent for testing"""
        config = {
            "primary_model": "gemini-2.0-flash-exp",
            "temperature": 0.2,
            "max_tokens": 2000,
            "timeout": 45
        }
        return ContentGenerationAgent(config)
    
    @pytest.fixture
    def sample_structure_design(self):
        """Sample structure design from Agent 2"""
        return {
            "milestone_structure": [
                {
                    "milestone_id": "M1",
                    "name": "Research and Planning",
                    "description": "Market research, requirements analysis, technical planning",
                    "position": 1,
                    "estimated_duration": "2_weeks",
                    "dependencies": [],
                    "critical_path": True,
                    "task_count": 5,
                    "complexity_weight": 0.7,
                    "tasks": [
                        {
                            "task_id": "M1_T1",
                            "name": "Market research and competitor analysis",
                            "estimated_duration": "1_week",
                            "complexity": "medium",
                            "required_skills": ["business_analysis", "market_research"],
                            "dependencies": [],
                            "deliverables": ["Research report", "Analysis document"],
                            "acceptance_criteria": "Research completed, findings documented"
                        },
                        {
                            "task_id": "M1_T2",
                            "name": "User persona development and user journey mapping",
                            "estimated_duration": "1_week",
                            "complexity": "medium",
                            "required_skills": ["ux_design", "user_research"],
                            "dependencies": ["M1_T1"],
                            "deliverables": ["User personas", "Journey maps"],
                            "acceptance_criteria": "Personas created and validated"
                        }
                    ]
                }
            ],
            "dependency_graph": {"M1": []},
            "optimization_score": 0.85
        }
    
    @pytest.fixture
    def sample_domain_analysis(self):
        """Sample domain analysis from Agent 1"""
        return {
            "primary_domain": "mobile_app_development",
            "sub_domains": ["e_commerce", "fashion_retail"],
            "complexity_level": "intermediate",
            "extracted_requirements": {
                "functional": ["user_authentication", "product_catalog", "payment_processing"],
                "non_functional": ["mobile_responsive", "secure_payments"],
                "technical": ["mobile_app", "backend_api"]
            },
            "constraints": {"time": "3_months"}
        }
    
    @pytest.fixture
    def sample_state(self, sample_domain_analysis, sample_structure_design):
        """Sample state with domain analysis and structure design"""
        state = create_initial_state("Tôi muốn tạo app e-commerce bán quần áo")
        state["domain_analysis"] = sample_domain_analysis
        state["structure_design"] = sample_structure_design
        return state
    
    def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent.agent_name == "content_generator"
        assert agent.subtasks_per_task == 5
        assert len(agent.content_templates) > 0
        assert len(agent.style_guidelines) > 0
        assert "clarity_score" in agent.quality_thresholds
    
    def test_milestone_content_enhancement(self, agent, sample_domain_analysis):
        """Test milestone content enhancement"""
        milestone = {
            "milestone_id": "M1",
            "name": "Research and Planning",
            "description": "Basic description",
            "position": 1
        }
        
        enhanced = agent._enhance_milestone_content(milestone, sample_domain_analysis)
        
        assert "🔍" in enhanced["name"]  # Should have emoji
        assert len(enhanced["description"]) > len(milestone["description"])
        assert "success_message" in enhanced
        assert "motivation_message" in enhanced
        assert "🎉" in enhanced["success_message"] or "🚀" in enhanced["success_message"]
    
    def test_task_content_enhancement(self, agent, sample_domain_analysis):
        """Test task content enhancement"""
        task = {
            "task_id": "M1_T1",
            "name": "Market research and competitor analysis",
            "estimated_duration": "1_week",
            "complexity": "medium"
        }
        
        milestone = {"name": "Research and Planning", "position": 1}
        
        enhanced = agent._enhance_task_content(task, milestone, sample_domain_analysis)
        
        assert "🎯" in enhanced["name"] or "📊" in enhanced["name"]  # Should have emoji
        assert len(enhanced["description"]) >= 100  # Should be detailed
        assert "acceptance_criteria" in enhanced
        assert "why_important" in enhanced
        assert "success_tips" in enhanced
        assert "estimated_effort" in enhanced
        assert "difficulty_level" in enhanced
        assert len(enhanced["success_tips"]) >= 2
    
    def test_subtask_generation(self, agent, sample_domain_analysis):
        """Test subtask generation"""
        task = {
            "task_id": "M1_T1",
            "name": "Market research and competitor analysis"
        }
        
        subtasks = agent._generate_subtasks(task, sample_domain_analysis)
        
        assert len(subtasks) == 5
        
        for i, subtask in enumerate(subtasks):
            assert subtask["subtask_id"] == f"M1_T1_ST{i+1}"
            assert "name" in subtask
            assert "description" in subtask
            assert "expected_outcome" in subtask
            assert "tools_needed" in subtask
            assert "estimated_hours" in subtask
            assert "actionable_steps" in subtask
            assert "success_criteria" in subtask
            assert len(subtask["actionable_steps"]) >= 3
    
    def test_quality_assessment(self, agent):
        """Test content quality assessment"""
        sample_content = {
            "milestones": [
                {
                    "milestone_id": "M1",
                    "name": "🔍 Research and Planning",
                    "description": "This is a comprehensive description with more than twenty-five words to meet the clarity requirements for quality assessment.",
                    "success_message": "🎉 Research Complete!",
                    "motivation_message": "Great start!",
                    "tasks": [
                        {
                            "task_id": "M1_T1",
                            "name": "🎯 Conduct market research",
                            "description": "Detailed task description with actionable content and specific outcomes for users.",
                            "actionable_steps": ["Step 1", "Step 2", "Step 3"],
                            "success_tips": ["Tip 1", "Tip 2"],
                            "subtasks": [
                                {
                                    "subtask_id": "M1_T1_ST1",
                                    "name": "Research competitors",
                                    "description": "Detailed subtask description with specific requirements.",
                                    "expected_outcome": "Competitor analysis",
                                    "actionable_steps": ["Action 1", "Action 2", "Action 3", "Action 4"]
                                }
                            ] * 5  # 5 subtasks
                        }
                    ] * 5  # 5 tasks
                }
            ] * 5  # 5 milestones
        }
        
        metrics = agent._assess_content_quality(sample_content)
        
        assert "clarity_score" in metrics
        assert "actionability_score" in metrics
        assert "engagement_score" in metrics
        assert "consistency_score" in metrics
        assert "overall_score" in metrics
        assert 0.0 <= metrics["overall_score"] <= 1.0
    
    @pytest.mark.asyncio
    async def test_process_method(self, agent, sample_state):
        """Test the main process method"""
        # Add more tasks to the structure for complete testing
        sample_state["structure_design"]["milestone_structure"][0]["tasks"] = [
            {
                "task_id": f"M1_T{i+1}",
                "name": f"Task {i+1} for testing",
                "estimated_duration": "1_week",
                "complexity": "medium",
                "required_skills": ["general"],
                "dependencies": [],
                "deliverables": ["Deliverable"],
                "acceptance_criteria": "Completed"
            } for i in range(5)
        ]
        
        result = await agent.process(sample_state)
        
        assert "content_data" in result
        assert "progress" in result
        assert result["progress"] == 50.0
        
        content_data = result["content_data"]
        
        # Validate structure
        assert "detailed_content" in content_data
        assert "content_metrics" in content_data
        
        detailed_content = content_data["detailed_content"]
        
        # Validate counts
        assert detailed_content["total_milestones"] == 1  # Only 1 milestone in test data
        assert detailed_content["total_tasks"] == 5
        assert detailed_content["total_subtasks"] == 25  # 5 tasks × 5 subtasks
        
        # Validate milestone enhancement
        milestone = detailed_content["milestones"][0]
        assert "🔍" in milestone["name"]  # Should have emoji
        assert "success_message" in milestone
        assert "motivation_message" in milestone
        
        # Validate task enhancement
        for task in milestone["tasks"]:
            assert len(task["subtasks"]) == 5
            assert "description" in task
            assert "why_important" in task
            assert "success_tips" in task
            
            # Validate subtasks
            for subtask in task["subtasks"]:
                assert "actionable_steps" in subtask
                assert "tools_needed" in subtask
                assert "expected_outcome" in subtask
    
    @pytest.mark.asyncio
    async def test_validation(self, agent, sample_state):
        """Test output validation"""
        result = await agent.process(sample_state)
        
        # Test valid output
        is_valid = await agent.validate_output(result)
        assert is_valid
        
        # Test invalid output - missing field
        invalid_result = {"content_data": {"detailed_content": {}}}
        is_valid = await agent.validate_output(invalid_result)
        assert not is_valid


if __name__ == "__main__":
    # Run a simple test
    async def run_test():
        agent = ContentGenerationAgent()
        
        # Sample data
        domain_analysis = {
            "primary_domain": "mobile_app_development",
            "complexity_level": "intermediate",
            "extracted_requirements": {
                "functional": ["user_authentication", "product_catalog"],
                "non_functional": ["mobile_responsive"],
                "technical": ["mobile_app"]
            }
        }
        
        structure_design = {
            "milestone_structure": [
                {
                    "milestone_id": "M1",
                    "name": "Research and Planning",
                    "description": "Market research and planning",
                    "position": 1,
                    "tasks": [
                        {
                            "task_id": "M1_T1",
                            "name": "Market research",
                            "complexity": "medium"
                        }
                    ] * 5
                }
            ]
        }
        
        state = create_initial_state("Test app")
        state["domain_analysis"] = domain_analysis
        state["structure_design"] = structure_design
        
        result = await agent.process(state)
        print("Content generation completed!")
        print(f"Generated {result['content_data']['detailed_content']['total_subtasks']} subtasks")
    
    asyncio.run(run_test())
