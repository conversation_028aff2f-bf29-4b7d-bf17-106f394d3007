"""
Test Suite for Multi-Agent System

This module contains comprehensive tests for all components
of the multi-agent plan generation system.
"""

# Test configuration
TEST_CONFIG = {
    'timeout': 30,
    'max_retries': 2,
    'test_data_path': 'test_data/',
    'mock_ai_responses': True
}

# Test utilities
from .utils import create_test_state, mock_ai_response, assert_state_valid

__all__ = [
    'TEST_CONFIG',
    'create_test_state',
    'mock_ai_response', 
    'assert_state_valid',
]
