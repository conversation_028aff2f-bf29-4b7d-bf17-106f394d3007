"""
Test cases for Quality Enhancement Agent

This module contains comprehensive tests for the QualityEnhancementAgent
to ensure it properly enhances plan quality and adds motivational elements.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from ..agents.quality_enhancer import QualityEnhancementAgent
from ..services.shared_memory import PlanGenerationState, create_initial_state


class TestQualityEnhancementAgent:
    """Test cases for Quality Enhancement Agent"""
    
    @pytest.fixture
    def agent(self):
        """Create a Quality Enhancement Agent for testing"""
        config = {
            "primary_model": "gemini-2.0-flash-exp",
            "temperature": 0.3,
            "max_tokens": 4000,
            "timeout": 90
        }
        return QualityEnhancementAgent(config)
    
    @pytest.fixture
    def sample_complete_state(self):
        """Sample complete state with all agent outputs including validation"""
        return {
            "domain_analysis": {
                "primary_domain": "mobile_app_development",
                "complexity_level": "intermediate",
                "extracted_requirements": {
                    "functional": ["user_authentication", "product_catalog", "payment_processing"],
                    "non_functional": ["mobile_responsive", "secure_payments"],
                    "technical": ["mobile_app", "backend_api"]
                },
                "constraints": {"time": "3_months", "team_size": "small_team_3_5_people"}
            },
            "structure_design": {
                "milestone_structure": [
                    {"milestone_id": "M1", "name": "Research and Planning"}
                ] * 5,
                "dependency_graph": {"M1": [], "M2": ["M1"], "M3": ["M2"], "M4": ["M3"], "M5": ["M4"]},
                "optimization_score": 0.85
            },
            "content_data": {
                "detailed_content": {
                    "milestones": [
                        {
                            "milestone_id": "M1",
                            "name": "Research and Planning",
                            "description": "Market research and technical planning",
                            "tasks": [
                                {
                                    "task_id": "M1_T1",
                                    "name": "Conduct market research",
                                    "description": "Detailed market analysis",
                                    "acceptance_criteria": "Research completed with findings documented",
                                    "subtasks": [
                                        {
                                            "subtask_id": "M1_T1_ST1",
                                            "name": "Survey competitors",
                                            "description": "Analyze top competitors",
                                            "actionable_steps": ["Step 1", "Step 2"],
                                            "tools_needed": ["Browser"],
                                            "expected_outcome": "Competitor analysis"
                                        }
                                    ] * 5
                                }
                            ] * 5
                        }
                    ] * 5
                },
                "content_metrics": {"clarity_score": 0.95, "actionability_score": 0.88}
            },
            "timeline_data": {
                "project_start_date": "2025-08-04",
                "project_end_date": "2025-10-24",
                "total_duration_weeks": 11,
                "milestone_schedule": [
                    {"milestone_id": "M1", "start_date": "2025-08-04", "end_date": "2025-08-18"}
                ] * 5,
                "optimization_score": 0.91
            },
            "validation_results": {
                "overall_score": 0.88,
                "dimension_scores": {
                    "completeness_score": 0.90,
                    "consistency_score": 0.85,
                    "feasibility_score": 0.87,
                    "requirement_alignment": 0.90
                },
                "issues_found": [
                    {
                        "issue_id": "TEST_001",
                        "type": "insufficient_actionable_steps",
                        "severity": "medium",
                        "auto_fixable": False
                    }
                ],
                "improvement_suggestions": [
                    {
                        "area": "completeness",
                        "suggestion": "Add more detailed content",
                        "priority": "high"
                    }
                ],
                "quality_gates": {
                    "structure_quality": "pass",
                    "content_quality": "pass_with_warnings",
                    "timeline_quality": "pass",
                    "overall_quality": "pass_with_warnings"
                }
            }
        }
    
    def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent.agent_name == "quality_enhancer"
        assert agent.target_quality_score == 0.95
        assert agent.enhancement_level == "premium"
        assert len(agent.enhancement_templates) > 0
        assert len(agent.motivational_elements) > 0
        assert len(agent.quality_metrics) > 0
    
    def test_enhancement_templates_loading(self, agent):
        """Test enhancement templates loading"""
        templates = agent.enhancement_templates
        
        # Check main template categories
        assert "executive_summary" in templates
        assert "milestone_enhancements" in templates
        assert "task_enhancements" in templates
        assert "quality_polish" in templates
        
        # Check specific templates
        exec_summary = templates["executive_summary"]
        assert "project_overview_template" in exec_summary
        assert "key_highlights_template" in exec_summary
        
        milestone_enhancements = templates["milestone_enhancements"]
        assert "celebration_messages" in milestone_enhancements
        assert "motivation_boosters" in milestone_enhancements
    
    def test_motivational_elements_loading(self, agent):
        """Test motivational elements loading"""
        motivational = agent.motivational_elements
        
        assert "project_start_messages" in motivational
        assert "milestone_completion_rewards" in motivational
        assert "encouragement_during_challenges" in motivational
        assert "final_completion_celebrations" in motivational
        
        # Check that messages are not empty
        assert len(motivational["project_start_messages"]) > 0
        assert len(motivational["milestone_completion_rewards"]) > 0
    
    def test_task_type_determination(self, agent):
        """Test task type determination"""
        assert agent._determine_task_type("conduct market research") == "research"
        assert agent._determine_task_type("design user interface") == "design"
        assert agent._determine_task_type("develop backend api") == "development"
        assert agent._determine_task_type("test application functionality") == "testing"
        assert agent._determine_task_type("deploy to production") == "deployment"
        assert agent._determine_task_type("general task") == "general"
    
    def test_importance_explanation_generation(self, agent):
        """Test importance explanation generation"""
        research_explanation = agent._generate_importance_explanation("market research", "research")
        assert "research" in research_explanation.lower()
        assert len(research_explanation) > 50
        
        design_explanation = agent._generate_importance_explanation("ui design", "design")
        assert "design" in design_explanation.lower()
        assert len(design_explanation) > 50
    
    def test_actionable_steps_generation(self, agent):
        """Test actionable steps generation"""
        subtask = {"name": "Research competitors", "description": "Analyze competition"}
        
        research_steps = agent._generate_actionable_steps(subtask, "research")
        assert len(research_steps) >= 3
        assert any("document" in step.lower() for step in research_steps)
        
        development_steps = agent._generate_actionable_steps(subtask, "development")
        assert len(development_steps) >= 3
        assert any("code" in step.lower() for step in development_steps)
    
    def test_tools_suggestion(self, agent):
        """Test tools suggestion"""
        subtask = {"name": "Design interface", "description": "Create UI design"}
        
        design_tools = agent._suggest_tools(subtask, "design")
        assert len(design_tools) >= 2
        assert any("figma" in tool.lower() or "sketch" in tool.lower() for tool in design_tools)
        
        development_tools = agent._suggest_tools(subtask, "development")
        assert len(development_tools) >= 2
        assert any("code" in tool.lower() or "git" in tool.lower() for tool in development_tools)
    
    def test_subtask_difficulty_assessment(self, agent):
        """Test subtask difficulty assessment"""
        easy_subtask = {"name": "Create simple form", "description": "Basic form"}
        assert "Beginner" in agent._assess_subtask_difficulty(easy_subtask, "development")
        
        complex_subtask = {"name": "Implement complex algorithm", "description": "Advanced optimization"}
        assert "Advanced" in agent._assess_subtask_difficulty(complex_subtask, "development")
        
        intermediate_subtask = {"name": "Develop API endpoint", "description": "Create REST API"}
        assert "Intermediate" in agent._assess_subtask_difficulty(intermediate_subtask, "development")
    
    def test_motivational_elements_addition(self, agent, sample_complete_state):
        """Test motivational elements addition"""
        plan_data = sample_complete_state.copy()
        
        enhanced_plan = agent._add_motivational_elements(plan_data)
        
        # Check project-level motivation
        detailed_content = enhanced_plan["content_data"]["detailed_content"]
        assert "project_motivation" in detailed_content
        assert "welcome_message" in detailed_content["project_motivation"]
        
        # Check milestone-level motivation
        milestones = detailed_content["milestones"]
        for milestone in milestones:
            assert "celebration_message" in milestone
            assert "motivation_booster" in milestone
            assert "progress_indicator" in milestone
            
            # Check task-level motivation
            for task in milestone.get("tasks", []):
                assert "success_celebration" in task
                
                # Check subtask-level motivation
                for subtask in task.get("subtasks", []):
                    assert "completion_reward" in subtask
                    assert "type_encouragement" in subtask
    
    def test_actionable_steps_enhancement(self, agent, sample_complete_state):
        """Test actionable steps enhancement"""
        plan_data = sample_complete_state.copy()
        
        enhanced_plan = agent._enhance_actionable_steps(plan_data)
        
        milestones = enhanced_plan["content_data"]["detailed_content"]["milestones"]
        
        for milestone in milestones:
            for task in milestone.get("tasks", []):
                # Check task enhancements
                assert "success_tips" in task
                assert "why_important" in task
                assert len(task["success_tips"]) <= 3
                
                for subtask in task.get("subtasks", []):
                    # Check subtask enhancements
                    actionable_steps = subtask.get("actionable_steps", [])
                    assert len(actionable_steps) >= 3
                    
                    tools_needed = subtask.get("tools_needed", [])
                    assert len(tools_needed) >= 2
                    
                    assert "estimated_hours" in subtask
                    assert "difficulty_level" in subtask
    
    def test_executive_summary_generation(self, agent, sample_complete_state):
        """Test executive summary generation"""
        enhanced_plan = sample_complete_state.copy()
        
        exec_summary = agent._generate_executive_summary(enhanced_plan)
        
        # Check required fields
        required_fields = [
            "project_title", "project_overview", "key_highlights", 
            "success_factors", "timeline_overview", "resource_requirements"
        ]
        
        for field in required_fields:
            assert field in exec_summary, f"Missing field: {field}"
        
        # Check content quality
        assert len(exec_summary["project_title"]) > 10
        assert len(exec_summary["project_overview"]) > 100
        assert len(exec_summary["key_highlights"]) >= 3
        assert len(exec_summary["success_factors"]) >= 3
        
        # Check timeline overview
        timeline_overview = exec_summary["timeline_overview"]
        assert "start_date" in timeline_overview
        assert "end_date" in timeline_overview
        assert "milestone_breakdown" in timeline_overview
    
    def test_quality_metrics_calculation(self, agent, sample_complete_state):
        """Test quality metrics calculation"""
        plan_data = sample_complete_state.copy()
        validation_results = plan_data["validation_results"]
        
        # Add some enhancements to test counting
        milestones = plan_data["content_data"]["detailed_content"]["milestones"]
        milestones[0]["celebration_message"] = "🎉 Great work!"
        milestones[0]["tasks"][0]["success_tips"] = ["Tip 1", "Tip 2"]
        
        quality_metrics = agent._calculate_final_quality_metrics(plan_data, validation_results)
        
        # Check required fields
        required_fields = [
            "final_quality_score", "base_validation_score", "enhancement_bonus",
            "improvements_applied", "motivational_elements_added", "actionable_steps_enhanced"
        ]
        
        for field in required_fields:
            assert field in quality_metrics, f"Missing field: {field}"
        
        # Check score validity
        final_score = quality_metrics["final_quality_score"]
        assert isinstance(final_score, (int, float))
        assert 0 <= final_score <= 1
        
        # Check that final score is higher than base score
        base_score = quality_metrics["base_validation_score"]
        assert final_score >= base_score
    
    @pytest.mark.asyncio
    async def test_process_method(self, agent, sample_complete_state):
        """Test the main process method"""
        state = create_initial_state("Test quality enhancement")
        state.update(sample_complete_state)
        
        result = await agent.process(state)
        
        assert "enhanced_plan" in result
        assert "progress" in result
        assert result["progress"] == 100.0
        
        enhanced_plan = result["enhanced_plan"]
        
        # Validate required fields
        required_fields = [
            "executive_summary", "enhanced_content", "quality_metrics", 
            "final_validation", "enhancement_summary"
        ]
        
        for field in required_fields:
            assert field in enhanced_plan, f"Missing required field: {field}"
        
        # Validate executive summary
        exec_summary = enhanced_plan["executive_summary"]
        assert "project_title" in exec_summary
        assert "project_overview" in exec_summary
        assert "key_highlights" in exec_summary
        
        # Validate quality metrics
        quality_metrics = enhanced_plan["quality_metrics"]
        assert "final_quality_score" in quality_metrics
        assert 0 <= quality_metrics["final_quality_score"] <= 1
        
        # Validate final validation
        final_validation = enhanced_plan["final_validation"]
        assert "overall_status" in final_validation
        assert "ready_for_execution" in final_validation
        assert isinstance(final_validation["ready_for_execution"], bool)
    
    @pytest.mark.asyncio
    async def test_validation_output_validation(self, agent, sample_complete_state):
        """Test output validation"""
        state = create_initial_state("Test output validation")
        state.update(sample_complete_state)
        
        result = await agent.process(state)
        
        # Test valid output
        is_valid = await agent.validate_output(result)
        assert is_valid
        
        # Test invalid output - missing field
        invalid_result = {"enhanced_plan": {"executive_summary": {}}}
        is_valid = await agent.validate_output(invalid_result)
        assert not is_valid
        
        # Test invalid quality score
        invalid_score = {
            "enhanced_plan": {
                "executive_summary": {"project_title": "Test", "project_overview": "Test", "key_highlights": []},
                "enhanced_content": {},
                "quality_metrics": {"final_quality_score": 1.5},  # Invalid score > 1
                "final_validation": {}
            }
        }
        is_valid = await agent.validate_output(invalid_score)
        assert not is_valid
    
    def test_quality_level_determination(self, agent):
        """Test quality level determination"""
        assert agent._determine_quality_level(0.96) == "Premium"
        assert agent._determine_quality_level(0.92) == "Professional"
        assert agent._determine_quality_level(0.87) == "Standard"
        assert agent._determine_quality_level(0.82) == "Basic"
        assert agent._determine_quality_level(0.75) == "Needs Improvement"
    
    def test_final_recommendations_generation(self, agent):
        """Test final recommendations generation"""
        premium_recs = agent._generate_final_recommendations(0.96)
        assert len(premium_recs) >= 2
        assert any("outstanding" in rec.lower() for rec in premium_recs)
        
        basic_recs = agent._generate_final_recommendations(0.82)
        assert len(basic_recs) >= 2
        assert any("basic" in rec.lower() for rec in basic_recs)


if __name__ == "__main__":
    # Run a simple test
    async def run_test():
        agent = QualityEnhancementAgent()
        
        # Sample state with validation results
        state = {
            "domain_analysis": {"primary_domain": "mobile_app_development"},
            "content_data": {"detailed_content": {"milestones": []}},
            "validation_results": {"overall_score": 0.85, "issues_found": []}
        }
        
        result = await agent.process(state)
        print("Quality enhancement completed!")
        print(f"Final quality score: {result['enhanced_plan']['quality_metrics']['final_quality_score']}")
        print(f"Quality level: {result['enhanced_plan']['final_validation']['quality_level']}")
    
    asyncio.run(run_test())
