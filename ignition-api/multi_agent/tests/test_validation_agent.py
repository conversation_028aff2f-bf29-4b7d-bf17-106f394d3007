"""
Test cases for Validation Agent

This module contains comprehensive tests for the ValidationAgent
to ensure it properly validates plan quality and identifies issues.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from ..agents.validation_agent import ValidationAgent
from ..services.shared_memory import PlanGenerationState, create_initial_state


class TestValidationAgent:
    """Test cases for Validation Agent"""
    
    @pytest.fixture
    def agent(self):
        """Create a Validation Agent for testing"""
        config = {
            "primary_model": "gemini-2.0-flash-exp",
            "temperature": 0.1,
            "max_tokens": 3000,
            "timeout": 60
        }
        return ValidationAgent(config)
    
    @pytest.fixture
    def sample_complete_state(self):
        """Sample complete state with all agent outputs"""
        return {
            "domain_analysis": {
                "primary_domain": "mobile_app_development",
                "complexity_level": "intermediate",
                "extracted_requirements": {
                    "functional": ["user_authentication", "product_catalog", "payment_processing"],
                    "non_functional": ["mobile_responsive", "secure_payments"],
                    "technical": ["mobile_app", "backend_api"]
                },
                "constraints": {"time": "3_months", "team_size": "small_team_3_5_people"}
            },
            "structure_design": {
                "milestone_structure": [
                    {
                        "milestone_id": "M1",
                        "name": "Research and Planning",
                        "description": "Market research and technical planning",
                        "position": 1,
                        "estimated_duration": "2_weeks",
                        "dependencies": [],
                        "critical_path": True
                    }
                ] * 5,
                "dependency_graph": {
                    "M1": [],
                    "M2": ["M1"],
                    "M3": ["M2"],
                    "M4": ["M3"],
                    "M5": ["M4"]
                },
                "optimization_score": 0.85
            },
            "content_data": {
                "detailed_content": {
                    "milestones": [
                        {
                            "milestone_id": "M1",
                            "name": "🔍 Research and Planning",
                            "description": "Comprehensive market research and technical planning phase to establish project foundation",
                            "tasks": [
                                {
                                    "task_id": "M1_T1",
                                    "name": "🎯 Conduct market research and competitor analysis",
                                    "description": "Detailed market analysis",
                                    "acceptance_criteria": "Research completed with findings documented",
                                    "subtasks": [
                                        {
                                            "subtask_id": "M1_T1_ST1",
                                            "name": "Survey competitors",
                                            "description": "Analyze top competitors",
                                            "actionable_steps": ["Step 1", "Step 2", "Step 3"],
                                            "tools_needed": ["Browser", "Spreadsheet"],
                                            "expected_outcome": "Competitor analysis"
                                        }
                                    ] * 5
                                }
                            ] * 5
                        }
                    ] * 5
                },
                "content_metrics": {
                    "clarity_score": 0.95,
                    "actionability_score": 0.88,
                    "engagement_score": 0.92,
                    "consistency_score": 0.94
                }
            },
            "timeline_data": {
                "project_start_date": "2025-08-04",
                "project_end_date": "2025-10-24",
                "total_duration_weeks": 11,
                "milestone_schedule": [
                    {
                        "milestone_id": "M1",
                        "start_date": "2025-08-04",
                        "end_date": "2025-08-18",
                        "duration_days": 10,
                        "duration_weeks": 2,
                        "resource_intensity": "medium",
                        "tasks": [
                            {
                                "task_index": 0,
                                "start_date": "2025-08-04",
                                "end_date": "2025-08-11",
                                "duration_days": 5,
                                "can_parallel": False,
                                "resource_requirements": ["senior_developer"],
                                "risk_level": "low"
                            }
                        ] * 5
                    }
                ] * 5,
                "bottlenecks": [],
                "optimization_score": 0.91
            }
        }
    
    def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent.agent_name == "validation_agent"
        assert agent.auto_fix_enabled == True
        assert len(agent.validation_rules) > 0
        assert len(agent.quality_thresholds) > 0
        assert len(agent.validation_weights) == 4
    
    def test_validation_rules_loading(self, agent):
        """Test validation rules loading"""
        rules = agent.validation_rules
        
        # Check main rule categories
        assert "structure_rules" in rules
        assert "content_rules" in rules
        assert "timeline_rules" in rules
        assert "consistency_rules" in rules
        assert "feasibility_rules" in rules
        
        # Check specific rules
        structure_rules = rules["structure_rules"]
        assert "milestone_count" in structure_rules
        assert structure_rules["milestone_count"]["optimal"] == 5
        
        content_rules = rules["content_rules"]
        assert "acceptance_criteria_presence" in content_rules
        assert content_rules["acceptance_criteria_presence"]["required"] == True
    
    def test_structure_completeness_validation(self, agent, sample_complete_state):
        """Test structure completeness validation"""
        plan_data = sample_complete_state
        
        issues, score = agent._validate_structure_completeness(plan_data)
        
        # Should have no major issues with complete data
        assert score >= 0.8
        assert len([i for i in issues if i.get("severity") == "high"]) == 0
        
        # Test with insufficient milestones
        incomplete_data = plan_data.copy()
        incomplete_data["content_data"]["detailed_content"]["milestones"] = [
            incomplete_data["content_data"]["detailed_content"]["milestones"][0]
        ]  # Only 1 milestone
        
        issues, score = agent._validate_structure_completeness(incomplete_data)
        
        # Should detect insufficient milestones
        assert score < 0.8
        insufficient_milestone_issue = next(
            (i for i in issues if i.get("type") == "insufficient_milestones"), None
        )
        assert insufficient_milestone_issue is not None
        assert insufficient_milestone_issue["severity"] == "high"
    
    def test_content_completeness_validation(self, agent, sample_complete_state):
        """Test content completeness validation"""
        plan_data = sample_complete_state
        
        issues, score = agent._validate_content_completeness(plan_data)
        
        # Should have good score with complete data
        assert score >= 0.8
        
        # Test with missing acceptance criteria
        incomplete_data = plan_data.copy()
        milestone = incomplete_data["content_data"]["detailed_content"]["milestones"][0]
        task = milestone["tasks"][0]
        del task["acceptance_criteria"]
        
        issues, score = agent._validate_content_completeness(incomplete_data)
        
        # Should detect missing acceptance criteria
        missing_criteria_issue = next(
            (i for i in issues if i.get("type") == "missing_acceptance_criteria"), None
        )
        assert missing_criteria_issue is not None
        assert missing_criteria_issue["severity"] == "high"
    
    def test_naming_consistency_validation(self, agent, sample_complete_state):
        """Test naming consistency validation"""
        plan_data = sample_complete_state
        
        issues, score = agent._validate_naming_consistency(plan_data)
        
        # Should have good consistency with sample data
        assert score >= 0.8
        
        # Test with inconsistent emoji usage
        inconsistent_data = plan_data.copy()
        milestones = inconsistent_data["content_data"]["detailed_content"]["milestones"]
        milestones[0]["name"] = "🔍 Research and Planning"  # Has emoji
        milestones[1]["name"] = "Development Phase"  # No emoji
        
        issues, score = agent._validate_naming_consistency(inconsistent_data)
        
        # Should detect inconsistent emoji usage
        emoji_issue = next(
            (i for i in issues if i.get("type") == "inconsistent_emoji_usage"), None
        )
        assert emoji_issue is not None
        assert emoji_issue["auto_fixable"] == True
    
    def test_data_consistency_validation(self, agent, sample_complete_state):
        """Test data consistency validation"""
        plan_data = sample_complete_state
        
        issues, score = agent._validate_data_consistency(plan_data)
        
        # Should have good consistency
        assert score >= 0.8
        
        # Test with milestone count mismatch
        inconsistent_data = plan_data.copy()
        # Remove one milestone from timeline but keep in content
        inconsistent_data["timeline_data"]["milestone_schedule"] = (
            inconsistent_data["timeline_data"]["milestone_schedule"][:4]
        )
        
        issues, score = agent._validate_data_consistency(inconsistent_data)
        
        # Should detect milestone count mismatch
        mismatch_issue = next(
            (i for i in issues if i.get("type") == "milestone_count_mismatch"), None
        )
        assert mismatch_issue is not None
        assert mismatch_issue["severity"] == "high"
    
    def test_timeline_feasibility_validation(self, agent, sample_complete_state):
        """Test timeline feasibility validation"""
        plan_data = sample_complete_state
        
        issues, score = agent._validate_timeline_feasibility(plan_data)
        
        # Should be feasible with reasonable timeline
        assert score >= 0.7
        
        # Test with too aggressive timeline
        aggressive_data = plan_data.copy()
        aggressive_data["timeline_data"]["total_duration_weeks"] = 2  # Too short
        
        issues, score = agent._validate_timeline_feasibility(aggressive_data)
        
        # Should detect aggressive timeline
        aggressive_issue = next(
            (i for i in issues if i.get("type") == "timeline_too_aggressive"), None
        )
        assert aggressive_issue is not None
        assert aggressive_issue["severity"] == "high"
    
    def test_auto_fix_functionality(self, agent, sample_complete_state):
        """Test auto-fix functionality"""
        plan_data = sample_complete_state.copy()
        
        # Create issues that can be auto-fixed
        issues = [
            {
                "issue_id": "TEST_001",
                "type": "missing_acceptance_criteria",
                "auto_fixable": True,
                "location": "milestone_M1.task_1"
            },
            {
                "issue_id": "TEST_002",
                "type": "inconsistent_emoji_usage",
                "auto_fixable": True,
                "location": "milestone_names"
            }
        ]
        
        fix_summary = agent._apply_auto_fixes(plan_data, issues)
        
        assert fix_summary["fixes_attempted"] == 2
        assert fix_summary["fixes_applied"] >= 0  # Some fixes may succeed
        assert "success_rate" in fix_summary
    
    @pytest.mark.asyncio
    async def test_process_method(self, agent, sample_complete_state):
        """Test the main process method"""
        state = create_initial_state("Test validation")
        state.update(sample_complete_state)
        
        result = await agent.process(state)
        
        assert "validation_results" in result
        assert "progress" in result
        assert result["progress"] == 83.33
        
        validation_results = result["validation_results"]
        
        # Validate required fields
        required_fields = [
            "overall_score", "dimension_scores", "issues_found",
            "quality_gates", "auto_fix_summary", "validation_summary"
        ]
        
        for field in required_fields:
            assert field in validation_results, f"Missing required field: {field}"
        
        # Validate dimension scores
        dimension_scores = validation_results["dimension_scores"]
        expected_dimensions = [
            "completeness_score", "consistency_score", 
            "feasibility_score", "requirement_alignment"
        ]
        
        for dimension in expected_dimensions:
            assert dimension in dimension_scores
            assert 0 <= dimension_scores[dimension] <= 1
        
        # Validate overall score
        overall_score = validation_results["overall_score"]
        assert isinstance(overall_score, (int, float))
        assert 0 <= overall_score <= 1
        
        # Validate quality gates
        quality_gates = validation_results["quality_gates"]
        expected_gates = ["structure_quality", "content_quality", "timeline_quality", "overall_quality"]
        
        for gate in expected_gates:
            assert gate in quality_gates
            assert quality_gates[gate] in ["pass", "pass_with_warnings", "fail"]
    
    @pytest.mark.asyncio
    async def test_validation_with_issues(self, agent):
        """Test validation with problematic data"""
        # Create state with various issues
        problematic_state = {
            "domain_analysis": {
                "primary_domain": "mobile_app_development",
                "complexity_level": "intermediate"
                # Missing extracted_requirements and constraints
            },
            "content_data": {
                "detailed_content": {
                    "milestones": [
                        {
                            "milestone_id": "M1",
                            "name": "Research",  # Too short, no emoji
                            "description": "Short desc",  # Too short
                            "tasks": [
                                {
                                    "task_id": "M1_T1",
                                    "name": "Do research",  # Poor naming
                                    # Missing acceptance_criteria
                                    "subtasks": [
                                        {
                                            "subtask_id": "M1_T1_ST1",
                                            "name": "Research",
                                            "actionable_steps": ["Do it"],  # Insufficient steps
                                            "tools_needed": ["Computer"]  # Insufficient tools
                                        }
                                    ]  # Only 1 subtask, need 5
                                }
                            ]  # Only 1 task, need 5
                        }
                    ]  # Only 1 milestone, need 5
                }
                # Missing content_metrics
            },
            "timeline_data": {
                "total_duration_weeks": 1,  # Too aggressive
                "project_start_date": "invalid-date",  # Invalid format
                "milestone_schedule": []  # Empty
            }
        }
        
        state = create_initial_state("Test problematic validation")
        state.update(problematic_state)
        
        result = await agent.process(state)
        validation_results = result["validation_results"]
        
        # Should detect many issues
        assert len(validation_results["issues_found"]) > 5
        assert validation_results["overall_score"] < 0.7
        assert validation_results["validation_summary"]["critical_issues"] > 0
        
        # Quality gates should fail or have warnings
        quality_gates = validation_results["quality_gates"]
        failing_gates = [gate for gate, status in quality_gates.items() if status == "fail"]
        assert len(failing_gates) > 0
    
    @pytest.mark.asyncio
    async def test_validation_output_validation(self, agent, sample_complete_state):
        """Test output validation"""
        state = create_initial_state("Test output validation")
        state.update(sample_complete_state)
        
        result = await agent.process(state)
        
        # Test valid output
        is_valid = await agent.validate_output(result)
        assert is_valid
        
        # Test invalid output - missing field
        invalid_result = {"validation_results": {"overall_score": 0.8}}
        is_valid = await agent.validate_output(invalid_result)
        assert not is_valid
        
        # Test invalid score
        invalid_score = {
            "validation_results": {
                "overall_score": 1.5,  # Invalid score > 1
                "dimension_scores": {},
                "issues_found": [],
                "quality_gates": {},
                "auto_fix_summary": {}
            }
        }
        is_valid = await agent.validate_output(invalid_score)
        assert not is_valid


if __name__ == "__main__":
    # Run a simple test
    async def run_test():
        agent = ValidationAgent()
        
        # Sample complete state
        state = {
            "domain_analysis": {"primary_domain": "mobile_app_development"},
            "content_data": {"detailed_content": {"milestones": []}},
            "timeline_data": {"total_duration_weeks": 10}
        }
        
        result = await agent.process(state)
        print("Validation completed!")
        print(f"Overall score: {result['validation_results']['overall_score']}")
        print(f"Issues found: {len(result['validation_results']['issues_found'])}")
    
    asyncio.run(run_test())
