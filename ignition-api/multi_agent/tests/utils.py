"""
Test Utilities for Multi-Agent System

This module provides utility functions and fixtures for testing
the multi-agent plan generation system.
"""

import uuid
from typing import Dict, Any
from datetime import datetime

from ..services.shared_memory import PlanGenerationState, create_initial_state


def create_test_state(user_input: str = "Test project", 
                     duration: str = "3 months",
                     language: str = "vietnamese") -> PlanGenerationState:
    """
    Create a test state for unit testing.
    
    Args:
        user_input: Test user input
        duration: Test duration
        language: Test language
        
    Returns:
        PlanGenerationState for testing
    """
    return create_initial_state(user_input, duration, language)


def mock_ai_response(response_type: str) -> Dict[str, Any]:
    """
    Create mock AI responses for testing.
    
    Args:
        response_type: Type of response ('domain', 'structure', 'content', etc.)
        
    Returns:
        Mock response data
    """
    mock_responses = {
        'domain': {
            "primary_domain": "test_domain",
            "sub_domains": ["test_sub1", "test_sub2"],
            "complexity_level": "intermediate",
            "confidence_score": 0.9,
            "extracted_requirements": {
                "functional": ["test_req1", "test_req2"],
                "non_functional": ["test_nf1", "test_nf2"],
                "technical": ["test_tech1", "test_tech2"]
            }
        },
        'structure': {
            "milestone_structure": [
                {
                    "milestone_id": "M1",
                    "name": "Test Milestone 1",
                    "position": 1,
                    "estimated_duration": "2_weeks",
                    "task_count": 4
                }
            ],
            "optimization_score": 0.85
        },
        'content': {
            "detailed_content": {
                "milestones": [
                    {
                        "milestone_id": "M1",
                        "name": "Test Milestone with Detailed Content",
                        "description": "This is a test milestone description with sufficient detail for testing purposes.",
                        "tasks": []
                    }
                ]
            },
            "content_metrics": {
                "clarity_score": 0.9,
                "actionability_score": 0.85
            }
        },
        'timeline': {
            "timeline_optimization": {
                "total_duration": "8_weeks",
                "start_date": "2025-02-01",
                "end_date": "2025-03-29",
                "milestones": []
            }
        },
        'validation': {
            "validation_results": {
                "overall_score": 0.9,
                "issues_found": [],
                "quality_gates": {
                    "structure_quality": "pass",
                    "content_quality": "pass",
                    "timeline_quality": "pass"
                }
            }
        },
        'final': {
            "enhanced_plan": {
                "plan_metadata": {
                    "title": "Test Enhanced Plan",
                    "quality_score": 0.95
                },
                "milestones": []
            },
            "final_metrics": {
                "overall_quality": 0.95,
                "readability_score": 0.9
            }
        }
    }
    
    return mock_responses.get(response_type, {})


def assert_state_valid(state: PlanGenerationState) -> bool:
    """
    Assert that a state object is valid.
    
    Args:
        state: State to validate
        
    Returns:
        True if valid, raises AssertionError if not
    """
    # Check required fields
    required_fields = ['user_input', 'session_id', 'progress', 'current_step']
    for field in required_fields:
        assert field in state, f"Missing required field: {field}"
    
    # Check data types
    assert isinstance(state['progress'], (int, float)), "Progress must be numeric"
    assert 0.0 <= state['progress'] <= 1.0, "Progress must be between 0 and 1"
    assert isinstance(state['messages'], list), "Messages must be a list"
    assert isinstance(state['errors'], list), "Errors must be a list"
    
    return True


def create_test_config() -> Dict[str, Any]:
    """Create test configuration."""
    return {
        'timeout': 10,
        'max_retries': 1,
        'debug': True,
        'mock_ai': True
    }


class MockAgent:
    """Mock agent for testing."""
    
    def __init__(self, name: str, response_data: Dict[str, Any] = None):
        self.name = name
        self.response_data = response_data or {}
        self.call_count = 0
        self.last_input = None
    
    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """Mock process method."""
        self.call_count += 1
        self.last_input = state
        return self.response_data
    
    def reset(self):
        """Reset mock state."""
        self.call_count = 0
        self.last_input = None


def create_sample_plan_data() -> Dict[str, Any]:
    """Create sample plan data for testing."""
    return {
        "plan_metadata": {
            "title": "Sample E-Commerce App Development Plan",
            "created_at": datetime.now().isoformat(),
            "quality_score": 0.92
        },
        "milestones": [
            {
                "milestone_id": "M1",
                "name": "Project Foundation and Market Research",
                "description": "Establish project foundation through comprehensive market research and technical planning.",
                "position": 1,
                "estimated_duration": "3_weeks",
                "tasks": [
                    {
                        "task_id": "T1_1",
                        "name": "Conduct comprehensive market analysis for e-commerce applications",
                        "description": "Research target market, analyze competitors, and identify opportunities.",
                        "estimated_duration": "1_week",
                        "subtasks": [
                            {
                                "subtask_id": "ST1_1_1",
                                "name": "Research top 10 e-commerce competitors",
                                "description": "Analyze features, pricing, and user experience",
                                "estimated_hours": 16
                            }
                        ]
                    }
                ]
            }
        ]
    }
