"""
Test cases for Timeline Optimization Agent

This module contains comprehensive tests for the TimelineOptimizationAgent
to ensure it properly calculates realistic timelines and detects bottlenecks.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from datetime import datetime, date
from ..agents.timeline_optimizer import TimelineOptimizationAgent
from ..services.shared_memory import PlanGenerationState, create_initial_state


class TestTimelineOptimizationAgent:
    """Test cases for Timeline Optimization Agent"""
    
    @pytest.fixture
    def agent(self):
        """Create a Timeline Optimization Agent for testing"""
        config = {
            "primary_model": "gemini-2.0-flash-exp",
            "temperature": 0.1,
            "max_tokens": 2500,
            "timeout": 45
        }
        return TimelineOptimizationAgent(config)
    
    @pytest.fixture
    def sample_content_data(self):
        """Sample content data from Agent 3"""
        return {
            "detailed_content": {
                "milestones": [
                    {
                        "milestone_id": "M1",
                        "name": "🔍 Research and Planning",
                        "description": "Market research and technical planning",
                        "tasks": [
                            {
                                "task_id": "M1_T1",
                                "name": "Market research and competitor analysis",
                                "estimated_duration": "1_week",
                                "complexity": "medium",
                                "required_skills": ["business_analysis"],
                                "dependencies": [],
                                "deliverables": ["Research report"]
                            },
                            {
                                "task_id": "M1_T2",
                                "name": "User persona development",
                                "estimated_duration": "1_week",
                                "complexity": "medium",
                                "required_skills": ["ux_design"],
                                "dependencies": ["M1_T1"],
                                "deliverables": ["User personas"]
                            }
                        ]
                    }
                ] * 5,  # 5 milestones
                "total_milestones": 5,
                "total_tasks": 10,
                "total_subtasks": 50
            },
            "content_metrics": {
                "clarity_score": 0.95,
                "actionability_score": 0.88,
                "engagement_score": 0.92,
                "consistency_score": 0.94,
                "overall_score": 0.92
            }
        }
    
    @pytest.fixture
    def sample_structure_design(self):
        """Sample structure design from Agent 2"""
        return {
            "milestone_structure": [
                {
                    "milestone_id": "M1",
                    "name": "Research and Planning",
                    "description": "Market research and technical planning",
                    "position": 1,
                    "estimated_duration": "2_weeks",
                    "dependencies": [],
                    "critical_path": True,
                    "task_count": 5,
                    "complexity_weight": 0.7
                }
            ] * 5,
            "dependency_graph": {
                "M1": [],
                "M2": ["M1"],
                "M3": ["M2"],
                "M4": ["M3"],
                "M5": ["M4"]
            },
            "critical_path_analysis": {
                "critical_milestones": ["M1", "M2", "M3", "M4", "M5"],
                "critical_path_duration": "10_weeks"
            },
            "optimization_score": 0.85
        }
    
    @pytest.fixture
    def sample_domain_analysis(self):
        """Sample domain analysis from Agent 1"""
        return {
            "primary_domain": "mobile_app_development",
            "sub_domains": ["e_commerce", "fashion_retail"],
            "complexity_level": "intermediate",
            "extracted_requirements": {
                "functional": ["user_authentication", "product_catalog", "payment_processing"],
                "non_functional": ["mobile_responsive", "secure_payments"],
                "technical": ["mobile_app", "backend_api"]
            },
            "constraints": {
                "time": "3_months",
                "team_size": "small_team_3_5_people"
            }
        }
    
    @pytest.fixture
    def sample_state(self, sample_domain_analysis, sample_structure_design, sample_content_data):
        """Sample state with all required data"""
        state = create_initial_state("Tôi muốn tạo app e-commerce bán quần áo")
        state["domain_analysis"] = sample_domain_analysis
        state["structure_design"] = sample_structure_design
        state["content_data"] = sample_content_data
        return state
    
    def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent.agent_name == "timeline_optimizer"
        assert agent.working_days_per_week == 5
        assert agent.hours_per_working_day == 8
        assert agent.buffer_percentage == 0.15
        assert len(agent.optimization_weights) == 4
    
    def test_duration_parsing(self, agent):
        """Test duration string parsing"""
        # Test valid duration strings
        assert agent._parse_duration("2_weeks") == (2, "weeks")
        assert agent._parse_duration("5_days") == (5, "days")
        assert agent._parse_duration("1_months") == (1, "months")
        
        # Test invalid duration strings
        assert agent._parse_duration("invalid") == (1, "weeks")
        assert agent._parse_duration("") == (1, "weeks")
    
    def test_duration_conversion(self, agent):
        """Test duration conversion to days"""
        assert agent._convert_to_days(1, "days") == 1
        assert agent._convert_to_days(1, "weeks") == 5
        assert agent._convert_to_days(1, "months") == 20
        assert agent._convert_to_days(2, "weeks") == 10
    
    def test_effort_hours_calculation(self, agent):
        """Test effort hours calculation"""
        assert agent._calculate_effort_hours(1, "days") == 8
        assert agent._calculate_effort_hours(1, "weeks") == 40
        assert agent._calculate_effort_hours(2, "weeks") == 80
    
    def test_task_duration_calculation(self, agent, sample_domain_analysis):
        """Test individual task duration calculation"""
        task = {
            "task_id": "M1_T1",
            "name": "Market research",
            "estimated_duration": "1_week",
            "complexity": "medium"
        }
        
        milestone = {"milestone_id": "M1", "name": "Research"}
        
        result = agent._calculate_task_duration(task, milestone, sample_domain_analysis)
        
        assert "original_duration" in result
        assert "calculated_duration" in result
        assert "duration_days" in result
        assert "effort_hours" in result
        assert "adjustments_applied" in result
        
        # Check that adjustments were applied
        adjustments = result["adjustments_applied"]
        assert adjustments["complexity_multiplier"] == 1.0  # medium complexity
        assert adjustments["domain_multiplier"] == 1.2  # mobile app development
        assert adjustments["team_multiplier"] == 1.0  # small team
        assert adjustments["buffer_percentage"] == 0.15
        
        # Duration should be adjusted upward due to mobile app domain
        assert result["duration_days"] > 5  # More than 1 week due to adjustments
    
    def test_parallel_task_identification(self, agent):
        """Test parallel task identification"""
        tasks = [
            {"name": "Market research and analysis", "task_id": "T1"},
            {"name": "Competitor research study", "task_id": "T2"},
            {"name": "UI design and wireframes", "task_id": "T3"},
            {"name": "UX design patterns", "task_id": "T4"},
            {"name": "Backend development", "task_id": "T5"}
        ]
        
        milestone = {"milestone_id": "M1"}
        
        parallel_groups = agent._identify_parallel_tasks(tasks, milestone)
        
        # Should identify research and design groups
        assert len(parallel_groups) >= 2
        
        # Check that research tasks are grouped together
        research_group = next((g for g in parallel_groups if g["group_type"] == "research"), None)
        assert research_group is not None
        assert len(research_group["tasks"]) == 2  # T1 and T2
        
        # Check that design tasks are grouped together
        design_group = next((g for g in parallel_groups if g["group_type"] == "design"), None)
        assert design_group is not None
        assert len(design_group["tasks"]) == 2  # T3 and T4
    
    def test_working_days_calculation(self, agent):
        """Test working days calculation (skipping weekends)"""
        # Test starting on Monday
        monday = date(2025, 1, 6)  # A Monday
        result = agent._add_working_days(monday, 5)
        
        # Should be the following Monday (skipping weekend)
        expected = date(2025, 1, 13)
        assert result == expected
        
        # Test starting on Friday
        friday = date(2025, 1, 10)  # A Friday
        result = agent._add_working_days(friday, 3)
        
        # Should be Wednesday of next week
        expected = date(2025, 1, 15)
        assert result == expected
    
    def test_resource_intensity_calculation(self, agent):
        """Test resource intensity calculation"""
        # Low intensity: 40 hours over 10 days = 4 hours/day
        assert agent._calculate_resource_intensity(40, 10) == "low"
        
        # Medium intensity: 80 hours over 10 days = 8 hours/day
        assert agent._calculate_resource_intensity(80, 10) == "low"
        
        # High intensity: 160 hours over 10 days = 16 hours/day
        assert agent._calculate_resource_intensity(160, 10) == "medium"
        
        # Very high intensity: 240 hours over 10 days = 24 hours/day
        assert agent._calculate_resource_intensity(240, 10) == "high"
    
    def test_bottleneck_detection(self, agent, sample_structure_design):
        """Test bottleneck detection"""
        timeline_data = {
            "milestone_schedule": [
                {
                    "milestone_id": "M1",
                    "start_date": "2025-01-06",
                    "end_date": "2025-01-20",
                    "duration_days": 10,
                    "resource_intensity": "very_high",
                    "tasks": []
                },
                {
                    "milestone_id": "M2",
                    "start_date": "2025-01-21",
                    "end_date": "2025-02-04",
                    "duration_days": 10,
                    "resource_intensity": "high",
                    "tasks": []
                }
            ]
        }
        
        bottlenecks = agent._detect_bottlenecks(timeline_data, sample_structure_design)
        
        # Should detect resource overallocation for M1
        resource_bottleneck = next((b for b in bottlenecks if b["type"] == "resource_overallocation"), None)
        assert resource_bottleneck is not None
        assert resource_bottleneck["severity"] == "high"
        
        # Should detect consecutive high intensity
        consecutive_bottleneck = next((b for b in bottlenecks if b["type"] == "consecutive_high_intensity"), None)
        assert consecutive_bottleneck is not None
        assert consecutive_bottleneck["severity"] == "medium"
    
    @pytest.mark.asyncio
    async def test_process_method(self, agent, sample_state):
        """Test the main process method"""
        result = await agent.process(sample_state)
        
        assert "timeline_data" in result
        assert "progress" in result
        assert result["progress"] == 66.67
        
        timeline_data = result["timeline_data"]
        
        # Validate required fields
        required_fields = [
            "project_start_date", "project_end_date", "total_duration_weeks",
            "milestone_schedule", "bottlenecks", "optimization_score"
        ]
        
        for field in required_fields:
            assert field in timeline_data, f"Missing required field: {field}"
        
        # Validate milestone schedule
        milestone_schedule = timeline_data["milestone_schedule"]
        assert len(milestone_schedule) == 5
        
        # Validate dates
        start_date = datetime.fromisoformat(timeline_data["project_start_date"])
        end_date = datetime.fromisoformat(timeline_data["project_end_date"])
        assert end_date > start_date
        
        # Validate optimization score
        opt_score = timeline_data["optimization_score"]
        assert isinstance(opt_score, (int, float))
        assert 0 <= opt_score <= 1
        
        # Validate additional fields
        assert "resource_recommendations" in timeline_data
        assert "risk_assessment" in timeline_data
        assert "optimization_recommendations" in timeline_data
    
    @pytest.mark.asyncio
    async def test_validation(self, agent, sample_state):
        """Test output validation"""
        result = await agent.process(sample_state)
        
        # Test valid output
        is_valid = await agent.validate_output(result)
        assert is_valid
        
        # Test invalid output - missing field
        invalid_result = {"timeline_data": {"project_start_date": "2025-01-01"}}
        is_valid = await agent.validate_output(invalid_result)
        assert not is_valid
        
        # Test invalid dates
        invalid_dates = {
            "timeline_data": {
                "project_start_date": "2025-01-01",
                "project_end_date": "2024-12-31",  # End before start
                "total_duration_weeks": 10,
                "milestone_schedule": [{}] * 5,
                "bottlenecks": [],
                "optimization_score": 0.8
            }
        }
        is_valid = await agent.validate_output(invalid_dates)
        assert not is_valid


if __name__ == "__main__":
    # Run a simple test
    async def run_test():
        agent = TimelineOptimizationAgent()
        
        # Sample data
        domain_analysis = {
            "primary_domain": "mobile_app_development",
            "complexity_level": "intermediate",
            "constraints": {"team_size": "small_team_3_5_people"}
        }
        
        content_data = {
            "detailed_content": {
                "milestones": [
                    {
                        "milestone_id": f"M{i+1}",
                        "name": f"Milestone {i+1}",
                        "tasks": [
                            {
                                "task_id": f"M{i+1}_T{j+1}",
                                "name": f"Task {j+1}",
                                "estimated_duration": "1_week",
                                "complexity": "medium"
                            } for j in range(2)
                        ]
                    } for i in range(5)
                ]
            }
        }
        
        structure_design = {
            "dependency_graph": {f"M{i+1}": [f"M{i}"] if i > 0 else [] for i in range(5)},
            "critical_path_analysis": {"critical_milestones": [f"M{i+1}" for i in range(5)]}
        }
        
        state = create_initial_state("Test app")
        state["domain_analysis"] = domain_analysis
        state["content_data"] = content_data
        state["structure_design"] = structure_design
        
        result = await agent.process(state)
        print("Timeline optimization completed!")
        print(f"Project duration: {result['timeline_data']['total_duration_weeks']} weeks")
        print(f"Optimization score: {result['timeline_data']['optimization_score']}")
    
    asyncio.run(run_test())
