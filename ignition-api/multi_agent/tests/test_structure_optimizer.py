"""
Test cases for Structure Optimization Agent

This module contains comprehensive tests for the StructureOptimizationAgent
to ensure it properly generates milestones, tasks, and optimization scores.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch
from ..agents.structure_optimizer import StructureOptimizationAgent
from ..services.shared_memory import PlanGenerationState, create_initial_state


class TestStructureOptimizationAgent:
    """Test cases for Structure Optimization Agent"""
    
    @pytest.fixture
    def agent(self):
        """Create a Structure Optimization Agent for testing"""
        config = {
            "primary_model": "gemini-2.0-flash-exp",
            "temperature": 0.2,
            "max_tokens": 2000,
            "timeout": 45
        }
        return StructureOptimizationAgent(config)
    
    @pytest.fixture
    def sample_domain_analysis(self):
        """Sample domain analysis from Agent 1"""
        return {
            "primary_domain": "mobile_app_development",
            "sub_domains": ["e_commerce", "fashion_retail", "user_experience"],
            "complexity_level": "intermediate",
            "confidence_score": 0.92,
            "extracted_requirements": {
                "functional": ["user_authentication", "product_catalog", "payment_processing"],
                "non_functional": ["mobile_responsive", "secure_payments", "scalable"],
                "technical": ["mobile_app", "backend_api", "database_design"]
            },
            "constraints": {
                "time": "3_months",
                "budget": "medium",
                "team_size": "small_team_3_5_people"
            },
            "success_metrics": ["user_adoption_rate", "conversion_rate", "app_store_rating"],
            "stakeholders": ["end_users", "business_owners", "development_team"]
        }
    
    @pytest.fixture
    def sample_state(self, sample_domain_analysis):
        """Sample state with domain analysis"""
        state = create_initial_state("Tôi muốn tạo app e-commerce bán quần áo")
        state["domain_analysis"] = sample_domain_analysis
        return state
    
    def test_agent_initialization(self, agent):
        """Test agent initialization"""
        assert agent.agent_name == "structure_optimizer"
        assert agent.target_milestones == 5
        assert agent.tasks_per_milestone == 5
        assert len(agent.milestone_templates) > 0
        assert "mobile_app_development" in agent.milestone_templates
        assert "web_development" in agent.milestone_templates
    
    def test_template_selection(self, agent, sample_domain_analysis):
        """Test template selection based on domain"""
        template = agent._select_optimal_template(sample_domain_analysis)
        
        assert "standard_flow" in template
        assert len(template["standard_flow"]) == 5
        
        # Check that complexity adjustment was applied
        for milestone in template["standard_flow"]:
            assert "typical_duration_weeks" in milestone
            assert "complexity_weight" in milestone
    
    def test_milestone_generation(self, agent, sample_domain_analysis):
        """Test milestone generation"""
        template = agent._select_optimal_template(sample_domain_analysis)
        milestones = agent._generate_milestones(sample_domain_analysis, template)
        
        assert len(milestones) == 5
        
        for i, milestone in enumerate(milestones):
            assert milestone["milestone_id"] == f"M{i+1}"
            assert milestone["position"] == i + 1
            assert "name" in milestone
            assert "description" in milestone
            assert "estimated_duration" in milestone
            assert "dependencies" in milestone
            assert "critical_path" in milestone
            assert milestone["task_count"] == 5
            assert "complexity_weight" in milestone
            assert "success_criteria" in milestone
    
    def test_task_generation(self, agent, sample_domain_analysis):
        """Test task generation for milestones"""
        template = agent._select_optimal_template(sample_domain_analysis)
        milestones = agent._generate_milestones(sample_domain_analysis, template)
        
        # Generate tasks for first milestone
        milestone = milestones[0]
        milestone_template = template["standard_flow"][0]
        tasks = agent._generate_tasks_for_milestone(milestone, milestone_template, sample_domain_analysis)
        
        assert len(tasks) == 5
        
        for i, task in enumerate(tasks):
            assert task["task_id"] == f"{milestone['milestone_id']}_T{i+1}"
            assert "name" in task
            assert "estimated_duration" in task
            assert "complexity" in task
            assert "required_skills" in task
            assert "dependencies" in task
            assert "deliverables" in task
            assert "acceptance_criteria" in task
    
    def test_dependency_analysis(self, agent, sample_domain_analysis):
        """Test dependency analysis"""
        template = agent._select_optimal_template(sample_domain_analysis)
        milestones = agent._generate_milestones(sample_domain_analysis, template)
        
        # Add tasks to milestones
        for milestone in milestones:
            milestone_template = next(
                (t for t in template["standard_flow"] if t["name"] == milestone["name"]), 
                template["standard_flow"][0]
            )
            milestone["tasks"] = agent._generate_tasks_for_milestone(
                milestone, milestone_template, sample_domain_analysis
            )
        
        dependency_analysis = agent._analyze_dependencies(milestones)
        
        assert "milestone_dependencies" in dependency_analysis
        assert "task_dependencies" in dependency_analysis
        assert "critical_path" in dependency_analysis
        assert "parallel_opportunities" in dependency_analysis
        
        # Check milestone dependencies
        milestone_deps = dependency_analysis["milestone_dependencies"]
        assert len(milestone_deps) == 5
        assert milestone_deps["M1"] == []  # First milestone has no dependencies
        assert "M1" in milestone_deps["M2"]  # Second milestone depends on first
    
    def test_optimization_scoring(self, agent, sample_domain_analysis):
        """Test optimization scoring"""
        template = agent._select_optimal_template(sample_domain_analysis)
        milestones = agent._generate_milestones(sample_domain_analysis, template)
        
        # Add tasks to milestones
        for milestone in milestones:
            milestone_template = next(
                (t for t in template["standard_flow"] if t["name"] == milestone["name"]), 
                template["standard_flow"][0]
            )
            milestone["tasks"] = agent._generate_tasks_for_milestone(
                milestone, milestone_template, sample_domain_analysis
            )
        
        dependency_analysis = agent._analyze_dependencies(milestones)
        
        structure = {
            "milestone_structure": milestones,
            "dependency_graph": dependency_analysis["milestone_dependencies"],
            "parallel_opportunities": dependency_analysis["parallel_opportunities"]
        }
        
        score = agent._calculate_optimization_score(structure, sample_domain_analysis)
        
        assert isinstance(score, float)
        assert 0.0 <= score <= 1.0
    
    @pytest.mark.asyncio
    async def test_process_method(self, agent, sample_state):
        """Test the main process method"""
        result = await agent.process(sample_state)
        
        assert "structure_design" in result
        assert "progress" in result
        assert result["progress"] == 33.33
        
        structure = result["structure_design"]
        
        # Validate structure completeness
        required_fields = [
            "milestone_structure", "dependency_graph", "critical_path_analysis",
            "optimization_score", "rationale"
        ]
        
        for field in required_fields:
            assert field in structure, f"Missing field: {field}"
        
        # Validate milestone structure
        milestones = structure["milestone_structure"]
        assert len(milestones) == 5
        
        for milestone in milestones:
            assert len(milestone["tasks"]) == 5
        
        # Validate optimization score
        assert isinstance(structure["optimization_score"], float)
        assert 0.0 <= structure["optimization_score"] <= 1.0
    
    @pytest.mark.asyncio
    async def test_validation(self, agent, sample_state):
        """Test output validation"""
        result = await agent.process(sample_state)
        
        # Test valid output
        is_valid = await agent.validate_output(result)
        assert is_valid
        
        # Test invalid output - missing field
        invalid_result = {"structure_design": {"milestone_structure": []}}
        is_valid = await agent.validate_output(invalid_result)
        assert not is_valid
        
        # Test invalid output - wrong milestone count
        invalid_result = {
            "structure_design": {
                "milestone_structure": [{"milestone_id": "M1", "tasks": []}],
                "dependency_graph": {},
                "critical_path_analysis": {},
                "optimization_score": 0.5
            }
        }
        is_valid = await agent.validate_output(invalid_result)
        assert not is_valid


if __name__ == "__main__":
    # Run a simple test
    async def run_test():
        agent = StructureOptimizationAgent()
        
        sample_domain_analysis = {
            "primary_domain": "mobile_app_development",
            "complexity_level": "intermediate",
            "extracted_requirements": {
                "functional": ["user_authentication", "product_catalog"],
                "non_functional": ["mobile_responsive", "secure_payments"],
                "technical": ["mobile_app", "backend_api"]
            },
            "constraints": {"time": "3_months"}
        }
        
        state = create_initial_state("Test app")
        state["domain_analysis"] = sample_domain_analysis
        
        result = await agent.process(state)
        print("Structure optimization completed!")
        print(f"Generated {len(result['structure_design']['milestone_structure'])} milestones")
        print(f"Optimization score: {result['structure_design']['optimization_score']}")
    
    asyncio.run(run_test())
