"""
Domain Analysis Prompts for AI Enhancement

This module contains optimized prompts for domain classification
and requirement extraction using AI models.
"""

DOMAIN_ANALYSIS_SYSTEM_PROMPT = """
You are a Senior Business Analyst and Universal Domain Expert with 15+ years of experience in analyzing and classifying technology projects across 50+ domains.

ADVANCED EXPERTISE:
- Universal Domain Classification (Technology, Business, Industry verticals)
- Cross-domain and Multi-domain project analysis
- Emerging technology trends (AI/ML, Blockchain, IoT, AR/VR)
- Industry-specific solutions (Fintech, Healthcare, Edtech, Proptech)
- Requirements engineering and technical architecture
- Technology stack recommendation with modern frameworks
- Project complexity assessment with risk analysis
- Market opportunity and competitive landscape analysis

DOMAIN EXPERTISE COVERAGE:
🔧 TECHNOLOGY: Mobile, Web, AI/ML, Blockchain, IoT, AR/VR, Game Development
💼 BUSINESS: E-commerce, Fintech, SaaS, Marketplace, Social Platforms
🏭 INDUSTRY: Healthcare, Education, Real Estate, Agriculture, Clean Energy, Media

ADVANCED MISSION:
Analyze user input and create comprehensive domain analysis including:

1. ADVANCED DOMAIN CLASSIFICATION
   - Primary domain from 50+ available domains
   - Confidence score (0.0-1.0) with detailed reasoning
   - Sub-domains and cross-domain relationships
   - Domain complexity and market maturity assessment

2. MULTI-DIMENSIONAL REQUIREMENTS ANALYSIS
   - Functional requirements (features, user stories, capabilities)
   - Non-functional requirements (performance, security, scalability, UX)
   - Technical requirements (architecture, infrastructure, integrations)
   - Business requirements (revenue model, market fit, compliance)

3. INTELLIGENT COMPLEXITY ASSESSMENT
   - Level: beginner/intermediate/advanced/expert
   - Technical complexity factors
   - Business complexity considerations
   - Integration complexity with existing systems
   - Scalability and maintenance complexity

4. COMPREHENSIVE CONSTRAINTS ANALYSIS
   - Time constraints and project timeline
   - Budget limitations and cost optimization
   - Team size, skill requirements, and hiring needs
   - Technical constraints and infrastructure limitations
   - Regulatory and compliance constraints
   - Market timing and competitive pressures

5. SUCCESS METRICS & KPI FRAMEWORK
   - Domain-specific KPIs and success metrics
   - Business metrics (revenue, growth, retention)
   - Technical metrics (performance, reliability, security)
   - User experience metrics (engagement, satisfaction)
   - Measurable outcomes with timeline expectations

6. STAKEHOLDER ECOSYSTEM MAPPING
   - Primary stakeholders (users, customers, decision makers)
   - Secondary stakeholders (partners, regulators, community)
   - Internal stakeholders (development team, management)
   - External stakeholders (investors, vendors, competitors)
   - Stakeholder interests, concerns, and success criteria

7. COMPREHENSIVE RISK ANALYSIS
   - Technical risks (architecture, scalability, security)
   - Business risks (market, competition, revenue)
   - Operational risks (team, timeline, budget)
   - External risks (regulatory, economic, technology changes)
   - Risk mitigation strategies with contingency planning

8. ADVANCED TECHNOLOGY RECOMMENDATIONS
   - Primary tech stack with modern best practices
   - Alternative technology options with trade-offs
   - Architecture patterns and design principles
   - Third-party integrations and APIs
   - Development tools and DevOps recommendations
   - Scalability and performance considerations
   - Security framework and compliance requirements

9. MARKET INTELLIGENCE & COMPETITIVE ANALYSIS
   - Market size and growth potential
   - Competitive landscape and differentiation opportunities
   - Technology trends and future-proofing considerations
   - Monetization strategies and revenue models
   - Go-to-market strategy recommendations

10. IMPLEMENTATION ROADMAP
    - Phase-based development approach
    - MVP definition and feature prioritization
    - Resource allocation recommendations
    - Timeline estimates with milestones
    - Quality assurance and testing strategy

OUTPUT FORMAT:
Comprehensive JSON with clearly defined structure, complete detailed information, actionable insights, and practical recommendations.
Always return valid JSON format, no markdown formatting.
Ensure deep, practical analysis that can be implemented.
"""

def format_domain_analysis_prompt(user_input: str, duration: str, preliminary_analysis: dict) -> str:
    """Format the user prompt for domain analysis"""
    return f"""
Preliminary analysis available:
{preliminary_analysis}

Original user input: "{user_input}"
Duration: {duration}

Please analyze deeper and improve this analysis. Pay special attention to:
1. Confirm or adjust primary_domain
2. Add important sub_domains
3. Re-evaluate complexity_level
4. Add potentially missed requirements
5. Identify hidden risk factors
6. Suggest appropriate technology stack

Return JSON format with similar structure to input but enhanced and improved.
"""

FEW_SHOT_EXAMPLES = {
    "mobile_app_ecommerce": {
        "input": "I want to create a mobile app for selling fashion clothes for young people",
        "output": {
            "primary_domain": "mobile_app_development",
            "sub_domains": ["e_commerce", "fashion_retail", "social_commerce", "user_experience"],
            "complexity_level": "intermediate",
            "confidence_score": 0.95,
            "extracted_requirements": {
                "functional": [
                    "user_management", "product_catalog", "shopping_cart",
                    "payment_processing", "order_management", "search_functionality",
                    "reviews_ratings", "social_features"
                ],
                "non_functional": [
                    "mobile_responsive", "performance", "security",
                    "usability", "scalability"
                ],
                "technical": [
                    "mobile_app", "backend_api", "database_design",
                    "third_party_integration", "cloud_hosting"
                ]
            },
            "constraints": {
                "time": "3_months",
                "budget": "medium",
                "team_size": "small_team_3_5_people",
                "technical_expertise": "intermediate"
            },
            "success_metrics": [
                "app_store_rating", "download_count", "user_retention_rate",
                "conversion_rate", "daily_active_users"
            ],
            "stakeholders": [
                "end_users", "development_team", "project_owner",
                "customers", "app_store_reviewers", "payment_providers"
            ],
            "risk_factors": [
                "payment_integration_complexity", "app_store_approval",
                "user_acquisition_challenges", "competition"
            ],
            "technology_recommendations": {
                "mobile": ["React Native", "Flutter"],
                "backend": ["Node.js", "Python Django"],
                "database": ["PostgreSQL", "MongoDB"],
                "payment": ["Stripe", "PayPal"],
                "hosting": ["AWS", "Google Cloud"]
            }
        }
    },

    "fintech_digital_banking": {
        "input": "Build a digital banking app with money transfer and investment features",
        "output": {
            "primary_domain": "fintech",
            "sub_domains": ["digital_banking", "payment_systems", "investment_tech", "mobile_app_development"],
            "complexity_level": "expert",
            "confidence_score": 0.98,
            "extracted_requirements": {
                "functional": [
                    "user_authentication", "account_management", "money_transfer",
                    "investment_portfolio", "transaction_history", "budgeting_tools",
                    "loan_applications", "card_management"
                ],
                "non_functional": [
                    "security", "compliance", "performance", "availability",
                    "data_privacy", "audit_trail"
                ],
                "technical": [
                    "mobile_app", "secure_backend", "encryption",
                    "api_integrations", "real_time_processing", "fraud_detection"
                ]
            },
            "constraints": {
                "time": "12_months",
                "budget": "high",
                "team_size": "large_team_10_plus",
                "technical_expertise": "expert",
                "regulatory_compliance": "required"
            },
            "success_metrics": [
                "user_acquisition", "transaction_volume", "security_incidents",
                "compliance_score", "customer_satisfaction"
            ],
            "stakeholders": [
                "bank_customers", "regulatory_bodies", "development_team",
                "security_team", "compliance_officers", "bank_management"
            ],
            "risk_factors": [
                "regulatory_compliance", "security_breaches", "fraud_risks",
                "integration_complexity", "market_competition"
            ],
            "technology_recommendations": {
                "mobile": ["Native iOS/Android", "React Native"],
                "backend": ["Java Spring", "Node.js"],
                "database": ["PostgreSQL", "Oracle"],
                "security": ["OAuth 2.0", "JWT", "Encryption"],
                "compliance": ["KYC/AML tools", "Audit systems"]
            }
        }
    },

    "ai_ml_computer_vision": {
        "input": "Develop an AI facial recognition system for employee attendance tracking",
        "output": {
            "primary_domain": "ai_ml_development",
            "sub_domains": ["computer_vision", "facial_recognition", "enterprise_software", "biometric_systems"],
            "complexity_level": "advanced",
            "confidence_score": 0.94,
            "extracted_requirements": {
                "functional": [
                    "face_detection", "face_recognition", "employee_database",
                    "attendance_tracking", "real_time_processing", "reporting",
                    "admin_dashboard", "mobile_app"
                ],
                "non_functional": [
                    "accuracy", "speed", "privacy", "scalability",
                    "reliability", "security"
                ],
                "technical": [
                    "computer_vision_models", "machine_learning", "camera_integration",
                    "database_design", "api_development", "cloud_deployment"
                ]
            },
            "constraints": {
                "time": "6_months",
                "budget": "high",
                "team_size": "medium_team_6_9_people",
                "technical_expertise": "advanced",
                "privacy_compliance": "required"
            },
            "success_metrics": [
                "recognition_accuracy", "processing_speed", "false_positive_rate",
                "system_uptime", "user_adoption"
            ],
            "stakeholders": [
                "employees", "hr_department", "it_department",
                "management", "privacy_officers", "development_team"
            ],
            "risk_factors": [
                "privacy_concerns", "accuracy_issues", "hardware_dependencies",
                "model_bias", "regulatory_compliance"
            ],
            "technology_recommendations": {
                "ai_ml": ["TensorFlow", "PyTorch", "OpenCV"],
                "backend": ["Python Flask/Django", "Node.js"],
                "database": ["PostgreSQL", "MongoDB"],
                "deployment": ["Docker", "Kubernetes", "AWS/GCP"],
                "hardware": ["IP cameras", "Edge devices"]
            }
        }
    },

    "blockchain_defi_platform": {
        "input": "Create a DeFi platform for cryptocurrency lending and borrowing",
        "output": {
            "primary_domain": "blockchain_development",
            "sub_domains": ["defi_protocols", "smart_contracts", "cryptocurrency", "web3_development"],
            "complexity_level": "expert",
            "confidence_score": 0.96,
            "extracted_requirements": {
                "functional": [
                    "smart_contracts", "lending_pools", "borrowing_mechanism",
                    "interest_calculation", "collateral_management", "liquidation",
                    "governance_token", "yield_farming"
                ],
                "non_functional": [
                    "security", "gas_efficiency", "scalability",
                    "decentralization", "transparency"
                ],
                "technical": [
                    "solidity_development", "web3_integration", "blockchain_deployment",
                    "frontend_dapp", "oracle_integration", "testing_framework"
                ]
            },
            "constraints": {
                "time": "9_months",
                "budget": "very_high",
                "team_size": "large_team_10_plus",
                "technical_expertise": "expert",
                "security_audit": "required"
            },
            "success_metrics": [
                "total_value_locked", "transaction_volume", "security_score",
                "gas_efficiency", "user_adoption"
            ],
            "stakeholders": [
                "defi_users", "token_holders", "liquidity_providers",
                "security_auditors", "blockchain_community", "regulators"
            ],
            "risk_factors": [
                "smart_contract_vulnerabilities", "market_volatility", "regulatory_uncertainty",
                "oracle_risks", "liquidity_risks"
            ],
            "technology_recommendations": {
                "blockchain": ["Ethereum", "Polygon", "Binance Smart Chain"],
                "smart_contracts": ["Solidity", "Hardhat", "Truffle"],
                "frontend": ["React", "Web3.js", "Ethers.js"],
                "oracles": ["Chainlink", "Band Protocol"],
                "security": ["OpenZeppelin", "Security audits"]
            }
        }
    },
    "web_platform": {
        "input": "Xây dựng platform học online với video streaming",
        "output": {
            "primary_domain": "web_development",
            "sub_domains": ["education_technology", "video_streaming", "user_management", "content_management"],
            "complexity_level": "advanced",
            "confidence_score": 0.92,
            "extracted_requirements": {
                "functional": [
                    "user_management", "content_management", "video_streaming",
                    "payment_processing", "progress_tracking", "assessments"
                ],
                "non_functional": [
                    "performance", "scalability", "security", "reliability"
                ],
                "technical": [
                    "web_application", "video_streaming", "backend_api",
                    "database_design", "cloud_hosting"
                ]
            },
            "constraints": {
                "time": "6_months",
                "budget": "high",
                "team_size": "large_team",
                "technical_expertise": "advanced"
            },
            "success_metrics": [
                "user_engagement_time", "course_completion_rate",
                "user_satisfaction_score", "platform_uptime"
            ],
            "stakeholders": [
                "students", "instructors", "administrators",
                "content_creators", "development_team"
            ]
        }
    },
    "data_analytics": {
        "input": "Phân tích dữ liệu bán hàng để dự đoán xu hướng",
        "output": {
            "primary_domain": "data_science",
            "sub_domains": ["business_intelligence", "predictive_analytics", "data_visualization"],
            "complexity_level": "advanced",
            "confidence_score": 0.88,
            "extracted_requirements": {
                "functional": [
                    "data_ingestion", "data_processing", "predictive_modeling",
                    "data_visualization", "reporting_analytics"
                ],
                "non_functional": [
                    "performance", "scalability", "accuracy", "reliability"
                ],
                "technical": [
                    "data_pipeline", "machine_learning", "database_design",
                    "visualization_tools", "cloud_hosting"
                ]
            }
        }
    }
}
