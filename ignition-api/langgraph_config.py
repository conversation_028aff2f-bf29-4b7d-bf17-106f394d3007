"""
LangGraph Configuration Settings for Multi-Agent System
"""
import os
from typing import Dict, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class LangGraphConfig:
    """Configuration class for LangGraph multi-agent system"""
    
    # Debug and Tracing
    DEBUG = os.getenv('LANGGRAPH_DEBUG', 'false').lower() == 'true'
    TRACING_V2 = os.getenv('LANGGRAPH_TRACING_V2', 'false').lower() == 'true'
    API_URL = os.getenv('LANGGRAPH_API_URL', 'https://api.langgraph.com')
    
    # Multi-Agent System Settings
    ENABLED = os.getenv('MULTI_AGENT_ENABLED', 'true').lower() == 'true'
    MAX_RETRIES = int(os.getenv('MULTI_AGENT_MAX_RETRIES', '3'))
    TIMEOUT_SECONDS = int(os.getenv('MULTI_AGENT_TIMEOUT_SECONDS', '300'))
    PARALLEL_EXECUTION = os.getenv('MULTI_AGENT_PARALLEL_EXECUTION', 'true').lower() == 'true'
    
    # Agent Configuration
    AGENT_CONFIG = {
        'domain_classifier': {
            'name': 'Domain Classification Agent',
            'timeout': 60,
            'max_retries': 2,
            'confidence_threshold': 0.8
        },
        'structure_optimizer': {
            'name': 'Structure Optimization Agent', 
            'timeout': 90,
            'max_retries': 2,
            'max_milestones': 7,
            'max_tasks_per_milestone': 7
        },
        'content_generator': {
            'name': 'Content Generation Agent',
            'timeout': 120,
            'max_retries': 2,
            'min_name_words': 7,
            'max_name_words': 15,
            'min_description_words': 30,
            'max_description_words': 60
        },
        'timeline_optimizer': {
            'name': 'Timeline Optimization Agent',
            'timeout': 90,
            'max_retries': 2,
            'buffer_percentage': 0.15
        },
        'validation_agent': {
            'name': 'Validation Agent',
            'timeout': 60,
            'max_retries': 1,
            'min_quality_score': 0.8
        },
        'quality_enhancer': {
            'name': 'Quality Enhancement Agent',
            'timeout': 120,
            'max_retries': 2,
            'target_quality_score': 0.9
        }
    }
    
    # Workflow Configuration
    WORKFLOW_CONFIG = {
        'max_concurrent_agents': 2,
        'state_persistence': True,
        'progress_tracking': True,
        'error_recovery': True,
        'checkpoint_interval': 30  # seconds
    }
    
    @classmethod
    def get_agent_config(cls, agent_name: str) -> Dict[str, Any]:
        """Get configuration for specific agent"""
        return cls.AGENT_CONFIG.get(agent_name, {})
    
    @classmethod
    def get_workflow_config(cls) -> Dict[str, Any]:
        """Get workflow configuration"""
        return cls.WORKFLOW_CONFIG
    
    @classmethod
    def is_enabled(cls) -> bool:
        """Check if multi-agent system is enabled"""
        return cls.ENABLED
    
    @classmethod
    def get_debug_settings(cls) -> Dict[str, Any]:
        """Get debug and tracing settings"""
        return {
            'debug': cls.DEBUG,
            'tracing_v2': cls.TRACING_V2,
            'api_url': cls.API_URL
        }

# Global configuration instance
config = LangGraphConfig()
