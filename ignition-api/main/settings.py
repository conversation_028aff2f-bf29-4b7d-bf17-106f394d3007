from datetime import timedelta
from pathlib import Path
import os
from django.contrib.messages import constants as messages

from dotenv import load_dotenv
load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-s0&1fmwjyo!u-7pvc0l)2$v#vbb)a6&^d)3q97%h&7)my7ttq%'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

CORS_ORIGIN_ALLOW_ALL = False
CORS_ORIGIN_WHITELIST = os.getenv('CORS_ORIGIN_WHITELIST', '127.0.0.1').split(',')
ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost,127.0.0.1').split(',')

# Application definition
INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.templatetags.static',
    'assistant',
    'users',
    'plans',
    'skills',
    'comments',
    'healthcheck',
    'memos',
    'tasks',
    'notifications',
    'rest_framework',
    'rest_framework.authtoken',
    'django_seed',
    'corsheaders',
    'drf_yasg',
    'django_celery_results',
    'django_celery_beat',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    # 'django.contrib.auth.middleware.SessionAuthenticationMiddleware'
]

ROOT_URLCONF = 'main.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 15,
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ],
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=1440),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
}

WSGI_APPLICATION = 'main.wsgi.application'
AUTH_USER_MODEL = 'users.User'

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

# Database configuration - Auto-select based on environment variables
# If MySQL settings are provided in .env, use MySQL; otherwise default to SQLite
if os.getenv('DB_NAME') and os.getenv('DB_HOST'):
    # MySQL configuration when environment variables are set
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': os.getenv('DB_NAME'),
            'USER': os.getenv('DB_USER', 'root'),
            'PASSWORD': os.getenv('DB_PASSWORD', ''),
            'HOST': os.getenv('DB_HOST'),
            'PORT': os.getenv('DB_PORT', '3306'),
            'OPTIONS': {
                'charset': 'utf8mb4',
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            },
        }
    }
    print("Using MySQL database configuration")
else:
    # SQLite configuration (default fallback)
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }
    print("Using SQLite database configuration (default)")

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = 'en'

TIME_ZONE = 'Asia/Bangkok'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, "static")

STATICFILES_DIRS = []

# Base url to serve media files
MEDIA_URL = '/media/'

# Path where media is stored
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

...
# Google OAuth2 settings
BASE_FRONTEND_URL = os.environ.get('DJANGO_BASE_FRONTEND_URL')
BASE_FRONTEND_URL = os.environ.get('DJANGO_BASE_FRONTEND_URL', default='http://localhost:3000')
GOOGLE_OAUTH2_CLIENT_ID = os.environ.get('GOOGLE_OAUTH2_CLIENT_ID')
GOOGLE_OAUTH2_CLIENT_SECRET = os.environ.get('GOOGLE_OAUTH2_CLIENT_SECRET')

# OPEN API CONFIGURATION
SWAGGER_SETTINGS = {
    'USE_SESSION_AUTH': False,
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header'
        }
    },
}

# SETUP EMAIL
# Mailjet credentials (switching from SendGrid to Mailjet)
MAILJET_API_KEY = os.getenv('MAILJET_API_KEY') or os.getenv('MAIL_JET_API_KEY')
MAILJET_SECRET_KEY = os.getenv('MAILJET_SECRET_KEY') or os.getenv('MAIL_JET_API_SECRET')
FROM_EMAIL = os.getenv('FROM_EMAIL')
FROM_NAME = os.getenv('FROM_NAME')

# Email backend configuration with fallback options
USE_CONSOLE_EMAIL = os.getenv('USE_CONSOLE_EMAIL', 'False').lower() == 'true'
USE_RESEND = os.getenv('USE_RESEND', 'False').lower() == 'true'

if USE_CONSOLE_EMAIL:
    # Console backend for testing - prints emails to console
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
elif USE_RESEND:
    # Resend.com has better deliverability
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = 'smtp.resend.com'
    EMAIL_HOST_USER = 'resend'
    EMAIL_HOST_PASSWORD = os.getenv('RESEND_API_KEY')
    EMAIL_PORT = 587
    EMAIL_USE_TLS = True
    EMAIL_TIMEOUT = 30
else:
    # Mailjet SMTP (current setup with SPF/DKIM issues)
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = 'in-v3.mailjet.com'
    EMAIL_HOST_USER = MAILJET_API_KEY
    EMAIL_HOST_PASSWORD = MAILJET_SECRET_KEY
    EMAIL_PORT = 587
    EMAIL_USE_TLS = True
    EMAIL_TIMEOUT = 30  # Add timeout to prevent hanging

# Default from email (optional, fall back to FROM_EMAIL if provided)
DEFAULT_FROM_EMAIL = FROM_EMAIL if FROM_EMAIL else None

# SETUP AWS SNS SMS
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_DEFAULT_REGION = os.getenv('AWS_DEFAULT_REGION', 'us-east-1')


#CELERY SETUP
CELERY_BROKER_URL = 'redis://localhost:6379/0'
CELERY_RESULT_BACKEND = 'redis://localhost:6379/0'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE  # Use the same timezone as Django

# Celery Beat Schedule for periodic tasks
from celery.schedules import crontab

CELERY_BEAT_SCHEDULE = {
    # Morning task reminders at 8:00 AM daily
    'daily-morning-task-reminders': {
        'task': 'notifications.tasks.send_daily_task_reminders_morning',
        'schedule': crontab(hour=8, minute=0),  # 8:00 AM every day
        'options': {
            'expires': 3600,  # Task expires after 1 hour if not executed
        }
    },
    # Evening task reminders at 8:00 PM daily
    'daily-evening-task-reminders': {
        'task': 'notifications.tasks.send_daily_task_reminders_evening',
        'schedule': crontab(hour=20, minute=0),  # 8:00 PM every day
        'options': {
            'expires': 3600,  # Task expires after 1 hour if not executed
        }
    },
}
