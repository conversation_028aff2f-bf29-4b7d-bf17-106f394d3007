# =============================================================================
# IGNITION API - ENVIRONMENT CONFIGURATION TEMPLATE
# =============================================================================
# Copy this file to .env and fill in your actual values
# DO NOT commit .env file to version control - it contains sensitive data
# =============================================================================

# =============================================================================
# DJANGO CORE CONFIGURATION
# =============================================================================

# Django Secret Key - Generate a new one for production
# Use: python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"
SECRET_KEY=your-secret-key-here-generate-a-new-one-for-production

# Database Configuration
# For local development (MySQL)
DB_HOST=localhost
DB_NAME=ignition_dev
DB_USER=root
DB_PASSWORD=your-database-password
DB_PORT=3306

# For production database (uncomment and configure)
# DB_HOST=your-production-db-host
# DB_USER=your-production-db-user
# DB_PASSWORD=your-production-db-password
# DB_PORT=3306
# DB_NAME=ignition_production

# Allowed Hosts (comma-separated, no spaces)
ALLOWED_HOSTS=127.0.0.1,localhost,localhost:8000,your-domain.com

# CORS Origins (comma-separated, no spaces)
CORS_ORIGIN_WHITELIST=http://localhost:3000,http://127.0.0.1:3000,https://your-frontend-domain.com

# Frontend and Backend URLs
FRONTEND_BASE_URL=http://localhost:3000
URL_FRONTEND=http://localhost:3000
URL_BACKEND=http://127.0.0.1:8000

# =============================================================================
# GOOGLE OAUTH2 CONFIGURATION
# =============================================================================
# Get credentials from: https://console.developers.google.com/

GOOGLE_OAUTH2_CLIENT_ID=your-google-oauth2-client-id
GOOGLE_OAUTH2_CLIENT_SECRET=your-google-oauth2-client-secret

# =============================================================================
# EMAIL CONFIGURATION (SENDGRID)
# =============================================================================
# Get API key from: https://app.sendgrid.com/settings/api_keys

SENDGRID_FROM_NAME=Ignition App
FROM_EMAIL=<EMAIL>
SENDGRID_API_KEY=your-sendgrid-api-key
MAIL_JET_API_KEY=your-mailjet-api-key
MAIL_JET_API_SECRET=your-mailjet-api-secret

# =============================================================================
# SMS SERVICE CONFIGURATION
# =============================================================================
# Options: "aws_sns", "vonage", "disabled"
SMS_PROVIDER=aws_sns

# AWS SNS SMS settings (when SMS_PROVIDER=aws_sns)
# Get credentials from: https://console.aws.amazon.com/iam/
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_DEFAULT_REGION=ap-southeast-1

# Vonage SMS settings (when SMS_PROVIDER=vonage)
# Get €2 free trial at https://www.vonage.com/communications-apis/
VONAGE_API_KEY=your-vonage-api-key
VONAGE_API_SECRET=your-vonage-api-secret
VONAGE_FROM_NUMBER=Ignition

# =============================================================================
# AI PROVIDERS CONFIGURATION
# =============================================================================

# Primary AI Provider Selection
# Options: "openai", "openrouter", "google"
AI_PROVIDER=openrouter

# OpenAI Configuration
# Get API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key
OPENAI_DEFAULT_MODEL=gpt-4o-mini
ASSISTANT_ID=your-openai-assistant-id

# OpenRouter Configuration (Alternative to OpenAI)
# Get API key from: https://openrouter.ai/keys
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_DEFAULT_MODEL=moonshotai/kimi-dev-72b:free
OPENROUTER_APP_NAME=Ignition
OPENROUTER_SITE_URL=https://your-domain.com

# Available OpenRouter Models (uncomment to use):
# OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-r1-0528:free
# OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-r1:free
# OPENROUTER_DEFAULT_MODEL=qwen/qwen3-235b-a22b:free
# OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-r1-0528-qwen3-8b:free
# OPENROUTER_DEFAULT_MODEL=deepseek/deepseek-chat-v3-0324:free
# OPENROUTER_DEFAULT_MODEL=meta-llama/llama-4-maverick:free
# OPENROUTER_DEFAULT_MODEL=anthropic/claude-3.5-sonnet

# Google Generative AI Configuration
# Get API key from: https://makersuite.google.com/app/apikey
GOOGLE_AI_API_KEY=your-google-ai-api-key
GOOGLE_AI_API_KEY_1=your-google-ai-api-key-1
GOOGLE_AI_API_KEY_2=your-google-ai-api-key-2
GOOGLE_AI_API_KEY_3=your-google-ai-api-key-3
GOOGLE_AI_API_KEY_4=your-google-ai-api-key-4
GOOGLE_AI_API_KEY_5=your-google-ai-api-key-5
GOOGLE_AI_API_KEY_6=your-google-ai-api-key-6
GOOGLE_AI_API_KEY_7=your-google-ai-api-key-7
GOOGLE_AI_DEFAULT_MODEL=gemini-2.0-flash

# Plan Options Service Configuration
PLAN_OPTIONS_AI_PROVIDER=google
PLAN_OPTIONS_AI_MODEL=gemini-2.0-flash
PLAN_OPTIONS_CACHE_TIMEOUT=3600
PLAN_OPTIONS_FALLBACK_CACHE_TIMEOUT=900

# Gemini Pro 2.5 for create-plan-from-option API
USE_GEMINI_PRO_FOR_PLAN_CREATION=true
GOOGLE_AI_PLAN_CREATION_MODEL=gemini-2.5-pro

# =============================================================================
# LANGGRAPH MULTI-AGENT SYSTEM CONFIGURATION
# =============================================================================

# LangGraph Core Settings
LANGGRAPH_DEBUG=true
LANGGRAPH_TRACING_V2=true
LANGGRAPH_API_URL=https://api.langgraph.com

# Optional: LangGraph API Key (for advanced features)
# LANGGRAPH_API_KEY=your-langgraph-api-key

# Multi-Agent Workflow Configuration
MULTI_AGENT_ENABLED=true
MULTI_AGENT_MAX_RETRIES=3
MULTI_AGENT_TIMEOUT_SECONDS=300
MULTI_AGENT_PARALLEL_EXECUTION=true

# Agent-Specific Configuration
DOMAIN_AGENT_TIMEOUT=60
DOMAIN_AGENT_MAX_RETRIES=2
DOMAIN_AGENT_CONFIDENCE_THRESHOLD=0.8

STRUCTURE_AGENT_TIMEOUT=90
STRUCTURE_AGENT_MAX_RETRIES=2
STRUCTURE_AGENT_MAX_MILESTONES=7
STRUCTURE_AGENT_MAX_TASKS_PER_MILESTONE=7

CONTENT_AGENT_TIMEOUT=120
CONTENT_AGENT_MAX_RETRIES=2
CONTENT_AGENT_MIN_NAME_WORDS=7
CONTENT_AGENT_MAX_NAME_WORDS=15
CONTENT_AGENT_MIN_DESCRIPTION_WORDS=30
CONTENT_AGENT_MAX_DESCRIPTION_WORDS=60

TIMELINE_AGENT_TIMEOUT=90
TIMELINE_AGENT_MAX_RETRIES=2
TIMELINE_AGENT_BUFFER_PERCENTAGE=0.15

VALIDATION_AGENT_TIMEOUT=60
VALIDATION_AGENT_MAX_RETRIES=1
VALIDATION_AGENT_MIN_QUALITY_SCORE=0.8

QUALITY_AGENT_TIMEOUT=120
QUALITY_AGENT_MAX_RETRIES=2
QUALITY_AGENT_TARGET_QUALITY_SCORE=0.9

# Workflow Performance Settings
WORKFLOW_MAX_CONCURRENT_AGENTS=2
WORKFLOW_STATE_PERSISTENCE=true
WORKFLOW_PROGRESS_TRACKING=true
WORKFLOW_ERROR_RECOVERY=true
WORKFLOW_CHECKPOINT_INTERVAL=30

# Quality Gates Configuration
QUALITY_GATES_ENABLED=true
QUALITY_GATES_MIN_SCORE=0.8
QUALITY_GATES_AUTO_FIX=true

# Progress Tracking Configuration
PROGRESS_TRACKING_ENABLED=true
PROGRESS_TRACKING_CALLBACKS=true
PROGRESS_TRACKING_METRICS=true

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================

# Django Debug Mode (NEVER set to true in production)
DEBUG=true

# Logging Configuration
LOG_LEVEL=INFO
MULTI_AGENT_LOG_LEVEL=DEBUG

# Performance Monitoring
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_AGENT_METRICS=true

# =============================================================================
# PRODUCTION SETTINGS (Uncomment for production)
# =============================================================================

# Security Settings
# SECURE_HSTS_SECONDS=31536000
# SECURE_SSL_REDIRECT=true
# SESSION_COOKIE_SECURE=true
# CSRF_COOKIE_SECURE=true

# Database Connection Pooling
# DB_CONN_MAX_AGE=600
# DB_CONN_HEALTH_CHECKS=true

# Caching (Redis)
# REDIS_URL=redis://localhost:6379/0
# CACHE_TTL=3600

# Celery Configuration (for background tasks)
# CELERY_BROKER_URL=redis://localhost:6379/0
# CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# OPTIONAL INTEGRATIONS
# =============================================================================

# Sentry Error Tracking (for production)
# SENTRY_DSN=your-sentry-dsn

# Analytics
# GOOGLE_ANALYTICS_ID=your-google-analytics-id

# File Storage (AWS S3)
# AWS_STORAGE_BUCKET_NAME=your-s3-bucket
# AWS_S3_REGION_NAME=ap-southeast-1
# USE_S3_STORAGE=false