from django.db import models
from users.models import User


class Skill(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    user = models.ForeignKey(User, related_name='skills', on_delete=models.SET_NULL, null=True)

    class Meta:
        db_table = 'skills'
        # Ensure a user can't have duplicate skills, but different users can have the same skill
        unique_together = ['name', 'user']
