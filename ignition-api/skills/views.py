from django.shortcuts import render
from skills.models import Skill
from skills.serializers import SkillSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework.response import Response
from starlette import status
from drf_yasg.utils import swagger_auto_schema
from django.shortcuts import get_object_or_404
from plans.models import Task
from django.db.models import Q


class UserSkillsView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        skills = Skill.objects.filter(user=user)
        serializer = SkillSerializer(skills, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class AddSkillForUserView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=SkillSerializer, responses={201: 'Skill created successfully'})
    def post(self, request):
        serializer = SkillSerializer(data=request.data)
        if serializer.is_valid():
            name = serializer.validated_data['name']
            description = serializer.validated_data.get('description', '')
            skill = Skill.objects.create(
                name=name,
                description=description,
                user=request.user
            )
            return Response(SkillSerializer(skill).data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CommonSkillsView(APIView):
    """Get common skills suggestions with search functionality"""
    permission_classes = [IsAuthenticated]

    # List of 50 common skills
    COMMON_SKILLS = [
        'Python', 'JavaScript', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'Swift',
        'React', 'Angular', 'Vue.js', 'Node.js', 'Django', 'Flask', 'Spring', 'Laravel', 'Express.js', 'Next.js',
        'HTML', 'CSS', 'SASS', 'Bootstrap', 'Tailwind CSS', 'jQuery', 'TypeScript', 'GraphQL', 'REST API', 'SQL',
        'MySQL', 'PostgreSQL', 'MongoDB', 'Redis', 'Docker', 'Kubernetes', 'AWS', 'Azure', 'Google Cloud', 'Git',
        'Project Management', 'Agile', 'Scrum', 'Leadership', 'Communication', 'Problem Solving', 'Critical Thinking', 'Teamwork', 'Time Management', 'Creativity'
    ]

    def get(self, request):
        search_query = request.GET.get('search', '').strip()

        if search_query:
            # Filter common skills based on search query
            filtered_skills = [
                skill for skill in self.COMMON_SKILLS
                if search_query.lower() in skill.lower()
            ]
        else:
            filtered_skills = self.COMMON_SKILLS

        # Return skills as list of dictionaries for consistency
        skills_data = [{'name': skill} for skill in filtered_skills]

        return Response(skills_data, status=status.HTTP_200_OK)


class deleteSkillsView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, id):
        skill = get_object_or_404(Skill, pk=id)
        self.check_object_permissions(request, skill)
        skill.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
