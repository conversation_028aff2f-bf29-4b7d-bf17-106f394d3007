import os
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.utils.encoding import force_bytes
from django.core.mail import send_mail
from django.contrib.auth import authenticate
from utils.email import send_email_with_name
from django.db import transaction, IntegrityError
from django.db.models import Q
from django.shortcuts import get_object_or_404
from django.utils.encoding import force_bytes
from django.contrib.auth.tokens import default_token_generator
from rest_framework import status
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, BasePermission
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
import urllib.parse
from users.models import User
from users.mixins import PublicApiMixin, ApiErrorsMixin
from users.utils import google_get_access_token, google_get_user_info, generate_tokens_for_user
from users.serializers import UserSerializer, UserLoginSerializer, CreateAccountSerializer, \
    ActivateUserSerializer, PasswordResetRequestSerializer, UpdateProfileSerializer, \
    ProfileSerializer, PasswordResetSerializer, GoogleInputSerializer, UserConnectSerializer, CheckInvitationSerializer
from users.models import BlackListedToken, User
from plans.models import Plan, Invitation
from notifications.serializers import NotificationSerializer
from notifications.models import Notification
from drf_yasg.utils import swagger_auto_schema
from dotenv import load_dotenv
from django.core import signing
from django.core.signing import BadSignature, SignatureExpired
load_dotenv()


class UserLoginView(APIView):
    @swagger_auto_schema(request_body=UserLoginSerializer, responses={200: 'Login successful'})
    def post(self, request):
        serializer = UserLoginSerializer(data=request.data)
        if serializer.is_valid():
            user = authenticate(
                request,
                username=serializer.validated_data['email'],
                password=serializer.validated_data['password']
            )
            if user:
                refresh = TokenObtainPairSerializer.get_token(user)
                data = {
                    'refresh_token': str(refresh),
                    'access_token': str(refresh.access_token)
                }
                return Response(data, status=status.HTTP_200_OK)

            return Response({
                'error_message': 'Email or password is incorrect!',
                'error_code': 400
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)


class IsTokenValid(BasePermission):
    def has_permission(self, request, view):
        user_id = request.user.id
        is_allowed_user = True
        auth_header = request.META.get('HTTP_AUTHORIZATION')

        if auth_header and auth_header.startswith('Bearer '):
            access_token = auth_header.split(' ')[1]
            try:
                is_blacklisted = BlackListedToken.objects.get(
                    user=user_id, token=access_token)
                if is_blacklisted:
                    is_allowed_user = False
            except BlackListedToken.DoesNotExist:
                is_allowed_user = True

        return is_allowed_user


class CreateUserView(APIView):

    @swagger_auto_schema(request_body=CreateAccountSerializer, responses={201: 'add user done'})
    def post(self, request, format=None):
        try:
            with transaction.atomic():
                serializer = CreateAccountSerializer(data=request.data)
                if serializer.is_valid():
                    email = serializer.validated_data['email']
                    serializer.validated_data['is_active'] = False
                    user = serializer.save()

                    token = default_token_generator.make_token(user)
                    uid = urlsafe_base64_encode(force_bytes(user.pk))
                    url_frontend = os.getenv('URL_FRONTEND')
                    activation_url = (
                        f'{url_frontend}/activate/{uid}/{token}?email={urllib.parse.quote(email, safe="")}')

                    subject = 'Welcome to Ignition - Activate Your Account'
                    lines = (
                        'Dear {first_name},\n\n'
                        'Welcome to Ignition! Thank you for creating your account.\n\n'
                        'To get started, please activate your account by clicking the link below:\n\n'
                        '{url}\n\n'
                        'This link will expire in 24 hours for security reasons.\n\n'
                        'If you did not create this account, please ignore this email.\n\n'
                        'Need help? Contact our support <NAME_EMAIL>\n\n'
                        'Best regards,\n'
                        'The Ignition Team\n'
                        'https://ignitionai.site'
                    )
                    message = lines.format(
                        first_name=user.first_name or 'User',
                        url=activation_url
                    )
                    recipient_list = [email]

                    try:
                        send_email_with_name(subject, message, recipient_list, fail_silently=False)
                    except Exception as e:
                        transaction.set_rollback(True)
                        return Response({
                            'message': f'Failure sendmail: {str(e)}',
                            'status': status.HTTP_500_INTERNAL_SERVER_ERROR
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                    return Response({
                        'message': 'đã tạo xong tài khoản và gửi mail xác thực',
                        'status': status.HTTP_201_CREATED,
                    }, status=status.HTTP_201_CREATED)

                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError:
            return Response({
                'message': 'Email address already exists. Try again.',
                'status': status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = User.objects.get(id=request.user.id)
        refresh_token = request.data.get('refresh_token')
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if refresh_token and auth_header and auth_header.startswith('Bearer '):
            try:
                access_token = auth_header.split(' ')[1]
                BlackListedToken.objects.create(user=user, token=access_token)
                token = RefreshToken(refresh_token)
                token.blacklist()
                return Response({'message': 'Logged out successfully'}, status=status.HTTP_200_OK)
            except Exception as e:
                print(e)
                return Response({'message': 'Error revoking token'}, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({'message': 'Refresh token is required'}, status=status.HTTP_400_BAD_REQUEST)


class ActivateUserView(APIView):

    @swagger_auto_schema(request_body=ActivateUserSerializer, responses={201: 'add user done'})
    def post(self, request, format=None):
        serializer = ActivateUserSerializer(data=request.data)
        if serializer.is_valid():
            uid = serializer.validated_data['uid']
            token = serializer.validated_data['token']

            try:
                user_id_bytes = urlsafe_base64_decode(uid)
                user_id_str = user_id_bytes.decode('utf-8')
                user = User.objects.get(pk=user_id_str)

                if default_token_generator.check_token(user, token):
                    user.is_active = True
                    user.save()

                    return Response({
                        'message': f'Welcome {user.first_name}! Your account has been activated successfully. You can now log in.',
                        'status': status.HTTP_201_CREATED,
                        'user_email': user.email,
                        'user_name': f'{user.first_name} {user.last_name}'
                    }, status=status.HTTP_201_CREATED)
                else:
                    return Response({
                        'message': 'Activation link is invalid or has expired. Please request a new activation email.',
                        'status': status.HTTP_400_BAD_REQUEST
                    }, status=status.HTTP_400_BAD_REQUEST)
            except User.DoesNotExist:
                return Response({
                    'message': 'User not found. The activation link may be invalid.',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                print(f"Activation error: {str(e)}")
                return Response({
                    'message': 'Activation failed due to a technical error. Please try again or contact support.',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({
                'message': 'Invalid activation data provided.',
                'errors': serializer.errors,
                'status': status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class GoogleLoginApi(PublicApiMixin, ApiErrorsMixin, APIView):
    def get(self, request, *args, **kwargs):
        input_serializer = GoogleInputSerializer(data=request.GET)
        input_serializer.is_valid(raise_exception=True)
        validated_data = input_serializer.validated_data
        code = validated_data.get('code')

        frontendUrl = os.environ.get('FRONTEND_BASE_URL')
        redirect_uri = f'{frontendUrl}/google'
        access_token = google_get_access_token(code=code, redirect_uri=redirect_uri)

        user_data = google_get_user_info(access_token=access_token)
        try:
            user = User.objects.get(email=user_data['email'])
            access_token, refresh_token = generate_tokens_for_user(user)
            response_data = {
                'user': UserSerializer(user).data,
                'access_token': str(access_token),
                'refresh_token': str(refresh_token)
            }
            return Response(response_data)
        except User.DoesNotExist:
            first_name = user_data.get('given_name', '')
            last_name = user_data.get('family_name', '')

            user = User.objects.create(
                email=user_data['email'],
                first_name=first_name,
                last_name=last_name,
                description='Register by google',
                is_active=True
            )
            access_token, refresh_token = generate_tokens_for_user(user)
            response_data = {
                'user': UserSerializer(user).data,
                'access_token': str(access_token),
                'refresh_token': str(refresh_token)
            }

            return Response(response_data)


class PasswordResetRequestView(APIView):
    @swagger_auto_schema(request_body=PasswordResetRequestSerializer, responses={201: 'Email success'})
    def post(self, request):
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            try:
                user = User.objects.get(email=email)
            except User.DoesNotExist:
                return Response({
                    'message': 'Account with this email does not exist.',
                    'status': status.HTTP_404_NOT_FOUND
                }, status=status.HTTP_404_NOT_FOUND)

            if not user.is_active:
                return Response({
                    'message': 'Account is not yet activated.',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)

            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.id))

            url_frontend = os.getenv('URL_FRONTEND')
            activation_url = f'{url_frontend}/reset/{uid}/{token}'
            subject = 'Reset Your Password'
            lines = (
                'Dear User,\n\n'
                'We received a request to reset your password. Please click the link below to reset your password:\n\n'
                'Reset Password Link: {url}\n\n'
                'If you did not request a password reset, please ignore this email or contact support if you have questions.\n\n'
                'Best regards,\n'
                'The Ignition Team'
            )
            message = lines.format(url=activation_url)
            recipient_list = [email]

            try:
                send_email_with_name(subject, message, recipient_list, fail_silently=False)
            except Exception as e:
                return Response({
                    'message': 'Email failed',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)
            return Response({
                'message': 'Email success',
                'status': status.HTTP_201_CREATED
            }, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class UpdateUserPreferencesView(APIView):
    """Update user preferences like sequential task completion and platform discovery"""
    permission_classes = [IsAuthenticated]

    def patch(self, request):
        user = request.user

        # Update sequential task completion preference
        if 'sequential_task_completion' in request.data:
            user.sequential_task_completion = request.data['sequential_task_completion']

        # Update platform discovery preference
        if 'discoverable_on_platform' in request.data:
            user.discoverable_on_platform = request.data['discoverable_on_platform']

        user.save()

        serializer = UserSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)


class UserProfileView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    serializer_class = ProfileSerializer

    def get(self, request):
        user = request.user
        serializer = ProfileSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)


class OtherUserProfileView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]

    def get(self, request, email):
        user = get_object_or_404(User, email=email)
        serializer = ProfileSerializer(user)
        return Response(serializer.data, status=status.HTTP_200_OK)


class UserUpdateProfileView(APIView):
    permission_classes = [IsTokenValid, IsAuthenticated]
    swagger_auto_schema(request_body=UserSerializer, responses={201: 'Skill created successfully'})
    def put(self, request):
        user = request.user
        serializer = UpdateProfileSerializer(data=request.data)
        if serializer.is_valid():
            user.first_name = serializer.validated_data.get('first_name')
            user.last_name = serializer.validated_data.get('last_name')
            user.description = serializer.validated_data.get('description')
            user.address = serializer.validated_data.get('address')
            user.occupation = serializer.validated_data.get('occupation')
            user.phone_number = serializer.validated_data.get('phone_number')
            user.country_code = serializer.validated_data.get('country_code')

            # Update notification preferences if provided
            if 'email_notifications_enabled' in serializer.validated_data:
                user.email_notifications_enabled = serializer.validated_data.get('email_notifications_enabled')
            if 'sms_notifications_enabled' in serializer.validated_data:
                user.sms_notifications_enabled = serializer.validated_data.get('sms_notifications_enabled')

            if 'avatar' in request.FILES:
                user.avatar = request.FILES['avatar']

            user.save()
            return Response(status=status.HTTP_201_CREATED)
        else:
            return Response({
                "message": serializer.errors,
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetView(APIView):

    @swagger_auto_schema(request_body=PasswordResetSerializer, responses={201: 'Email success'})
    def post(self, request, uid, token):
        serializer = PasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            user_id = (urlsafe_base64_decode(uid))
            user = get_object_or_404(User, pk=user_id)
            if default_token_generator.check_token(user, token):
                password = serializer.validated_data['password']
                user.set_password(password)
                user.save()
                return Response({
                    'message': 'Password reset successfully',
                    'status': status.HTTP_201_CREATED
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({
                    'message': 'Token is invalid. Plz try again!'
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response({
                'message': serializer.errors,
                'status': status.HTTP_400_BAD_REQUEST},
                status=status.HTTP_400_BAD_REQUEST)


class UsersInRelatedPlansView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        paginator = PageNumberPagination()
        paginator.page_size = 12

        user = request.user
        user_email = user.email
        keyword = request.query_params.get('keyword', '')
        skills = request.query_params.get('skills', '')

        user_plans = Plan.objects.filter(user=user)
        invited_plans = Plan.objects.filter(invitations__email=user_email, invitations__accepted=True).distinct()
        combined_plans = list(user_plans) + list(invited_plans)
        invited_emails = Invitation.objects.filter(plan__in=combined_plans).values_list('email', flat=True).distinct()

        invited_users = User.objects.filter(email__in=invited_emails).exclude(id=user.id)
        invited_users = invited_users.distinct()

        if keyword:
            invited_users = invited_users.filter(
                Q(first_name__icontains=keyword) | Q(last_name__icontains=keyword))

        if skills:
            invited_users = invited_users.filter(skills__name__icontains=skills)

        paginated_users = paginator.paginate_queryset(invited_users, request)
        serializer = UserConnectSerializer(paginated_users, many=True)
        return paginator.get_paginated_response(serializer.data)


class NotificationPagination(PageNumberPagination):
    page_size = 10


class UserNotificationView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        notifications = Notification.objects.filter(user=user).order_by('-created_at')

        paginator = NotificationPagination()
        paginated_notifications = paginator.paginate_queryset(notifications, request)

        serializer = NotificationSerializer(paginated_notifications, many=True)

        return paginator.get_paginated_response(serializer.data)


class MarkNotificationAsReadView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, noti_id):
        user = request.user
        notification_id = noti_id
        notification = get_object_or_404(Notification, id=notification_id, user=user)
        notification.is_read = True
        notification.save()
        return Response("This notification has been read", status=status.HTTP_200_OK)


class MarkAllNotificationsAsReadView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request):
        user = request.user
        Notification.objects.filter(user=user).update(is_read=True)
        return Response("All notifications have been read", status=status.HTTP_200_OK)


class DeleteNotificationView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, noti_id):
        user = request.user
        notification_id = noti_id
        notification = get_object_or_404(Notification, id=notification_id, user=user)
        notification.delete()
        return Response("This notification has been deleted", status=status.HTTP_200_OK)


class CreateInvitedUserView(APIView):

    @swagger_auto_schema(request_body=CreateAccountSerializer, responses={201: 'add user done'})
    def post(self, request, format=None):
        try:
            with transaction.atomic():
                serializer = CreateAccountSerializer(data=request.data)
                if serializer.is_valid():
                    email = serializer.validated_data['email']
                    serializer.validated_data['is_active'] = True
                    user = serializer.save()

                    subject = 'Register Information'
                    message = (
                        'Dear User,\n\n'
                        'You have successfully created an Ignition account through an invitation'
                        'Best regards,\n'
                        'The Ignition Team'
                    )
                    recipient_list = [email]

                    try:
                        send_email_with_name(subject, message, recipient_list, fail_silently=False)
                    except Exception as e:
                        transaction.set_rollback(True)
                        return Response({
                            'message': f'Failure sendmail: {str(e)}',
                            'status': status.HTTP_500_INTERNAL_SERVER_ERROR
                        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    refresh = TokenObtainPairSerializer.get_token(user)
                    data = {
                        'refresh_token': str(refresh),
                        'access_token': str(refresh.access_token)
                    }
                    return Response(data, status=status.HTTP_200_OK)
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        except IntegrityError:
            return Response({
                'message': 'Email address already exists. Try again.',
                'status': status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

# 2.1. TODO for Minh
# Write API to check signId here
# Optional class, post method, no permission needed
# Process: get the value of 2 data sent up which are email and signedId
# Start
# Step 1: signedId is an id of invitation that is encoded to avoid information leakage, so it needs to be decoded and then searched by model
# Step 2: use the following code to detect invitation_id

class ResendActivationEmailView(APIView):
    """
    Resend activation email for users who haven't activated their account yet
    """

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['email'],
            properties={
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='User email address'),
            },
        ),
        responses={
            200: openapi.Response('Activation email sent successfully'),
            400: openapi.Response('Bad request'),
            404: openapi.Response('User not found'),
            429: openapi.Response('Too many requests')
        }
    )
    def post(self, request):
        email = request.data.get('email')

        if not email:
            return Response({
                'message': 'Email address is required',
                'status': status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)

            # Check if user is already activated
            if user.is_active:
                return Response({
                    'message': 'Your account is already activated. You can log in now.',
                    'status': status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)

            # Generate new activation token
            token = default_token_generator.make_token(user)
            uid = urlsafe_base64_encode(force_bytes(user.pk))
            url_frontend = os.getenv('URL_FRONTEND')
            activation_url = (
                f'{url_frontend}/activate/{uid}/{token}?email={urllib.parse.quote(email, safe="")}')

            # Send activation email
            subject = 'Activate Your Ignition Account'
            message = (
                f'Dear {user.first_name},\n\n'
                'Thank you for registering with Ignition! Please click the link below to activate your account:\n\n'
                f'{activation_url}\n\n'
                'If you did not create this account, please ignore this email.\n\n'
                'Best regards,\n'
                'The Ignition Team'
            )
            recipient_list = [email]

            try:
                send_email_with_name(subject, message, recipient_list, fail_silently=False)

                return Response({
                    'message': f'Activation email has been resent to {email}. Please check your inbox.',
                    'status': status.HTTP_200_OK
                }, status=status.HTTP_200_OK)

            except Exception as e:
                print(f"Failed to send activation email: {str(e)}")
                return Response({
                    'message': 'Failed to send activation email. Please try again later.',
                    'status': status.HTTP_500_INTERNAL_SERVER_ERROR
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except User.DoesNotExist:
            return Response({
                'message': 'No account found with this email address.',
                'status': status.HTTP_404_NOT_FOUND
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            print(f"Error in resend activation: {str(e)}")
            return Response({
                'message': 'An error occurred. Please try again later.',
                'status': status.HTTP_500_INTERNAL_SERVER_ERROR
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckInvitationSignedIdView(APIView):
    def post(self, request):
        serializer = CheckInvitationSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            signed_id = serializer.validated_data['signed_id']
        try:
            invitation_id = signing.loads(signed_id, max_age=3600)
        except SignatureExpired:
            return Response({"message": "The invitation link has expired."}, status=status.HTTP_400_BAD_REQUEST)
        except BadSignature:
            return Response({"message": "Invalid invitation link."}, status=status.HTTP_400_BAD_REQUEST)
        invitation = get_object_or_404(Invitation, id=invitation_id)
        return Response({"is_valid": invitation.email==email}, status=status.HTTP_200_OK)

