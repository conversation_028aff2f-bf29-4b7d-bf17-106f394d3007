import uuid
from django.core.management.base import BaseCommand
from users.models import User, BlackListedToken
from faker import Faker
import random

class Command(BaseCommand):
    help = 'Seed dữ liệu cho model BlackListedToken'

    def add_arguments(self, parser):
        parser.add_argument('--tokens', type=int, default=5, help='Số lượng token cần tạo')
        parser.add_argument('--clear', action='store_true', help='Xóa tất cả token hiện có')

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Đang xóa tất cả blacklisted token hiện có...')
            BlackListedToken.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Đã xóa tất cả blacklisted token!'))

        number_of_tokens = options['tokens']
        self.stdout.write(f'Đang tạo {number_of_tokens} blacklisted token mẫu...')

        # Kiểm tra xem có users nào không
        users = list(User.objects.all())
        if not users:
            self.stdout.write(self.style.ERROR('Không có user nào trong cơ sở dữ liệu!'))
            self.stdout.write('Vui lòng chạy lệnh "python manage.py seed_users" trước.')
            return

        fake = Faker()
        
        for i in range(number_of_tokens):
            # Tạo token giả mạo
            token = f"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.{uuid.uuid4().hex}.{uuid.uuid4().hex[:10]}"
            
            # Chọn ngẫu nhiên một user
            user = random.choice(users)
            
            # Tạo và lưu token
            try:
                blacklisted_token = BlackListedToken(
                    token=token,
                    user=user
                )
                blacklisted_token.save()
                self.stdout.write(f'Đã tạo blacklisted token cho user: {user.email}')
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Lỗi khi tạo blacklisted token: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS(f'Đã tạo thành công {number_of_tokens} blacklisted token!')) 