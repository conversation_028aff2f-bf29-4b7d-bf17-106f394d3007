"""
Notification utility functions that respect user preferences.
Handles email and SMS notifications based on user settings.
"""
import logging
from typing import Optional, Dict, Any
from users.models import User
from utils.email import send_email_with_name, send_sms

logger = logging.getLogger(__name__)


def should_send_email_notification(user: User, notification_type: str = 'general') -> bool:
    """
    Check if email notification should be sent to user based on their preferences.
    
    Args:
        user: User object
        notification_type: Type of notification ('account_creation', 'password_reset', 'general')
    
    Returns:
        bool: True if email should be sent, False otherwise
    """
    # Always send emails for critical notifications (exempt scenarios)
    exempt_types = ['account_creation', 'password_reset', 'account_activation', 'plan_invitation', 'plan_access_granted']
    if notification_type in exempt_types:
        logger.info(f"Email notification exempt for type '{notification_type}' - sending regardless of user preference")
        return True
    
    # Check user preference for other notifications
    if hasattr(user, 'email_notifications_enabled'):
        enabled = user.email_notifications_enabled
        logger.info(f"Email notification preference for user {user.email}: {enabled}")
        return enabled
    
    # Default to True if preference not set (backward compatibility)
    logger.info(f"Email notification preference not set for user {user.email} - defaulting to True")
    return True


def should_send_sms_notification(user: User, notification_type: str = 'general') -> bool:
    """
    Check if SMS notification should be sent to user based on their preferences.
    
    Args:
        user: User object
        notification_type: Type of notification ('account_creation', 'password_reset', 'general')
    
    Returns:
        bool: True if SMS should be sent, False otherwise
    """
    # Always send SMS for account creation and password reset (exempt scenarios)
    exempt_types = ['account_creation', 'password_reset', 'account_activation']
    if notification_type in exempt_types:
        logger.info(f"SMS notification exempt for type '{notification_type}' - sending regardless of user preference")
        return True
    
    # Check if user has phone number
    if not user.phone_number:
        logger.info(f"SMS notification skipped for user {user.email} - no phone number")
        return False
    
    # Check user preference for other notifications
    if hasattr(user, 'sms_notifications_enabled'):
        enabled = user.sms_notifications_enabled
        logger.info(f"SMS notification preference for user {user.email}: {enabled}")
        return enabled
    
    # Default to False if preference not set (SMS is opt-in)
    logger.info(f"SMS notification preference not set for user {user.email} - defaulting to False")
    return False


def send_notification_email(user: User, subject: str, message: str, 
                          notification_type: str = 'general', 
                          fail_silently: bool = False) -> Dict[str, Any]:
    """
    Send email notification respecting user preferences.
    
    Args:
        user: User object
        subject: Email subject
        message: Email message body
        notification_type: Type of notification
        fail_silently: Whether to fail silently on errors
    
    Returns:
        dict: Result dictionary with success status
    """
    try:
        if not should_send_email_notification(user, notification_type):
            logger.info(f"Email notification skipped for user {user.email} - disabled by user preference")
            return {
                "success": False, 
                "skipped": True, 
                "reason": "Email notifications disabled by user",
                "user": user.email
            }
        
        logger.info(f"Sending email notification to {user.email} (type: {notification_type})")
        result = send_email_with_name(subject, message, [user.email], fail_silently)
        
        return {
            "success": True,
            "emails_sent": result,
            "user": user.email,
            "notification_type": notification_type
        }
        
    except Exception as e:
        logger.error(f"Email notification failed for user {user.email}: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "user": user.email,
            "notification_type": notification_type
        }


def send_notification_sms(user: User, message: str, 
                         notification_type: str = 'general',
                         fail_silently: bool = True) -> Dict[str, Any]:
    """
    Send SMS notification respecting user preferences.
    
    Args:
        user: User object
        message: SMS message content
        notification_type: Type of notification
        fail_silently: Whether to fail silently on errors
    
    Returns:
        dict: Result dictionary with success status
    """
    try:
        if not should_send_sms_notification(user, notification_type):
            reason = "SMS notifications disabled by user" if user.phone_number else "No phone number available"
            logger.info(f"SMS notification skipped for user {user.email} - {reason}")
            return {
                "success": False,
                "skipped": True,
                "reason": reason,
                "user": user.email
            }
        
        logger.info(f"Sending SMS notification to {user.email} (type: {notification_type})")
        result = send_sms(user.phone_number, message, fail_silently)
        
        # Add user context to result
        result["user"] = user.email
        result["notification_type"] = notification_type
        
        return result
        
    except Exception as e:
        logger.error(f"SMS notification failed for user {user.email}: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "user": user.email,
            "notification_type": notification_type
        }


def send_task_assignment_notifications(user: User, task, assigner) -> Dict[str, Any]:
    """
    Send both email and SMS notifications for task assignment, respecting user preferences.
    
    Args:
        user: User object who was assigned to the task
        task: Task object that was assigned
        assigner: User object who assigned the task
    
    Returns:
        dict: Result dictionary with both email and SMS results
    """
    logger.info(f"Sending task assignment notifications to {user.email}")
    
    # Prepare notification content
    task_name = task.name or "Unnamed Task"
    plan_name = task.milestone.plan.name if task.milestone and task.milestone.plan else "Unknown Plan"
    assigner_name = f"{assigner.first_name} {assigner.last_name}".strip() or assigner.email
    
    # Email content
    email_subject = f"New Task Assignment: {task_name}"
    email_message = (
        f"Dear {user.first_name},\n\n"
        f"You have been assigned to a new task in the Ignition platform:\n\n"
        f"Task: {task_name}\n"
        f"Project: {plan_name}\n"
        f"Assigned by: {assigner_name}\n\n"
        f"Please log in to your Ignition account to view the task details and get started.\n\n"
        f"Best regards,\n"
        f"The Ignition Team"
    )
    
    # SMS content (shorter)
    sms_message = (
        f"Hi {user.first_name}! You've been assigned to a new task:\n\n"
        f"Task: {task_name}\n"
        f"Plan: {plan_name}\n"
        f"Assigned by: {assigner_name}\n\n"
        f"Check your Ignition App for more details."
    )
    
    # Send notifications
    email_result = send_notification_email(user, email_subject, email_message, 'task_assignment')
    sms_result = send_notification_sms(user, sms_message, 'task_assignment')
    
    return {
        "user": user.email,
        "task": task_name,
        "email": email_result,
        "sms": sms_result
    }
