import os
import logging
from starlette import status

logger = logging.getLogger(__name__)
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from plans.serializers import PlanViewSerializer, SubtaskUpdateSerializer, \
    TaskUpdateSerializer, UserSerializer, TaskAssignSerializer, SubtaskSerializer, \
    TaskSerializer, MilestoneSerializer, CheckUserExistsSerializer, \
    AddInvitationSerializer, InvitationSerializer, InvitationUpdateSerializer, \
    CheckInvitationSerializer, PlanAccessSerializer
from plans.models import Plan, Subtask, Task, Milestone, Invitation, PlanAccess
from plans.permissions import IsPlanOwner, canAccessPlan, IsSubtaskPlanOwner, CanUpdatePlanMilestone, \
    IsTaskPlanOwner, canAccessTask, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IsHeadOwnerOnly, IsOwnerOrEditor
from users.models import User
from django.db.models import Q
from django.core import signing
from django.utils import timezone
from django.db import transaction
from django.core.mail import send_mail
from django.shortcuts import get_object_or_404
from django.core.signing import BadSignature, SignatureExpired
from utils.email import send_email_with_name, send_task_assignment_sms
from utils.notifications import send_task_assignment_notifications
from dotenv import load_dotenv
import os
load_dotenv()


class PlanDetailInfoView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, pk):
        if not pk:
            return Response({
                "message": "Invalid plan_id",
            }, status=status.HTTP_400_BAD_REQUEST)

        plan = get_object_or_404(Plan, pk=pk)
        serializer = PlanViewSerializer(plan, context={'request': request})

        return Response({
            "data": serializer.data,
        }, status=status.HTTP_200_OK)


class PlanDetailInformationBySlugView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, slug):
        if not slug:
            return Response({
                "message": "Invalid plan_slug_id",
            }, status=status.HTTP_400_BAD_REQUEST)

        plan = get_object_or_404(Plan, slug=slug)
        serializer = PlanViewSerializer(plan, context={'request': request})

        return Response({
            "data": serializer.data,
        }, status=status.HTTP_200_OK)


class UpdateSubTaskView(APIView):
    permission_classes = [IsAuthenticated, IsSubtaskPlanOwner]

    @swagger_auto_schema(request_body=SubtaskUpdateSerializer, responses={200: 'Updated successfully!'})
    def put(self, request, slug):
        subtask = get_object_or_404(Subtask, slug=slug)
        user = request.user

        # Check for sequential completion validation before updating
        new_status = request.data.get('status')
        if (user.sequential_task_completion and
            new_status == 3 and  # DONE_STATUS
            subtask.task):

            # Check if there are any incomplete subtasks with lower order in the same task
            incomplete_previous_subtasks = Subtask.objects.filter(
                task=subtask.task,
                order__lt=subtask.order,
                status__in=[1, 2]  # TODO_STATUS, INPROGRESS_STATUS
            ).exists()

            if incomplete_previous_subtasks:
                return Response({
                    "message": "Sequential task completion is enabled. Please complete previous subtasks first."
                }, status=status.HTTP_400_BAD_REQUEST)

        serializer = SubtaskUpdateSerializer(subtask, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class UpdateTaskView(APIView):
    permission_classes = [IsAuthenticated, canAccessTask]

    @swagger_auto_schema(request_body=TaskUpdateSerializer, responses={200: 'Login success'})
    def put(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        user = request.user

        data = request.data.copy()

        # Check for sequential completion validation before updating
        new_status = data.get('status')
        if (user.sequential_task_completion and
            new_status == 3 and  # DONE_STATUS
            task.milestone):

            # Check if there are any incomplete tasks with lower order in the same milestone
            incomplete_previous_tasks = Task.objects.filter(
                milestone=task.milestone,
                order__lt=task.order,
                status__in=[1, 2]  # TODO_STATUS, INPROGRESS_STATUS
            ).exists()

            if incomplete_previous_tasks:
                return Response({
                    "message": "Sequential task completion is enabled. Please complete previous tasks first."
                }, status=status.HTTP_400_BAD_REQUEST)

        serializer = TaskUpdateSerializer(task, data=data)
        if serializer.is_valid():
            if 'start_date' not in data:
                task.start_date = None
            if 'end_date' not in data:
                task.end_date = None
            serializer.save()
            task.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class getPlanUsersView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, slug):
        plan = get_object_or_404(Plan, slug=slug)
        assignees = User.objects.filter(is_active=True)
        serializer = UserSerializer(assignees, many=True)
        return Response({
            "data": serializer.data,
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)


class AssignTaskView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    @swagger_auto_schema(
        request_body=TaskAssignSerializer,
        responses={
            200: openapi.Response(description="Assignees updated successfully"),
            400: openapi.Response(description="Invalid input")
        }
    )
    def put(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        serializer = TaskAssignSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        assignees_data = serializer.validated_data.get('assignees', [])
        current_assignees = set(task.assignees.values_list('id', flat=True))

        valid_assignees = []
        invalid_assignees = []
        unauthorized_assignees = []

        for user_id in assignees_data:
            try:
                user = User.objects.get(id=user_id.id)
                if user_id.id not in current_assignees:
                    # Validate that user has access to the plan
                    plan = task.milestone.plan if task.milestone else None
                    if plan:
                        # Check if user has PlanAccess
                        has_access = PlanAccess.objects.filter(plan=plan, user=user).exists()

                        # Fallback: Check invitation system for backward compatibility
                        if not has_access:
                            has_access = Invitation.objects.filter(
                                plan=plan,
                                email=user.email,
                                accepted=Invitation.ACCEPTED
                            ).exists()

                        if has_access:
                            valid_assignees.append(user)
                        else:
                            unauthorized_assignees.append(user.email)
                    else:
                        # If no plan context, allow assignment (for standalone tasks)
                        valid_assignees.append(user)
            except User.DoesNotExist:
                invalid_assignees.append(user_id.id)

        if invalid_assignees:
            return Response({
                "message": f"Invalid user IDs: {invalid_assignees}"
            }, status=status.HTTP_400_BAD_REQUEST)

        if unauthorized_assignees:
            return Response({
                "message": f"Users do not have access to this plan: {unauthorized_assignees}. Please invite them first."
            }, status=status.HTTP_403_FORBIDDEN)

        task.assignees.add(*valid_assignees)

        # Send notifications to newly assigned users (respecting user preferences)
        notification_results = []
        print(f"📋 Task Assignment: {len(valid_assignees)} new assignees for task '{task.name}' (slug: {task.slug})")

        for assignee in valid_assignees:
            print(f"📧 Processing notifications for user: {assignee.email}")
            print(f"   Email notifications: {getattr(assignee, 'email_notifications_enabled', True)}")
            print(f"   SMS notifications: {getattr(assignee, 'sms_notifications_enabled', False)}")
            print(f"   Phone number: {assignee.phone_number}")

            try:
                notification_result = send_task_assignment_notifications(assignee, task, request.user)

                email_result = notification_result.get('email', {})
                sms_result = notification_result.get('sms', {})

                # Log results
                if email_result.get("success"):
                    print(f"✅ Email sent successfully to {assignee.email}")
                elif email_result.get("skipped"):
                    print(f"⏭️  Email skipped for {assignee.email}: {email_result.get('reason')}")
                else:
                    print(f"❌ Email failed for {assignee.email}: {email_result.get('error')}")

                if sms_result.get("success"):
                    print(f"✅ SMS sent successfully to {assignee.email} - Message ID: {sms_result.get('message_id')}")
                elif sms_result.get("skipped"):
                    print(f"⏭️  SMS skipped for {assignee.email}: {sms_result.get('reason')}")
                else:
                    print(f"❌ SMS failed for {assignee.email}: {sms_result.get('error')}")

                notification_results.append({
                    "user": assignee.email,
                    "email_sent": email_result.get("success", False),
                    "email_skipped": email_result.get("skipped", False),
                    "email_error": email_result.get("error") if not email_result.get("success") and not email_result.get("skipped") else None,
                    "sms_sent": sms_result.get("success", False),
                    "sms_skipped": sms_result.get("skipped", False),
                    "sms_error": sms_result.get("error") if not sms_result.get("success") and not sms_result.get("skipped") else None,
                    "message_id": sms_result.get("message_id") if sms_result.get("success") else None
                })
            except Exception as e:
                print(f"❌ Notification exception for {assignee.email}: {str(e)}")
                notification_results.append({
                    "user": assignee.email,
                    "email_sent": False,
                    "email_error": str(e),
                    "sms_sent": False,
                    "sms_error": str(e)
                })

        # Summary
        email_sent = sum(1 for r in notification_results if r.get('email_sent'))
        email_skipped = sum(1 for r in notification_results if r.get('email_skipped'))
        sms_sent = sum(1 for r in notification_results if r.get('sms_sent'))
        sms_skipped = sum(1 for r in notification_results if r.get('sms_skipped'))

        print(f"📊 Notification Summary:")
        print(f"   📧 Email: {email_sent} sent, {email_skipped} skipped")
        print(f"   📱 SMS: {sms_sent} sent, {sms_skipped} skipped")

        return Response({
            "message": "Assignees updated successfully",
            "invalid_assignees": invalid_assignees,
            "notifications": notification_results,
            "notification_summary": {
                "email": {"sent": email_sent, "skipped": email_skipped},
                "sms": {"sent": sms_sent, "skipped": sms_skipped}
            }
        }, status=status.HTTP_200_OK)


class retrievePlanThroughUserAccountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        paginator = PageNumberPagination()
        paginator.page_size = 5

        user = request.user
        name = request.query_params.get('name', None)
        invite_user = request.query_params.get('invite_user', None)

        # Get plans from unified access system (exclude failed plans)
        # 1. Plans where user is the owner
        user_plans = Plan.objects.filter(user=user).exclude(status='failed')

        # 2. Plans where user has access through PlanAccess system
        access_plan_ids = PlanAccess.objects.filter(user=user).values_list('plan_id', flat=True)
        access_plans = Plan.objects.filter(id__in=access_plan_ids).exclude(user=user).exclude(status='failed')

        # 3. Fallback: Plans from old invitation system (for backward compatibility)
        user_email = user.email
        invited_plans = Plan.objects.filter(
            invitations__email=user_email,
            invitations__accepted=True
        ).exclude(
            id__in=access_plan_ids  # Avoid duplicates with PlanAccess
        ).exclude(
            user=user  # Avoid duplicates with owned plans
        ).exclude(
            status='failed'  # Exclude failed plans
        ).distinct()

        # Combine all plans
        combined_plans = list(user_plans) + list(access_plans) + list(invited_plans)

        # Remove duplicates while preserving order
        seen = set()
        unique_plans = []
        for plan in combined_plans:
            if plan.id not in seen:
                seen.add(plan.id)
                unique_plans.append(plan)
        combined_plans = unique_plans

        if name:
            combined_plans = [plan for plan in combined_plans if name.lower() in plan.name.lower()]

        combined_plans = sorted(list({plan.id: plan for plan in combined_plans}.values()), key=lambda plan: plan.id, reverse=True)
        result_page = paginator.paginate_queryset(combined_plans, request)
        serializer = PlanViewSerializer(result_page, many=True)
        paginated_response = paginator.get_paginated_response(serializer.data)
        paginated_response.data['status'] = status.HTTP_200_OK
        return paginated_response


class AddSubtaskView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    @swagger_auto_schema(
        request_body=SubtaskSerializer,
        responses={
            200: openapi.Response(description="Subtask created successfully"),
            400: openapi.Response(description="Invalid input")
        }
    )
    def post(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        data = request.data.copy()
        data['task'] = task.id

        serializer = SubtaskSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                "message": "Subtask created successfully",
                "data": serializer.data
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class DeleteSubtaskView(APIView):
    permission_classes = [IsAuthenticated, IsSubtaskPlanOwner]

    def delete(self, request, slug):
        subtask = get_object_or_404(Subtask, slug=slug)
        self.check_object_permissions(request, subtask)
        subtask.delete()

        return Response({
            "message": "Subtask deleted successfully"
        }, status=status.HTTP_204_NO_CONTENT)


class UpdateSubtaskOrderView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=openapi.Items(type=openapi.TYPE_OBJECT, properties={
                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Subtask ID'),
                'order': openapi.Schema(type=openapi.TYPE_INTEGER, description='New order index'),
            }),
        ),
        responses={
            200: openapi.Response(description="Order updated successfully"),
            400: openapi.Response(description="Invalid input"),
        }
    )
    def put(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        subtasks_data = request.data

        for subtask_data in subtasks_data:
            subtask_id = subtask_data.get('id')
            order = subtask_data.get('order')
            if subtask_id is not None and order is not None:
                try:
                    subtask = Subtask.objects.get(id=subtask_id, task=task)
                    subtask.order = order
                    subtask.save()
                except Subtask.DoesNotExist:
                    return Response({"message": f"Subtask with ID {subtask_id} not found"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": "Order updated successfully"}, status=status.HTTP_200_OK)


class UpdateTaskOrderView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, id):
        milestone = get_object_or_404(Milestone, id=id)
        tasks_data = request.data

        try:
            with transaction.atomic():
                for task_data in tasks_data:
                    task_id = task_data.get('id')
                    order = task_data.get('order')
                    if task_id is not None and order is not None:
                        task = Task.objects.get(id=task_id, milestone=milestone)
                        task.order = order
                        task.save()

            return Response({"message": "Task order updated successfully"}, status=status.HTTP_200_OK)
        except Task.DoesNotExist:
            return Response({"message": "Task not found"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AddTaskToMilestoneView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=TaskSerializer, responses={201: 'Task created successfully'})
    def post(self, request, id):
        milestone = get_object_or_404(Milestone, id=id)
        data = request.data.copy()
        data['milestone'] = milestone.id

        serializer = TaskUpdateSerializer(data=data)
        if serializer.is_valid():
            task = serializer.save()

            if 'order' not in data:
                task.order = Task.objects.filter(milestone=milestone).count()
                task.save()

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response({"message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class DeleteTaskView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    def delete(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        task.delete()

        return Response({"message": "Task and its subtasks deleted successfully"}, status=status.HTTP_204_NO_CONTENT)


class UpdateMilestoneNameView(APIView):
    permission_classes = [IsAuthenticated, CanUpdatePlanMilestone]

    def put(self, request, id):
        milestone = get_object_or_404(Milestone, id=id)
        serializer = MilestoneSerializer(milestone, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class DeleteMilestoneView(APIView):
    permission_classes = [IsAuthenticated, CanUpdatePlanMilestone]

    def delete(self, request, id):
        milestone = get_object_or_404(Milestone, id=id)
        milestone_name = milestone.name
        task_count = milestone.task_set.count()

        # Delete the milestone (this will cascade delete tasks and subtasks)
        milestone.delete()

        return Response({
            "message": f"Milestone '{milestone_name}' and {task_count} associated tasks deleted successfully"
        }, status=status.HTTP_204_NO_CONTENT)


class UpdatePlanNameView(APIView):
    permission_classes = [IsAuthenticated, IsOwnerOrEditor]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'name': openapi.Schema(type=openapi.TYPE_STRING, description='New plan name')
            },
            required=['name']
        ),
        responses={
            200: openapi.Response(description="Plan name updated successfully"),
            400: openapi.Response(description="Invalid input"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Plan not found")
        }
    )
    def put(self, request, slug):
        plan = get_object_or_404(Plan, slug=slug)

        new_name = request.data.get('name', '').strip()
        if not new_name:
            return Response({
                "error": "Plan name cannot be empty"
            }, status=status.HTTP_400_BAD_REQUEST)

        if len(new_name) > 500:  # Based on model max_length
            return Response({
                "error": "Plan name cannot exceed 500 characters"
            }, status=status.HTTP_400_BAD_REQUEST)

        plan.name = new_name
        plan.save()

        return Response({
            "message": "Plan name updated successfully",
            "name": plan.name
        }, status=status.HTTP_200_OK)


class AddMilestoneToPlanView(APIView):
    permission_classes = [IsAuthenticated, CanUpdatePlanMilestone]

    @swagger_auto_schema(
        request_body=MilestoneSerializer,
        responses={
            201: openapi.Response(description="Milestone created successfully"),
            400: openapi.Response(description="Invalid input")
        }
    )
    def post(self, request, slug):
        plan = get_object_or_404(Plan, slug=slug)
        data = request.data.copy()
        data['plan'] = plan.id

        serializer = MilestoneSerializer(data=data)
        if serializer.is_valid():
            milestone = serializer.save()
            return Response({
                "message": "Milestone created successfully",
                "data": serializer.data
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class CheckUserExistsView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = CheckUserExistsSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            user_exists = User.objects.filter(email=email).exists()

            if user_exists:
                return Response({"exists": True, "message": "User exists in the system"}, status=status.HTTP_200_OK)
            else:
                return Response({"exists": False, "message": "User does not exist in the system"}, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SendInvitationView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, slug):
        print(f"🔍 SendInvitationView: Received POST request for plan slug: {slug}")
        print(f"🔍 Request data: {request.data}")

        plan = get_object_or_404(Plan, slug=slug)
        print(f"🔍 Found plan: {plan.name} (ID: {plan.id})")

        serializer = AddInvitationSerializer(data=request.data)
        print(f"🔍 Serializer validation...")

        if serializer.is_valid():
            print(f"✅ Serializer is valid")
            email = serializer.validated_data['email']
            access_level = 'viewer'  # Default access level for old invitation system

            existing_invitation = Invitation.objects.filter(email=email, plan=plan).first()
            if existing_invitation:
                # Check rate limiting - allow re-invitation only after 1 minute
                from django.utils import timezone
                from datetime import timedelta

                time_since_last_invite = timezone.now() - existing_invitation.created_at
                if time_since_last_invite < timedelta(minutes=1):
                    remaining_seconds = 60 - time_since_last_invite.total_seconds()
                    return Response({
                        "error": "rate_limit",
                        "message": f"Please wait {int(remaining_seconds)} seconds before sending another invitation to this email",
                        "remaining_seconds": int(remaining_seconds),
                        "email": email
                    }, status=status.HTTP_429_TOO_MANY_REQUESTS)

                # Update existing invitation timestamp for re-invitation
                existing_invitation.created_at = timezone.now()
                existing_invitation.access_level = access_level  # Update access level if changed
                existing_invitation.save()
                invitation = existing_invitation
                print(f"🔄 Re-sending invitation to existing invitation: {invitation.id}")
                is_reinvitation = True
            else:
                # Create new invitation
                invitation = Invitation.objects.create(
                    email=email,
                    plan=plan,
                    invited_by=request.user,
                    access_level=access_level
                )
                print(f"✅ Created new invitation: {invitation.id}")
                is_reinvitation = False

            if email == plan.user.email:
                return Response({
                    "message": "You cannot invite yourself to your own plan."
                }, status=status.HTTP_400_BAD_REQUEST)

            print(f"📧 Plan Invitation: Sending invitation to {email} for plan '{plan.name}'")

            # Send Email Invitation
            subject = "You've been invited to collaborate"
            url_frontend = os.getenv('URL_FRONTEND')
            signed_id = signing.dumps(invitation.id)
            accept_url = f'{url_frontend}/p/accept-invitation/{signed_id}'
            lines = (
                "Dear User,\n\n"
                "You've been invited to collaborate on the plan: {name}.\n"
                "This invitation will expire in 1 hour.\n"
                "Please click the link below to accept:\n\n"
                'Invitation Link: {url}\n\n'
                'Best regards,\n'
                'The Ignition Team'
            )
            email_message = lines.format(name=plan.name, url=accept_url)
            recipient_list = [email]

            # Send SMS Notification (respecting user preferences)
            sms_result = None
            try:
                invited_user = User.objects.get(email=email)
                print(f"📱 Plan Invitation: Found user {email}")
                print(f"   SMS notifications enabled: {getattr(invited_user, 'sms_notifications_enabled', False)}")
                print(f"   Phone number: {invited_user.phone_number}")

                from utils.notifications import send_notification_sms

                sms_message = (
                    f"Hi {invited_user.first_name}! You've been invited to collaborate on:\n\n"
                    f"Plan: {plan.name}\n"
                    f"Invited by: {request.user.first_name} {request.user.last_name}\n\n"
                    f"Check your email for the invitation link!"
                )

                print(f"📱 Plan Invitation: Attempting to send SMS...")
                sms_result = send_notification_sms(invited_user, sms_message, 'plan_invitation')

                if sms_result.get("success"):
                    print(f"✅ Plan Invitation SMS sent successfully - Message ID: {sms_result.get('message_id')}")
                elif sms_result.get("skipped"):
                    print(f"⏭️  Plan Invitation SMS skipped: {sms_result.get('reason')}")
                else:
                    print(f"❌ Plan Invitation SMS failed: {sms_result.get('error')}")

            except User.DoesNotExist:
                print(f"📱 Plan Invitation: User {email} not found in system, skipping SMS")

            # Send email invitation (simplified logic)
            email_result = None
            try:
                # Check if user exists to use notification system
                try:
                    invited_user = User.objects.get(email=email)
                    from utils.notifications import send_notification_email
                    print(f"📧 Plan Invitation: Sending to existing user {email} via notification system")
                    email_result = send_notification_email(invited_user, subject, email_message, 'plan_invitation')

                    if email_result.get("success"):
                        print(f"✅ Plan Invitation email sent successfully to {email}")
                    else:
                        print(f"❌ Plan Invitation email failed: {email_result.get('error', 'Unknown error')}")
                        raise Exception(email_result.get('error', 'Email sending failed'))

                except User.DoesNotExist:
                    # User doesn't exist, send email directly
                    print(f"📧 Plan Invitation: User {email} not in system, sending email directly")
                    send_email_with_name(subject, email_message, recipient_list, fail_silently=False)
                    email_result = {"success": True, "new_user": True}
                    print(f"✅ Plan Invitation email sent successfully to {email}")

            except Exception as e:
                print(f"❌ Plan Invitation email failed: {str(e)}")
                return Response({
                    'message': f'Failure sendmail: {str(e)}',
                    'status': status.HTTP_500_INTERNAL_SERVER_ERROR
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            response_data = {"message": "Invitation sent successfully"}

            # Add email result
            if email_result:
                response_data["email_notification"] = {
                    "email_sent": email_result.get("success", False),
                    "email_skipped": email_result.get("skipped", False),
                    "email_error": email_result.get("error") if not email_result.get("success") and not email_result.get("skipped") else None,
                    "override": email_result.get("override", False),
                    "new_user": email_result.get("new_user", False)
                }

            # Add SMS result
            if sms_result:
                response_data["sms_notification"] = {
                    "sms_sent": sms_result.get("success", False),
                    "sms_skipped": sms_result.get("skipped", False),
                    "sms_error": sms_result.get("error") if not sms_result.get("success") and not sms_result.get("skipped") else None,
                    "message_id": sms_result.get("message_id") if sms_result.get("success") else None
                }

            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CheckInvitationView(APIView):
    permission_classes = []  # Allow unauthenticated access

    def post(self, request):
        print(f"🔍 CheckInvitationView: Received request")
        print(f"🔍 Request data: {request.data}")

        signed_id = request.data.get('signed_id')
        print(f"🔍 Signed ID: {signed_id}")

        if not signed_id:
            return Response({"message": "Signed ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            invitation_id = signing.loads(signed_id, max_age=3600)
            print(f"✅ Decoded invitation ID: {invitation_id}")
        except SignatureExpired:
            print(f"❌ Signature expired for signed_id: {signed_id}")
            return Response({"message": "The invitation link has expired."}, status=status.HTTP_400_BAD_REQUEST)
        except BadSignature:
            print(f"❌ Bad signature for signed_id: {signed_id}")
            return Response({"message": "Invalid invitation link."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            invitation = Invitation.objects.get(id=invitation_id)
            print(f"✅ Found invitation: {invitation.email} for plan {invitation.plan.name}")
        except Invitation.DoesNotExist:
            print(f"❌ Invitation with ID {invitation_id} does not exist")
            return Response({"detail": "No Invitation matches the given query."}, status=status.HTTP_404_NOT_FOUND)

        # Check if user exists in system
        find_user = User.objects.filter(email=invitation.email).exists()
        print(f"🔍 User {invitation.email} exists in system: {find_user}")

        if not find_user:
            print(f"📧 User not registered, returning registration required response")
            return Response({
                'is_not_registered': 1,
                "invitation_email": invitation.email,
                "message": "This email is not registered in the system."
            }, status=status.HTTP_404_NOT_FOUND)

        # If user is authenticated, check if invitation is for them
        if request.user.is_authenticated:
            print(f"🔍 User is authenticated: {request.user.email}")
            if invitation.email != request.user.email:
                print(f"❌ Invitation email mismatch: {invitation.email} vs {request.user.email}")
                return Response({
                    "message": "This invitation is not for you."
                }, status=status.HTTP_403_FORBIDDEN)
        else:
            print(f"🔍 User not authenticated, returning login required response")
            return Response({
                "message": "You need to be logged in to access this invitation."
            }, status=status.HTTP_401_UNAUTHORIZED)

        # Check if invitation already accepted
        if invitation.accepted:
            print(f"❌ Invitation already accepted")
            return Response({"message": "This invitation has already been accepted."}, status=status.HTTP_400_BAD_REQUEST)

        print(f"✅ Invitation valid, returning invitation details")
        serializer = CheckInvitationSerializer(invitation)
        return Response({
            "message": "Invitation found successfully.",
            "invitation": serializer.data,
        }, status=status.HTTP_200_OK)


class UpdateInvitationStatusView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, id):
        invitation = get_object_or_404(Invitation, id=id)
        if invitation.email!= request.user.email:
            return Response({
                "message": "This invitation is not for you."
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = InvitationUpdateSerializer(invitation, data=request.data, partial=True)

        if serializer.is_valid():
            with transaction.atomic():
                serializer.save()
                if serializer.validated_data['accepted'] == Invitation.ACCEPTED:
                    invitation.accepted_at = timezone.now()

                    # Auto-create PlanAccess record when invitation is accepted
                    plan_access, created = PlanAccess.objects.get_or_create(
                        user=request.user,
                        plan=invitation.plan,
                        defaults={
                            'access_level': invitation.access_level,
                            'is_head_owner': False,
                            'granted_by': invitation.invited_by
                        }
                    )

                    if created:
                        print(f"✅ Auto-created PlanAccess for {request.user.email} on plan '{invitation.plan.name}' with level '{invitation.access_level}'")
                    else:
                        print(f"ℹ️ PlanAccess already exists for {request.user.email} on plan '{invitation.plan.name}'")

                elif serializer.validated_data['accepted'] == Invitation.REJECTED:
                    invitation.accepted_at = None

                    # Remove PlanAccess if invitation is rejected
                    try:
                        plan_access = PlanAccess.objects.get(user=request.user, plan=invitation.plan)
                        # Only remove if not head owner and was granted by invitation
                        if not plan_access.is_head_owner and plan_access.granted_by == invitation.invited_by:
                            plan_access.delete()
                            print(f"🗑️ Removed PlanAccess for {request.user.email} on plan '{invitation.plan.name}' due to rejection")
                    except PlanAccess.DoesNotExist:
                        pass
                else:
                    invitation.accepted_at = None

                invitation.save()

            return Response({
                "message": "Invitation status updated successfully.",
                "accepted_at": invitation.accepted_at
            }, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ListInvitedUsersView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, slug):
        plan = get_object_or_404(Plan, slug=slug)
        invitations = Invitation.objects.filter(plan=plan)
        serializer = InvitationSerializer(invitations, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


class DeletePlanView(APIView):
    permission_classes = [IsAuthenticated, IsPlanOwner]

    def delete(self, request, slug):
        try:
            plan = get_object_or_404(Plan, slug=slug)

            # Clear all many-to-many relationships before deletion
            for milestone in plan.milestone_set.all():
                for task in milestone.task_set.all():
                    # Clear task assignees
                    task.assignees.clear()

                    # Clear subtask relationships if any
                    for subtask in task.subtask_set.all():
                        subtask.delete()

                # Clear milestone risks if any
                milestone.risks.all().delete()

            # Delete the plan (this will cascade delete milestones and tasks due to the model changes)
            plan.delete()

            return Response({"message": "Plan and related data deleted successfully"}, status=status.HTTP_204_NO_CONTENT)
        except Plan.DoesNotExist:
            return Response({"error": "Plan not found or you do not have permission to delete this plan"}, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({"error": f"Error deleting plan: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class OptOutPlanView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, slug):
        try:
            plan = get_object_or_404(Plan, slug=slug)

            # Check if user is the plan owner
            if plan.user == request.user:
                return Response({
                    "error": "Plan owners cannot opt out of their own plans. Use delete instead."
                }, status=status.HTTP_400_BAD_REQUEST)

            # Find and delete the user's invitation
            invitation = get_object_or_404(Invitation, plan=plan, email=request.user.email, accepted=Invitation.ACCEPTED)
            invitation.delete()

            return Response({
                "message": "Successfully opted out of the plan"
            }, status=status.HTTP_204_NO_CONTENT)

        except Invitation.DoesNotExist:
            return Response({
                "error": "You are not a member of this plan or invitation not found"
            }, status=status.HTTP_404_NOT_FOUND)
        except Plan.DoesNotExist:
            return Response({
                "error": "Plan not found"
            }, status=status.HTTP_404_NOT_FOUND)


class PlanAccessManagementView(APIView):
    """Manage access levels for a plan"""
    permission_classes = [IsAuthenticated, IsOwnerOnly]

    def get(self, request, slug):
        """Get all access levels for a plan"""
        plan = get_object_or_404(Plan, slug=slug)
        access_levels = PlanAccess.objects.filter(plan=plan)
        serializer = PlanAccessSerializer(access_levels, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, slug):
        """Add a new access level with email invitation"""
        print(f"🔍 PlanAccessManagementView: Received POST request for plan slug: {slug}")
        print(f"🔍 Request data: {request.data}")

        plan = get_object_or_404(Plan, slug=slug)
        print(f"🔍 Found plan: {plan.name} (ID: {plan.id})")

        email = request.data.get('email')
        access_level = request.data.get('access_level', PlanAccess.VIEWER)
        send_invitation = request.data.get('send_invitation', True)  # Default to True

        if not email:
            return Response({
                "error": "Email is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)
            user_exists = True
            print(f"🔍 User exists: {user.email}")
        except User.DoesNotExist:
            user_exists = False
            print(f"🔍 User does not exist: {email}")

        # Check if user already has access (if user exists)
        if user_exists and PlanAccess.objects.filter(plan=plan, user=user).exists():
            return Response({
                "error": "User already has access to this plan"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Always create invitation first (for both existing and new users)
        # Check if invitation already exists and handle re-invitations with rate limiting
        existing_invitation = Invitation.objects.filter(email=email, plan=plan).first()
        if existing_invitation:
            # Check rate limiting - allow re-invitation only after 1 minute
            from django.utils import timezone
            from datetime import timedelta

            time_since_last_invite = timezone.now() - existing_invitation.created_at
            if time_since_last_invite < timedelta(minutes=1):
                remaining_seconds = 60 - time_since_last_invite.total_seconds()
                return Response({
                    "error": "rate_limit",
                    "message": f"Please wait {int(remaining_seconds)} seconds before sending another invitation to this email",
                    "remaining_seconds": int(remaining_seconds),
                    "email": email
                }, status=status.HTTP_429_TOO_MANY_REQUESTS)

            # Update existing invitation timestamp for re-invitation
            existing_invitation.created_at = timezone.now()
            existing_invitation.access_level = access_level  # Update access level if changed
            existing_invitation.save()
            invitation = existing_invitation
            print(f"🔄 Re-sending invitation to existing invitation: {invitation.id}")
        else:
            # Create new invitation
            invitation = Invitation.objects.create(
                email=email,
                plan=plan,
                invited_by=request.user,
                access_level=access_level
            )
            print(f"✅ Created new invitation: {invitation.id}")

        # Invitation already created or updated above

        # If user exists, also create PlanAccess immediately (but keep invitation for tracking)
        if user_exists:
            access = PlanAccess.objects.create(
                user=user,
                plan=plan,
                access_level=access_level,
                granted_by=request.user
            )
            print(f"✅ Also created PlanAccess for existing user: {user.email}")

        # Send invitation email (same for both existing and new users)
        if send_invitation:
            try:
                from django.core import signing
                from utils.email import send_email_with_name

                # Generate signed URL
                url_frontend = os.getenv('URL_FRONTEND')
                signed_id = signing.dumps(invitation.id)
                accept_url = f'{url_frontend}/p/accept-invitation/{signed_id}'

                # Prepare enhanced email content
                inviter_name = f"{request.user.first_name} {request.user.last_name}".strip()
                if not inviter_name:
                    inviter_name = request.user.email

                subject = f"🚀 You're invited to collaborate on '{plan.name}'"

                # Create a professional HTML-style email template
                email_message = f"""Dear Collaborator,

🎉 Great news! You've been invited to join an exciting project on Ignition!

📋 PROJECT DETAILS:
   • Project Name: "{plan.name}"
   • Access Level: {access_level.title()}
   • Invited by: {inviter_name} ({request.user.email})

📝 PROJECT DESCRIPTION:
{plan.description[:200]}{'...' if len(plan.description) > 200 else ''}

🔗 ACCEPT YOUR INVITATION:
Click the link below to join the project and start collaborating:

{accept_url}

⏰ IMPORTANT: This invitation will expire in 1 hour for security reasons.

🤝 WHAT'S NEXT?
Once you accept the invitation, you'll be able to:
   • View and contribute to project milestones
   • Collaborate on tasks and subtasks
   • Track project progress in real-time
   • Communicate with team members

Need help? Reply to this email or contact our support team.

Best regards,
The Ignition Team
🚀 Making project management effortless

---
This is an automated message from Ignition. Please do not reply to this email directly."""

                send_email_with_name(subject, email_message, [email], fail_silently=False)
                print(f"✅ Invitation email sent successfully to {email}")

            except Exception as e:
                print(f"❌ Failed to send invitation email: {str(e)}")
                return Response({
                    "error": f"Invitation created but email failed: {str(e)}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Return enhanced invitation data with success messaging
        is_reinvitation = existing_invitation is not None
        if is_reinvitation:
            success_message = f"🔄 Re-invitation sent successfully to {email}!"
        else:
            success_message = f"🎉 Invitation sent successfully to {email}!"

        if user_exists:
            success_message += f" They already have an account and can access the project immediately."
        else:
            success_message += f" They'll receive an email with instructions to join."

        return Response({
            "id": invitation.id,
            "user": {
                "id": user.id if user_exists else None,
                "email": email,
                "first_name": user.first_name if user_exists else "",
                "last_name": user.last_name if user_exists else "",
                "avatar": user.avatar.url if user_exists and user.avatar else None
            },
            "plan": plan.id,
            "access_level": access_level,
            "is_head_owner": False,
            "granted_by": {
                "id": request.user.id,
                "email": request.user.email,
                "first_name": request.user.first_name,
                "last_name": request.user.last_name
            },
            "created_at": invitation.created_at.isoformat(),
            "status": "joined" if user_exists else "pending",
            "email_sent": send_invitation,
            "user_exists": user_exists,
            "is_reinvitation": is_reinvitation,
            "success_message": success_message,
            "invitation_details": {
                "project_name": plan.name,
                "access_level": access_level.title(),
                "invited_by": f"{request.user.first_name} {request.user.last_name}".strip() or request.user.email,
                "email_status": "sent" if send_invitation else "not_sent",
                "reinvitation": is_reinvitation
            }
        }, status=status.HTTP_201_CREATED)



class PlanAccessDetailView(APIView):
    """Manage individual access level"""
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.request.method == 'DELETE':
            # Only head owner can remove owners, owners can remove editors/viewers
            return [IsAuthenticated(), IsOwnerOnly()]
        elif self.request.method == 'PUT':
            # Only head owner can change access levels
            return [IsAuthenticated(), IsHeadOwnerOnly()]
        return [IsAuthenticated(), IsOwnerOnly()]

    def put(self, request, slug, access_id):
        """Update access level"""
        plan = get_object_or_404(Plan, slug=slug)
        access = get_object_or_404(PlanAccess, id=access_id, plan=plan)

        new_access_level = request.data.get('access_level')
        is_head_owner = request.data.get('is_head_owner', False)

        if not new_access_level:
            return Response({
                "error": "Access level is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Handle head owner transfer
        if is_head_owner and new_access_level == PlanAccess.OWNER:
            # Remove head owner from current user
            current_head = PlanAccess.objects.get(plan=plan, is_head_owner=True)
            current_head.is_head_owner = False
            current_head.save()

            # Set new head owner
            access.is_head_owner = True

        access.access_level = new_access_level
        access.save()

        serializer = PlanAccessSerializer(access)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request, slug, access_id):
        """Remove access level"""
        plan = get_object_or_404(Plan, slug=slug)
        access = get_object_or_404(PlanAccess, id=access_id, plan=plan)

        # Cannot remove head owner
        if access.is_head_owner:
            return Response({
                "error": "Cannot remove head owner. Transfer ownership first."
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get current user's access level
        user_access = PlanAccess.objects.get(plan=plan, user=request.user)

        # Only head owner can remove owners
        if access.access_level == PlanAccess.OWNER and not user_access.is_head_owner:
            return Response({
                "error": "Only head owner can remove other owners"
            }, status=status.HTTP_403_FORBIDDEN)

        access.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class CancelInvitationView(APIView):
    """Cancel a pending invitation"""
    permission_classes = [IsAuthenticated, IsOwnerOnly]

    def post(self, request, slug):
        """Cancel invitation by email"""
        print(f"🔍 CancelInvitationView: Received POST request for plan slug: {slug}")
        print(f"🔍 Request data: {request.data}")

        plan = get_object_or_404(Plan, slug=slug)
        email = request.data.get('email')

        if not email:
            return Response({
                "error": "Email is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Find the invitation to cancel
            invitation = Invitation.objects.get(plan=plan, email=email)
            print(f"🔍 Found invitation: {invitation.id} for {email}")

            # Delete the invitation
            invitation.delete()
            print(f"✅ Canceled invitation for {email}")

            # Also remove PlanAccess if it exists (for users who were pre-granted access)
            try:
                user = User.objects.get(email=email)
                plan_access = PlanAccess.objects.get(plan=plan, user=user)
                if not plan_access.is_head_owner:  # Don't remove head owner
                    plan_access.delete()
                    print(f"✅ Also removed PlanAccess for {email}")
            except (User.DoesNotExist, PlanAccess.DoesNotExist):
                print(f"ℹ️ No PlanAccess found for {email}")

            return Response({
                "message": f"Invitation for {email} has been canceled successfully"
            }, status=status.HTTP_200_OK)

        except Invitation.DoesNotExist:
            print(f"❌ No invitation found for {email}")
            return Response({
                "error": f"No pending invitation found for {email}"
            }, status=status.HTTP_404_NOT_FOUND)
