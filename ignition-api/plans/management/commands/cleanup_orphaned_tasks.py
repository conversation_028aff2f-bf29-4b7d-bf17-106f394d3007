from django.core.management.base import BaseCommand
from plans.models import Task


class Command(BaseCommand):
    help = 'Clean up orphaned tasks (tasks without milestones)'

    def handle(self, *args, **options):
        # Find tasks without milestones
        orphaned_tasks = Task.objects.filter(milestone__isnull=True)
        count = orphaned_tasks.count()

        if count > 0:
            self.stdout.write(f'Found {count} orphaned tasks. Deleting...')
            orphaned_tasks.delete()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {count} orphaned tasks.')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS('No orphaned tasks found.')
            )
