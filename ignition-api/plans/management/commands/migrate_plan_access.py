from django.core.management.base import BaseCommand
from django.db import transaction, models
from plans.models import Plan, PlanAccess, Invitation
from users.models import User


class Command(BaseCommand):
    help = 'Migrate existing plans to use the unified access level system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup-invitations',
            action='store_true',
            help='Remove accepted invitations after converting to PlanAccess',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        cleanup_invitations = options.get('cleanup_invitations', False)

        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        else:
            self.stdout.write('Starting migration of plan access levels...')

        with transaction.atomic():
            # Create head owner access for all existing plans
            plans_without_access = Plan.objects.exclude(
                access_levels__user=models.F('user'),
                access_levels__is_head_owner=True
            )

            created_count = 0
            for plan in plans_without_access:
                if plan.user:
                    if not dry_run:
                        _, created = PlanAccess.objects.get_or_create(
                            user=plan.user,
                            plan=plan,
                            defaults={
                                'access_level': PlanAccess.OWNER,
                                'is_head_owner': True
                            }
                        )
                        if created:
                            created_count += 1
                    else:
                        created_count += 1
                    self.stdout.write(f'{"[DRY RUN] " if dry_run else ""}Created head owner access for plan: {plan.name}')

            # Convert accepted invitations to access levels
            accepted_invitations = Invitation.objects.filter(accepted=Invitation.ACCEPTED)
            converted_count = 0
            cleanup_count = 0

            for invitation in accepted_invitations:
                try:
                    user = User.objects.get(email=invitation.email)
                    access_level = getattr(invitation, 'access_level', PlanAccess.VIEWER)

                    if not dry_run:
                        _, created = PlanAccess.objects.get_or_create(
                            user=user,
                            plan=invitation.plan,
                            defaults={
                                'access_level': access_level,
                                'is_head_owner': False,
                                'granted_by': invitation.invited_by
                            }
                        )

                        if created:
                            converted_count += 1
                            self.stdout.write(f'Converted invitation to access level for {user.email} on plan: {invitation.plan.name}')

                            # Cleanup accepted invitation if requested
                            if cleanup_invitations:
                                invitation.delete()
                                cleanup_count += 1
                                self.stdout.write(f'Removed accepted invitation for {user.email}')
                    else:
                        # Check if would be created
                        existing = PlanAccess.objects.filter(user=user, plan=invitation.plan).exists()
                        if not existing:
                            converted_count += 1
                            self.stdout.write(f'[DRY RUN] Would convert invitation to access level for {user.email} on plan: {invitation.plan.name}')
                            if cleanup_invitations:
                                cleanup_count += 1
                                self.stdout.write(f'[DRY RUN] Would remove accepted invitation for {user.email}')

                except User.DoesNotExist:
                    self.stdout.write(f'Warning: User {invitation.email} not found for invitation to plan: {invitation.plan.name}')
                    continue

            cleanup_msg = f"\nWould cleanup {cleanup_count} invitations" if cleanup_invitations else ""
            success_cleanup_msg = f"\nCleaned up {cleanup_count} invitations" if cleanup_invitations else ""

            if dry_run:
                message = (
                    'DRY RUN RESULTS:\n'
                    f'Would create {created_count} head owner access levels\n'
                    f'Would convert {converted_count} invitations'
                    f'{cleanup_msg}'
                )
                self.stdout.write(self.style.WARNING(message))
            else:
                message = (
                    'Migration completed successfully!\n'
                    f'Created {created_count} head owner access levels\n'
                    f'Converted {converted_count} invitations to access levels'
                    f'{success_cleanup_msg}'
                )
                self.stdout.write(self.style.SUCCESS(message))
