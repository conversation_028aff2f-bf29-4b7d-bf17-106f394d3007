from django.core.management.base import BaseCommand
from django.db import transaction
from plans.models import Plan, PlanAccess, Invitation
from users.models import User


class Command(BaseCommand):
    help = 'Sync invitation and access level systems - run this regularly to keep systems in sync'

    def add_arguments(self, parser):
        parser.add_argument(
            '--auto-cleanup',
            action='store_true',
            help='Automatically cleanup accepted invitations after creating PlanAccess',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        auto_cleanup = options.get('auto_cleanup', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        with transaction.atomic():
            # Find accepted invitations that don't have corresponding PlanAccess
            accepted_invitations = Invitation.objects.filter(accepted=Invitation.ACCEPTED)
            sync_count = 0
            cleanup_count = 0
            
            for invitation in accepted_invitations:
                try:
                    user = User.objects.get(email=invitation.email)
                    
                    # Check if PlanAccess already exists
                    existing_access = PlanAccess.objects.filter(
                        user=user, 
                        plan=invitation.plan
                    ).exists()
                    
                    if not existing_access:
                        if not dry_run:
                            # Create PlanAccess record
                            PlanAccess.objects.create(
                                user=user,
                                plan=invitation.plan,
                                access_level=invitation.access_level,
                                is_head_owner=False,
                                granted_by=invitation.invited_by
                            )
                            sync_count += 1
                            
                            # Cleanup invitation if requested
                            if auto_cleanup:
                                invitation.delete()
                                cleanup_count += 1
                                self.stdout.write(f'Synced and cleaned up invitation for {user.email} on plan: {invitation.plan.name}')
                            else:
                                self.stdout.write(f'Synced invitation for {user.email} on plan: {invitation.plan.name}')
                        else:
                            sync_count += 1
                            self.stdout.write(f'[DRY RUN] Would sync invitation for {user.email} on plan: {invitation.plan.name}')
                            if auto_cleanup:
                                cleanup_count += 1
                                self.stdout.write(f'[DRY RUN] Would cleanup invitation for {user.email}')
                    
                except User.DoesNotExist:
                    self.stdout.write(f'Warning: User {invitation.email} not found for invitation to plan: {invitation.plan.name}')
                    continue
            
            # Find PlanAccess records that should have corresponding invitations (for audit)
            orphaned_access = []
            all_access = PlanAccess.objects.exclude(is_head_owner=True).select_related('user', 'plan')
            
            for access in all_access:
                # Check if there's a corresponding invitation
                invitation_exists = Invitation.objects.filter(
                    email=access.user.email,
                    plan=access.plan,
                    accepted=Invitation.ACCEPTED
                ).exists()
                
                if not invitation_exists:
                    orphaned_access.append(access)
            
            if orphaned_access:
                self.stdout.write(self.style.WARNING(f'Found {len(orphaned_access)} PlanAccess records without corresponding invitations:'))
                for access in orphaned_access:
                    self.stdout.write(f'  - {access.user.email} on plan: {access.plan.name}')
            
            cleanup_msg = f"\nWould cleanup {cleanup_count} invitations" if auto_cleanup else ""
            success_cleanup_msg = f"\nCleaned up {cleanup_count} invitations" if auto_cleanup else ""

            if dry_run:
                message = (
                    'DRY RUN RESULTS:\n'
                    f'Would sync {sync_count} invitations to PlanAccess'
                    f'{cleanup_msg}\n'
                    f'Found {len(orphaned_access)} orphaned PlanAccess records'
                )
                self.stdout.write(self.style.WARNING(message))
            else:
                message = (
                    'Sync completed successfully!\n'
                    f'Synced {sync_count} invitations to PlanAccess'
                    f'{success_cleanup_msg}\n'
                    f'Found {len(orphaned_access)} orphaned PlanAccess records'
                )
                self.stdout.write(self.style.SUCCESS(message))
