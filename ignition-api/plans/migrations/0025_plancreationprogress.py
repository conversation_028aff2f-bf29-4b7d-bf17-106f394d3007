# Generated by Django 5.2.1 on 2025-06-17 10:19

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('plans', '0024_invitation_access_level_planaccess'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PlanCreationProgress',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100, unique=True)),
                ('current_step', models.CharField(default='initializing', max_length=50)),
                ('progress_percentage', models.IntegerField(default=0)),
                ('status_message', models.TextField(default='Starting plan creation...')),
                ('step_details', models.J<PERSON><PERSON>ield(default=dict)),
                ('started_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('initializing', 'Initializing'), ('generating_structure', 'Generating Plan Structure'), ('creating_milestones', 'Creating Milestones'), ('creating_tasks', 'Creating Tasks'), ('creating_subtasks', 'Creating Subtasks'), ('finalizing', 'Finalizing Plan'), ('completed', 'Completed'), ('failed', 'Failed')], default='initializing', max_length=30)),
                ('plan', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='plans.plan')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'plan_creation_progress',
                'ordering': ['-started_at'],
            },
        ),
    ]
