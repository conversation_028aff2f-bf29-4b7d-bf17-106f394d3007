# Generated by Django 5.1.7 on 2025-06-17 08:31

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('plans', '0024_invitation_access_level_planaccess'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='milestone',
            name='name',
            field=models.Char<PERSON>ield(blank=True, max_length=500, null=True),
        ),
        migrations.AlterField(
            model_name='subtask',
            name='name',
            field=models.CharField(blank=True, max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='task',
            name='assignees',
            field=models.ManyToManyField(blank=True, related_name='assigned_task', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='task',
            name='name',
            field=models.CharField(blank=True, max_length=1000, null=True),
        ),
    ]
