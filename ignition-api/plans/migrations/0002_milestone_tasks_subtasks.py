# Generated by Django 5.0.4 on 2024-04-13 03:47

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('plans', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Milestone',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, null=True)),
                ('description', models.TextField(null=True)),
                ('plan', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='plans.plan')),
            ],
            options={
                'db_table': 'milestones',
            },
        ),
        migrations.CreateModel(
            name='Tasks',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=255, null=True)),
                ('milestone', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='plans.milestone')),
            ],
            options={
                'db_table': 'tasks',
            },
        ),
        migrations.CreateModel(
            name='Subtasks',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, null=True)),
                ('tasks', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='plans.tasks')),
            ],
            options={
                'db_table': 'subtasks',
            },
        ),
    ]
