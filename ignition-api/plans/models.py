from django.db import models
from users.models import User
import uuid
from django.utils import timezone


STATUS_CHOICES = (
    (1, 'Todo'),
    (2, 'In Progress'),
    (3, 'Done'),
)

PRIORITY_CHOICES = (
    (1, 'Low'),
    (2, 'Medium'),
    (3, 'High'),
    (4, 'Critical'),
)

TODO_STATUS = 1
INPROGRESS_STATUS = 2
DONE_STATUS = 3


class Plan(models.Model):
    name = models.CharField(null=True, blank=True, max_length=500)
    description = models.TextField(null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=1)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    assignees = models.ManyToManyField(User, related_name='assigned_plan', blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    # Thêm trường status
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    class Meta:
        db_table = 'plans'

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = uuid.uuid4()
        is_new = self.pk is None
        super(Plan, self).save(*args, **kwargs)

        # Create head owner access level for the plan creator
        if is_new and self.user:
            from plans.models import PlanAccess  # Import here to avoid circular import
            PlanAccess.objects.create(
                user=self.user,
                plan=self,
                access_level=PlanAccess.OWNER,
                is_head_owner=True
            )


class Milestone(models.Model):
    name = models.CharField(null=True, blank=True, max_length=500)
    description = models.TextField(null=True, blank=True)
    plan = models.ForeignKey(Plan, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    estimated_duration = models.CharField(max_length=100, null=True, blank=True)
    success_criteria = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'milestones'


class Risk(models.Model):
    risk = models.TextField(null=True, blank=True)
    mitigation = models.TextField(null=True, blank=True)
    milestone = models.ForeignKey(Milestone, on_delete=models.CASCADE, related_name='risks')
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'risks'


class Task(models.Model):
    name = models.CharField(null=True, blank=True, max_length=1000)
    description = models.TextField(null=True, blank=True)
    milestone = models.ForeignKey(Milestone, on_delete=models.CASCADE, null=True, blank=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    status = models.IntegerField(choices=STATUS_CHOICES, default=1)
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    deadline = models.DateField(null=True, blank=True)  # New deadline field
    progress = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    assignees = models.ManyToManyField(User, related_name='assigned_task', blank=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(null=True, blank=True)
    estimated_duration = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'tasks'
        ordering = ['order']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = uuid.uuid4()
        super(Task, self).save(*args, **kwargs)


class Subtask(models.Model):
    name = models.CharField(null=True, blank=True, max_length=1000)
    description = models.TextField(null=True, blank=True)
    task = models.ForeignKey(Task, on_delete=models.CASCADE, null=True, blank=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    status = models.IntegerField(choices=STATUS_CHOICES, default=1)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    progress = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, blank=True)

    class Meta:
        db_table = 'subtasks'
        ordering = ['order']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = uuid.uuid4()

        super(Subtask, self).save(*args, **kwargs)

        # Auto-update parent task status and progress after saving subtask
        if self.task:
            self._update_parent_task_status_and_progress()

    def _update_parent_task_status_and_progress(self):
        """
        Update parent task status and progress based on subtasks completion.
        Logic:
        - If no subtasks completed: Task = TODO (status=1), progress=0
        - If some subtasks completed: Task = IN_PROGRESS (status=2), progress=calculated
        - If all subtasks completed: Task = DONE (status=3), progress=100
        """
        if not self.task:
            return

        # Get all subtasks for this task
        all_subtasks = self.task.subtask_set.all()
        total_subtasks = all_subtasks.count()

        if total_subtasks == 0:
            return

        # Count completed subtasks
        completed_subtasks = all_subtasks.filter(status=3).count()  # status=3 is DONE

        # Calculate progress percentage
        progress = int((completed_subtasks / total_subtasks) * 100)

        # Determine new status
        if completed_subtasks == 0:
            # No subtasks completed -> TODO
            new_status = 1  # TODO
            progress = 0
        elif completed_subtasks == total_subtasks:
            # All subtasks completed -> DONE
            new_status = 3  # DONE
            progress = 100
        else:
            # Some subtasks completed -> IN_PROGRESS
            new_status = 2  # IN_PROGRESS

        # Update task if status or progress changed
        task_updated = False
        if self.task.status != new_status:
            self.task.status = new_status
            task_updated = True

        if self.task.progress != progress:
            self.task.progress = progress
            task_updated = True

        if task_updated:
            # Use update() to avoid triggering save() recursion
            Task.objects.filter(id=self.task.id).update(
                status=new_status,
                progress=progress
            )

            print(f"🔄 Auto-updated Task '{self.task.name}': status={new_status}, progress={progress}% ({completed_subtasks}/{total_subtasks} subtasks completed)")

    def delete(self, *args, **kwargs):
        """Override delete to update parent task after subtask deletion"""
        parent_task = self.task
        super(Subtask, self).delete(*args, **kwargs)

        # Update parent task after deletion
        if parent_task:
            # Create a temporary subtask instance to call the update method
            temp_subtask = Subtask(task=parent_task)
            temp_subtask._update_parent_task_status_and_progress()


class PlanAccess(models.Model):
    OWNER = 'owner'
    EDITOR = 'editor'
    VIEWER = 'viewer'

    ACCESS_LEVEL_CHOICES = [
        (OWNER, 'Owner'),
        (EDITOR, 'Editor'),
        (VIEWER, 'Viewer'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE, related_name="access_levels")
    access_level = models.CharField(max_length=10, choices=ACCESS_LEVEL_CHOICES)
    is_head_owner = models.BooleanField(default=False)
    granted_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="granted_access", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'plan_access'
        unique_together = ('user', 'plan')

    def __str__(self):
        head_indicator = " (Head)" if self.is_head_owner else ""
        return f"{self.user.email} - {self.access_level}{head_indicator} on {self.plan.name}"


class PlanCreationProgress(models.Model):
    """Track detailed progress of AI plan generation"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    session_id = models.CharField(max_length=100, unique=True)  # Unique identifier for this creation session

    # Progress tracking
    current_step = models.CharField(max_length=50, default='initializing')
    status_message = models.TextField(default='Starting plan creation...')

    # Detailed progress steps
    step_details = models.JSONField(default=dict)  # Store detailed step information

    # Timestamps
    started_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    # Final result
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE, null=True, blank=True)
    error_message = models.TextField(null=True, blank=True)

    STATUS_CHOICES = [
        ('initializing', 'Initializing'),
        ('generating_structure', 'Generating Plan Structure'),
        ('creating_milestones', 'Creating Milestones'),
        ('creating_tasks', 'Creating Tasks'),
        ('creating_subtasks', 'Creating Subtasks'),
        ('finalizing', 'Finalizing Plan'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    status = models.CharField(max_length=30, choices=STATUS_CHOICES, default='initializing')

    class Meta:
        db_table = 'plan_creation_progress'
        ordering = ['-started_at']


class Invitation(models.Model):
    PENDING = 0
    ACCEPTED = 1
    REJECTED = -1
    ACCEPTANCE_CHOICES = [
        (PENDING, 'Pending'),
        (ACCEPTED, 'Accepted'),
        (REJECTED, 'Rejected'),
    ]

    email = models.EmailField()
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE, related_name="invitations")
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="sent_invitations")
    access_level = models.CharField(max_length=10, choices=PlanAccess.ACCESS_LEVEL_CHOICES, default=PlanAccess.VIEWER)
    accepted = models.IntegerField(choices=ACCEPTANCE_CHOICES, default=PENDING)
    created_at = models.DateTimeField(default=timezone.now)
    accepted_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Invitation to {self.email} for {self.plan.name} as {self.access_level}"

    def save(self, *args, **kwargs):
        if self.accepted and not self.accepted_at:
            self.accepted_at = timezone.now()
        super().save(*args, **kwargs)
