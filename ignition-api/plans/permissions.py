from rest_framework.permissions import BasePermission
from django.shortcuts import get_object_or_404
from plans.models import Subtask, Task, Plan, Invitation, Milestone, PlanAccess


class canAccessPlan(BasePermission):
    message = "You do not have permission to view this plan detail."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)

            # Primary check: PlanAccess system (unified approach)
            try:
                plan_access = PlanAccess.objects.get(plan=plan, user=request.user)
                # Store access level in request for later use
                request.user_plan_access = plan_access
                return True
            except PlanAccess.DoesNotExist:
                pass

            # Fallback: Check invitation system for backward compatibility
            # This should be temporary until all invitations are migrated
            try:
                invitation = Invitation.objects.get(plan=plan, email=request.user.email, accepted=True)
                # Create a temporary access object for backward compatibility
                request.user_plan_access = type('obj', (object,), {
                    'access_level': invitation.access_level,
                    'is_head_owner': False,
                    'user': request.user,
                    'plan': plan,
                    'granted_by': invitation.invited_by
                })()
                print(f"⚠️ Using fallback invitation access for {request.user.email} on plan '{plan.name}' - consider running migration")
                return True
            except Invitation.DoesNotExist:
                pass

        return False


class CanUpdatePlanMilestone(BasePermission):
    message = "You can not update this milestone."

    def has_permission(self, request, view):
        milestone_id = view.kwargs.get('id', None)
        if milestone_id:
            milestone = get_object_or_404(Milestone, id=milestone_id)
            if milestone.plan.user == request.user:
                return True
            try:
                Invitation.objects.get(plan=milestone.plan, email=request.user.email, accepted=True)
                return True
            except Invitation.DoesNotExist:
                pass
        return False


class IsSubtaskPlanOwner (BasePermission):
    message = "You are not the owner of this plan."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            subtask = get_object_or_404(Subtask, slug=slug)
            if subtask.task.milestone.plan.user == request.user:
                return True
        return False


class IsTaskPlanOwner (BasePermission):
    message = "You are not the owner of this plan."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            task = get_object_or_404(Task, slug=slug)
            if task.milestone.plan.user == request.user:
                return True
        return False


class IsPlanOwner (BasePermission):
    message = "You are not the owner of this plan."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            if plan.user == request.user:
                return True
        return False


class canAccessTask(BasePermission):
    message = "You do not have permission to view this task detail."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            task = get_object_or_404(Task, slug=slug)

            if task.milestone and task.milestone.plan:
                # Primary check: PlanAccess system
                try:
                    plan_access = PlanAccess.objects.get(plan=task.milestone.plan, user=request.user)
                    request.user_plan_access = plan_access
                    return True
                except PlanAccess.DoesNotExist:
                    pass

                # Fallback: Check invitation system for backward compatibility
                try:
                    invitation = Invitation.objects.get(plan=task.milestone.plan, email=request.user.email, accepted=True)
                    request.user_plan_access = type('obj', (object,), {
                        'access_level': invitation.access_level,
                        'is_head_owner': False,
                        'user': request.user,
                        'plan': task.milestone.plan,
                        'granted_by': invitation.invited_by
                    })()
                    return True
                except Invitation.DoesNotExist:
                    pass

            # Allow access if user is the task creator
            if task.user == request.user:
                return True

        return False


class IsOwnerOrEditor(BasePermission):
    """Permission for users who can edit plan content (owners and editors)"""
    message = "You need owner or editor access to perform this action."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            try:
                access = PlanAccess.objects.get(plan=plan, user=request.user)
                return access.access_level in [PlanAccess.OWNER, PlanAccess.EDITOR]
            except PlanAccess.DoesNotExist:
                pass
        return False


class IsOwnerOnly(BasePermission):
    """Permission for owners only"""
    message = "You need owner access to perform this action."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            try:
                access = PlanAccess.objects.get(plan=plan, user=request.user)
                return access.access_level == PlanAccess.OWNER
            except PlanAccess.DoesNotExist:
                pass
        return False


class IsHeadOwnerOnly(BasePermission):
    """Permission for head owner only"""
    message = "You need head owner access to perform this action."

    def has_permission(self, request, view):
        slug = view.kwargs.get('slug', None)
        if slug:
            plan = get_object_or_404(Plan, slug=slug)
            try:
                access = PlanAccess.objects.get(plan=plan, user=request.user)
                return access.access_level == PlanAccess.OWNER and access.is_head_owner
            except PlanAccess.DoesNotExist:
                pass
        return False
