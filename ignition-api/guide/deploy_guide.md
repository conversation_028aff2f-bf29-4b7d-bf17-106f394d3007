# Guide to deploy Django on AWS EC2

### Step 1: Access [AWS Console](https://aws.amazon.com/en/) and click on sign in to the console

### Step 2: The account will be sent separately by <PERSON><PERSON> with root user and email + password

### Step 3: Access <https://ap-southeast-1.console.aws.amazon.com/ec2/home?region=ap-southeast-1#Home>: to purchase a server. (explanation about EC2 server)

### Step 4: Perform the steps to select the configuration and get the server

### Step 5: Set up the configuration to SSH into the server

1. Select launch instance -> ubuntu -> t3.small -> Create new key pair -> save key pair (expand further on setting up elastic ip)
2. Configure storage: 30GB

### Step 6: SSH into the newly set up Ubuntu OS

### Step 7: Set up basic packages on Ubuntu

- Set up/update Ubuntu libraries

```bash
sudo apt-get update
```

- Install Python, pip

```bash
sudo apt-get install python3-pip python3-dev libpq-dev
```

- Install MySQL

```bash
sudo apt install mysql-server
```

```bash
sudo mysql_secure_installation
```

- Create MySQL user

```bash
mysql -u root -p
```

- Set up password for root user

```bash
CREATE USER 'your_user'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON *.* TO 'your_user'@'%' WITH GRANT OPTION;
FLUSH PRIVILEGES;
EXIT;
```

- Install nginx

```bash
sudo apt install nginx
```

### Step 8: Clone project

- Create SSH key
- Add SSH to GitHub
- Clone BE project

### Step 9: makemigrations, migrate, and collectstatic

```bash
python3 manage.py makemigrations
python3 manage.py migrate
python3 manage.py collectstatic
```

### Step 10: Add content to requirements.txt file

Use the gunicorn package to create a WSGI HTTP Server for Django/Python.
Add 'gunicorn' to the requirements.txt file (add to the last line)
Then pip install again
(You can add it locally and push the code up, the server will pull and install)

### Step 11: Run a test command (similar to runserver) to check if gunicorn is running

```bash
gunicorn --workers 3 main.wsgi:application
```

Next, set up gunicorn to run as a daemon - meaning it will continue running even after exiting the server
First, set up the socket config file for gunicorn

Run the command

```bash
sudo nano /etc/systemd/system/gunicorn.socket
```

And add the following content to the file

```text
[Unit]
Description=gunicorn socket

[Socket]
ListenStream=/run/gunicorn.sock

[Install]
WantedBy=sockets.target
```

Create a gunicorn.service file to run as a daemon. Run the command

```bash
sudo nano /etc/systemd/system/gunicorn.service
```

And add the following content to the file

```text
[Unit]
Description=gunicorndaemon
Requires=gunicorn.socket
After=network.target

[Service]
User=ubuntu
Group=www-data
WorkingDirectory=/home/<USER>/hackademic-be-api
ExecStart=/home/<USER>/hackademic-be-api/venv/bin/gunicorn \
          --access-logfile - \
          --workers 3 \
          --bind unix:/run/gunicorn.sock \
 main.wsgi:application


[Install]
WantedBy=multi-user.target
```

Run the commands to reload the daemon

```bash
sudo systemctl daemon-reload
sudo systemctl start gunicorn
sudo systemctl enable gunicorn
sudo systemctl status gunicorn
```

### Step 12: Set up with nginx

- Check if nginx is already installed? Check the status of nginx

```bash
sudo systemctl status nginx

```

- Set up the config file for nginx. First, create a file api.dev in the path: /etc/nginx/sites-available
In the file, you need to pay attention to the following contents:

  - server: opens a config to set up child options within it
  - listen: always listens on port 80 for http protocol or 443 for https
  - server name: set as IP, after config is complete, IP will be configured as domain name
  - location /static/: determines handling when there are requests to static files (such as swagger files)
  - This location block defines how to handle requests to all other paths: for example: <http://domain.com/docs> ...

File content as below with the command:

```bash
sudo nano /etc/nginx/sites-available/api.dev
```

Add the following content to the file

```text
server {
    listen 80;
    server_name *************;

    location = /favicon.ico { access_log off; log_not_found off; }
    location /static/ {
        alias /home/<USER>/hackademic-be-api/static/;
    }

    location / {
        include proxy_params;
        proxy_pass http://unix:/run/gunicorn.sock;
    }

    client_max_body_size 20M;
    large_client_header_buffers 4 16k;
    client_body_buffer_size 1M;
}
```

Link from sites-available to sites-enabled:

```bash
sudo ln -s /etc/nginx/sites-available/api.dev /etc/nginx/sites-enabled/
```

Restart nginx

```bash
sudo systemctl restart nginx
```

### Step 13: Go to GoDaddy and point the IP to the purchased domain

[https://dcc.godaddy.com/]<https://dcc.godaddy.com/>
Account as before.

### Step 14: Set up SSL - HTTPS for the server

Step 1. Install certbot to register a free certificate

```bash
sudo snap install core; sudo snap refresh core
sudo apt remove certbot
sudo snap install --classic certbot
sudo ln -s /snap/bin/certbot /usr/bin/certbot
```

Step 2. Edit the nginx config created above. Use the command:

```bash
sudo mv api.dev planplaner.online
sudo nano /etc/nginx/sites-available/api.dev
```

Step 3. Edit the server name in the config

```text
server_name planplaner.online www.planplaner.online;
```

Step 4. Create Certificate

```bash
sudo certbot --nginx -d planplaner.online -d www.planplaner.online
```

Step 5. Add to the nginx config file

```text
listen 443 ssl;
ssl_certificate /etc/letsencrypt/live/planplaner.online/fullchain.pem;
ssl_certificate_key /etc/letsencrypt/live/planplaner.online/privkey.pem;
include /etc/letsencrypt/options-ssl-nginx.conf;
```

## Final

Truy cập link: [https://planplaner.online](https://planplaner.online) để kiểm tra
