import requests
import json
import time

api_key = '2b22596d7b6b70d47e7ba4db05cc475e'
api_secret = '1c4907c2cbd644152c87e3c5078bf7af'

print('=== Testing email delivery across different providers ===')

# Test with different email providers
test_emails = [
    '<EMAIL>',  # Gmail (new account)
    '<EMAIL>',      # Gmail (original account)
]

# Simple, professional email
def send_test_email(recipient, test_name):
    data = {
        'Messages': [
            {
                'From': {
                    'Email': '<EMAIL>',
                    'Name': 'Ignition Platform'
                },
                'To': [{'Email': recipient}],
                'Subject': f'Test Email Delivery - {test_name}',
                'TextPart': f'''Hello,

This is a test email to check delivery to {recipient}.

Test: {test_name}
Time: January 8, 2025
Status: Delivery Test

This email is sent to verify our email delivery system is working correctly.

Best regards,
Ignition Platform Team

---
Ignition Platform
https://ignitionai.site
''',
                'HTMLPart': f'''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Email Delivery</title>
</head>
<body style="font-family: Arial, sans-serif; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
        <h2 style="color: #2c3e50;">Test Email Delivery</h2>
        
        <p>Hello,</p>
        
        <p>This is a test email to check delivery to <strong>{recipient}</strong>.</p>
        
        <div style="background: white; padding: 15px; border-radius: 6px; margin: 15px 0;">
            <p><strong>Test:</strong> {test_name}</p>
            <p><strong>Time:</strong> January 8, 2025</p>
            <p><strong>Status:</strong> Delivery Test</p>
        </div>
        
        <p>This email is sent to verify our email delivery system is working correctly.</p>
        
        <p>Best regards,<br>Ignition Platform Team</p>
        
        <hr style="margin: 20px 0; border: none; border-top: 1px solid #ddd;">
        <p style="font-size: 12px; color: #666; text-align: center;">
            Ignition Platform<br>
            <a href="https://ignitionai.site" style="color: #3498db;">https://ignitionai.site</a>
        </p>
    </div>
</body>
</html>
                '''
            }
        ]
    }
    
    try:
        response = requests.post(
            'https://api.mailjet.com/v3.1/send',
            headers={'Content-Type': 'application/json'},
            data=json.dumps(data),
            auth=(api_key, api_secret),
            timeout=30
        )
        
        if response.status_code in (200, 201):
            result = response.json()
            message_id = result['Messages'][0]['To'][0].get('MessageID', 'N/A')
            print(f'✅ {test_name} - Email sent to {recipient}')
            print(f'   Message ID: {message_id}')
            return True
        else:
            print(f'❌ {test_name} - Failed to send to {recipient}: {response.text}')
            return False
            
    except Exception as e:
        print(f'❌ {test_name} - Exception for {recipient}: {e}')
        return False

# Send test emails
success_count = 0
total_count = len(test_emails)

for i, email in enumerate(test_emails):
    test_name = f"Test {i+1}"
    print(f'\n--- {test_name}: {email} ---')
    
    if send_test_email(email, test_name):
        success_count += 1
    
    # Small delay between sends
    if i < len(test_emails) - 1:
        time.sleep(2)

print(f'\n=== Summary ===')
print(f'Successfully sent: {success_count}/{total_count} emails')
print('\n🔍 Please check:')
print('1. <EMAIL> (new Gmail account)')
print('2. <EMAIL> (original Gmail account)')
print('\nCompare:')
print('- Which emails go to inbox vs spam')
print('- Any differences in delivery')
print('- Email formatting and appearance')

print('\n💡 If <EMAIL> receives emails in <NAME_EMAIL> gets spam:')
print('   → Gmail has learned to mark emails from this sender as spam for the original account')
print('   → Solution: Use a different verified sender email or improve sender reputation')
